# MQ日志配置说明

## 配置开关

### 两套独立的配置项

```json
{
  "LinShiMQ": {
    "Enabled": false  // 控制旧的MQ日志组件（临时保留）
  },
  "MQLogging": {
    "Enabled": false  // 控制新的MQ日志功能（正式使用）
  }
}
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `LinShiMQ:Enabled` | bool | false | 控制旧的MQ日志组件（临时保留） |
| `MQLogging:Enabled` | bool | false | 控制新的MQ日志功能（正式使用） |

### 配置值说明

#### LinShiMQ（旧的MQ日志组件）
- **`true`**: 启用旧的MQ日志功能
  - MQGrpcLogInterceptor 会记录gRPC接口日志
  - MQLogMiddleware 会记录HTTP接口日志
  - 这些组件是临时保留的，建议不使用

- **`false`**: 禁用旧的MQ日志功能（默认）
  - MQGrpcLogInterceptor 直接跳过，不记录日志
  - MQLogMiddleware 直接跳过，不记录日志
  - 不影响原有功能和性能

#### MQLogging（新的MQ日志功能）
- **`true`**: 启用新的MQ日志功能
  - GrpcPerformanceInterceptor 会记录gRPC接口日志
  - ResponseCompletionMiddleware 会记录HTTP接口日志
  - 这是正式的MQ日志功能

- **`false`**: 禁用新的MQ日志功能（默认）
  - GrpcPerformanceInterceptor 跳过MQ日志记录
  - ResponseCompletionMiddleware 跳过MQ日志记录
  - 不影响原有功能和性能

## 使用场景

### 开发环境（使用新的MQ日志）
```json
{
  "LinShiMQ": {
    "Enabled": false
  },
  "MQLogging": {
    "Enabled": true
  }
}
```

### 生产环境（使用新的MQ日志）
```json
{
  "LinShiMQ": {
    "Enabled": false
  },
  "MQLogging": {
    "Enabled": true
  }
}
```

### 测试环境（禁用所有MQ日志）
```json
{
  "LinShiMQ": {
    "Enabled": false
  },
  "MQLogging": {
    "Enabled": false
  }
}
```

### 临时启用旧组件（不推荐）
```json
{
  "LinShiMQ": {
    "Enabled": true
  },
  "MQLogging": {
    "Enabled": false
  }
}
```

## 影响范围

### LinShiMQ启用时影响
- **MQGrpcLogInterceptor**: 记录所有gRPC接口的请求/响应日志
- **MQLogMiddleware**: 记录所有HTTP接口的请求/响应日志
- **性能影响**: 轻微的性能开销
- **存储影响**: 增加日志存储量

### MQLogging启用时影响
- **GrpcPerformanceInterceptor**: 记录所有gRPC接口的响应日志
- **ResponseCompletionMiddleware**: 记录所有HTTP接口的请求日志
- **性能影响**: 轻微的性能开销（异步记录）
- **存储影响**: 增加日志存储量

### 禁用时影响
- **无性能影响**: 完全跳过MQ日志记录
- **无存储影响**: 不产生MQ日志
- **功能完整**: 不影响原有日志系统和其他功能

## 注意事项

1. **默认禁用**: 项目启动时默认不启用任何MQ日志功能
2. **独立控制**: 两套配置开关完全独立，可以分别控制
3. **建议使用**: 建议使用新的MQLogging配置，LinShiMQ仅作为临时保留
4. **热配置**: 修改配置后需要重启应用才能生效
5. **依赖服务**: 启用时需要确保MQ相关服务正常运行
6. **权限要求**: 需要确保应用有写入MQ的权限

## 相关文件

### 旧的MQ日志组件（LinShiMQ控制）
- `MQGrpcLogInterceptor.cs`: gRPC接口日志记录
- `MQLogMiddleware.cs`: HTTP接口日志记录

### 新的MQ日志功能（MQLogging控制）
- `GrpcPerformanceInterceptor.cs`: gRPC接口日志记录
- `ResponseCompletionMiddleware.cs`: HTTP接口日志记录

### 配置文件
- `appsettings.json`: 配置文件 