# 数据库连接池配置最佳实践

## 📊 配置策略概览

### 🎯 总体设计原则
- **分层管理**：DbContextPool + ADO.NET连接池 两层优化
- **业务分离**：根据数据库使用频率差异化配置
- **环境适配**：生产环境和开发环境使用不同参数
- **监控导向**：可观测的连接池状态

## 🔧 ADO.NET连接池配置

### 生产环境 (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "...;Max Pool Size=150;Min Pool Size=30;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LoggerConnection": "...;Max Pool Size=80;Min Pool Size=15;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LibraryConnection": "...;Max Pool Size=100;Min Pool Size=20;Pooling=true;Connection Lifetime=600;Connection Reset=true;"
  }
}
```

### 开发环境 (appsettings.Development.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "...;Max Pool Size=80;Min Pool Size=10;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LoggerConnection": "...;Max Pool Size=40;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LibraryConnection": "...;Max Pool Size=60;Min Pool Size=8;Pooling=true;Connection Lifetime=600;Connection Reset=true;"
  }
}
```

### 📋 参数说明

| 参数 | 作用 | 推荐值 | 说明 |
|------|------|--------|------|
| `Max Pool Size` | 最大连接数 | 80-150 | 根据并发需求设置 |
| `Min Pool Size` | 最小保持连接数 | 5-30 | 避免冷启动延迟 |
| `Pooling` | 启用连接池 | true | 必须启用 |
| `Connection Lifetime` | 连接最大生存时间(秒) | 600 | 10分钟，避免长连接问题 |
| `Connection Reset` | 连接重置 | true | 清理连接状态 |

## 🏊 DbContextPool配置

### Startup.cs 配置
```csharp
// 主业务数据库 - 高并发访问
services.AddDbContextPool<TeamDbContext>(..., poolSize: 64);

// 日志数据库 - 主要写操作
services.AddDbContextPool<LogDbContext>(..., poolSize: 32);

// 资源库数据库 - 查询为主
services.AddDbContextPool<LibraryDbContext>(..., poolSize: 48);
```

### 📊 池大小设计依据

| 数据库 | DbContext池大小 | ADO.NET池大小(生产) | 使用特征 |
|--------|-----------------|---------------------|----------|
| TeamDB | 64 | 150 | 高频CRUD，多租户隔离 |
| LogDB | 32 | 80 | 写入密集，查询较少 |
| LibraryDB | 48 | 100 | 读取密集，缓存友好 |

## 🎯 性能优化配置

### 查询优化
```csharp
sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
```
- **作用**：将复杂查询拆分为多个SQL语句
- **优势**：减少笛卡尔积，提高查询性能
- **适用**：包含多个Include()的查询

### 连接管理优化
```csharp
// 连接超时设置
Connect Timeout=300         // 5分钟连接超时
Connection Lifetime=600     // 10分钟连接生命周期
Connection Reset=true       // 启用连接重置
```

## 📈 监控建议

### 1. 数据库连接监控
```sql
-- 查看当前连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看最大连接数历史
SHOW STATUS LIKE 'Max_used_connections';

-- 查看MySQL配置的最大连接数
SHOW VARIABLES LIKE 'max_connections';
```

### 2. 应用层监控
建议添加以下监控指标：
- DbContext池使用率
- 连接池等待时间
- 连接创建/销毁频率
- 数据库查询响应时间

## ⚠️ 注意事项

### MySQL服务器配置
确保MySQL服务器配置足够的连接数：
```sql
-- 建议设置为连接池总和的1.5倍
SET GLOBAL max_connections = 500;
```

### 内存使用
- 每个DbContext实例约占用 1-2MB 内存
- 总池配置：64+32+48 = 144个实例 ≈ 200-300MB

### 故障恢复
- `Connection Reset=true` 确保连接状态清理
- `Connection Lifetime=600` 避免长连接积累问题
- 合理的超时设置避免连接泄露

## 🔄 调优建议

### 监控指标阈值
- 连接池使用率 > 80% 时考虑扩容
- 平均等待时间 > 100ms 时优化查询
- 连接创建频率过高时增加Min Pool Size

### 渐进式调优
1. 从保守配置开始
2. 基于监控数据逐步调整
3. 压力测试验证配置效果
4. 建立配置变更记录 