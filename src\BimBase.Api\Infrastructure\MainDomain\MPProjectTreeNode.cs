﻿using System.Runtime.Serialization;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.MainDomain
{
    [Table("mpprojecttreenodes")]
    public class MPProjectTreeNode
    {

        public MPProjectTreeNode()
        {

        }
        [Key]
        [Column("ID")]
        public int ID { get; set; }
        
        [Column("NodeId")]
        public long NodeId { get; set; }

        [Column("InstanceId")]
        public long InstanceId { get; set; }
        /// <summary>
        /// 所属树节点
        /// </summary>
        [Column("TreeId")]
        public long TreeId { get; set; }
        /// <summary>
        /// 父节点
        /// </summary>
        [Column("ParentNodeId")]
        public long ParentNodeId { get; set; }
        /// <summary>
        /// 节点类型
        /// </summary>
        [Column("NodeType")]
        public int NodeType { get; set; }

        [Column("NodeName")]
        [StringLength(190)]
        public string NodeName { get; set; }
        /// <summary>
        /// 节点业务对象
        /// </summary>
        [Column("bPDataKey")]
        public long bPDataKey { get; set; }
        /// <summary>
        /// modelinfo对象
        /// </summary>
        [Column("modelnfoKey")]
        public long modelnfoKey { get; set; }
        ///// <summary>
        ///// 树类型
        ///// </summary>
        //
        //[Required, EnumDataType(typeof(TreeType))]
        //public TreeType TreeType { get; set; }

        /// <summary>
        /// 所属子项目（模型）id
        /// </summary>
        [Column("subProjectld")]
        public Guid subProjectld { get; set; }

        /// <summary>
        /// 节点层级
        /// </summary>
        [Column("level")]
        public int level { get; set; }

        /// <summary>
        /// 节点索引编码
        /// </summary>
        [Column("indexCode")]
        public string indexCode { get; set; }
        
        /// <summary>
        /// 节点索引编码（截取前190字符，用于索引）
        /// </summary>
        [Column("indexCode2")]
        [StringLength(190)]
        public string indexCode2 { get; set; }

        /// <summary>
        /// 原始节点名称（用于记录冲突前的名称）
        /// </summary>
        [Column("OriginalNodeName")]
        [StringLength(190)]
        public string OriginalNodeName { get; set; }

        /// <summary>
        /// 冲突标记（1=存在冲突，0=无冲突）
        /// </summary>
        [Column("HasConflict")]
        public int HasConflict { get; set; }

        /// <summary>
        /// 冲突检测范围标识（用于动态唯一索引）
        /// 全局检测：使用节点名称作为范围标识
        /// 同级检测：使用父节点ID作为范围标识
        /// </summary>
        [Column("ConflictScope")]
        [StringLength(190)]
        public string ConflictScope { get; set; }

    }
}
