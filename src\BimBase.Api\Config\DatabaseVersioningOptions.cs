using System.ComponentModel.DataAnnotations;

namespace BimBase.Api.Config
{
    /// <summary>
    /// 数据库版本控制配置选项
    /// </summary>
    public class DatabaseVersioningOptions
    {
        public const string SectionName = "DatabaseVersioning";

        /// <summary>
        /// 获取或设置数据库版本控制操作。
        /// 可选值: "Upgrade", "Downgrade", "Initialize", "None" (或其他自定义操作)。
        /// 默认为 "Upgrade"。
        /// </summary>
        public string Action { get; set; } = "Upgrade";

        /// <summary>
        /// 获取或设置目标数据库版本。
        /// 例如 "v1", "v2", "latest"。
        /// </summary>
        public string TargetVersion { get; set; }

        /// <summary>
        /// 获取或设置是否在应用程序启动时自动执行数据库版本控制操作。
        /// 默认为 true。
        /// </summary>
        public bool AutoExecute { get; set; } = true;

        // 您可以在此处添加更多与数据库版本控制相关的配置属性
        // 例如：
        // /// <summary>
        // /// 获取或设置连接字符串的名称，用于版本控制（如果不同于默认连接）。
        // /// </summary>
        // public string ConnectionStringName { get; set; }

        // /// <summary>
        // /// 获取或设置执行数据库操作的超时时间（秒）。
        // /// </summary>
        // [Range(30, 600)]
        // public int CommandTimeoutSeconds { get; set; } = 120;
    }
} 