﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class ObjectInfo
    {
        [Key]
        public int ID { get; set; }

        public long InstanceId { get; set; }  //构件ID

        public string DomainClassName { get; set; }

        public String ECSchemaName { get; set; }
    }
}
