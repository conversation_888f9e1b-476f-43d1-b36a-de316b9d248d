using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.LibraryDb
{
    /// <summary>
    /// LibraryDb升级接口
    /// </summary>
    public interface ILibraryDbUpgrade
    {
        /// <summary>
        /// 源版本号
        /// </summary>
        string FromVersion { get; }

        /// <summary>
        /// 目标版本号
        /// </summary>
        string ToVersion { get; }

        /// <summary>
        /// 升级描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 执行升级
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <returns></returns>
        Task UpgradeAsync(LibraryDbContext context);

        /// <summary>
        /// 回滚升级（如果支持）
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <returns></returns>
        Task RollbackAsync(LibraryDbContext context);

        /// <summary>
        /// 是否支持回滚
        /// </summary>
        bool SupportsRollback { get; }
    }
} 