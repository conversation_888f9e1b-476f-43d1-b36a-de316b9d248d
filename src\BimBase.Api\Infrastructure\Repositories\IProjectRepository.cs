﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.ModelDomain;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BimBase.Api.Infrastructure.Repositories
{
    public interface IProjectRepository : IRepository
    {
        int CurrentVersionNo { get; }
        IQueryable<Relationship> Relationships { get; }
        IQueryable<ResourceClass> ResourceClasses { get; }
        IQueryable<HistoryData> AllHistoryDatas { get; }
        IQueryable<HistoryRelationship> AllHistoryRelationships { get; }
        IQueryable<ProjectMember> Members { get; }
        IQueryable<LockedComponents> LockedComponents { get; }
        IQueryable<ModelData> Datas { get; }
        IQueryable<ModelLock> ModelLocks { get; }
        IQueryable<ModelFile> ModelFiles { get; }
        IQueryable<MilestoneFiles> MilestoneFiles { get; }
        IQueryable<VersionData> Versions { get; }
        IQueryable<StoreyLock> StoreyLocks { get; }
        IQueryable<VersionDomain> VersionDomains { get; }
        IQueryable<ModelDatasDomainClassSummary> ModelDatasDomainClassSummaries { get; }

        IQueryable<ModelDataVersionTracker> ModelDataVersionTrackers { get; }

        ProjectMember AddMember(TeamMember tmbr, bool asAdmin = false);

        ProjectMember DeleteMember(ProjectMember mbr);

        ReleaseInformation AddReleaseInformation(ReleaseInformation ri);

        bool SetProjectSchema(ModelDomain.SchemaVersion version);

        /// <summary>
        /// add by asdf 2018-06-07 将解锁的构件从表LockedComponents 删除
        /// 替代老接口 UnlockDatas
        /// </summary>
        /// <param name="member"></param>
        /// <param name="dataids"></param>
        /// <returns></returns>
        bool UnlockDatasNew(ProjectMember member, ICollection<long> dataids, Guid projectGuid);
        bool SetProjectAdmin(ProjectMember pm, bool isProjectAdmin);
        /// <summary>
        /// 通过用户名获取用户Id (TeamMember ID)
        /// </summary>
        /// <param name="getMemberIdByLoginName">通过用户名获取用户Id (TeamMember ID)</param>
        /// <param name="loginName">用户名</param>
        /// <returns></returns>
        ProjectMember GetProjectMember(Func<string,Guid> getMemberIdByLoginName, string loginName);

        ModelDomain.RepositoryInformation CreateRepository(ProjectMember owner);
        ModelDomain.RepositoryInformation DeleteRepository(int id);


        bool GetProjectSchema(out ModelDomain.SchemaVersion version);


        /// <summary>
        /// add by asdf 2018-04-02 从缓存获取关系数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="ids"></param>
        /// <param name="source"></param>
        /// <returns></returns>
        List<long> FindRelatedDatasFromCahce(Guid projectGuid, List<long> ids, IEnumerable<Relationship> source);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="ids"></param>
        /// <param name="source"></param>
        /// <returns></returns>
        List<long> FindRelatedDatasFromCahce(Guid projectGuid, List<long> ids, IEnumerable<RelatedSearchDto> source);


        List<long> FindRelatedDatas(List<long> ids);

        bool CheckSingletonClass(List<SingletonClass> singletonClasses,
            out List<SingletonClass> conflictSingletonClasses);

        bool LockDatasNew(ProjectMember member, ICollection<LockedComponents> lockedComponents);
        bool LockDatasNewWithLockType(ProjectMember member, ICollection<LockedComponents> lockedComponents, Guid projectGuid);
        bool CheckResourceClassNew(int userId, List<string> resourceClassNames,
            out List<ResourceClass> conflictResourceClasses, out List<ModelData> needLockList, out List<int?> conflictUserList);

        bool CheckResourceClass(int userId, List<ResourceClass> resourceClasses,
            out List<ResourceClass> conflictResourceClasses, out List<ModelData> needLockList, out List<int?> conflictUserList);

        /// <summary>
        /// add by asdf 2018-02-11 生成版本号就可以返回结果，异步执行后面的批量插入数据
        /// </summary>
        /// <param name="versionData"></param>
        /// <returns></returns>
        int SaveVersionInfo(VersionData versionData);

        bool CheckInWithDatasForWeb(ITeamRepository teamRepository, string author, string path, int verNo);

        bool UpdataVersionInfo(int verNo, int flag);

        bool AddVersionDomains(ICollection<VersionDomain> versionDomains);
        //判断数据库中是否有重复ID
        bool IsRepeatIdInServer(List<Int64> modeldata_InstanceId_list, List<Int64> relationship_InstanceId_list, out string repeateDataIdsStr, out string repeateRelationIdsStr, out List<long> repeateDatas, out List<long> repeateRelations);

        bool AddSingletonClass(List<SingletonClass> singletonClasses, out List<SingletonClass> conflictSingletonClasses);
        bool AddResourceClass(List<ResourceClass> resourceClasses, out List<ResourceClass> conflictResourceClasses);
        bool ReplaceResourceClass(List<ResourceClass> resourceClasses, List<long> ids, out List<ModelData> delResourceModelDatas);
        bool DeleteSingletonClass(List<SingletonClass> singletonClasses);
        /// <summary>
        /// add by asdf 2018-07-17 保存上传成功后操作记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        bool AddCheckInRecord(CheckInRequestRecord request);

        bool CheckInToDBFromLocalFile(long requestId);
        bool WriteDataToLocalFile(long requestId, List<ModelData> addDatas, List<ModelData> modifyDatas,
                                    List<ModelData> deleteDatas, List<Relationship> addRelationships,
                                    List<Relationship> modifyRelationships,
                                    List<Relationship> deleteRelationships, int verNo);
        bool CheckInWithDatas(ITeamRepository teamRepository, List<ModelData> addDatas, List<ModelData> modifyDatas, List<ModelData> deleteDatas,
            List<Relationship> addRelationships, List<Relationship> modifyRelationships,
            List<Relationship> deleteRelationships, ProjectMember projMbr, bool unlock, String author, int verNo,
            string desc = null);

        bool CheckLockConflict(int userId, ICollection<Int64> dataIds, out List<int?> conflictids);
        List<ModelData> GetDatas(IEnumerable<Int64> dataIds);
        bool GetDatabyUser(int userId, List<Int64> dataIds, out List<Int64> conflictIds);
        bool AddStoreyLock(StoreyLock storeyLock);

        bool DeleteModelLock(string lockuser);

        bool AddModelLock(ModelLock lockuser);
        long AddModelFile(ModelFile mf);
        bool AddMilestoneFile(MilestoneFiles msf);
        bool Destroy();

    }
}
