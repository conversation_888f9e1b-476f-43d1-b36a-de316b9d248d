using Serilog.Core;
using Serilog.Events;
using BimBase.Api.Infrastructure.Grpc;

namespace BimBase.Api.Infrastructure.Logging
{
    /// <summary>
    /// Serilog日志增强器，自动为所有日志添加RequestId和ClientIp
    /// </summary>
    public class RequestContextEnricher : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            // 直接使用RequestContext.Current获取当前请求上下文
            var context = RequestContext.Current;
            if (context != null)
            {
                // 添加RequestId
                if (!string.IsNullOrEmpty(context.RequestId))
                {
                    logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("RequestId", context.RequestId));
                }

                // 添加ClientIp
                if (!string.IsNullOrEmpty(context.ClientIp))
                {
                    logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("ClientIp", context.ClientIp));
                }
            }
        }
    }
} 