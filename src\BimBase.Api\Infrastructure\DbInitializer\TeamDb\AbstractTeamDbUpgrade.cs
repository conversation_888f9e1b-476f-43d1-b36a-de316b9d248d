using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库升级的抽象基类
    /// </summary>
    public abstract class AbstractTeamDbUpgrade : ITeamDbUpgrade
    {
        protected readonly ILogger Logger;

        public abstract string SourceVersion { get; }
        public abstract string TargetVersion { get; }
        public abstract string Description { get; }
        
        /// <summary>
        /// 是否在回滚后更新版本表，默认为true
        /// 对于会删除版本表的升级类（如v0_to_v1），应设置为false
        /// </summary>
        protected virtual bool ShouldUpdateVersionOnRollback => true;

        protected AbstractTeamDbUpgrade(ILogger logger)
        {
            Logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger.Instance;
        }

        /// <summary>
        /// 执行升级操作的模板方法
        /// </summary>
        public async Task UpgradeAsync(TeamDbContext context)
        {
            await ExecuteUpgradeAsync(context);
            await UpdateVersionAsync(context, TargetVersion, Description);
        }

        /// <summary>
        /// 执行回滚操作的模板方法
        /// </summary>
        public async Task RollbackAsync(TeamDbContext context)
        {
            await ExecuteRollbackAsync(context);
            
            // 根据配置决定是否更新版本表
            if (ShouldUpdateVersionOnRollback)
            {
                await RemoveVersionAsync(context, TargetVersion);
            }
        }

        /// <summary>
        /// 具体的升级实现，由子类实现
        /// </summary>
        protected abstract Task ExecuteUpgradeAsync(TeamDbContext context);

        /// <summary>
        /// 具体的回滚实现，由子类实现
        /// </summary>
        protected abstract Task ExecuteRollbackAsync(TeamDbContext context);

        #region 辅助方法

        /// <summary>
        /// 检查列是否存在
        /// </summary>
        protected async Task<bool> ColumnExistsAsync(TeamDbContext context, string tableName, string columnName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND column_name = '{columnName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查索引是否存在
        /// </summary>
        protected async Task<bool> IndexExistsAsync(TeamDbContext context, string tableName, string indexName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.statistics 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND index_name = '{indexName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查表是否存在
        /// </summary>
        protected async Task<bool> TableExistsAsync(TeamDbContext context, string tableName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 添加列（如果不存在）
        /// </summary>
        protected async Task AddColumnIfNotExistsAsync(TeamDbContext context, string tableName, string columnName, string columnDefinition)
        {
            if (await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogInformation($"{tableName}表{columnName}字段已存在，跳过添加");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnDefinition};
            ");
            Logger.LogInformation($"成功添加{columnName}列到{tableName}表");
        }
        
        /// <summary>
        /// 删除列（如果存在）
        /// </summary>
        protected async Task DropColumnIfExistsAsync(TeamDbContext context, string tableName, string columnName)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogInformation($"{tableName}表{columnName}字段不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` DROP COLUMN `{columnName}`;
            ");
            Logger.LogInformation($"成功从{tableName}表中移除{columnName}列");
        }
        
        /// <summary>
        /// 创建索引（如果不存在）
        /// </summary>
        protected async Task CreateIndexIfNotExistsAsync(TeamDbContext context, string tableName, string indexName, string columnName)
        {
            if (await IndexExistsAsync(context, tableName, indexName))
            {
                Logger.LogInformation($"{tableName}表{indexName}索引已存在，跳过创建");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                CREATE INDEX `{indexName}` ON `{tableName}` (`{columnName}`);
            ");
            Logger.LogInformation($"成功为{tableName}表的{columnName}字段创建索引{indexName}");
        }
        
        /// <summary>
        /// 删除索引（如果存在）
        /// </summary>
        protected async Task DropIndexIfExistsAsync(TeamDbContext context, string tableName, string indexName)
        {
            if (!await IndexExistsAsync(context, tableName, indexName))
            {
                Logger.LogInformation($"{tableName}表{indexName}索引不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                DROP INDEX `{indexName}` ON `{tableName}`;
            ");
            Logger.LogInformation($"成功移除{tableName}表{indexName}索引");
        }
        /// <summary>
        /// 修改列定义（如果存在）
        /// </summary>
        protected async Task ModifyColumnIfExistsAsync(TeamDbContext context, string tableName, string columnName, string newColumnDefinition)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogWarning($"{tableName}表{columnName}字段不存在，无法修改");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` MODIFY COLUMN `{columnName}` {newColumnDefinition};
            ");
            Logger.LogInformation($"成功修改{tableName}表{columnName}列定义");
        }
        #endregion

        #region 版本管理方法

        /// <summary>
        /// 更新版本记录
        /// </summary>
        protected async Task UpdateVersionAsync(TeamDbContext context, string version, string description)
        {
            // 检查版本记录是否已存在，避免重复添加
            var existingVersion = await context.DatabaseVersions
                .FirstOrDefaultAsync(v => v.Version == version);
                
            if (existingVersion != null)
            {
                Logger.LogInformation($"版本记录 {version} 已存在，跳过添加");
                return;
            }

            var databaseVersion = new Domain.DatabaseVersion
            {
                Version = version,
                UpgradedAt = DateTime.Now,
                Description = description
            };

            context.DatabaseVersions.Add(databaseVersion);
            Logger.LogInformation($"版本记录已准备更新: {version}");
        }

        /// <summary>
        /// 删除版本记录
        /// </summary>
        protected async Task RemoveVersionAsync(TeamDbContext context, string version)
        {
            // 先检查 DatabaseVersion 表是否存在
            if (!await TableExistsAsync(context, "DatabaseVersion"))
            {
                Logger.LogInformation("DatabaseVersion 表不存在，跳过版本记录删除");
                return;
            }

            var versionToRemove = await context.DatabaseVersions
                .FirstOrDefaultAsync(v => v.Version == version);
                
            if (versionToRemove != null)
            {
                context.DatabaseVersions.Remove(versionToRemove);
                Logger.LogInformation($"版本记录已准备删除: {version}");
            }
            else
            {
                Logger.LogWarning($"未找到版本记录: {version}");
            }
        }

        #endregion
    }
} 