﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.DbInitializer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace BimBase.Api.Infrastructure.Repositories
{

    /// <summary>
    /// 
    /// </summary>
    public class ProjectRepository: IProjectRepository, IDisposable
    {
        private readonly ModelDbContext _modelDbContext;
        private readonly DbConnection _dbConnection;
        private volatile bool disposedValue;
        const int DatabaseExecTimeout = 60000;
        
        // 静态字段用于存储全局服务提供者
        private static IServiceProvider _globalServiceProvider;
        
        /// <summary>
        /// 设置全局服务提供者（在应用启动时调用）
        /// </summary>
        public static void SetGlobalServiceProvider(IServiceProvider serviceProvider)
        {
            _globalServiceProvider = serviceProvider;
        }
        
        /// <summary>
        /// 获取全局服务提供者
        /// </summary>
        public static IServiceProvider GetGlobalServiceProvider()
        {
            if (_globalServiceProvider == null)
            {
                throw new InvalidOperationException("全局服务提供者未初始化。请确保在应用启动时调用SetGlobalServiceProvider方法。");
            }
            
            return _globalServiceProvider;
        }
        public ProjectRepository(DbConnection dbConnection, Guid guid)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
            
            // 获取全局服务提供者来访问配置
            var serviceProvider = GetGlobalServiceProvider();
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            
            // 从配置中获取模板连接字符串
            var templateConnectionString = configuration.GetConnectionString("TemplateConnection");
            if (!string.IsNullOrEmpty(templateConnectionString))
            {
                // 使用配置中的模板连接字符串
                var databaseName = "PKPM-PBIMServer-ModelDB-" + guid.ToString();
                var modelConn = templateConnectionString.Replace("{DatabaseName}", databaseName);
                _modelDbContext = new ModelDbContextFactory().CreateDbContext(new string[] { modelConn });
            }
            else
            {
                // 回退到原有逻辑（使用传入的dbConnection）
                var modelConn = _dbConnection.ConnectionString
                    .Replace("database=pkpm-pbimserver-teamdb", "database=PKPM-PBIMServer-ModelDB-" + guid.ToString(), StringComparison.OrdinalIgnoreCase);
                _modelDbContext = new ModelDbContextFactory().CreateDbContext(new string[] { modelConn });
            }
            
            _modelDbContext.Database.SetCommandTimeout(DatabaseExecTimeout);
            
            ModelDbInitializer.Initialize(_modelDbContext, serviceProvider);
        }

        public int CurrentVersionNo
        {
            get
            {
                if (_modelDbContext.VersionDatas.Any())
                    return _modelDbContext.VersionDatas.AsNoTracking().Where(a => a.IsComplete > 0).Max(v => v.VersionNo);
                return 0;
            }
        }

        public IQueryable<ResourceClass> ResourceClasses
        {
            get { return _modelDbContext.ResourceClasses.AsNoTracking(); }
        }

        public IQueryable<Relationship> Relationships
        {
            get
            {
                return _modelDbContext.Relationships.AsNoTracking();
            }
        }

        public IQueryable<HistoryData> AllHistoryDatas
        {
            get
            {
                return _modelDbContext.HistoryDatas.AsNoTracking();
            }
        }

        public IQueryable<HistoryRelationship> AllHistoryRelationships
        {
            get
            {
                return _modelDbContext.HistoryRelationships.AsNoTracking();
            }
        }

        public IQueryable<ProjectMember> Members
        {
            get { return _modelDbContext.ProjectMembers.AsNoTracking(); }
        }
        public IQueryable<LockedComponents> LockedComponents
        {
            get
            {
                return _modelDbContext.LockedComponents.AsNoTracking();
            }
        }

        public IQueryable<ModelData> Datas
        {
            get
            {
                return _modelDbContext.ModelDatas.AsNoTracking();
            }
        }
        public IQueryable<VersionData> Versions
        {
            get
            {
                return _modelDbContext.VersionDatas.AsNoTracking();
            }
        }

        public IQueryable<VersionDomain> VersionDomains
        {
            get
            {
                return _modelDbContext.VersionDomains.AsNoTracking();
            }
        }

        public IQueryable<StoreyLock> StoreyLocks
        {
            get { return _modelDbContext.StoreyLocks.AsNoTracking(); }
        }

        public IQueryable<ModelFile> ModelFiles
        {
            get
            {
                return _modelDbContext.ModelFiles.AsNoTracking();
            }
        }
        public IQueryable<ModelLock> ModelLocks
        {
            get
            {
                return _modelDbContext.ModelLocks.AsNoTracking();
            }
        }
        public IQueryable<MilestoneFiles> MilestoneFiles
        {
            get { return _modelDbContext.MilestoneFiles.AsNoTracking(); }
        }

        public IQueryable<ModelDatasDomainClassSummary> ModelDatasDomainClassSummaries
        {
            get { return _modelDbContext.ModelDatasDomainClassSummaries.AsNoTracking(); }
        }

        public IQueryable<ModelDataVersionTracker> ModelDataVersionTrackers
        {
            get
            {
                return _modelDbContext.ModelDataVersionTrackers.AsNoTracking();
            }
        }
        public ProjectMember AddMember(TeamMember tmbr, bool asAdmin = false)
        {
            var member = _modelDbContext.ProjectMembers.SingleOrDefault(mem => mem.TeamMemberID == tmbr.ID);
            if (member == null)
            {
                ProjectMember mbr = new ProjectMember
                {
                    TeamMemberID = tmbr.ID,
                    Color = tmbr.Color,
                    IsProjectAdmin = asAdmin ? 1 : 0
                };
                member = _modelDbContext.ProjectMembers.Add(mbr).Entity;
                _modelDbContext.SaveChanges();
            }
            return member;
        }

        public ProjectMember DeleteMember(ProjectMember mbr)
        {
            if (null == mbr)
                return null;
            _modelDbContext.Database.ExecuteSqlRaw("delete from repositoryinformations where Owner_ID = {0}", mbr.ID);
            _modelDbContext.Database.ExecuteSqlRaw("update modeldatas set LockUserID=null where LockUserID={0}", mbr.ID);
            var entity = _modelDbContext.ProjectMembers.Remove(mbr).Entity;
            _modelDbContext.SaveChanges();
            return entity;
        }


        public ReleaseInformation AddReleaseInformation(ReleaseInformation ri)
        {
            ReleaseInformation dbri = _modelDbContext.ReleaseInformations.SingleOrDefault(r => r.ID == ri.ID);
            if (null != dbri)
                return dbri;
            _modelDbContext.ReleaseInformations.Add(ri);
            _modelDbContext.SaveChanges();
            return ri;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    (_modelDbContext as IDisposable)?.Dispose();
                }

                disposedValue = true;
            }
        }
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        public bool SetProjectSchema(ModelDomain.SchemaVersion version)
        {
            var oldversion = _modelDbContext.SchemaVersions.FirstOrDefault();

            if (oldversion == null)
            {
                _modelDbContext.SchemaVersions.Add(version);
                _modelDbContext.SaveChanges();
                return true;
            }

            if ((oldversion.MajorVersion < version.MajorVersion) || (oldversion.MajorVersion == version.MajorVersion && oldversion.MinorVersion < version.MinorVersion) || (oldversion.MajorVersion == version.MajorVersion && oldversion.MinorVersion == version.MinorVersion && oldversion.DevelopVersion < version.DevelopVersion))
            {
                _modelDbContext.SchemaVersions.Remove(oldversion);
                _modelDbContext.SchemaVersions.Add(version);
                _modelDbContext.SaveChanges();
                return true;
            }
            return false;
        }

        /// <summary>
        /// add by asdf 2018-06-07
        /// 替换原接口UnlockDatas
        /// </summary>
        /// <param name="user"></param>
        /// <param name="dataids"></param>
        /// <returns></returns>
        public bool UnlockDatasNew(ProjectMember user, ICollection<long> dataids, Guid projectGuid)
        {
            //没有数据会造成后面的sql语句异常
            if (dataids.Count <= 0) return true;
            HashSet<long> dataIdSet = new HashSet<long>(dataids);
            //优化第一次创建联机项目，无需走下面的代码
            var isExist = _modelDbContext.LockedComponents.AsNoTracking().Where(a => a.LockUserId == user.ID).Any();
            if (!isExist) return true;

            var sb = new StringBuilder();
            var values = string.Join(",", dataIdSet);

            sb.Append("DELETE FROM `LockedComponents` WHERE LockUserId = {0} AND InstanceId in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            _modelDbContext.LockedComponents.RemoveRange();

            _modelDbContext.Database.ExecuteSqlRaw(deleteStr, user.ID);
            return true;

        }

        public bool SetProjectAdmin(ProjectMember pm, bool isProjectAdmin)
        {
            var member = _modelDbContext.ProjectMembers.SingleOrDefault(mem => mem.ID == pm.ID);
            int isAdmin = 0;
            if (isProjectAdmin)
            {
                isAdmin = 1;
            }
            if (member != null)
            {
                member.IsProjectAdmin = isAdmin;
                _modelDbContext.ProjectMembers.Attach(member);
                _modelDbContext.Entry(member).Property(a => a.IsProjectAdmin).IsModified = true;
                _modelDbContext.SaveChanges();
                return true;
            }
            return false;
        }

        public ProjectMember GetProjectMember(Func<string, Guid> getMemberIdByLoginName, string loginName)
        {
            return _modelDbContext.ProjectMembers.SingleOrDefault(m => m.TeamMemberID == getMemberIdByLoginName(loginName));
        }

        public ModelDomain.RepositoryInformation CreateRepository(ProjectMember owner)
        {
            ModelDomain.RepositoryInformation repository = new ModelDomain.RepositoryInformation { Owner = owner };
            var entity =  _modelDbContext.RepositoryInformations.Add(repository).Entity;
            _modelDbContext.SaveChanges();
            return entity;
        }

        public ModelDomain.RepositoryInformation DeleteRepository(int id)
        {
            var repository = _modelDbContext.RepositoryInformations.SingleOrDefault(repo => repo.ID == id);
            if (null != repository)
            {
                var entity =  _modelDbContext.RepositoryInformations.Remove(repository).Entity;
                _modelDbContext.SaveChanges();
                return entity;
            }
                
            return null;
        }


        public bool GetProjectSchema(out ModelDomain.SchemaVersion version)
        {

            version = _modelDbContext.SchemaVersions.FirstOrDefault();

            if (null == version) return false;

            return true;
        }

        /// <summary>
        /// add by asdf 2018-04-02 从缓存获取关系数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="ids"></param>
        /// <param name="source"></param>
        /// <returns></returns>
        public List<long> FindRelatedDatasFromCahce(Guid projectGuid, List<long> ids, IEnumerable<Relationship> source)
        {
            int count = ids.Count;
            if (ids.Count > 0)
            {
                var hashids = new HashSet<long>(ids);
                if (source == null || !source.Any())
                {
                    source = CacheHelper.FromCacheRelationShip(projectGuid, hashids);
                }

                var relationships = source.Where(r =>
                        ((r.Type == RelationshipType.Assemble || r.Type == RelationshipType.Contain ||
                          r.Type == RelationshipType.Dependence)
                         && (hashids.Contains(r.SourceID) || hashids.Contains(r.TargetID))));

                foreach (var relationship in relationships)
                {

                    switch (relationship.Type)
                    {
                        case RelationshipType.Assemble:
                            ids.Add(relationship.SourceID);
                            ids.Add(relationship.TargetID);
                            break;
                        case RelationshipType.Contain:
                            ids.Add(relationship.IsForward ? relationship.SourceID : relationship.TargetID);
                            break;
                        case RelationshipType.Dependence:
                            ids.Add(relationship.IsForward ? relationship.TargetID : relationship.SourceID);
                            break;
                    }
                }

                ids = ids.Distinct().ToList();

                if (count != ids.Count)
                {
                    source = CacheHelper.FromCacheRelationShip(projectGuid, ids);
                    ids = FindRelatedDatasFromCahce(projectGuid,ids, source);
                }

            }
            return ids;
        }

        public List<long> FindRelatedDatasFromCahce(Guid projectGuid, List<long> ids, IEnumerable<RelatedSearchDto> source)
        {
            int count = ids.Count;
            if (ids.Count > 0)
            {
                var hashids = new HashSet<long>(ids);
                if (source == null || !source.Any())
                {
                    source = CacheHelper.FromCacheRelatedSearchDto(projectGuid, ids);
                }
                var relationships = source.Where(r =>
                        ((r.Type == RelationshipType.Assemble || r.Type == RelationshipType.Contain ||
                          r.Type == RelationshipType.Dependence)
                         && (hashids.Contains(r.SourceID) || hashids.Contains(r.TargetID))));

                foreach (var relationship in relationships)
                {

                    switch (relationship.Type)
                    {
                        case RelationshipType.Assemble:
                            ids.Add(relationship.SourceID);
                            ids.Add(relationship.TargetID);
                            break;
                        case RelationshipType.Contain:
                            ids.Add(relationship.IsForward ? relationship.SourceID : relationship.TargetID);
                            break;
                        case RelationshipType.Dependence:
                            ids.Add(relationship.IsForward ? relationship.TargetID : relationship.SourceID);
                            break;
                    }
                }

                ids = ids.Distinct().ToList();

                if (count != ids.Count)
                {
                    source = CacheHelper.FromCacheRelatedSearchDto(projectGuid, ids);
                    ids = FindRelatedDatasFromCahce(projectGuid, ids, source);
                }

            }
            return ids;
        }

        public List<long> FindRelatedDatas(List<long> ids)
        {
            int count = ids.Count;
            int step = 1000;

            if (ids.Count > 0)
            {
                long loop = count / step;

                List<Relationship> relationships = new List<Relationship>();

                for (int i = 0; i <= loop; i++)
                {
                    List<long> tmpids = ids.GetRange(i * step, count - i * step > step ? step : count % step);

                    var hashids = new HashSet<long>(tmpids);

                    var tmprelationships = _modelDbContext.Relationships.Where(r =>
                        ((r.Type == RelationshipType.Assemble || r.Type == RelationshipType.Contain ||
                          r.Type == RelationshipType.Dependence)
                         && (hashids.Contains(r.SourceID) || hashids.Contains(r.TargetID))));

                    relationships.AddRange(tmprelationships);
                }

                foreach (var relationship in relationships)
                {

                    switch (relationship.Type)
                    {
                        case RelationshipType.Assemble:
                            ids.Add(relationship.SourceID);
                            ids.Add(relationship.TargetID);
                            break;
                        case RelationshipType.Contain:
                            ids.Add(relationship.IsForward ? relationship.SourceID : relationship.TargetID);
                            break;
                        case RelationshipType.Dependence:
                            ids.Add(relationship.IsForward ? relationship.TargetID : relationship.SourceID);
                            break;
                    }
                }

                ids = ids.Distinct().ToList();

                if (count != ids.Count)
                    ids = FindRelatedDatas(ids);
            }
            return ids;
        }

        public bool CheckSingletonClass(List<SingletonClass> singletonClasses, out List<SingletonClass> conflictSingletonClasses)
        {
            conflictSingletonClasses = new List<SingletonClass>();

            foreach (var singletonClass in singletonClasses)
            {
                if (_modelDbContext.SingletonClasses.Any(s => s.DomainClassName == singletonClass.DomainClassName && s.SchemaName == singletonClass.SchemaName))
                    conflictSingletonClasses.Add(singletonClass);
            }

            if (conflictSingletonClasses.Any())
                return false;

            return true;
        }
        public bool LockDatasNewWithLockType(ProjectMember user, ICollection<LockedComponents> lockedComponents, Guid projectGuid)
        {
            if (lockedComponents == null || !lockedComponents.Any())
                return true;
            var dataIdSet = lockedComponents.GroupBy(x => x.InstanceId).Select(a => a.First()).ToList();
            var mbr = _modelDbContext.ProjectMembers.SingleOrDefault(mem => mem.ID == user.ID);
            if (mbr == null) return false;

            var sb = new StringBuilder();
            sb.Append("REPLACE INTO `lockedcomponents` (Domain,InstanceId,LockUserId,LockedAt,LockType) VALUES ");
            foreach (var v in dataIdSet)
            {
                sb.AppendFormat("({0},{1},{2},'{3}',{4}),", v.Domain, v.InstanceId, v.LockUserId, v.LockedAt.ToString("yyyy-MM-dd HH:mm:ss"), v.LockType);
            }
            var batchInsertStr = sb.ToString().TrimEnd(',');

            //提高批量插入性能
            _modelDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
            using (var dbContextTransaction = _modelDbContext.Database.BeginTransaction())
            {

                try
                {
                    _modelDbContext.Database.ExecuteSqlRaw(batchInsertStr);
                    dbContextTransaction.Commit();


                    //if (CacheHelper.IsRunningNew)
                    //{
                    //    var addToCacheDto = lockedComponents.Select(l => new LockedComponentsDto
                    //    {
                    //        InstanceId = l.InstanceId,
                    //        LockType = l.LockType,
                    //        LockUserId = l.LockUserId
                    //    }).ToList();
                    //    CacheHelper.CacheLockedDto(projectGuid, addToCacheDto);
                    //}
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }

            }
        }
        public bool LockDatasNew(ProjectMember user, ICollection<LockedComponents> lockedComponents)
        {
            if (lockedComponents == null || !lockedComponents.Any())
                return false;
            var dataIdSet = lockedComponents.GroupBy(x => x.InstanceId).Select(a => a.First()).ToList();
            var mbr = _modelDbContext.ProjectMembers.SingleOrDefault(mem => mem.ID == user.ID);
            if (mbr == null) return false;

            var sb = new StringBuilder();
            sb.Append("REPLACE INTO `lockedcomponents` (Domain,InstanceId,LockUserId,LockedAt) VALUES ");
            foreach (var v in dataIdSet)
            {
                sb.AppendFormat("({0},{1},{2},'{3}'),", v.Domain, v.InstanceId, v.LockUserId, v.LockedAt);
            }
            var batchInsertStr = sb.ToString().TrimEnd(',');

            //提高批量插入性能
            _modelDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
            using (var dbContextTransaction = _modelDbContext.Database.BeginTransaction())
            {
                try
                {
                    _modelDbContext.Database.ExecuteSqlRaw(batchInsertStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }

            }

        }

        public bool CheckResourceClassNew(int userId, List<string> resourceClassNames,
            out List<ResourceClass> conflictResourceClasses, out List<ModelData> needLockList, out List<int?> conflictUserList)
        {
            conflictResourceClasses = new List<ResourceClass>();
            needLockList = new List<ModelData>();
            conflictUserList = new List<int?>();
            foreach (var resourceClass in resourceClassNames)
            {
                if (_modelDbContext.ResourceClasses.Any(s => resourceClass == s.ResourceName))
                {
                    ResourceClass rc = _modelDbContext.ResourceClasses.FirstOrDefault(s => resourceClass == s.ResourceName);
                    long resourceModeldataInstanceId = rc.InstanceId;
                    if (_modelDbContext.LockedComponents.Any(l => l.InstanceId == resourceModeldataInstanceId && l.LockUserId != userId))
                    {
                        var datas = _modelDbContext.LockedComponents.AsNoTracking().FirstOrDefault(l => l.InstanceId == resourceModeldataInstanceId && l.LockUserId != userId);
                        conflictUserList.Add(datas.LockUserId);
                        conflictResourceClasses.Add(rc);
                        return false;
                    }
                    else
                    {
                        if (_modelDbContext.ModelDatas.Any(m => m.InstanceId == resourceModeldataInstanceId))
                        {
                            ModelData md = _modelDbContext.ModelDatas.FirstOrDefault(m => m.InstanceId == resourceModeldataInstanceId);
                            needLockList.Add(md);
                        }
                    }

                }
            }
            return true;
        }


        public bool CheckResourceClass(int userId, List<ResourceClass> resourceClasses, out List<ResourceClass> conflictResourceClasses, out List<ModelData> needLockList, out List<int?> conflictUserList)
        {
            conflictResourceClasses = new List<ResourceClass>();
            needLockList = new List<ModelData>();
            conflictUserList = new List<int?>();
            foreach (var resourceClass in resourceClasses)
            {
                if (_modelDbContext.ResourceClasses.Any(s => s.DomainClassName == resourceClass.DomainClassName && s.SchemaName == resourceClass.SchemaName && resourceClass.ResourceName == s.ResourceName))
                {
                    ResourceClass rc = _modelDbContext.ResourceClasses.FirstOrDefault(s => s.DomainClassName == resourceClass.DomainClassName && s.SchemaName == resourceClass.SchemaName && resourceClass.ResourceName == s.ResourceName);
                    long resourceModeldataInstanceId = rc.InstanceId;
                    if (_modelDbContext.LockedComponents.Any(l => l.InstanceId == resourceModeldataInstanceId && l.LockUserId != userId))
                    {
                        var datas = _modelDbContext.LockedComponents.AsNoTracking().FirstOrDefault(l => l.InstanceId == resourceModeldataInstanceId && l.LockUserId != userId);
                        conflictUserList.Add(datas.LockUserId);
                        conflictResourceClasses.Add(resourceClass);
                    }
                    else
                    {
                        if (_modelDbContext.ModelDatas.Any(m => m.InstanceId == resourceModeldataInstanceId))
                        {
                            ModelData md = _modelDbContext.ModelDatas.FirstOrDefault(m => m.InstanceId == resourceModeldataInstanceId);
                            needLockList.Add(md);
                        }
                    }

                }
            }

            if (conflictResourceClasses.Any())
                return false;

            return true;
        }


        public int SaveVersionInfo(VersionData versionData)
        {
            if (null == versionData)
                throw new Exception("参数有误！");

            _modelDbContext.VersionDatas.Add(versionData);
            _modelDbContext.SaveChanges();

            var addedversion =
                _modelDbContext.ChangeTracker.Entries<VersionData>()
                    .FirstOrDefault(entry => entry.State == EntityState.Unchanged);

            if (addedversion != null) return addedversion.Entity.VersionNo;

            return -1;
        }

        public bool CheckInWithDatasForWeb(ITeamRepository teamRepository, string author, string path, int verNo)
        {
            TeamMember tmbr = teamRepository.Members.SingleOrDefault(m => m.LoginName == author);
            if (null == tmbr || !_modelDbContext.Set<ProjectMember>().Where(mbr => mbr.TeamMemberID == tmbr.ID).Any())
                throw new Exception("版本提交者\"" + author + "\"不存在，或不是项目参与者！");
            IModelBulkOperation bulkOperation = new ModelBulkOperationByMysql(); ;

            if (bulkOperation != null)
            {
                if (verNo == 0)
                {
                    verNo = CurrentVersionNo;
                }
                int versionNo = verNo;//CurrentVersionNo;
                if (versionNo > -1)
                {                                                             
                    bulkOperation.SaveModelDatasForWeb(_modelDbContext, path, versionNo);
                    bulkOperation.SaveHistoryDatasForWeb(_modelDbContext, path, versionNo);
                    bulkOperation.SaveRelationshipDatasForWeb(_modelDbContext, path, versionNo);
                    bulkOperation.SaveHistoryRelationshipDatasForWeb(_modelDbContext, path, versionNo);
                    bulkOperation.SaveResourceForWeb(_modelDbContext, path, versionNo);
                    bulkOperation.SaveSingletonClassDatasForWeb(_modelDbContext, path, versionNo);

                    //_currentNewVersion = null;
                    return true;
                }
            }
            return false;
        }

        //判断数据库中是否有重复的ID
        public bool IsRepeatIdInServer(List<Int64> modeldata_InstanceId_list, List<Int64> relationship_InstanceId_list, out string repeateDataIdsStr, out string repeateRelationIdsStr, out List<long> repeateDatas, out List<long> repeateRelations)
        {
            repeateDatas = new List<long>();
            repeateRelations = new List<long>();
            //optimization by asdf 2018-02-10  查询使用 AsNoTracking()
            //使用缓存将Datas 、 Relationships 的 InstanceId 存放在redis
            List<Int64> modeldataInstanceIdServer = new List<Int64>(_modelDbContext.ModelDatas.AsNoTracking().Select(db => db.InstanceId));
            List<Int64> relationshipInstanceIdServer = new List<Int64>(_modelDbContext.Relationships.AsNoTracking().Select(r => r.InstanceId));
            repeateDataIdsStr = "";
            repeateRelationIdsStr = "";
            if (null != modeldata_InstanceId_list)
            {
                //optimization by asdf 2018-02-10 
                //取两个集合的并集，数量大于0 则有重复
                var repeateDataIds = modeldataInstanceIdServer.Intersect(modeldata_InstanceId_list);
                if (repeateDataIds.Count() > 0)
                {
                    if (repeateDataIds.Count() > 20)
                    {
                        repeateDataIds = repeateDataIds.Take(20);
                    }
                    var temp = _modelDbContext.ModelDatas.AsNoTracking().Where(b => repeateDataIds.Contains(b.InstanceId)).ToList();
                    var t = temp.Select(x => x.DomainClassName).Distinct();
                    var str = "";
                    foreach (var i in t)
                    {
                        str += i + ":";
                        var y = temp.Where(x => x.DomainClassName == i).Select(s => s.InstanceId).ToList();
                        str += string.Join(",", y);
                    }
                    //var str = string.Join(",", repeateDataIds);
                    repeateDatas = repeateDataIds.ToList();
                    repeateDataIdsStr = "DataID重复：" + str;
                    return true;
                }
            }

            if (null != relationship_InstanceId_list)
            {
                var repeateRelationIds = relationshipInstanceIdServer.Intersect(relationship_InstanceId_list);
                if (repeateRelationIds.Count() > 0)
                {
                    if (repeateRelationIds.Count() > 20)
                    {
                        repeateRelationIds = repeateRelationIds.Take(20);
                    }
                    var temp = _modelDbContext.Relationships.AsNoTracking().Where(b => repeateRelationIds.Contains(b.InstanceId)).ToList();
                    var t = temp.Select(x => x.DomainClassName).Distinct();
                    var str = "";
                    foreach (var i in t)
                    {
                        str += i + ":";
                        var y = temp.Where(x => x.DomainClassName == i).Select(s => s.InstanceId).ToList();
                        str += string.Join(",", y);
                    }
                    //var str = string.Join(",", repeateRelationIds);
                    repeateRelations = repeateRelationIds.ToList();
                    repeateRelationIdsStr = "RelationshipID重复：" + str;
                    return true;
                }
            }
            return false;
        }

        public bool AddSingletonClass(List<SingletonClass> singletonClasses, out List<SingletonClass> conflictSingletonClasses)
        {
            conflictSingletonClasses = new List<SingletonClass>();

            foreach (var singletonClass in singletonClasses)
            {
                if (_modelDbContext.SingletonClasses.Any(s => s.DomainClassName == singletonClass.DomainClassName && s.SchemaName == singletonClass.SchemaName))
                    conflictSingletonClasses.Add(singletonClass);
                else
                    _modelDbContext.SingletonClasses.Add(singletonClass);
            }
            _modelDbContext.SaveChanges();
            if (conflictSingletonClasses.Any())
                return false;

            return true;
        }

        public bool AddResourceClass(List<ResourceClass> resourceClasses, out List<ResourceClass> conflictResourceClasses)
        {
            conflictResourceClasses = new List<ResourceClass>();

            foreach (var resourceClass in resourceClasses)
            {
                if (_modelDbContext.ResourceClasses.Any(s => s.DomainClassName == resourceClass.DomainClassName && s.SchemaName == resourceClass.SchemaName && resourceClass.ResourceName == s.ResourceName))
                {
                    conflictResourceClasses.Add(resourceClass);
                }
            }
            _modelDbContext.SaveChanges();
            if (conflictResourceClasses.Any())
                return false;

            return true;
        }


        public bool ReplaceResourceClass(List<ResourceClass> resourceClasses, List<long> ids, out List<ModelData> delResourceModelDatas)
        {
            delResourceModelDatas = new List<ModelData>();

            foreach (var resourceClass in resourceClasses)
            {
                if (_modelDbContext.ResourceClasses.Any(s => s.DomainClassName == resourceClass.DomainClassName && s.SchemaName == resourceClass.SchemaName && resourceClass.ResourceName == s.ResourceName))
                {
                    ResourceClass rc = _modelDbContext.ResourceClasses.FirstOrDefault(s => s.DomainClassName == resourceClass.DomainClassName && s.SchemaName == resourceClass.SchemaName && resourceClass.ResourceName == s.ResourceName);
                    long modelInstanceID = rc.InstanceId;

                    if (_modelDbContext.ModelDatas.Any(m => m.InstanceId == modelInstanceID))
                    {
                        ModelData md = _modelDbContext.ModelDatas.FirstOrDefault(m => m.InstanceId == modelInstanceID);
                        delResourceModelDatas.Add(md);
                    }
                }
            }
            IModelBulkOperation bulkOperation = new ModelBulkOperationByMysql();
            bulkOperation.ReplaceResourceClasses(_modelDbContext, resourceClasses);
            return true;
        }

        public bool DeleteSingletonClass(List<SingletonClass> singletonClasses)
        {
            foreach (var singletonClass in singletonClasses)
            {
                var removeClass = _modelDbContext.SingletonClasses.FirstOrDefault(s => s.DomainClassName == singletonClass.DomainClassName && s.SchemaName == singletonClass.SchemaName && s.InstanceId == singletonClass.InstanceId);

                if (null != removeClass)
                    _modelDbContext.SingletonClasses.Remove(removeClass);
            }
            return true;
        }

        public bool AddCheckInRecord(CheckInRequestRecord request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.RequestId))
            {
                throw new ArgumentNullException("CheckInRequestRecord");
            }
            _modelDbContext.CheckInRequestRecord.Add(request);
            return _modelDbContext.SaveChanges() > 0;
        }

        public bool CheckInToDBFromLocalFile(long requestId)
        {
            IModelBulkOperation bulkOperation = new ModelBulkOperationByMysql();
            if (bulkOperation != null)
            {
                bulkOperation.SaveModelToDB(_modelDbContext,requestId);

                bulkOperation.SaveModelHistoryToDB(_modelDbContext, requestId);
                return true;
            }

            return false;
        }

        public bool WriteDataToLocalFile(long requestId, List<ModelData> addDatas, List<ModelData> modifyDatas,
                                    List<ModelData> deleteDatas, List<Relationship> addRelationships,
                                    List<Relationship> modifyRelationships,
                                    List<Relationship> deleteRelationships,int verNo)
        {
            IModelBulkOperation bulkOperation = new ModelBulkOperationByMysql();
            if (bulkOperation != null)
            {
                if (verNo == 0)
                {
                    verNo = CurrentVersionNo;
                }
                int versionNo = verNo;//CurrentVersionNo;
                if (versionNo > -1)
                {
                    bulkOperation.SaveAddModelDatasToFile(addDatas, versionNo,requestId);
                    bulkOperation.SaveModifyModelDatasToFile(_modelDbContext, modifyDatas, versionNo,requestId);
                    bulkOperation.SaveDeleteIdToFile(deleteDatas,requestId);
                    bulkOperation.SaveHistoryDataToFile(_modelDbContext, addDatas, modifyDatas, deleteDatas, versionNo,requestId);

                    bulkOperation.SaveAddRelationshipDataToFile(addRelationships, versionNo, requestId);
                    bulkOperation.SaveModifyRelationshipDataToFile(_modelDbContext, modifyRelationships, versionNo, requestId);
                    bulkOperation.SaveDeleteRelationshipIdToFile(deleteRelationships,requestId);
                    bulkOperation.SaveHistoryRelationToFile(_modelDbContext, addRelationships, modifyRelationships, deleteRelationships, versionNo,requestId);
                    return true;
                }
            }

            return false;
        }



        public bool CheckInWithDatas(ITeamRepository teamRepository, List<ModelData> addDatas, List<ModelData> modifyDatas,
                                    List<ModelData> deleteDatas, List<Relationship> addRelationships,
                                    List<Relationship> modifyRelationships,
                                    List<Relationship> deleteRelationships,
                                    ProjectMember projectMember,
                                    bool unlock, String author, int verNo, String desc = null)
        {

            TeamMember tmbr = teamRepository.Members.SingleOrDefault(m => m.LoginName == author);
            if (null == tmbr || !_modelDbContext.Set<ProjectMember>().Where(mbr => mbr.TeamMemberID == tmbr.ID).Any())
                throw new Exception("版本提交者\"" + author + "\"不存在，或不是项目参与者！");

            IModelBulkOperation bulkOperation = new ModelBulkOperationByMysql(); 
            if (bulkOperation != null)
            {
                if (verNo == 0)
                {
                    verNo = CurrentVersionNo;
                }
                int versionNo = verNo;//CurrentVersionNo;
                if (versionNo > -1)
                {
                    bulkOperation.SaveModelDatas(_modelDbContext, addDatas, versionNo);
                    bulkOperation.ModifyModelDatas(_modelDbContext, modifyDatas, versionNo);
                    bulkOperation.DeleteModelDatas(_modelDbContext, deleteDatas);
                    bulkOperation.SaveHistoryDatas(_modelDbContext, addDatas, modifyDatas, deleteDatas, versionNo);

                    bulkOperation.SaveRelationshipDatas(_modelDbContext, addRelationships, versionNo);
                    bulkOperation.ModifyRelationshipDatas(_modelDbContext, modifyRelationships, versionNo);
                    bulkOperation.DeleteRelationshipDatas(_modelDbContext, deleteRelationships);
                    bulkOperation.SaveHistoryRelationshipDatas(_modelDbContext, addRelationships, modifyRelationships, deleteRelationships, versionNo);
                    return true;
                }
            }

            return false;
        }

        public bool CheckLockConflict(int userId, ICollection<Int64> dataIds, out List<int?> conflictids)
        {
            conflictids = new List<int?>();
            var dataIdSet = new HashSet<Int64>(dataIds);
            //modify by asdf 2018-06-19 从LockedComponents 表获取构件是否锁定
            var datas = _modelDbContext.LockedComponents.AsNoTracking().Where(d => dataIdSet.Contains(d.InstanceId));
            var lockuserIds = datas.Select(d => d.LockUserId).Distinct().ToList();

            if (lockuserIds.Any(id => id != userId))
            {
                //convert List<int> to List<int?>
                conflictids = lockuserIds.Except(new List<int> { userId }).Select(i => (int?)i).ToList();
                return false;
            }

            return true;
        }

        public List<ModelData> GetDatas(IEnumerable<Int64> dataIds)
        {
            HashSet<Int64> dataIdSet = new HashSet<Int64>(dataIds);
            return _modelDbContext.ModelDatas.AsNoTracking().Where(data => dataIdSet.Contains(data.InstanceId)).ToList();
        }

        public bool GetDatabyUser(int userId, List<Int64> dataIds, out List<Int64> conflictIds)
        {
            conflictIds = new List<long>();
            var dataIdSet = new HashSet<Int64>(dataIds);

            //var datas = m_db.Datas.AsNoTracking().Where(d => dataIdSet.Contains(d.InstanceId));
            //modify by asdf 2018-06-19 查找锁定构建信息从LockedComponents 表获取
            var datas = _modelDbContext.LockedComponents.AsNoTracking().Where(d => dataIdSet.Contains(d.InstanceId) && d.LockUserId == userId).ToList();
            if (!datas.Any())
                return true;

            conflictIds = datas.Select(data => data.InstanceId).ToList();

            return true;
        }

        public bool AddStoreyLock(StoreyLock storeyLock)
        {
            if (_modelDbContext.StoreyLocks.Any(s => s.Domain == storeyLock.Domain))
                return false;

            _modelDbContext.StoreyLocks.Add(storeyLock);
            _modelDbContext.SaveChanges();
            return true;
        }


        public bool UpdataVersionInfo(int verNo, int flag)
        {
            var version = _modelDbContext.VersionDatas.FirstOrDefault(d => d.VersionNo == verNo);

            if (version == null)
            {
                return false;
            }
            else
            {
                version.IsComplete = flag;
                _modelDbContext.VersionDatas.Attach(version);
                _modelDbContext.Entry(version).Property(a => a.IsComplete).IsModified = true;
                _modelDbContext.SaveChanges();
                return true;
            }

        }

        public bool AddVersionDomains(ICollection<VersionDomain> versionDomains)
        {
            _modelDbContext.VersionDomains.AddRange(versionDomains);
            _modelDbContext.SaveChanges();
            return true;
        }
        public bool DeleteModelLock(string lockuser)
        {
            var delModelLock = _modelDbContext.ModelLocks.ToList();
            _modelDbContext.ModelLocks.RemoveRange(delModelLock);
            _modelDbContext.SaveChanges();
            return true;
        }
        public bool AddModelLock(ModelLock lockuser)
        {
            if (_modelDbContext.ModelLocks.Any(s => s.ModelLockUser == lockuser.ModelLockUser))
                return true;

            _modelDbContext.ModelLocks.Add(lockuser);
            _modelDbContext.SaveChanges();
            return true;
        }

        public long AddModelFile(ModelFile mf)
        {
            if (_modelDbContext.ModelFiles.Any(s => s.FileName == mf.FileName && s.VersionNo == mf.VersionNo))
                return -1;

            _modelDbContext.ModelFiles.Add(mf);
            _modelDbContext.SaveChanges();


            var addedversion =
                _modelDbContext.ChangeTracker.Entries<ModelFile>()
                    .FirstOrDefault(entry => entry.State == EntityState.Unchanged);

            if (addedversion != null) return addedversion.Entity.Id;

            return -1;
        }

        public bool AddMilestoneFile(MilestoneFiles msf)
        {
            if (_modelDbContext.MilestoneFiles.Any(s => s.FileName == msf.FileName && s.VersionNo == msf.VersionNo))
                return false;
            _modelDbContext.MilestoneFiles.Add(msf);
            _modelDbContext.SaveChanges();
            return true;
        }
        public bool Destroy()
        {
            return _modelDbContext.Database.EnsureDeleted();
        }

        public bool Insert<T>(T t) where T : class
        {
            _modelDbContext.Set<T>().Add(t);
            return _modelDbContext.SaveChanges() > 0;
        }

        public bool Insert<T>(IEnumerable<T> tList) where T : class
        {
            _modelDbContext.Set<T>().AddRange(tList);
            return _modelDbContext.SaveChanges() > 0;
        }

        public void Update<T>(T t) where T : class
        {
            _modelDbContext.Set<T>().Update(t);
            _modelDbContext.SaveChanges();
        }

        public void Update<T>(IEnumerable<T> tList) where T : class
        {
            _modelDbContext.Set<T>().UpdateRange(tList);
            _modelDbContext.SaveChanges();
        }

        public void Delete<T>(T t) where T : class
        {
            _modelDbContext.Set<T>().Remove(t);
            _modelDbContext.SaveChanges();
        }

        public void Delete<T>(IEnumerable<T> tList) where T : class
        {
            _modelDbContext.Set<T>().RemoveRange(tList);
            _modelDbContext.SaveChanges();
        }

        public IQueryable<TResult> QueryFrom<TSource, TResult>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TResult>> projection) where TSource : class
        {
            var x = _modelDbContext.Set<TSource>().Where(predicate).Select(projection);
            return x;
        }

        public IQueryable<TSource> QueryFrom<TSource>(Expression<Func<TSource, bool>> predicate = null) where TSource : class
        {
            if (predicate is null) return _modelDbContext.Set<TSource>();
            var x = _modelDbContext.Set<TSource>().Where(predicate);
            return x;
        }

    }
}
