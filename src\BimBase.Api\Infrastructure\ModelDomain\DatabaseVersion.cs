using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// ModelDb数据库版本管理表
    /// </summary>
    [Table("DatabaseVersion")]
    public class DatabaseVersion
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 升级时间
        /// </summary>
        [Required]
        public DateTime UpgradedAt { get; set; }

        /// <summary>
        /// 版本描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
} 