﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{

    public enum OperationRecordType
    {
        Add,
        Delete,
        Modify,
        Unchange
    }

    public class HistoryData : BasicModelDataInfomation
    {

        public Int64 StoreyID { get; set; }    //楼层ID

        public int Domain { get; set; }

        [Required, EnumDataType(typeof(OperationRecordType))]
        public OperationRecordType OperationRecordType { get; set; }

        public Guid StoreyGuid
        {
            get;
            set;
        }   //增加构件所属楼层Guid  2019-9-24 重构
        public HistoryData Clone()
        {
            HistoryData historyData = new HistoryData()
            {
                InstanceId = this.InstanceId,
                Data = this.Data,
                ECSchemaName = this.ECSchemaName,
                StoreyID = this.StoreyID,
                Domain = this.Domain,
                OperationRecordType = this.OperationRecordType,
                VersionNo = this.VersionNo,
                StoreyGuid = this.StoreyGuid,
                DomainClassName = this.DomainClassName,
            };
            return historyData;
        }
    }
}
