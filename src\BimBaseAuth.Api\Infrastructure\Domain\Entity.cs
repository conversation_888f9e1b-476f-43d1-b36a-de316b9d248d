﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Domain
{
	public abstract class Entity
	{
		[Key]
		public Guid Id
		{
			get;
			set;
		}

		public Guid CreateId
		{
			get;
			set;
		}

		public string AppId
		{
			get;
			set;
		}

		public DateTime UpdateTime
		{
			get;
			set;
		}

		public short DelFlag
		{
			get;
			set;
		}

		public Entity()
		{
			Id = Guid.NewGuid();
			UpdateTime = DateTime.Now;
			DelFlag = 0;
			AppId = "";
		}
	}
}
