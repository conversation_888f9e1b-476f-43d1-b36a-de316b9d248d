using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.Domain
{
    /// <summary>
    /// 数据库版本实体，用于记录当前数据库版本和升级历史
    /// </summary>
    [Table("DatabaseVersion")]
    public class DatabaseVersion
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Version { get; set; }
        
        [Required]
        public DateTime UpgradedAt { get; set; }
        
        [StringLength(255)]
        public string Description { get; set; }
    }
} 