using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 事务协调器，用于协调多个数据库的事务
    /// </summary>
    public class TransactionCoordinator : IDisposable
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, (DbContext Context, IDbContextTransaction Transaction)> _transactions;
        private bool _prepared = false;
        private bool _committed = false;
        private bool _rolledBack = false;

        public TransactionCoordinator(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _transactions = new Dictionary<string, (DbContext, IDbContextTransaction)>();
        }

        /// <summary>
        /// 注册数据库上下文到事务协调器
        /// </summary>
        /// <param name="name">数据库名称，用于日志标识</param>
        /// <param name="dbContext">数据库上下文</param>
        /// <returns>事务协调器实例，用于链式调用</returns>
        public async Task<TransactionCoordinator> RegisterContextAsync(string name, DbContext dbContext)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));
            
            if (dbContext == null)
                throw new ArgumentNullException(nameof(dbContext));
            
            if (_transactions.ContainsKey(name))
            {
                _logger.LogWarning($"数据库 {name} 已经注册，忽略重复注册");
                return this;
            }
            
            var transaction = await dbContext.Database.BeginTransactionAsync();
            _transactions.Add(name, (dbContext, transaction));
            
            _logger.LogInformation($"数据库 {name} 已注册到事务协调器");
            return this;
        }

        /// <summary>
        /// 准备提交所有事务（第一阶段）
        /// </summary>
        public async Task PrepareAsync()
        {
            if (_prepared)
                throw new InvalidOperationException("事务已经准备提交");
            
            if (_committed || _rolledBack)
                throw new InvalidOperationException("事务已经提交或回滚");
            
            _logger.LogInformation("开始准备事务提交（第一阶段）");
            
            try
            {
                // 第一阶段：验证所有数据库操作是否可以提交
                foreach (var (name, (context, _)) in _transactions)
                {
                    // 这里可以添加验证逻辑
                    await context.SaveChangesAsync();
                    _logger.LogInformation($"数据库 {name} 准备提交成功");
                }
                
                _prepared = true;
                _logger.LogInformation("所有数据库事务准备提交成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "事务准备阶段失败，将回滚所有事务");
                await RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// 提交所有事务（第二阶段）
        /// </summary>
        public async Task CommitAsync()
        {
            if (_committed)
                throw new InvalidOperationException("事务已经提交");
            
            if (_rolledBack)
                throw new InvalidOperationException("事务已经回滚");
            
            if (!_prepared)
                await PrepareAsync();
            
            _logger.LogInformation("开始提交事务（第二阶段）");
            
            try
            {
                // 第二阶段：实际提交
                foreach (var (name, (_, transaction)) in _transactions)
                {
                    await transaction.CommitAsync();
                    _logger.LogInformation($"数据库 {name} 事务提交成功");
                }
                
                _committed = true;
                _logger.LogInformation("所有数据库事务提交成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "事务提交阶段失败，将尝试回滚所有事务");
                
                try
                {
                    await RollbackAsync();
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "事务回滚失败，数据可能处于不一致状态");
                }
                
                throw;
            }
        }

        /// <summary>
        /// 回滚所有事务
        /// </summary>
        public async Task RollbackAsync()
        {
            if (_committed)
                throw new InvalidOperationException("事务已经提交，无法回滚");
            
            if (_rolledBack)
                throw new InvalidOperationException("事务已经回滚");
            
            _logger.LogInformation("开始回滚所有数据库事务");
            
            List<Exception> exceptions = new List<Exception>();
            
            foreach (var (name, (_, transaction)) in _transactions)
            {
                try
                {
                    await transaction.RollbackAsync();
                    _logger.LogInformation($"数据库 {name} 事务回滚成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"数据库 {name} 事务回滚失败");
                    exceptions.Add(ex);
                }
            }
            
            _rolledBack = true;
            
            if (exceptions.Count > 0)
            {
                throw new AggregateException("一个或多个数据库事务回滚失败", exceptions);
            }
            
            _logger.LogInformation("所有数据库事务回滚成功");
        }

        /// <summary>
        /// 释放所有事务资源
        /// </summary>
        public void Dispose()
        {
            if (!_committed && !_rolledBack)
            {
                _logger.LogWarning("事务协调器被释放但事务未提交或回滚，将尝试回滚");
                try
                {
                    RollbackAsync().Wait();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "事务自动回滚失败");
                }
            }
            
            foreach (var (_, (_, transaction)) in _transactions)
            {
                transaction.Dispose();
            }
            
            _transactions.Clear();
        }
    }
} 