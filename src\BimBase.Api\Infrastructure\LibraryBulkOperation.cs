﻿using BimBase.Api.Infrastructure.LibDomain;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System;
using System.Linq;
using System.Runtime.InteropServices;
using System.Diagnostics;
using BimBase.Api.Infrastructure.MainDomain;
using MySqlConnector;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;

namespace BimBase.Api.Infrastructure
{
    public class LibraryBulkOperation : ILibraryBulkOperation
    {
        const int DatabaseExecTimeout = 60000;
        const int RecordCountLimit = 10000;
        private string _foldPath;
        public const int CHUNK_STRING_LENGTH = 30000;
        private readonly char separator = Path.DirectorySeparatorChar;
        public LibraryBulkOperation()
        {
            string temppath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                temppath = "C:/ProgramData/MySQL/MySQL Server 8.0/Uploads";
                temppath = temppath.Replace(@"\", @"/");
                //_foldPath = temppath + @"/PBIMS/LoadinFileDir/";
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                var options = serviceProvider.GetService<IOptions<UrlsConfig>>();
                temppath = options?.Value?.LoadDataSavePath;
                //temppath = Environment.GetEnvironmentVariable("MY_DOCUMENTS");
                //_foldPath = @"/home/<USER>/LoadinFileDir/";
            }
            
            Console.WriteLine("temppath replace===>" + temppath);
            //_foldPath = temppath+@"/PBIMS/LoadinFileDir/";
            _foldPath = temppath + @"/";
            Console.WriteLine("_foldPath====>" + _foldPath);
            //_foldPath =  @"/home/<USER>/LoadinFileDir/";
            if (!Directory.Exists(_foldPath))
            {
                Directory.CreateDirectory(_foldPath);
            }
        }



        private void WriteDataFile(StringBuilder dataSb, string foldPath, string filename)
        {
            string datafilePath = foldPath + filename + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                fileStream.Position = fileStream.Length;
                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {

                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());
                }
            }


        }

        /// <summary>
        /// 使用MySqlBulkLoader执行批量数据导入
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="foldPath">文件路径</param>
        /// <param name="dataSb">要导入的数据</param>
        /// <param name="bulkLoader">配置好的MySqlBulkLoader对象</param>
        private void ExecuteBulkLoad(DbContext context, string foldPath, StringBuilder dataSb, MySqlBulkLoader bulkLoader)
        {
            // 如果bulkLoader没有关联连接，则获取并设置连接
            if (bulkLoader.Connection == null)
            {
                // 获取数据库连接
                var connection = context.Database.GetDbConnection() as MySqlConnection;
                if (connection == null)
                {
                    throw new Exception("Failed to get MySqlConnection");
                }

                // 确保连接打开
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    connection.Open();
                }
                
                // 设置连接
                bulkLoader.Connection = connection;
            }
            else if (bulkLoader.Connection.State != System.Data.ConnectionState.Open)
            {
                // 如果连接已设置但未打开，则打开连接
                bulkLoader.Connection.Open();
            }
            
            Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + "data" + fileGuid + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.Create, FileAccess.Write))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);

                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {
                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());
                }
            }
            
            try
            {
                // 设置文件路径
                bulkLoader.FileName = datafilePath;
                
                Console.WriteLine($"开始导入数据到表: {bulkLoader.TableName}");
                
                // 执行导入
                int result;
                try 
                {
                    result = bulkLoader.Load();
                    Console.WriteLine($"成功导入数据，共 {result} 行");
                }
                catch (MySqlException mysqlEx)
                {
                    Console.WriteLine($"MySQL异常: {mysqlEx.Message}, 错误码: {mysqlEx.Number}");
                    if (mysqlEx.Message.Contains("LOAD DATA LOCAL INFILE"))
                    {
                        Console.WriteLine("请确保连接字符串中包含 AllowLoadLocalInfile=true 参数");
                    }
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Load data in file fail======================>" + ex.Message);
                Console.WriteLine("Load data in file fail======================>" + ex.StackTrace);
                throw;
            }
            finally
            {
                try
                {
                    File.Delete(datafilePath);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
                }
            }
        }

        private void ExecuteLibraryDbCommand(DbContext context, string foldPath, string command, string dataFileName)
        {
            string datafilePath = foldPath + dataFileName;
            try
            {

                string realpath = "";
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    realpath = datafilePath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                    var options = serviceProvider.GetService<IOptions<UrlsConfig>>();
                    //此路径需要传递给集群中的MySQL执行load data file命令，需要使用MysqLoadDataPath配置路径
                    realpath = Path.Combine(options?.Value?.MysqLoadDataPath, dataFileName);   
                    //realpath = "/home/<USER>/LoadinFileDir/" + dataFileName;
                }
                Console.WriteLine($"realpath===>{realpath}");
                context.Database.SetCommandTimeout(DatabaseExecTimeout);
                var rawSql = "load data infile '" + realpath + command;
                int result = context.Database.ExecuteSqlRaw(rawSql);

            }
            catch (Exception ex)
            {
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.Message);
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.StackTrace);
                throw new Exception("Load data in file fail" + ex.Message);
            }

            try
            {
                File.Delete(datafilePath);
            }
            catch (Exception)
            {
                Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
            }

        }


        private string ReadDataFile(string foldPath, string filename)
        {
            //Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + filename;
            string strLine = "";
            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {

                using (StreamReader sr = new StreamReader(fileStream))
                {

                    strLine = sr.ReadToEnd();
                }
            }

            return strLine;
        }

        public void SaveHistoryLibData(DbContext context, List<CLLibraryData> addDatas, List<CLLibraryData> modifyDatas, List<CLLibraryData> delDatas, int? version)
        {
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "cllibrarydatahistories",
                FieldTerminator = ",",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8"
            };
            
            // 添加列
            bulkLoader.Columns.Add("DataId");
            bulkLoader.Columns.Add("SchemaName");
            bulkLoader.Columns.Add("ClassName");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("@hexdata");
            bulkLoader.Columns.Add("OperationType");
            
            // 添加表达式
            bulkLoader.Expressions.Add("Data=UNHEX(@hexdata)");

            // 处理添加数据的历史记录
            if (addDatas.Any())
            {
                ProcessBatch(context, addDatas, data => {
                    string dataStr = "";
                    if (data.Data != null)
                    {
                        string str = BitConverter.ToString(data.Data);
                        String[] tempArr = str.Split('-');
                        dataStr = string.Join("", tempArr);
                    }
                    return data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                        (data.Data == null ? "\\N" : dataStr) + "," + (int)OperationType.Add + "\n";
                }, bulkLoader, RecordCountLimit, -1);
            }

            // 处理修改数据的历史记录
            if (modifyDatas.Any())
            {
                ProcessBatch(context, modifyDatas, data => {
                    string dataStr = "";
                    if (data.Data != null)
                    {
                        string str = BitConverter.ToString(data.Data);
                        String[] tempArr = str.Split('-');
                        dataStr = string.Join("", tempArr);
                    }
                    return data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                        (data.Data == null ? "\\N" : dataStr) + "," + (int)OperationType.Modify + "\n";
                }, bulkLoader, RecordCountLimit, -1);
            }

            // 处理删除数据的历史记录
            if (delDatas.Any())
            {
                ProcessBatch(context, delDatas, data => {
                    return data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                        "\\N" + "," + (int)OperationType.Delete + "\n";
                }, bulkLoader, RecordCountLimit, -1);
            }
        }
        public void saveLockLibDatas(DbContext m_db, List<CLTreeNodeLock> recordDatas)
        {
            //PbimLog log = new PbimLog("Info");
            //log.Info("saveLockLibDatas===begin");
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            if (!recordDatas.Any()) return;
            
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "cltreenodelocks",
                FieldTerminator = ",",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8",
                ConflictOption = MySqlBulkLoaderConflictOption.Replace
            };
            
            // 添加列
            bulkLoader.Columns.Add("NodeId");
            bulkLoader.Columns.Add("LockUserName");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("LockType");
            
            // 处理批量导入
            ProcessBatch(m_db, recordDatas, data => {
                return data.NodeId + "," + data.LockUserName + "," + data.LibId + "," + data.LockType + "\n";
            }, bulkLoader, -1, 200 * 1024 * 1024);
            
            stopwatch.Stop();
            //log.Info("saveLockLibDatas===end;插入" + recordDatas.Count + "条数据,耗时：" + stopwatch.ElapsedMilliseconds + "毫秒");
        }
        public void SaveHistoryTreeNodes(DbContext context, List<CLCatalogTreeNode> addDatas, List<CLCatalogTreeNode> modifyDatas, List<CLCatalogTreeNode> delDatas, int? version)
        {
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "clcatalogtreenodehistories",
                FieldTerminator = "^^",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8"
            };
            
            // 添加列
            bulkLoader.Columns.Add("NodeId");
            bulkLoader.Columns.Add("InstanceId");
            bulkLoader.Columns.Add("TreeId");
            bulkLoader.Columns.Add("ParentNodeId");
            bulkLoader.Columns.Add("NodeType");
            bulkLoader.Columns.Add("NodeName");
            bulkLoader.Columns.Add("bPDataKey");
            bulkLoader.Columns.Add("modelnfoKey");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("OperationType");

            // 处理添加数据的历史记录
            if (addDatas.Any())
            {
                ProcessBatch(context, addDatas, data => {
                    return data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                        data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                        data.LibId + "^^" + (int)OperationType.Add + "\n";
                }, bulkLoader, RecordCountLimit,-1);
            }

            // 处理修改数据的历史记录
            if (modifyDatas.Any())
            {
                ProcessBatch(context, modifyDatas, data => {
                    return data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                        data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                        data.LibId + "^^" + (int)OperationType.Modify + "\n";
                }, bulkLoader, RecordCountLimit, -1);
            }

            // 处理删除数据的历史记录
            if (delDatas.Any())
            {
                ProcessBatch(context, delDatas, data => {
                    return data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                        data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                        data.LibId + "^^" + (int)OperationType.Delete + "\n";
                }, bulkLoader, RecordCountLimit, -1);
            }
        }

        public void DeleteTreeNodes(DbContext m_db, List<CLCatalogTreeNode> delDatas, Guid libId)
        {
            if (!delDatas.Any()) return;

            string command = "delete from clcatalogtreenodes where LibId = '" + libId + "' and InstanceId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.InstanceId + ",");
            }

            dataSb.Append("-1)");

            m_db.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        public void ModifyTreeNodes(DbContext context, List<CLCatalogTreeNode> recordDatas, int version)
        {
            if (!recordDatas.Any()) return;
            
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "clcatalogtreenodes",
                FieldTerminator = "^^",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8",
                ConflictOption = MySqlBulkLoaderConflictOption.Replace
            };
            
            // 添加列
            bulkLoader.Columns.Add("NodeId");
            bulkLoader.Columns.Add("InstanceId");
            bulkLoader.Columns.Add("TreeId");
            bulkLoader.Columns.Add("ParentNodeId");
            bulkLoader.Columns.Add("NodeType");
            bulkLoader.Columns.Add("NodeName");
            bulkLoader.Columns.Add("bPDataKey");
            bulkLoader.Columns.Add("modelnfoKey");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("ID");
            
            var dataContext = context as LibraryDbContext;
            if (null == dataContext) return;

            // 查询并准备数据
            var dataWithOldIds = recordDatas
                .Select(data => {
                    var oldData = dataContext.CLCatalogTreeNodes.FirstOrDefault(d => d.InstanceId == data.InstanceId && d.LibId == data.LibId);
                    return new { Data = data, OldData = oldData };
                })
                .Where(pair => pair.OldData != null);

            // 处理批量导入
            ProcessBatch(context, dataWithOldIds, pair => {
                var data = pair.Data;
                var oldData = pair.OldData;

                return data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" +
                    data.LibId + "^^" + version + "^^" + oldData.ID + "\n";
            }, bulkLoader, RecordCountLimit, -1);
        }

        public void SaveTreeNodes(DbContext m_db, List<CLCatalogTreeNode> recordDatas, int version)
        {
            //PbimLog //log = new //PbimLog("Info");
            //log.Info("SaveTreeNodes===dataSb.Capacity");
            if (!recordDatas.Any()) return;
            
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "clcatalogtreenodes",
                FieldTerminator = "^^",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8",
                ConflictOption = MySqlBulkLoaderConflictOption.Replace
            };
            
            // 添加列
            bulkLoader.Columns.Add("NodeId");
            bulkLoader.Columns.Add("InstanceId");
            bulkLoader.Columns.Add("TreeId");
            bulkLoader.Columns.Add("ParentNodeId");
            bulkLoader.Columns.Add("NodeType");
            bulkLoader.Columns.Add("NodeName");
            bulkLoader.Columns.Add("bPDataKey");
            bulkLoader.Columns.Add("modelnfoKey");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("LibId");

            // 处理批量导入
            ProcessBatch(m_db, recordDatas, data => {
                return data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                      data.LibId + "\n";
            }, bulkLoader, RecordCountLimit, 200 * 1024 * 1024);
        }

        public void SaveLibDatas(DbContext m_db, List<CLLibraryData> recordDatas, int version)
        {
            //PbimLog //log = new //PbimLog("Info");
            //log.Info("SaveLibDatas===dataSb.Capacity");
            if (!recordDatas.Any()) return;
            
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "cllibrarydatas",
                FieldTerminator = ",",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8",
                ConflictOption = MySqlBulkLoaderConflictOption.Replace
            };
            
            // 添加列
            bulkLoader.Columns.Add("DataId");
            bulkLoader.Columns.Add("SchemaName");
            bulkLoader.Columns.Add("ClassName");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("@hexdata");
            
            // 添加表达式
            bulkLoader.Expressions.Add("Data=UNHEX(@hexdata)");

            // 处理批量导入
            ProcessBatch(m_db, recordDatas, data => {
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }

                return data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n";
            }, bulkLoader, RecordCountLimit, 200 * 1024 * 1024);
        }
        public void ModifyLibDatas(DbContext m_db, List<CLLibraryData> recordDatas, int version)
        {
            if (!recordDatas.Any()) return;
            
            // 创建并配置MySqlBulkLoader
            var bulkLoader = new MySqlBulkLoader(null)
            {
                TableName = "cllibrarydatas",
                FieldTerminator = ",",
                LineTerminator = "\n",
                Local = true,
                Timeout = DatabaseExecTimeout,
                CharacterSet = "UTF8",
                ConflictOption = MySqlBulkLoaderConflictOption.Replace
            };
            
            // 添加列
            bulkLoader.Columns.Add("DataId");
            bulkLoader.Columns.Add("SchemaName");
            bulkLoader.Columns.Add("ClassName");
            bulkLoader.Columns.Add("LibId");
            bulkLoader.Columns.Add("VersionNo");
            bulkLoader.Columns.Add("ID");
            bulkLoader.Columns.Add("TreeType");
            bulkLoader.Columns.Add("@hexdata");
            
            // 添加表达式
            bulkLoader.Expressions.Add("Data=UNHEX(@hexdata)");
            
            var dataContext = m_db as LibraryDbContext;
            if (null == dataContext) return;
            
            // 查询并准备数据
            var dataWithOldIds = recordDatas
                .Select(data => {
                    var oldData = dataContext.CLLibraryDatas.FirstOrDefault(d => d.DataId == data.DataId && d.LibId == data.LibId);
                    return new { Data = data, OldData = oldData };
                })
                .Where(pair => pair.OldData != null);

            // 处理批量导入
            ProcessBatch(m_db, dataWithOldIds, pair => {
                var data = pair.Data;
                var oldData = pair.OldData;
                
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }

                return data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + oldData.ID + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n";
            }, bulkLoader, RecordCountLimit, -1);
        }
        public void DeleteLibDatas(DbContext m_db, List<CLLibraryData> delDatas, Guid libId)
        {
            if (!delDatas.Any()) return;

            string command = "delete from cllibrarydatas where LibId = '" + libId + "' and DataId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.DataId + ",");
            }

            dataSb.Append("-1)");

            m_db.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        /// <summary>
        /// 通用批量处理方法，处理数据集合并执行批量导入
        /// </summary>
        /// <typeparam name="T">数据项类型</typeparam>
        /// <param name="context">数据库上下文</param>
        /// <param name="items">数据集合</param>
        /// <param name="formatter">数据格式化委托，将对象转换为字符串</param>
        /// <param name="bulkLoader">预配置的MySqlBulkLoader对象</param>
        /// <param name="batchSize">批处理的记录数量限制，设为-1表示不限制记录数量</param>
        /// <param name="maxBufferSize">批处理的最大缓冲区大小(字节)，设为-1表示不限制缓冲区大小</param>
        private void ProcessBatch<T>(
            DbContext context, 
            IEnumerable<T> items, 
            Func<T, string> formatter, 
            MySqlBulkLoader bulkLoader,
            int batchSize = -1,
            int maxBufferSize = -1)
        {
            StringBuilder dataSb = new StringBuilder();
            int count = 0;
            
            foreach (var item in items)
            {
                count++;
                string formattedData = formatter(item);
                dataSb.Append(formattedData);
                
                // 判断是否需要执行批处理
                bool shouldProcessBatch = false;
                
                // 如果设置了记录数限制且达到了限制
                if (batchSize > 0 && count >= batchSize)
                {
                    shouldProcessBatch = true;
                }
                
                // 如果设置了缓冲区大小限制且达到了限制
                if (maxBufferSize > 0 && dataSb.Length >= maxBufferSize)
                {
                    shouldProcessBatch = true;
                }
                
                // 执行批处理
                if (shouldProcessBatch)
                {
                    ExecuteBulkLoad(context, _foldPath, dataSb, bulkLoader);
                    count = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            
            // 处理剩余数据
            if (dataSb.Length > 0)
            {
                ExecuteBulkLoad(context, _foldPath, dataSb, bulkLoader);
            }
        }



        public void SaveDeleteCLLibDataIdToFile(List<CLLibraryData> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deletecllibdata";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.DataId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveCLLibDataHistoryToFile(DbContext context, List<CLLibraryData> addDatas, List<CLLibraryData> modifyDatas, List<CLLibraryData> delDatas, int version, long requestId)
        {
            string filename = requestId + "_cllibdatahistory";
            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)OperationType.Add + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)OperationType.Modify + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    "\\N" + "," + (int)OperationType.Delete + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveAddCLLibDataToFile(List<CLLibraryData> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savecllibdata";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModifyCLLibDataToFile(DbContext context, List<CLLibraryData> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifycllibdata";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as LibraryDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {
                i++;

                long dataId = data.DataId;
                var oldData = dataContext.CLLibraryDatas.FirstOrDefault(d => d.DataId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + oldData.ID + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveAddCataLogTreeNodeToFile(List<CLCatalogTreeNode> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_saveclcatalogtreenode";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {

                i++;
                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                      data.LibId + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveDeleteCataLogTreeNodeIdToFile(List<CLCatalogTreeNode> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deleteclcatalogtreenode";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.InstanceId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModifyCataLogTreeNodeToFile(DbContext context, List<CLCatalogTreeNode> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifyclcatalogtreenode";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as LibraryDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {

                i++;
                long dataId = data.InstanceId;
                var oldData = dataContext.CLCatalogTreeNodes.FirstOrDefault(d => d.InstanceId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" +
                      data.LibId + "^^" + version + "^^" + oldData.ID + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveCLCataLogTreeNodeToDB(DbContext context, long requestId)
        {
            const string addmpcatalogcommand = @"' replace into table clcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,VersionNo,LibId) ";
            string filename = requestId + "_saveclcatalogtreenode.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteLibraryDbCommand(context, _foldPath, addmpcatalogcommand, filename);
            }
            const string modifympcatalogcommand = @"' replace into table clcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,LibId,VersionNo,ID) ";
            string modifympcatalogfilename = requestId + "_modifyclcatalogtreenode.txt";
            if (File.Exists(_foldPath + modifympcatalogfilename))
            {
                ExecuteLibraryDbCommand(context, _foldPath, modifympcatalogcommand, modifympcatalogfilename);
            }

            string deletefilename = requestId + "_deleteclcatalogtreenode.txt";

            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from clcatalogtreenodes where InstanceId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveMPTreeNodeToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());

                try
                {
                    File.Delete(_foldPath + deletefilename);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + _foldPath + deletefilename + "无法释放，请手动删除");
                }

            }
        }

        public void SaveCLLibDataToDB(DbContext context, long requestId)
        {
            const string addmplibdatacommand = @"' replace into table cllibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savecllibdata.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteLibraryDbCommand(context, _foldPath, addmplibdatacommand, filename);
            }
            const string modifymplibdatacommand = @"' replace into table cllibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,ID,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            string modifymplibdatafilename = requestId + "_modifycllibdata.txt";
            if (File.Exists(_foldPath + modifymplibdatafilename))
            {
                ExecuteLibraryDbCommand(context, _foldPath, modifymplibdatacommand, modifymplibdatafilename);
            }

            string deletefilename = requestId + "_deletecllibdata.txt";

            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from cllibrarydatas where DataId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveMPTreeNodeToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());

                try
                {
                    File.Delete(_foldPath + deletefilename);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + _foldPath + deletefilename + "无法释放，请手动删除");
                }

            }
        }

        public void SaveCLLibDataHistoryToDB(DbContext context, long requestId)
        {
            const string addmplibdatahistorycommand = @"' into table cllibrarydatahistories fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata,OperationType) set Data=UNHEX(@hexdata) ";
            string filename = requestId + "_cllibdatahistory.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteLibraryDbCommand(context, _foldPath, addmplibdatahistorycommand, filename);
            }
        }
    }
}
