# MQ日志追踪说明文档

## 概述

本文档详细说明了基于RabbitMQ的日志系统的完整流程，以及如何通过日志精确定位数据丢失环节。

## 系统架构

```
生产端 (BimBase.Api) → RabbitMQ队列 → 消费端 (BimBase.LogConsumer) → MySQL数据库
```

### 数据流程
每个HTTP请求会产生3条队列消息，分别对应3个阶段：
- **Request阶段**：请求开始时的基础信息
- **Middle阶段**：中间件处理完成后的耗时信息  
- **Response阶段**：业务处理完成后的完整信息

## 完整日志流程

### 1. 生产端日志（入队）

#### Request阶段
```
[MQ日志] 开始入队 Request 阶段日志, RequestId: abc123, 接口: /api/test
[MQ发布] 开始发布消息, RequestId: abc123, 阶段: request, 尝试次数: 1
[MQ发布] 消息发布确认成功, RequestId: abc123, 阶段: request, 尝试次数: 1
[MQ日志] Request 阶段日志入队完成, RequestId: abc123
```

#### Middle阶段
```
[MQ日志] 开始入队 Middle 阶段日志, RequestId: abc123, 耗时: 50ms
[MQ发布] 开始发布消息, RequestId: abc123, 阶段: middle, 尝试次数: 1
[MQ发布] 消息发布确认成功, RequestId: abc123, 阶段: middle, 尝试次数: 1
[MQ日志] Middle 阶段日志入队完成, RequestId: abc123
```

#### Response阶段
```
[MQ日志] 开始入队 Response 阶段日志, RequestId: abc123, 是否异常: false
[MQ发布] 开始发布消息, RequestId: abc123, 阶段: response, 尝试次数: 1
[MQ发布] 消息发布确认成功, RequestId: abc123, 阶段: response, 尝试次数: 1
[MQ日志] Response 阶段日志入队完成, RequestId: abc123, 耗时: 100ms
```

### 2. 消费端日志（出队）

#### Request阶段
```
[MQ消费] 收到消息, RequestId: abc123, 阶段: request
[MQ消费] request 阶段完成, RequestId: abc123, 耗时: 5ms
[MQ确认] 消息确认成功, RequestId: abc123, 阶段: request
```

#### Middle阶段
```
[MQ消费] 收到消息, RequestId: abc123, 阶段: middle
[MQ消费] middle 阶段完成, RequestId: abc123, 耗时: 3ms
[MQ确认] 消息确认成功, RequestId: abc123, 阶段: middle
```

#### Response阶段
```
[MQ消费] 收到消息, RequestId: abc123, 阶段: response
[MQ消费] response 阶段完成, RequestId: abc123, 耗时: 8ms
[MQ确认] 消息确认成功, RequestId: abc123, 阶段: response
```

### 3. 系统监控日志

#### 初始化日志
```
[MQ初始化] 开始初始化RabbitMQ
[MQ初始化] 连接成功: localhost:5672
[MQ监控] 队列状态 - 接口队列: mq_interface_logs, 消息数: 50, 消费者数: 1
[MQ监控] 队列状态 - 错误队列: mq_error_logs, 消息数: 5, 消费者数: 1
[MQ初始化] 消费者启动完成
```

#### 定期监控日志
```
[MQ监控] 队列状态监控 - 接口队列: mq_interface_logs, 消息数: 45, 消费者数: 1
[MQ监控] 队列状态监控 - 错误队列: mq_error_logs, 消息数: 3, 消费者数: 1
```

## 问题定位指南

### 查询命令模板

```bash
# 1. 查询某个RequestId的所有相关日志
grep "abc123" application.log

# 2. 查询某个RequestId的特定阶段日志
grep "abc123.*阶段: request" application.log
grep "abc123.*阶段: middle" application.log
grep "abc123.*阶段: response" application.log

# 3. 查询某个RequestId的生产端日志
grep "abc123.*MQ发布" application.log

# 4. 查询某个RequestId的消费端日志
grep "abc123.*MQ消费" application.log

# 5. 查询某个RequestId的确认日志
grep "abc123.*MQ确认" application.log

# 6. 查询某个RequestId的错误日志
grep "abc123.*异常\|失败" application.log
```

### 问题场景分析

#### 场景1：某个阶段完全没有入队

**现象**：数据库中缺少某个阶段的数据

**查询步骤**：
```bash
# 查询该RequestId的所有日志
grep "abc123" application.log

# 检查是否有该阶段的入队日志
grep "abc123.*开始入队.*阶段: middle" application.log
```

**判断标准**：
- 如果没有找到该阶段的任何日志 → **该阶段根本没有入队**
- 可能原因：业务代码异常、中间件异常、配置问题

**解决方案**：
- 检查业务代码是否正常执行
- 检查中间件配置是否正确
- 检查MQ连接是否正常

#### 场景2：入队了但发布失败

**现象**：有入队日志，但没有发布确认成功日志

**查询步骤**：
```bash
# 查询该阶段的发布日志
grep "abc123.*阶段: middle.*MQ发布" application.log
```

**判断标准**：
- 有"开始发布"但没有"发布确认成功" → **发布到队列失败**
- 可能原因：网络问题、RabbitMQ服务不可用、队列满

**解决方案**：
- 检查RabbitMQ服务状态
- 检查网络连接
- 检查队列容量

#### 场景3：发布成功但消费失败

**现象**：有发布确认成功，但没有消费完成日志

**查询步骤**：
```bash
# 查询该阶段的消费日志
grep "abc123.*阶段: middle.*MQ消费" application.log
```

**判断标准**：
- 有"收到消息"但没有"阶段完成" → **消费处理失败**
- 可能原因：消息反序列化失败、业务逻辑异常

**解决方案**：
- 检查消息格式是否正确
- 检查消费端业务逻辑

#### 场景4：消费成功但数据库更新失败

**现象**：有消费完成日志，但没有确认成功日志

**查询步骤**：
```bash
# 查询该阶段的确认日志
grep "abc123.*阶段: middle.*MQ确认" application.log
```

**判断标准**：
- 有"阶段完成"但没有"确认成功" → **数据库操作失败**
- 可能原因：数据库连接问题、SQL执行异常、死锁

**解决方案**：
- 检查数据库连接池状态
- 检查SQL语句是否正确
- 检查数据库性能

#### 场景5：消息被拒绝重新入队

**现象**：有"消息拒绝并重新入队"日志

**查询步骤**：
```bash
# 查询拒绝日志
grep "abc123.*消息拒绝并重新入队" application.log
```

**判断标准**：
- 有"处理失败"和"消息拒绝并重新入队" → **处理异常，消息重新入队**
- 可能原因：数据库异常、业务逻辑异常

**解决方案**：
- 检查错误日志中的具体异常信息
- 修复业务逻辑问题
- 检查数据库状态

#### 场景6：高并发导致消息积压

**现象**：队列消息数持续增长

**查询步骤**：
```bash
# 查询队列监控日志
grep "队列状态监控" application.log
```

**判断标准**：
- 队列消息数 > 1000 → **消息积压严重**
- 可能原因：消费速度跟不上生产速度

**解决方案**：
- 增加消费者实例
- 优化消费端性能
- 检查数据库性能

## 日志级别说明

### Debug级别（详细追踪）
- 消息发布详细过程
- 消息消费详细过程
- 确认过程

### Info级别（关键节点）
- 阶段完成状态
- 系统启动状态
- 队列监控状态

### Warning级别（警告信息）
- 重试过程
- 未知阶段
- 消息积压

### Error级别（错误信息）
- 发布失败
- 消费失败
- 数据库异常

## 性能优化说明

### 日志优化
- 详细过程使用Debug级别，生产环境可关闭
- 关键节点使用Info级别，保留追踪能力
- 错误信息使用Error级别，及时告警

### 系统优化
- 生产端：3次重试机制，指数退避
- 消费端：QoS设置，提高并发
- 数据库：3次重试机制，指数退避

## 监控建议

### 实时监控
1. 队列消息数监控
2. 消费者状态监控
3. 错误率监控

### 告警设置
1. 队列积压 > 1000条
2. 错误率 > 5%
3. 消费者离线

### 定期检查
1. 每日检查日志完整性
2. 每周分析数据丢失情况
3. 每月优化系统性能

## 常见问题FAQ

### Q1：如何快速判断某个RequestId是否完整？
A1：查询该RequestId的所有日志，检查是否有3个阶段的完整流程。

### Q2：如何区分是生产端还是消费端的问题？
A2：检查是否有"MQ发布"和"MQ消费"的对应日志。

### Q3：高并发时如何保证数据不丢失？
A3：系统已配置重试机制和QoS设置，但仍需监控队列状态。

### Q4：如何优化日志查询性能？
A4：使用结构化日志，按RequestId和时间范围查询。

### Q5：如何设置合适的告警阈值？
A5：根据业务量和系统性能，设置队列积压和错误率阈值。 