﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    //public enum FeedbackType
    //{
    //    Related,
    //    Added
    //}

    //public enum FeedbackStatus
    //{
    //    Default,
    //    Solved,
    //    Rejected,
    //    Postponed,
    //    Ignored
    //}


    //public class ChangesetFeedback
    //{
    //    [Key]
    //    public Int64 Id { get; set; }

        
    //    public Int64 SourceID { get; set; }

        
    //    public int SourceDomain { get; set; }

        
    //    public int SourceDomainClassID { get; set; }

        
    //    public String SourceECSchemaName { get; set; }

        
    //    public Int64 TargetID { get; set; }

        
    //    public int TargetDomain { get; set; }

        
    //    public int TargetDomainClassID { get; set; }

        
    //    public String TargetECSchemaName { get; set; }

        
    //    [EnumDataType(typeof(FeedbackType))]
    //    public FeedbackType Type { get; set; }

        
    //    [EnumDataType(typeof(FeedbackStatus))]
    //    public FeedbackStatus Status { get; set; }

        
    //    public String Description { get; set; }

        
    //    [Required, EnumDataType(typeof(OperationRecordType))]
    //    public OperationRecordType OperationType { get; set; }

    //    public int SourceVersionNo { get; set; }

        
    //    public int ReleatedVersionNo { get; set; }
    //    public ChangesetFeedback()
    //    {
    //        Status = FeedbackStatus.Default;
    //        Type = FeedbackType.Related;
    //    }
    //}
}
