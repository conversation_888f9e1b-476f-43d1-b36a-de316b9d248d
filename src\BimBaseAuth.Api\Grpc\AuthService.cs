﻿using AutoMapper;
using BimBaseAuth.Api.Infrastructure;
using BimBaseAuth.Api.Infrastructure.Common;
using BimBaseAuth.Api.Infrastructure.Domain;
using BimBaseAuth.Api.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using static BimBaseAuth.Api.Protos.AuthGrpc;

namespace BimBaseAuth.Api.Grpc
{
    public class AuthService: AuthGrpcBase
    {
        private readonly ILogger<AuthService> _logger;
        private readonly OpenAuthDBContext _dbContext;
        private readonly IMapper _mapper;
        public AuthService(ILogger<AuthService> logger, OpenAuthDBContext openAuthDBContext, IMapper mapper)
        {
            _logger = logger;
            _dbContext = openAuthDBContext ?? throw new ArgumentNullException(nameof(OpenAuthDBContext));
            _mapper = mapper;
        }

        public override Task<AuthResult> AddProjectRole(AddProjectRoleRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);
            _dbContext.Add<RoleExtend>(new RoleExtend { 
                Id = Guid.Parse(request.RoleExtend.Id),
                AppId = request.RoleExtend.AppId,
                IsAdmin = request.RoleExtend.IsAdmin,
                CreateId = Guid.Parse(request.RoleExtend.CreateId),
                CreateTime = request.RoleExtend.CreateTime.ToDateTime(),
                DelFlag = (short)request.RoleExtend.DelFlag,
                Name = request.RoleExtend.Name,
                RelatedId = Guid.Parse(request.RoleExtend.RelatedId),
                Status = request.RoleExtend.Status,
                Type = request.RoleExtend.Type
            });

            var x = _dbContext.SaveChanges();
            return Task.FromResult(new AuthResult
            {
                IsSuccess = x > 0,
                Result = Any.Pack(Value.ForNumber(x)),
            }) ;
        }

        public override Task<ProjectRoleResponse> GetProjectRole(GetProjectRoleRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);
            List<Role> list = new List<Role>();
            var result = new ProjectRoleResponse();

            var projectId = Guid.Parse(request.Projectguid);
            IQueryable<RoleExtend> list2 = _dbContext.RoleExtends.Where(t => t.RelatedId.Equals(projectId));
            if (list2 is null || !list2.Any())
            {
                return Task.FromResult(result);
            }
            foreach (RoleExtend item in list2)
            {
                Role role = new Role();
                ObjectHelper.CopyPropertyValueNull(item, role);
                list.Add(role);
            }


            result.IsSuccess = true;

            result.RoleList.AddRange(MapGrpcRoleResponse(list));

            return Task.FromResult(result);
        }

        public override Task<AuthResult> AddRole(AddRoleRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var entity = request.Role;
            if (!Guid.TryParse(entity.Id,out var tmp))
            {
                entity.Id = Guid.NewGuid().ToString();
            }
            _dbContext.Add(entity);

            return Task.FromResult(new AuthResult { IsSuccess = _dbContext.SaveChanges() > 0 });
        }

        public override Task<AuthResult> DeleteProject(DeleteProjectRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new AuthResult();
            if(!Guid.TryParse(request.ProjectId,out var projectId))
            {
                x.Message = $"参数{request.ProjectId} 有误！";
                return Task.FromResult(x);
            }
            IQueryable<RoleExtend> queryable = _dbContext.RoleExtends.Where((RoleExtend t) => t.RelatedId == projectId);
            List<Guid> Ids = new List<Guid>();
            foreach (RoleExtend item in queryable)
            {
                Ids.Add(item.Id);
            }
            using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    IQueryable<RoleRelation> entities = _dbContext.RoleRelations.Where((RoleRelation t) => Ids.Contains(t.RoleId));
                    _dbContext.RoleRelations.RemoveRange(entities);
                    IQueryable<UserRole> entities2 = _dbContext.UserRoles.Where((UserRole t) => Ids.Contains(t.RoleId));
                    _dbContext.UserRoles.RemoveRange(entities2);
                    _dbContext.RoleExtends.RemoveRange(queryable);
                    _dbContext.SaveChanges();
                    dbContextTransaction.Commit();
                    x.IsSuccess = true;
                }
                catch
                {
                    dbContextTransaction.Rollback();
                }
                finally
                {
                    dbContextTransaction.Dispose();
                }
            }


            return Task.FromResult(x);
        }

        public override Task<AuthResult> DeleteProjectRole(DeleteProjectRoleRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new AuthResult();
            var roleIds = new List<Guid>();
            foreach (var r in request.RoleIds)
            {
                if (Guid.TryParse(r, out var tmp))
                {
                    roleIds.Add(tmp);
                }
                else
                {
                    x.Message = $"参数 {r} 错误！";
                    return Task.FromResult(x);
                }
            }


            using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    IQueryable<UserRole> entities = _dbContext.UserRoles.Where((UserRole t) => roleIds.Contains(t.RoleId));
                    _dbContext.UserRoles.RemoveRange(entities);
                    IQueryable<RoleRelation> entities2 = _dbContext.RoleRelations.Where((RoleRelation t) => roleIds.Contains(t.RoleId));
                    _dbContext.RoleRelations.RemoveRange(entities2);
                    IQueryable<RoleExtend> entities3 = _dbContext.RoleExtends.Where((RoleExtend t) => roleIds.Contains(t.Id));
                    _dbContext.RoleExtends.RemoveRange(entities3);
                    _dbContext.SaveChanges();
                    dbContextTransaction.Commit();
                    x.IsSuccess = true;
                }
                catch
                {
                    dbContextTransaction.Rollback();
                }
                finally
                {
                    dbContextTransaction.Dispose();
                }
            }

            return Task.FromResult(x);
        }

        public override Task<AuthResult> DeleteRoleAuth(DeleteRoleAuthRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new AuthResult();

            if(!Guid.TryParse(request.RoleId,out var roleId))
            {
                x.Message = $"参数{request.RoleId}有误！";
                return Task.FromResult(x);
            }

            using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    IQueryable<RoleRelation> entities = _dbContext.RoleRelations.Where((RoleRelation t) => t.RoleId == roleId);
                    _dbContext.RoleRelations.RemoveRange(entities);
                    _dbContext.SaveChanges();
                    dbContextTransaction.Commit();
                    x.IsSuccess = true;
                }
                catch
                {
                    dbContextTransaction.Rollback();
                }
                finally
                {
                    dbContextTransaction.Dispose();
                }
            }

            return Task.FromResult(x);
        }

        public override Task<AuthResult>  DeleteRoleUser(DeleteRoleUserRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);
            var x = new AuthResult();

            if (!Guid.TryParse(request.RoleId, out var roleId))
            {
                x.Message = $"参数{request.RoleId}有误！";
                return Task.FromResult(x);
            }

            var userIds = new List<Guid>();
            foreach (var r in request.UserIds)
            {
                if (Guid.TryParse(r, out var tmp))
                {
                    userIds.Add(tmp);
                }
                else
                {
                    x.Message = $"参数 {r} 错误！";
                    return Task.FromResult(x);
                }
            }

            using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
            {
                try
                {
                    IQueryable<UserRole> entities = _dbContext.UserRoles.Where((UserRole t) => t.RoleId == roleId && userIds.Contains(t.UserId));
                    _dbContext.UserRoles.RemoveRange(entities);
                    _dbContext.SaveChanges();
                    dbContextTransaction.Commit();
                    x.IsSuccess = true;
                }
                catch
                {
                    dbContextTransaction.Rollback();
                }
                finally
                {
                    dbContextTransaction.Dispose();
                }
            }
            return Task.FromResult(x);
        }

        public override Task<CurrentUserAuthResponse> GetCurrentUserAuth(GetCurrentUserAuthRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new CurrentUserAuthResponse();

            if (!Guid.TryParse(request.UserId, out var userId))
            {
                x.Message = "参数有误！";
                return Task.FromResult(x);
            }

            //var userRoleWithAccessedCtrls = new UserRoleWithAccessedCtrls();
            try
            {
                List<Role> list = new List<Role>();
                list = GetRoleInfoByUserId(userId);
                List<Role> roleList = new List<Role>();
                List<AuthInfo> globalAuth = new List<AuthInfo>();
                List<Guid> projectInfoList = new List<Guid>();
                List<Dictionary<Guid, List<Role>>> list2 = new List<Dictionary<Guid, List<Role>>>();
                int num = RoleInfoContainsSpecialAuth(list, out roleList, out globalAuth, out projectInfoList);
                projectInfoList.AddRange(GetProjectInfoListByUserId(userId));
                globalAuth.AddRange(GetGlobalAuthByUserId(userId));
                if (globalAuth != null && globalAuth.Count > 0)
                {
                    globalAuth = globalAuth.Distinct().ToList();
                }
                if (projectInfoList != null && projectInfoList.Count > 0)
                {
                    projectInfoList = projectInfoList.Distinct().ToList();
                }
                x.Status = num;
                x.RoleList.AddRange(MapGrpcRoleResponse(roleList));

                x.GlobalAuth.AddRange(MapGrpcAuthInfoResponse(globalAuth));


                var tmp = new List<string>();

                projectInfoList.ForEach(p=> {
                    tmp.Add(p.ToString());
                });

                x.ProjectInfoList.AddRange(tmp);


                x.Status = 0;
                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = "获取角色权限失败:" + ex.Message;
                return Task.FromResult(x);
            }


            return Task.FromResult(x);
        }

        public override Task<OwnRoleListResponse> GetOwnRoleList(GetOwnRoleListRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new OwnRoleListResponse();

            if (!Guid.TryParse(request.UserId, out var userId))
            {
                x.Message = "参数有误！";
                return Task.FromResult(x);
            }

            var roles = _dbContext.Roles.Where((Role t) => t.CreateId == userId);

            x.IsSuccess = true;
            x.RoleList.AddRange(MapGrpcRoleResponse(roles));

            return Task.FromResult(x);
        }

        private List<Guid> GetProjectInfoListByUserId(Guid userId)
        {
            List<Guid> list = new List<Guid>();
            list.AddRange((from t in _dbContext.UserRelateds.Where(t => t.UserID.Equals(userId))
                           select t.RelatedId).ToList());
            return list;
        }

        private List<AuthInfo> GetGlobalAuthByUserId(Guid userId)
        {
            List<AuthInfo> result = new List<AuthInfo>();
            UserRelation userRelation = _dbContext.UserRelations.Where(t => t.UserId.Equals(userId) && t.IsSpecific == false).FirstOrDefault();
            if (userRelation != null && !string.IsNullOrEmpty(userRelation.Auth))
            {
                result = JsonSerializer.Deserialize<List<AuthInfo>>(userRelation.Auth);
            }
            return result;
        }

        private int RoleInfoContainsSpecialAuth(List<Role> listRole, out List<Role> roleList, out List<AuthInfo> globalAuth, out List<Guid> projectInfoList)
        {
            try
            {
                List<Guid> roleIds = new List<Guid>();
                Parallel.ForEach(listRole, delegate (Role item)
                {
                    roleIds.Add(item.Id);
                });
                roleList = new List<Role>();
                globalAuth = new List<AuthInfo>();
                projectInfoList = new List<Guid>();
                if (listRole == null || listRole.Count <= 0)
                {
                    return -1;
                }
                if (listRole.Find((Role t) => t.Type == 0) != null)
                {
                    roleList = _dbContext.Roles.ToList();
                    globalAuth = _dbContext.AuthInfos.Where(t => t.IsGlobalAuth == true).ToList();
                    return 0;
                }
                if (listRole.Find((Role t) => t.Type == 1) != null)
                {
                    roleList = _dbContext.Roles.ToList();
                    globalAuth = _dbContext.AuthInfos.Where(t => t.IsGlobalAuth == true).ToList();
                    return 1;
                }
                if (listRole.Find((Role t) => t.Type == 2) != null)
                {
                    globalAuth = GetGlobalAuth(roleIds);
                    return 2;
                }
                roleList = listRole;
                globalAuth = GetGlobalAuth(roleIds);
                projectInfoList = GetProjectInfoListByRoleIds(roleIds);
                return 3;
            }
            catch
            {
                throw;
            }
        }

        public List<AuthInfo> GetGlobalAuth(List<Guid> roleIds)
        {
            List<AuthInfo> list = new List<AuthInfo>();
            IQueryable<RoleRelation> roleRelationList = _dbContext.RoleRelations.Where(t => roleIds.Contains(t.RoleId));
            List<RoleRelation> list2 = new List<RoleRelation>();
            list2 = roleRelationList.ToList();
            if (list2 != null && list2.Count > 0)
            {
                foreach (RoleRelation item in list2)
                {
                    if (!string.IsNullOrEmpty(item.Auth))
                    {
                        list.AddRange(JsonSerializer.Deserialize<List<AuthInfo>>(item.Auth));
                    }
                }
            }
            return list;
        }

        public List<Guid> GetProjectInfoListByRoleIds(List<Guid> roleIds)
        {
            List<Guid> list = new List<Guid>();
            list.AddRange((from t in _dbContext.RoleExtends.Where(t => roleIds.Contains(t.Id))
                           select t.RelatedId).ToList());
            return list;
        }

        private List<Role> GetRoleInfoByUserId(Guid userId)
        {
            List<Role> list = new List<Role>();
            IQueryable<Guid> roleids = from u in _dbContext.UserRoles
                                       where u.UserId.Equals(userId)
                                       select u.RoleId;
            IQueryable<Role> source = _dbContext.Roles.Where((Role u) => roleids.Contains(u.Id));
            list = source.ToList();
            List<RoleExtend> list2 = new List<RoleExtend>();
            list2 = GetRoleExtendListBuIds(roleids);
            if (list2 != null && list2.Count > 0)
            {
                foreach (RoleExtend item in list2)
                {
                    Role role = new Role();
                    ObjectHelper.CopyPropertyValueNull(item, role);
                    list.Add(role);
                }
            }
            return list;
        }

        private List<RoleExtend> GetRoleExtendListBuIds(IQueryable<Guid> ids)
        {
            return _dbContext.RoleExtends.Where((RoleExtend t) => ids.Contains(t.Id)).ToList();
        }

        //public override Task<AuthResult>  AddTeamManager(AddTeamManagerRequest request, ServerCallContext context)
        //{
        //    _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

        //    var x = new AuthResult();

        //    if (!Guid.TryParse(request.CurrentUserId, out var userId))
        //    {
        //        x.Message = "参数有误！";
        //        return Task.FromResult(x);
        //    }

        //    IQueryable<Guid> roleids = from u in _dbContext.UserRoles
        //                               where u.UserId.Equals(userId)
        //                               select u.RoleId;
        //    var list = _dbContext.Roles.Where((Role u) => roleids.Contains(u.Id)).ToList();
        //    var list2 = _dbContext.RoleExtends.Where((RoleExtend t) => roleids.Contains(t.Id)).ToList();
        //    if (list2 != null && list2.Count > 0)
        //    {
        //        foreach (RoleExtend item in list2)
        //        {
        //            Role role = new Role();
        //            ObjectHelper.CopyPropertyValueNull(item, role);
        //            list.Add(role);
        //        }
        //    }


        //    if (list == null || list.Count <= 0)
        //    {
        //        x.Result = Any.Pack(Value.ForNumber(-1));
        //    }

        //    var roleType = list.FirstOrDefault()?.Type;
        //    switch (roleType)
        //    {
        //        case 0:
        //            x.Result = Any.Pack(Value.ForNumber(0));
        //            break;
        //        case 1:
        //            x.Result = Any.Pack(Value.ForNumber(1));
        //            break;
        //        case 2:
        //            x.Result = Any.Pack(Value.ForNumber(2));
        //            break;
        //        default:
        //            x.Result = Any.Pack(Value.ForNumber(3));
        //            break;
        //    }

        //    x.IsSuccess = true;
        //    return Task.FromResult(x);
        //}

        //public override Task<AuthResult> DeleteRole(DeleteRoleRequest request, ServerCallContext context)
        //{
        //    _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);
        //    var x = new AuthResult();
        //    var roleIds = new List<Guid>();
        //    foreach (var r in request.RoleIds)
        //    {
        //        if(Guid.TryParse(r,out var tmp))
        //        {
        //            roleIds.Add(tmp);
        //        }
        //        else
        //        {
        //            x.Message = $"参数 {r} 错误！";
        //            return Task.FromResult(x);
        //        }


        //    }
        //    using (var dbContextTransaction = _dbContext.Database.BeginTransaction())
        //    {
        //        try
        //        {
        //            IQueryable<Role> entities = _dbContext.Roles.Where((Role t) => roleIds.Contains(t.Id));
        //            _dbContext.Roles.RemoveRange(entities);
        //            _dbContext.SaveChanges();
        //            dbContextTransaction.Commit();
        //            x.IsSuccess = true;
        //        }
        //        catch
        //        {
        //            dbContextTransaction.Rollback();
        //        }
        //        finally
        //        {
        //            dbContextTransaction.Dispose();
        //        }

        //        return Task.FromResult(x);
        //    }
        //}

        private Google.Protobuf.Collections.RepeatedField<GrpcRole> MapGrpcRoleResponse(IEnumerable<Role> roles)
        {
            var x = new Google.Protobuf.Collections.RepeatedField<GrpcRole>();
            foreach (var r in roles)
            {
                x.Add(new GrpcRole
                {
                    Id = r.Id.ToString(),
                    AppId = r.AppId,
                    CreateId = r.CreateId.ToString(),
                    CreateTime = Timestamp.FromDateTimeOffset(r.CreateTime),
                    DelFlag = r.DelFlag,
                    Name = r.Name,
                    Status = r.Status,
                    Type = r.Type
                });
            }
            return x;
        }

        private Google.Protobuf.Collections.RepeatedField<GrpcAuthInfo> MapGrpcAuthInfoResponse(IEnumerable<AuthInfo> authInfos)
        {
            var x = new Google.Protobuf.Collections.RepeatedField<GrpcAuthInfo>();
            foreach (var r in authInfos)
            {
                x.Add(new GrpcAuthInfo
                {
                    AppId = r.AppId,
                    CreateId = r.CreateId.ToString(),
                    DelFlag = r.DelFlag,
                    Code = r.Code,
                    Id = r.Id.ToString(),
                    IsGlobalAuth = r.IsGlobalAuth,
                    ParentId = r.ParentId.ToString(),
                    Permission = r.Permission,
                    RelatedId = r.RelatedId.ToString(),
                    UpdateTime = Timestamp.FromDateTimeOffset(r.UpdateTime),
                    Name = r.Name,
                    Type = r.Type
                });
            }
            return x;
        }


        public override Task<AuthResult> GetUsersFromRoleId(GetUsersFromRoleIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new AuthResult();

            if (!Guid.TryParse(request.RoleId, out var roleId))
            {
                x.Message = "参数有误！";
                return Task.FromResult(x);
            }

            var roleIds = _dbContext.UserRoles.Where((UserRole t) => t.RoleId == roleId).Select(r=>r.UserId);

            x.IsSuccess = true;

            var roleIdsJson = System.Text.Json.JsonSerializer.Serialize(roleIds);
            x.Result = Any.Pack(Value.ForString(roleIdsJson));

            return Task.FromResult(x);
        }


        public override Task<GetRoleInfoByUserIdAndProjectIdResponse> GetRoleInfoByUserIdAndProjectId(GetRoleInfoByUserIdAndProjectIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new GetRoleInfoByUserIdAndProjectIdResponse();

            if (!Guid.TryParse(request.UserId, out var userId))
            {
                x.Message = "参数userId有误！";
                return Task.FromResult(x);
            }

            if (!Guid.TryParse(request.ProjecId, out var projectId))
            {
                x.Message = "参数projectId有误！";
                return Task.FromResult(x);
            }

            var roleList = GetRoleInfoByUserIdAndProjectId(userId, projectId);

            if(roleList != null)
            {
                x.IsSuccess = true;
                x.RoleList.AddRange(_mapper.Map<List<GrpcRole>>(roleList));
            }
            return Task.FromResult(x);
        }


        private List<Role> GetRoleInfoByUserIdAndProjectId(Guid userId, Guid projecId)
        {
            List<Role> list = new List<Role>();
            List<Guid> roleids = (from u in _dbContext.UserRoles
                                  where u.UserId.Equals(userId)
                                  select u.RoleId).ToList();
            IQueryable<RoleExtend> queryable = _dbContext.RoleExtends.Where((RoleExtend r) => r.RelatedId == projecId && roleids.Contains(r.Id));
            if (queryable != null && queryable.Count() > 0)
            {
                foreach (RoleExtend item in queryable)
                {
                    Role role = new Role();
                    ObjectHelper.CopyPropertyValueNull(item, role);
                    list.Add(role);
                }
            }
            return list;
        }


        public override Task<GetProjectAuthByUserIdResponse> GetProjectAuthByUserId(GetProjectAuthByUserIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request);

            var x = new GetProjectAuthByUserIdResponse();

            if (!Guid.TryParse(request.UserId, out var userId))
            {
                x.Message = "参数userId有误！";
                return Task.FromResult(x);
            }

            if (!Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = "参数projectId有误！";
                return Task.FromResult(x);
            }

            var authInfoList = GetProjectAuthByUserId(userId, projectId);
            var authInfos = new List<GrpcAuthInfo>();
            foreach (var commonAuthinfo in authInfoList)
            {

                authInfos.Add(new GrpcAuthInfo
                {
                    Code = commonAuthinfo.Code,
                    Name = commonAuthinfo.Name,
                    Type = commonAuthinfo.Type,
                    Permission = commonAuthinfo.Permission,
                    RelatedId = commonAuthinfo.RelatedId.ToString(),
                    ParentId = commonAuthinfo.ParentId.ToString()
                });
            }

            //获取经过处理的最小权限序列
            authInfos = GetUserMinPermission(authInfos);
            x.IsSuccess = true;
            x.AuthInfoList.AddRange(authInfos);
            return Task.FromResult(x);
        }

        //16进制转10进制
        private List<GrpcAuthInfo> GetUserMinPermission(List<GrpcAuthInfo> authInfos)
        {
            //依据AuthId分组，获取唯一AuthId列表
            var authIdsObjList = authInfos.GroupBy(a => a.RelatedId).Select(g => (new { AuthId = g.Key, count = g.Count() }));
            List<GrpcAuthInfo> returnAuthInfos = new List<GrpcAuthInfo>();
            foreach (var authIdsObj in authIdsObjList)
            {
                List<GrpcAuthInfo> sameAuthIdAuthInfos = new List<GrpcAuthInfo>();
                foreach (var authInfo in authInfos)
                {
                    if (authIdsObj.AuthId == authInfo.RelatedId)
                    {
                        sameAuthIdAuthInfos.Add(authInfo);
                    }
                }
                GrpcAuthInfo onlyAuthInfo = sameAuthIdAuthInfos.First();
                onlyAuthInfo.Permission = GetNewestPerisson(sameAuthIdAuthInfos);
                returnAuthInfos.Add(onlyAuthInfo);
            }
            //将正确的唯一最小权限序列返回
            return returnAuthInfos;
        }

        //重新比对增删改查，获得最新的增删改查权限
        private int GetNewestPerisson(List<GrpcAuthInfo> SameAuthIdAuthInfos)
        {
            string permission = string.Empty;
            int permission1 = 1;
            int permission2 = 1;
            int permission3 = 1;
            int permission4 = 1;
            foreach (var AuthInfo in SameAuthIdAuthInfos)
            {
                permission1 *= GetBinaryDigit(AuthInfo.Permission, 0);
                permission2 *= GetBinaryDigit(AuthInfo.Permission, 1);
                permission3 *= GetBinaryDigit(AuthInfo.Permission, 2);
                permission4 *= GetBinaryDigit(AuthInfo.Permission, 3);
            }
            permission = permission1.ToString() + permission2.ToString() + permission3.ToString() +
                         permission4.ToString();
            return HexToDecimal(Convert.ToString(Convert.ToInt32(permission, 2), 16).ToUpper());
        }

        //16进制转10进制
        private int HexToDecimal(string str)
        {
            int ret = 0;
            if (str != "")
            {
                switch (str)
                {
                    case "A":
                        ret = 10;
                        break;
                    case "B":
                        ret = 11;
                        break;
                    case "C":
                        ret = 12;
                        break;
                    case "D":
                        ret = 13;
                        break;
                    case "E":
                        ret = 14;
                        break;
                    case "F":
                        ret = 15;
                        break;
                    default:
                        ret = int.Parse(str);
                        break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="Permission"></param>
        /// <param name="Position">二进制位的位置0第一位，1第二位，2第三位，3第四位</param>
        private int GetBinaryDigit(int Permission, int Position)
        {

            string Hex = string.Empty;
            switch (Permission)
            {
                case 10:
                    Hex = "A";
                    break;
                case 11:
                    Hex = "B";
                    break;
                case 12:
                    Hex = "C";
                    break;
                case 13:
                    Hex = "D";
                    break;
                case 14:
                    Hex = "E";
                    break;
                case 15:
                    Hex = "F";
                    break;
                default:
                    Hex = Permission.ToString();
                    break;
            }
            string BinaryPermission = Convert.ToString(Convert.ToInt32("0x" + Hex, 16), 2);
            if (BinaryPermission.Length == 3)
            {
                BinaryPermission = "0" + BinaryPermission;
            }
            else if (BinaryPermission.Length == 2)
            {
                BinaryPermission = "00" + BinaryPermission;
            }
            else if (BinaryPermission.Length == 1)
            {
                BinaryPermission = "000" + BinaryPermission;
            }
            else if (BinaryPermission == "")
            {
                BinaryPermission = "0000";
            }
            else if (BinaryPermission == "0")
            {
                BinaryPermission = "0000";
            }
            return int.Parse(BinaryPermission.Substring(Position, 1));
        }

        private List<AuthInfo> GetProjectAuthByUserId(Guid userId, Guid projectId)
        {
            List<AuthInfo> list = new List<AuthInfo>();
            List<Guid> roleIds = (from t in _dbContext.RoleExtends
                                  where t.RelatedId.Equals(projectId)
                                  select t.Id).ToList();
            if (!roleIds.Any())
            {
                return list;
            }
            List<Guid> userRoleIds = (from r in _dbContext.UserRoles
                                      where roleIds.Contains(r.RoleId) && r.UserId == userId
                                      select r into t
                                      select t.RoleId).ToList();
            if (userRoleIds.Any())
            {
                List<RoleRelation> source = _dbContext.RoleRelations.Where((RoleRelation t) => userRoleIds.Contains(t.RoleId)).ToList();
                if (source.Any())
                {
                    Parallel.ForEach(source, delegate (RoleRelation item)
                    {
                        List<AuthInfo> list2 = new List<AuthInfo>();
                        if (!string.IsNullOrEmpty(item.Auth))
                        {
                            list2 = System.Text.Json.JsonSerializer.Deserialize<List<AuthInfo>>(item.Auth);
                            list.AddRange(list2);
                        }
                    });
                }
            }
            return list;
        }

        public override Task<AuthResult> GiveRoleUser(GiveRoleUserRequest request, ServerCallContext context)
        {
            var x = new AuthResult();

            if (!Guid.TryParse(request.RoleId, out var roleId))
            {
                x.Message = "参数roleId有误！";
                return Task.FromResult(x);
            }
            var userId = request.UserId.ToList();
            List<UserRole> list = new List<UserRole>();
            foreach (string item in userId)
            {
                list.Add(new UserRole
                {
                    UserId = Guid.Parse(item),
                    CreateId = Guid.Empty,
                    RoleId = roleId
                });
            }
            _dbContext.Set<UserRole>().AddRange(list);
            var saveSuccess = _dbContext.SaveChanges() > 0;
            x.Result = Any.Pack(Value.ForBool(saveSuccess));
            x.IsSuccess = saveSuccess;
            return Task.FromResult(x);
        }



        public override Task<AuthResult> GiveRoleAuth(GiveRoleAuthRequest request, ServerCallContext context)
        {
            var x = new AuthResult();

            if (!Guid.TryParse(request.RoleId, out var roleId))
            {
                x.Message = "参数roleId有误！";
                return Task.FromResult(x);
            }
            RoleRelation roleRelation = new RoleRelation();
            roleRelation.RoleId = roleId;
            roleRelation.IsSpecific = false;
            roleRelation.Auth = System.Text.Json.JsonSerializer.Serialize(request.AuthInfoList);
            roleRelation.CreateId = Guid.Parse(request.AuthInfoList[0].CreateId);
            _dbContext.Set<RoleRelation>().Add(roleRelation);
            var saveSuccess = _dbContext.SaveChanges() > 0;
            x.Result = Any.Pack(Value.ForBool(saveSuccess));
            x.IsSuccess = saveSuccess;
            return Task.FromResult(x);
        }

        public override Task<GetRoleAuthResponse> GetRoleAuth(GetRoleAuthRequest request, ServerCallContext context)
        {
            var x = new GetRoleAuthResponse();

            if (!Guid.TryParse(request.RoleId, out var roleId))
            {
                x.Message = "参数roleId有误！";
                return Task.FromResult(x);
            }
            List<AuthInfo> result = new List<AuthInfo>();
            RoleRelation roleRelation = _dbContext.RoleRelations.Where((RoleRelation t) => t.RoleId.Equals(roleId)).FirstOrDefault();
            if (roleRelation != null)
            {
                result = System.Text.Json.JsonSerializer.Deserialize<List<AuthInfo>>(roleRelation.Auth);
            }
            x.AuthInfoList.AddRange(_mapper.Map<List<GrpcAuthInfo>>(result));
            return Task.FromResult(x);
        }

    }
}
