﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    // <summary>
    /// 项目模型在客户端本地的映射信息信息
    /// </summary>
    public class RepositoryInformation
    {
        public RepositoryInformation()
        {
            CreationTime = DateTime.Now;
        }
        /// <summary>
        /// 本地映射的ID，在Graphite中用于在客户端设定RepositoryID
        /// </summary>
        public int ID { get; set; }
        public DateTime CreationTime { get; set; }
        virtual public ProjectMember Owner { get; set; }
    }
}
