using System;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.DbInitializer;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级的抽象基类，提供基本实现
    /// </summary>
    public abstract class AbstractDatabaseUpgrade : IDatabaseUpgrade
    {
        protected readonly IServiceProvider ServiceProvider;
        protected readonly ILogger Logger;
        
        private TransactionCoordinator _transactionCoordinator;
        
        public abstract string SourceVersion { get; }
        public abstract string TargetVersion { get; }
        public abstract string Description { get; }
        
        protected AbstractDatabaseUpgrade(IServiceProvider serviceProvider, ILogger logger)
        {
            ServiceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 获取TeamDbContext实例
        /// </summary>
        protected TeamDbContext GetTeamDbContext()
        {
            return ServiceProvider.GetRequiredService<TeamDbContext>();
        }
        
        /// <summary>
        /// 获取LogDbContext实例
        /// </summary>
        protected LogDbContext GetLogDbContext()
        {
            return ServiceProvider.GetRequiredService<LogDbContext>();
        }
        
        /// <summary>
        /// 获取LibraryDbContext实例
        /// </summary>
        protected LibraryDbContext GetLibraryDbContext()
        {
            return ServiceProvider.GetRequiredService<LibraryDbContext>();
        }
        
        /// <summary>
        /// 根据类型获取DbContext实例
        /// </summary>
        protected T GetDbContext<T>() where T : DbContext
        {
            return ServiceProvider.GetRequiredService<T>();
        }
        
        /// <summary>
        /// 确保当前有一个活动的事务协调器，如果没有则创建
        /// </summary>
        private void EnsureActiveTransactionCoordinator()
        {
            if (_transactionCoordinator != null)
            {
                Logger.LogWarning("EnsureActiveTransactionCoordinator: 发现一个已存在的 TransactionCoordinator，可能存在资源未正确释放或嵌套作用域问题。将尝试释放旧的并创建新的。");
                DisposeActiveTransactionCoordinator();
            }
            _transactionCoordinator = new TransactionCoordinator(Logger);
        }
        
        /// <summary>
        /// 注册数据库上下文到事务协调器
        /// </summary>
        /// <param name="name">数据库名称标识</param>
        /// <param name="dbContext">数据库上下文</param>
        protected async Task RegisterContextAsync(string name, DbContext dbContext)
        {
            if (_transactionCoordinator == null) 
            {
                 Logger.LogError("RegisterContextAsync: TransactionCoordinator 为 null，这不符合预期流程。");
                 throw new InvalidOperationException("TransactionCoordinator 未初始化，无法注册上下文。");
            }
            await _transactionCoordinator.RegisterContextAsync(name, dbContext);
        }
        
        /// <summary>
        /// 提交所有注册的数据库事务
        /// </summary>
        protected async Task CommitTransactionAsync()
        {
            if (_transactionCoordinator == null) 
                throw new InvalidOperationException("事务协调器无效，无法提交事务");
            await _transactionCoordinator.CommitAsync();
        }
        
        /// <summary>
        /// 回滚所有注册的数据库事务
        /// </summary>
        protected async Task RollbackTransactionAsync()
        {
            if (_transactionCoordinator == null) 
            {
                Logger.LogWarning("RollbackTransactionAsync: TransactionCoordinator 为 null，无法回滚。");
                return; 
            }
            await _transactionCoordinator.RollbackAsync();
        }
        
        /// <summary>
        /// 释放当前活动的事务协调器及其关联的上下文
        /// </summary>
        private void DisposeActiveTransactionCoordinator()
        {
            _transactionCoordinator?.Dispose();
            _transactionCoordinator = null;
        }
        
        private async Task ExecuteScopedDatabaseOperationAsync(string operationName, Func<Task> internalAction)
        {
            EnsureActiveTransactionCoordinator();
            Logger.LogInformation($"开始数据库操作: {operationName}");
            try
            {
                await internalAction();
                await CommitTransactionAsync();
                Logger.LogInformation($"数据库操作成功完成: {operationName}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"数据库操作 [{operationName}] 发生异常，尝试回滚事务。");
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                DisposeActiveTransactionCoordinator();
            }
        }

        public async Task UpgradeAsync()
        {
            await ExecuteScopedDatabaseOperationAsync(
                $"全局数据库升级: {SourceVersion} -> {TargetVersion}", 
                async () => 
                {
                    var teamDbContext = GetTeamDbContext();
                    await RegisterContextAsync(nameof(TeamDbContext), teamDbContext); 
                    await UpgradeInternalAsync(); 
                    await UpdateVersionInfoAsync(); 
                });
        }
        
        public async Task RollbackAsync()
        {
            await ExecuteScopedDatabaseOperationAsync(
                $"全局数据库回滚: {TargetVersion} -> {SourceVersion}", 
                async () => 
                {
                    var teamDbContext = GetTeamDbContext();
                    await RegisterContextAsync(nameof(TeamDbContext), teamDbContext);
                    await RollbackInternalAsync();
                    await RemoveVersionInfoAsync();
                });
        }
        
        /// <summary>
        /// 更新版本信息
        /// </summary>
        private async Task UpdateVersionInfoAsync()
        {
            var dbContext = GetTeamDbContext(); 
            dbContext.Set<DatabaseVersion>()?.Add(new DatabaseVersion
            { Version = TargetVersion, UpgradedAt = DateTime.Now, Description = Description });
            await dbContext.SaveChangesAsync(); 
            Logger.LogInformation($"版本记录已更新: {TargetVersion}");
        }
        
        /// <summary>
        /// 删除版本信息
        /// </summary>
        private async Task RemoveVersionInfoAsync()
        {
            var dbContext = GetTeamDbContext();
            var versionToRemove = await dbContext.Set<DatabaseVersion>()
                .FirstOrDefaultAsync(v => v.Version == TargetVersion);
            if (versionToRemove != null)
            {
                dbContext.Set<DatabaseVersion>().Remove(versionToRemove);
                await dbContext.SaveChangesAsync(); 
                Logger.LogInformation($"版本记录已删除: {TargetVersion}");
            }
        }
        
        /// <summary>
        /// 具体的升级实现，由子类实现
        /// </summary>
        protected abstract Task UpgradeInternalAsync();
        
        /// <summary>
        /// 具体的回滚实现，由子类实现
        /// </summary>
        protected abstract Task RollbackInternalAsync();
        
        /// <summary>
        /// 具体的主项目数据库升级实现，由子类实现
        /// </summary>
        /// <param name="mainProjectId">主项目ID</param>
        protected abstract Task UpgradeMainProjectInternalAsync(Guid mainProjectId);
        
        /// <summary>
        /// 具体的主项目数据库回滚实现，由子类实现
        /// </summary>
        /// <param name="mainProjectId">主项目ID</param>
        protected abstract Task RollbackMainProjectInternalAsync(Guid mainProjectId);
        
        /// <summary>
        /// 具体的主项目数据库升级实现，由子类实现
        /// </summary>
        /// <param name="modelId">主项目ID</param>
        protected abstract Task UpgradeModelInternalAsync(Guid modelId);
        
        /// <summary>
        /// 具体的主项目数据库回滚实现，由子类实现
        /// </summary>
        /// <param name="modelId">主项目ID</param>
        protected abstract Task RollbackModelInternalAsync(Guid modelId);
        
        /// <summary>
        /// 具体的主项目数据库升级实现，由子类实现
        /// </summary>
        /// <param name="libraryId">库ID</param>
        protected abstract Task UpgradeLibraryInternalAsync(Guid libraryId);
        
        /// <summary>
        /// 具体的主项目数据库回滚实现，由子类实现
        /// </summary>
        /// <param name="libraryId">库ID</param>
        protected abstract Task RollbackLibraryInternalAsync(Guid libraryId);

        // Correctly implement interface members by calling ExecuteScopedDatabaseOperationAsync
        public async Task UpgradeMainProjectDatabaseAsync(Guid mainProjectId)
        {
            await ExecuteScopedDatabaseOperationAsync($"主项目数据库升级 ID={mainProjectId}, {SourceVersion}->{TargetVersion}", 
                () => UpgradeMainProjectInternalAsync(mainProjectId));
        }

        public async Task RollbackMainProjectDatabaseAsync(Guid mainProjectId)
        {
            await ExecuteScopedDatabaseOperationAsync($"主项目数据库回滚 ID={mainProjectId}, {TargetVersion}->{SourceVersion}", 
                () => RollbackMainProjectInternalAsync(mainProjectId));
        }

        public async Task UpgradeModelDatabaseAsync(Guid modelId)
        {
            await ExecuteScopedDatabaseOperationAsync($"模型数据库升级 ID={modelId}, {SourceVersion}->{TargetVersion}", 
                () => UpgradeModelInternalAsync(modelId));
        }

        public async Task RollbackModelDatabaseAsync(Guid modelId)
        {
            await ExecuteScopedDatabaseOperationAsync($"模型数据库回滚 ID={modelId}, {TargetVersion}->{SourceVersion}", 
                () => RollbackModelInternalAsync(modelId));
        }

        public async Task UpgradeLibraryDatabaseAsync(Guid libraryId)
        {
            await ExecuteScopedDatabaseOperationAsync($"库数据库升级 ID={libraryId}, {SourceVersion}->{TargetVersion}", 
                () => UpgradeLibraryInternalAsync(libraryId));
        }

        public async Task RollbackLibraryDatabaseAsync(Guid libraryId)
        {
            await ExecuteScopedDatabaseOperationAsync($"库数据库回滚 ID={libraryId}, {TargetVersion}->{SourceVersion}", 
                () => RollbackLibraryInternalAsync(libraryId));
        }
    }
} 