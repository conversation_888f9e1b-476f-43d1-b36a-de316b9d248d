apiVersion: v1
kind: Service
metadata:
  name: {{ template "bimbaseauthapi.fullname" . }}
  labels:
    app: {{ template "bimbaseauthapi.name" . }}
    chart: {{ template "bimbaseauthapi.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ template "bimbaseauthapi.name" . }}
    release: {{ .Release.Name }}
