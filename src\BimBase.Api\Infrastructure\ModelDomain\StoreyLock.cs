﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class StoreyLock
    {
        [Key]
        public Int64 Id { get; set; }

        public int? LockUserId { get; set; }

        public int Domain { get; set; }

        public int VersionNo { get; set; }

    }

    public class ModelLock
    {
        [Key]
        public int Id { get; set; }
        public string ModelLockUser { get; set; }
    }
}
