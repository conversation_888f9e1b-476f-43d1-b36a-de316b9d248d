using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Data.Common;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Config;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级服务的扩展方法
    /// </summary>
    public static class DatabaseUpgradeServiceExtensions
    {
        /// <summary>
        /// 添加数据库升级服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDatabaseUpgradeServices(this IServiceCollection services)
        {
            // 注册数据库升级工厂
            services.AddSingleton<DatabaseUpgradeFactory>(provider => {
                var serviceProvider = provider;
                var serviceScopeFactory = provider.GetRequiredService<IServiceScopeFactory>();
                return new DatabaseUpgradeFactory(serviceProvider, serviceScopeFactory);
            });

            // 注册数据库升级服务
            services.AddScoped<DatabaseUpgradeService>(provider =>
            {
                var factory = provider.GetRequiredService<DatabaseUpgradeFactory>();
                var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<DatabaseUpgradeService>>();
                var serviceScopeFactory = provider.GetRequiredService<IServiceScopeFactory>();
                var mainProjectRepositoryFactory = provider.GetRequiredService<Func<DbConnection, Guid, IMainProjectRepository>>();
                var versioningOptions = provider.GetRequiredService<IOptions<DatabaseVersioningOptions>>();

                return new DatabaseUpgradeService(
                    factory, 
                    logger, 
                    serviceScopeFactory,
                    mainProjectRepositoryFactory,
                    versioningOptions);
            });
            
            return services;
        }
    }
} 