﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class LoginSession
    {
        [Key]
        public string ID { get; set; }
        /// <summary>
        /// TeamMember 表主键
        /// </summary>
        public Guid TeamMember_ID { get; set; }
        /// <summary>
        /// 登录成功，生成sessionid ，登出后清空，每次调用login 接口必须重新设置此字段的值
        /// </summary>
        public string SessionId { get; set; }

        public Guid ProjectGuid { get; set; }

        /// <summary>
        /// 客户端登录标识
        /// </summary>
        public Guid? ClientGuid { get; set; }
        public DateTime LoginAt { get; set; } = DateTime.Now;

    }
}
