﻿using System.ComponentModel.DataAnnotations;
using System;
using BimBase.Api.Infrastructure.MainDomain;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    [XmlRoot(Namespace = "")]
    /// <summary>
    /// 用户-节点-权限表
    /// </summary>
    public class MainProjectUserGroupAuth
    {
        
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// 用户组id或用户id
        /// </summary>
        
        public string GroupOrMemberId { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        
        public Guid MainProjectID { get; set; }

        /// <summary>
        /// 标记是用户组还是用户 0：用户组 1：用户
        /// </summary>
        
        public int IsGroupOrTeamMember { get; set; }
        
        public string AuthInfo { get; set; }
        /// <summary>
        /// 权限对象类型0：特殊（是否项目管理员） 1：项目 2：文件夹 3：模型 4：树节点 
        /// </summary>
        
        public int ObjectType { get; set; }

        /// <summary>
        /// ojecttype为4时，该字段为子项目（模型）id;或者库的libid
        /// </summary>
        
        public Guid ObjectId { get; set; }

        /// <summary>
        /// 节点instanceid，objecttype为4时需赋值，用于pdms设置节点权限
        /// </summary>
        
        public long InstanceId { get; set; }
        /// <summary>
        /// 节点TreeId，objecttype为4时需赋值，用于pdms设置节点权限
        /// </summary>
        
        public long TreeId { get; set; }
    }
    [XmlRoot(Namespace = "")]
    public class MainProjectUserGroupLibAuth
    {
        [Key]
        
        public int ID { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>
        
        public Guid MainProjectID { get; set; }
        /// <summary>
        /// 用户组ID
        /// </summary>
        
        public int MPUserGroupId { get; set; }
        /// <summary>
        /// 库类型
        /// </summary>
        
        [Required, EnumDataType(typeof(LibType))]
        public LibType LibType { get; set; }

        /// <summary>
        /// 是否具有权限
        /// </summary>
        
        public int Permission { set; get; }
        /// <summary>
        /// 自定义权限信息 MPAuthInfo json字符串 
        /// </summary>
        
        public string AuthInfo { get; set; }
        /// <summary>
        /// 扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }
    }
}
