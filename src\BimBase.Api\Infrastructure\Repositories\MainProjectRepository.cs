﻿using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Protos;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Utilities.Encoders;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.Json;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Repositories
{
    public class MainProjectRepository : IMainProjectRepository, IDisposable
    {
        private readonly MainProjectDbContext _mainProjectDbContext;
        private readonly DbConnection _dbConnection;
        private readonly IMainProjectBulkOperation _bulkOperation;
        private volatile bool disposedValue;
        private readonly char separator = '/';//Path.DirectorySeparatorChar;
        const int DatabaseExecTimeout = 60000;

        public MainProjectRepository(DbConnection dbConnection, Guid guid, IMainProjectBulkOperation bulkOperation)
        {
            _dbConnection = dbConnection ?? throw new ArgumentNullException(nameof(dbConnection));
            _bulkOperation = bulkOperation ?? throw new ArgumentNullException(nameof(bulkOperation));
            
            // 获取全局服务提供者来访问配置
            var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            
            // 从配置中获取模板连接字符串
            var templateConnectionString = configuration.GetConnectionString("TemplateConnection");
            if (!string.IsNullOrEmpty(templateConnectionString))
            {
                // 使用配置中的模板连接字符串
                var databaseName = "PKPM-PBIMServer-MPDB-" + guid.ToString();
                var mpConn = templateConnectionString.Replace("{DatabaseName}", databaseName);
                _mainProjectDbContext = new MainProjectDbContextFactory().CreateDbContext(new string[] { mpConn });
            }
            else
            {
                // 回退到原有逻辑（使用传入的dbConnection）
                var mpConn = _dbConnection.ConnectionString
                    .Replace("database=pkpm-pbimserver-teamdb", "database=PKPM-PBIMServer-MPDB-" + guid.ToString(), StringComparison.OrdinalIgnoreCase);
                _mainProjectDbContext = new MainProjectDbContextFactory().CreateDbContext(new string[] { mpConn });
            }
            
            _mainProjectDbContext.Database.SetCommandTimeout(DatabaseExecTimeout);
            MainProjectDbInitializer.Initialize(_mainProjectDbContext, serviceProvider);
        }

        /// <summary>
        /// 获取主项目数据库上下文
        /// </summary>
        /// <returns>主项目数据库上下文实例</returns>
        public MainProjectDbContext GetMainProjectDbContext()
        {
            return _mainProjectDbContext;
        }

        #region BPExObject
        public IQueryable<BPExObject> BPExObjects
        {
            get
            {
                return _mainProjectDbContext.BPExObjects.AsNoTracking();
            }
        }

        public bool CheckInTreeNodes(
            List<MPProjectTreeNode> addDatas,
            List<MPProjectTreeNode> modifyDatas,
            List<MPProjectTreeNode> delDatas)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveMPProjectTreeNodes(_mainProjectDbContext, addDatas);
                _bulkOperation.SaveMPProjectTreeNodes(_mainProjectDbContext, modifyDatas);
                _bulkOperation.DeleteMPProjectTreeNodes(_mainProjectDbContext, delDatas);
                return true;
            }
            return false;
        }

        public bool AddBPExObjects(List<BPExObject> bpExObjects)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveBPExObjects(_mainProjectDbContext, bpExObjects);
                return true;
            }

            return false;
        }


        public bool DeleteBPExObjects(List<Guid> deleteGuidList)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.DeleteBPExObjects(_mainProjectDbContext, deleteGuidList);
                //PbimLog //PbimLog = new //PbimLog("Info");
                //PbimLog.Info("DeleteBPExObjects  end");
                return true;
            }

            return false;
        }
        #endregion

        #region BPExObjectPublish
        public IQueryable<BPExObjectPublish> BPExObjectPublishs
        {
            get
            {
                return _mainProjectDbContext.BPExObjectPublishes.AsNoTracking();
            }
        }

        public bool AddBPExObjectPublishs(List<BPExObjectPublish> bpExObjectPublishs, int mainVersion, int subVersion)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveBPExObjectPublishs(_mainProjectDbContext, bpExObjectPublishs, mainVersion, subVersion);
                //PbimLog //PbimLog = new //PbimLog("Info");
                //PbimLog.Info(" ==> AddBPExObjectPublishs end;  mainVersion==>  " + mainVersion + ",subversion==>" + subVersion);
                return true;
            }

            return false;
        }

        public bool RestoreMPDb(string sqlPath)
        {
            // 检查飞件是否存在
            if (!File.Exists(sqlPath))
            {
                Console.WriteLine($"sQL file not found: {sqlPath}");
                return false;
            }
                
            // 读 SQL X件内蓉
            //string sqlScript = File.ReadAllText(sqlPath);
            using var reader = new StreamReader(sqlPath);
            string? line;
            try
            {
                string sqlstr = "";
                using var transaction = _mainProjectDbContext.Database.BeginTransaction();
                while ((line = reader.ReadLine()) != null)
                {
                    if (line.StartsWith("--"))
                    {
                        continue;
                    }
                    if (line.EndsWith(";"))
                    {
                        sqlstr += line;
                        if (!string.IsNullOrEmpty(sqlstr))
                        {
                            var replacesql = sqlstr.Replace("{", "{{").Replace("}", "}}");
                            Console.WriteLine($"正在执行的sql语句：：：===》{replacesql}");
                            _mainProjectDbContext.Database.ExecuteSqlRaw(replacesql);
                            Console.WriteLine($"Executed: {sqlstr.Trim()}");
                            sqlstr = "";
                        }
                    }
                    else
                    {
                        sqlstr += line;
                    }
                    
                }
                transaction.Commit();
            }
            catch (Exception ex)
            {

                Console.WriteLine($"Error during database restoration:{ex.Message}"); 
            }
            
            //try
            //{
            //    using var transaction = _mainProjectDbContext.Database.BeginTransaction();
            //    // 黄行SOL 脚本
            //    foreach (var sql in sqlScript.Split(";", StringSplitOptions.RemoveEmptyEntries))
            //    {
                    
            //        if (!string.IsNullOrWhiteSpace(sql))
            //        {
            //            var replacesql = sql.Trim().Replace("{", "{{").Replace("}", "}}");
            //            Console.WriteLine($"正在执行的sql语句：：：===》{replacesql}");
            //            _mainProjectDbContext.Database.ExecuteSqlRaw(replacesql);
            //            Console.WriteLine($"Executed: {sql.Trim()}");
            //        }
            //    }
            //    transaction.Commit();
            //    //_mainProjectDbContext.SaveChanges();
            //    Console.WriteLine("Database restoration completed.");
            //}
            //catch (Exception ex)
            //{

            //    Console.WriteLine($"Error during database restoration:{ex.Message}") ;;
            //}
            //using var reader = new StreamReader(sqlPath);
            //string? line;
            //while ((line = reader.ReadLine()) != null)
            //{
            //    if (!string.IsNullOrEmpty(line))
            //    {
            //        _mainProjectDbContext.Database.ExecuteSqlRaw(line.Trim());
            //        Console.WriteLine($"Executed: {line.Trim()}");
            //    }
            //}
            //_mainProjectDbContext.Database.ExecuteSqlRaw(commandText);
            //_mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool CheckInBPExObjectPublishsWithVersionName(List<BPExObjectPublish> bpExObjectPublishs, List<BPExObjectPublish> delBPExObjectPublishs, string mainVersionName, string subVersionName)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveBPExObjectPublishsWithVersionName(_mainProjectDbContext, bpExObjectPublishs, mainVersionName,subVersionName);
                _bulkOperation.DeleteBPExObjectPublishsWithVersionName(_mainProjectDbContext, delBPExObjectPublishs, mainVersionName, subVersionName);
                return true;
            }

            return false;
        }

        public bool RemoveBPExObjectPublishsWithVersionName(string versionName, string subVersionName)
        {
            var select = _mainProjectDbContext.BPExObjectPublishes.Where(s => s.VersionName == versionName && s.SubVersionName == subVersionName).ToList();
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _mainProjectDbContext.BPExObjectPublishes.RemoveRange(select);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }
        #endregion

        #region ProvideVersionInfo
        public IQueryable<ProvideVersionInfo> ProvideVersionInfoes
        {
            get
            {
                return _mainProjectDbContext.ProvideVersionInfoes.AsNoTracking();
            }
        }

        public bool AddProvideVersionInfo(ProvideVersionInfo pvInfo)
        {
            var select = _mainProjectDbContext.ProvideVersionInfoes.Where(p => p.VersionName == pvInfo.VersionName && p.SubVersionName == pvInfo.SubVersionName);
            if (select.Any()) return false;
            _mainProjectDbContext.ProvideVersionInfoes.Add(pvInfo);
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool RemoveProvideVersionInfo(string mainVersionName, string subVersionName)
        {
            var select = _mainProjectDbContext.ProvideVersionInfoes.FirstOrDefault(p => p.VersionName == mainVersionName && p.SubVersionName == subVersionName);
            if (select == null) return true;
            _mainProjectDbContext.ProvideVersionInfoes.Remove(select);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool UpdateProvideVersionItemCount(string mainVersionName, string subVersionName, int count)
        {
            //PbimLog //PbimLog = new //PbimLog("Info");
            //PbimLog.Info(" ==> UpdateProvideVersionItemCount begin;  mainVersion==>  " + mainVersionName + ",subversion==>" + subVersionName + ",count:" + count.ToString());
            var select = _mainProjectDbContext.ProvideVersionInfoes.FirstOrDefault(p => p.VersionName == mainVersionName && p.SubVersionName == subVersionName);
            if (select == null)
            {
                //PbimLog.Info("UpdateProvideVersionItemCount 未找到对应版本");
                return true;
            }
            //PbimLog.Info("UpdateProvideVersionItemCount 开始更新ProvideItemCount");
            select.ProvideItemCount = count;
            _mainProjectDbContext.ProvideVersionInfoes.Attach(select);
            _mainProjectDbContext.Entry(select).Property(a => a.ProvideItemCount).IsModified = true;
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool PublishProvideVersion(string mainVersionName, string subVersionName, int state, string username, int count)
        {
            var select = _mainProjectDbContext.ProvideVersionInfoes.FirstOrDefault(p => p.VersionName == mainVersionName && p.SubVersionName == subVersionName);
            if (select == null) return true;
            select.CheckState = state;
            select.ProvidePublishUser = username;
            select.ProvidePublishTime = DateTime.Now;
            select.ProvideItemCount = count;
            _mainProjectDbContext.ProvideVersionInfoes.Attach(select);
            _mainProjectDbContext.Entry(select).Property(a => a.CheckState).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.ProvidePublishUser).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.ProvidePublishTime).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.ProvideItemCount).IsModified = true;
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool ModifyProvideVersion(string mainVersionName, string subVersionName, int state, ProvideVersionInfo newVerisonInfo)
        {
            //PbimLog //PbimLog = new //PbimLog("Info");
            //PbimLog.Info(" ==> ModifyProvideVersion begin;  mainVersion==>  " + mainVersionName + ",subversion==>" + subVersionName + ",mark:" + newVerisonInfo.Mark.ToString());
            var select = _mainProjectDbContext.ProvideVersionInfoes.FirstOrDefault(p => p.VersionName == mainVersionName && p.SubVersionName == subVersionName);
            if (select == null)
            {
                //PbimLog.Info(" ==> ModifyProvideVersion null  ");
                return true;
            }
            if (select.CheckState != state)
            {
                select.CheckState = state;
                select.AcceptPublishTime = DateTime.Now;
            }
            select.Mark = newVerisonInfo.Mark;
            select.AcceptPublishUser = newVerisonInfo.AcceptPublishUser;
            select.AcceptDomain = newVerisonInfo.AcceptDomain;
            select.ProvideDomain = newVerisonInfo.ProvideDomain;
            select.DesignState = newVerisonInfo.DesignState;
            select.Note = newVerisonInfo.Note;
            select.CountersignMark = newVerisonInfo.CountersignMark;
            _mainProjectDbContext.ProvideVersionInfoes.Attach(select);

            _mainProjectDbContext.Entry(select).Property(a => a.CheckState).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.AcceptPublishTime).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.Mark).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.AcceptPublishUser).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.AcceptDomain).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.ProvideDomain).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.Note).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.DesignState).IsModified = true;
            _mainProjectDbContext.Entry(select).Property(a => a.CountersignMark).IsModified = true;
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        #endregion

        #region usergroup

        public IQueryable<MPUserGroup> MPUserGroups
        {
            get
            {
                return _mainProjectDbContext.MPUserGroups.AsNoTracking();
            }
        }

        public IQueryable<MPUserGroupMember> MPUserGroupMembers
        {
            get
            {
                return _mainProjectDbContext.MPUserGroupMembers.AsNoTracking();
            }
        }

        public MPUserGroup AddMPUserGroup(MPUserGroup grp)
        {
            var select = _mainProjectDbContext.MPUserGroups.Where(mp => mp.UserGroupName == grp.UserGroupName);
            if (select.Any())
            {
                return null;
            }
            grp = _mainProjectDbContext.MPUserGroups.Add(grp).Entity;
            _mainProjectDbContext.SaveChanges();
            return grp;
        }

        public bool UpdateMPUsergroup(MPUserGroup grp)
        {
            var old = _mainProjectDbContext.MPUserGroups.FirstOrDefault(m => m.ID == grp.ID);
            if (old == null)
            {
                return false;
            }

            old.UserGroupName = grp.UserGroupName;
            old.Description = grp.Description;
            _mainProjectDbContext.MPUserGroups.Attach(old);
            _mainProjectDbContext.Entry(old).Property(a => a.UserGroupName).IsModified = true;
            _mainProjectDbContext.Entry(old).Property(a => a.Description).IsModified = true;
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool DeleteMPUserGroup(int groupId)
        {
            var oldMPUserGroup = _mainProjectDbContext.MPUserGroups.FirstOrDefault(t => t.ID == groupId);
            var oldMPUserGroupMember = _mainProjectDbContext.MPUserGroupMembers.Where(t => t.UserGroupId == groupId).ToList();
            var oldMPUserGroupAuth = _mainProjectDbContext.MPUserGroupAuths.Where(t => t.GroupOrMemberId == groupId.ToString()).ToList();
            _mainProjectDbContext.MPUserGroups.Remove(oldMPUserGroup);
            _mainProjectDbContext.MPUserGroupMembers.RemoveRange(oldMPUserGroupMember);
            _mainProjectDbContext.MPUserGroupAuths.RemoveRange(oldMPUserGroupAuth);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool AddTeamMemberListToMPUsergroup(List<TeamMember> teamMemberList, MPUserGroup mPUserGroup)
        {
            List<MPUserGroupMember> mtgList = new List<MPUserGroupMember>();
            foreach (var item in teamMemberList)
            {
                MPUserGroupMember tur = new MPUserGroupMember();
                tur.UserGroupId = mPUserGroup.ID;
                tur.TeamMemberId = item.ID;
                mtgList.Add(tur);
            }

            _mainProjectDbContext.MPUserGroupMembers.AddRange(mtgList);

            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool AddTeamMemberToMPUsergroup(TeamMember teamMember, MPUserGroup mPUserGroup)
        {
            var select = _mainProjectDbContext.MPUserGroupMembers.Where(mp => mp.UserGroupId == mPUserGroup.ID && mp.TeamMemberId == teamMember.ID);
            if (select.Any())
            {
                return true;
            }
            MPUserGroupMember tur = new MPUserGroupMember();
            tur.UserGroupId = mPUserGroup.ID;
            tur.TeamMemberId = teamMember.ID;
            _mainProjectDbContext.MPUserGroupMembers.Add(tur);
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool RemoveTeamMemberFromMPUsergroupList(TeamMember teamMember, List<MPUserGroup> mPUserGroupList)
        {
            var all = _mainProjectDbContext.MPUserGroupMembers.Where(mp => mp.TeamMemberId == teamMember.ID).ToList();
            List<MPUserGroupMember> delList = new List<MPUserGroupMember>();
            foreach (var mPUserGroup in mPUserGroupList)
            {
                var select = all.Where(mp => mp.UserGroupId == mPUserGroup.ID).ToList();
                if (select.Any())
                {
                    delList.AddRange(select);
                }
            }
            _mainProjectDbContext.MPUserGroupMembers.RemoveRange(delList);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool RemoveTeamMemberListFromMPUserGroupList(List<Guid> members, List<MPUserGroup> mPUserGroupList)
        {
            if (!members.Any() || (!mPUserGroupList.Any()))
            {
                return true;
            }
            var groupids = mPUserGroupList.Select(s => s.ID).ToList();
            var all = _mainProjectDbContext.MPUserGroupMembers.Where(mp => members.Contains(mp.TeamMemberId) && groupids.Contains(mp.UserGroupId)).ToList();

            _mainProjectDbContext.MPUserGroupMembers.RemoveRange(all);
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool RemoveTeamMemberListFromMPUsergroup(List<TeamMember> teamMemberList, MPUserGroup mPUserGroup)
        {
            var all = _mainProjectDbContext.MPUserGroupMembers.Where(mp => mp.UserGroupId == mPUserGroup.ID).ToList();
            List<MPUserGroupMember> delList = new List<MPUserGroupMember>();
            foreach (var teamMember in teamMemberList)
            {
                var select = all.Where(mp => mp.TeamMemberId == teamMember.ID).ToList();
                if (select.Any())
                {
                    delList.AddRange(select);
                }
            }
            _mainProjectDbContext.MPUserGroupMembers.RemoveRange(delList);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool RemoveTeamMemberFromMPUsergroup(TeamMember teamMember, MPUserGroup mPUserGroup)
        {
            var select = _mainProjectDbContext.MPUserGroupMembers.Where(mp => mp.UserGroupId == mPUserGroup.ID && mp.TeamMemberId == teamMember.ID);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _mainProjectDbContext.MPUserGroupMembers.Remove(select.First());
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }
        #endregion

        #region MPUserGroupAuth
        public IQueryable<MPUserGroupAuth> MPUserGroupAuths
        {
            get
            {
                return _mainProjectDbContext.MPUserGroupAuths.AsNoTracking();
            }
        }

        public bool GiveAuthToMPUserGroupMultiple(List<MPUserGroupAuth> userGroupAuthList)
        {
            foreach (var userGroupAuth in userGroupAuthList)
            {
                var authListJson = userGroupAuth.AuthInfo;
                var authList = JsonSerializer.Deserialize<List<MPAuthInfo>>(authListJson);
                var select = _mainProjectDbContext.MPUserGroupAuths.Where(t => t.GroupOrMemberId == userGroupAuth.GroupOrMemberId
                    && t.ObjectId == userGroupAuth.ObjectId
                    && t.InstanceId == userGroupAuth.InstanceId).FirstOrDefault();
                if (select != null)
                {
                    List<MPAuthInfo> curAuth = JsonSerializer.Deserialize<List<MPAuthInfo>>(select.AuthInfo);
                    foreach (var au in authList)
                    {
                        var toChange = curAuth.FirstOrDefault(x => x.AuthName == au.AuthName);
                        if (toChange != null)
                        {
                            toChange.Permission = au.Permission;
                        }
                        else
                        {
                            curAuth.Add(au);
                        }
                    }
                    select.AuthInfo = JsonSerializer.Serialize(curAuth);
                    _mainProjectDbContext.MPUserGroupAuths.Attach(select);
                    _mainProjectDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;

                }
                else
                {
                    MPUserGroupAuth mainProjectUserGroupAuth = new MPUserGroupAuth
                    {
                        GroupOrMemberId = userGroupAuth.GroupOrMemberId,
                        IsGroupOrTeamMember = userGroupAuth.IsGroupOrTeamMember,
                        AuthInfo = userGroupAuth.AuthInfo,
                        ObjectType = userGroupAuth.ObjectType,
                        ObjectId = userGroupAuth.ObjectId,
                        InstanceId = userGroupAuth.InstanceId,
                        TreeId = userGroupAuth.TreeId
                    };
                    userGroupAuth.AuthInfo = JsonSerializer.Serialize(authList);
                    _mainProjectDbContext.MPUserGroupAuths.Add(mainProjectUserGroupAuth);
                    //_mainProjectDbContext.SaveChanges();
                    //return true;
                }
            }
            _mainProjectDbContext.SaveChanges();
            return true;

        }


        public bool GiveAuthToMPUserGroup(MPUserGroupAuth userGroupAuth, List<MPAuthInfo> authList)
        {
            var select = _mainProjectDbContext.MPUserGroupAuths.Where(t => t.GroupOrMemberId == userGroupAuth.GroupOrMemberId && t.ObjectId == userGroupAuth.ObjectId && t.InstanceId == userGroupAuth.InstanceId).FirstOrDefault();
            if (select != null)
            {
                List<MPAuthInfo> curAuth = JsonSerializer.Deserialize<List<MPAuthInfo>>(select.AuthInfo);
                foreach (var au in authList)
                {
                    var toChange = curAuth.FirstOrDefault(x => x.AuthName == au.AuthName);
                    if (toChange != null)
                    {
                        toChange.Permission = au.Permission;
                    }
                    else
                    {
                        curAuth.Add(au);
                    }
                }
                select.AuthInfo = JsonSerializer.Serialize(curAuth);
                _mainProjectDbContext.MPUserGroupAuths.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            else
            {
                userGroupAuth.AuthInfo = JsonSerializer.Serialize(authList);
                _mainProjectDbContext.MPUserGroupAuths.Add(userGroupAuth);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }

        public List<MPAuthInfo> GetCurrentUserMPAuthInfosByObjectId(Guid objectGuid, long nodeid, Guid teammemberid, int objecttype)
        {
            //查询当前用户对于objectid对象及祖辈节点的权限列表
            //PbimLog log = new //PbimLog("Info");
            try
            {
                List<long> objIds = new List<long>();
                List<MPAuthInfo> allAuthList = new List<MPAuthInfo>();
                List<MPAuthInfo> retList = new List<MPAuthInfo>();
                //查询当前用户参与的teamgroup
                var userGroups = _mainProjectDbContext.MPUserGroupMembers.Where(g => g.TeamMemberId == teammemberid).ToList();
                //查询当前用户参与的teamgroupid
                var groupids = new List<int>();
                if (userGroups.Any())
                {
                    groupids = userGroups.Select(s => s.UserGroupId).ToList();
                }
                //树节点权限



                //获取该子项目（模型）的所有节点
                var alltreenodes = _mainProjectDbContext.MPProjectTreeNodes.Where(t => t.subProjectld == objectGuid);

                //递归获取所有上层节点
                var objectNode = alltreenodes.FirstOrDefault(f => f.NodeId == nodeid);
                if (objectNode != null)
                {
                    objIds.Add(objectNode.NodeId);

                    while (objectNode != null && objectNode.ParentNodeId != -2)
                    {
                        objectNode = alltreenodes.FirstOrDefault(f => f.NodeId == objectNode.ParentNodeId);
                        if (objectNode != null)
                        {
                            objIds.Add(objectNode.NodeId);
                        }
                    }
                }
                var mpUseGroupAuths = _mainProjectDbContext.MPUserGroupAuths.ToList();
                //权限列表，权限对象id为所有父辈节点id
                var objectGroupAuth = mpUseGroupAuths.Where(a => a.ObjectType == objecttype && a.ObjectId == objectGuid && objIds.Contains(a.InstanceId) && a.GroupOrMemberId == teammemberid.ToString()).ToList();
                foreach (var ob in objectGroupAuth)
                {
                    List<MPAuthInfo> authList = JsonSerializer.Deserialize<List<MPAuthInfo>>(ob.AuthInfo);
                    if (authList.Any())
                    {
                        allAuthList.AddRange(authList);
                    }
                }

                if (groupids.Any())
                {
                    //groupids具有的权限列表
                    foreach (var gid in groupids)
                    {
                        var groupauth = mpUseGroupAuths.Where(a => a.ObjectType == 4 && a.ObjectId == objectGuid && objIds.Contains(a.InstanceId) && a.GroupOrMemberId == gid.ToString()).ToList();
                        foreach (var ob in groupauth)
                        {
                            List<MPAuthInfo> gauth = JsonSerializer.Deserialize<List<MPAuthInfo>>(ob.AuthInfo);
                            if (gauth.Any())
                            {
                                allAuthList.AddRange(gauth);
                            }
                        }
                    }
                }


                List<string> authNameList = allAuthList.Select(s => s.AuthName).Distinct().ToList();
                foreach (var authName in authNameList)
                {
                    MPAuthInfo ta = new MPAuthInfo();
                    ta.AuthName = authName;
                    var noAuth = allAuthList.Where(a => a.AuthName == authName && a.Permission == 0).ToList();
                    if (noAuth.Any())
                    {
                        ta.Permission = 0;
                    }
                    else
                    {
                        ta.Permission = 1;
                    }
                    retList.Add(ta);
                }
                return retList;

            }
            catch (Exception)
            {

            }
            return null;

        }


        public List<MPAuthInfo> GetMPAuthInfosByObjectId(Guid objectGuid, long nodeid, string groupormemberid, int objecttype)
        {
            //查询用户或用户组对于objectid对象及祖辈节点的权限列表
            //PbimLog log = new //PbimLog("Info");
            try
            {


                List<long> objIds = new List<long>();
                List<MPAuthInfo> allAuthList = new List<MPAuthInfo>();
                List<MPAuthInfo> retList = new List<MPAuthInfo>();

                //获取该子项目（模型）的所有节点
                var alltreenodes = _mainProjectDbContext.MPProjectTreeNodes.AsNoTracking().Where(t => t.subProjectld == objectGuid);

                if (nodeid != 0)
                {
                    var objectNode = alltreenodes.FirstOrDefault(f => f.InstanceId == nodeid);
                    if (objectNode != null)
                    {
                        objIds.Add(objectNode.NodeId);

                        while (objectNode != null && objectNode.ParentNodeId != -2)
                        {
                            objectNode = alltreenodes.FirstOrDefault(f => f.NodeId == objectNode.ParentNodeId);
                            if (objectNode != null)
                            {
                                objIds.Add(objectNode.NodeId);
                            }
                        }
                    }
                }
                else
                {
                    objIds.Add(0);
                }
                //递归获取所有上层节点

                var mpUseGroupAuths = _mainProjectDbContext.MPUserGroupAuths.AsNoTracking().ToList();
                //权限列表，权限对象id为所有父辈节点id
                var objectGroupAuth = mpUseGroupAuths.Where(a => a.ObjectType == objecttype && a.ObjectId == objectGuid && objIds.Contains(a.InstanceId) && a.GroupOrMemberId == groupormemberid).ToList();
                //log.Info("GetMPAuthInfosByObjectId==>objectGroupAuth count=" + objectGroupAuth.Count + "|objecttype=" + objecttype + "|objectGuid=" + objectGuid + "|objIds=" + objIds.Count + "|groupormemberid=" + groupormemberid);
                foreach (var ob in objectGroupAuth)
                {
                    List<MPAuthInfo> authList = JsonSerializer.Deserialize<List<MPAuthInfo>>(ob.AuthInfo);
                    if (authList.Any())
                    {
                        allAuthList.AddRange(authList);
                    }
                }



                List<string> authNameList = allAuthList.Select(s => s.AuthName).Distinct().ToList();
                foreach (var authName in authNameList)
                {
                    MPAuthInfo ta = new MPAuthInfo();
                    ta.AuthName = authName;
                    var noAuth = allAuthList.Where(a => a.AuthName == authName && a.Permission == 0).ToList();
                    if (noAuth.Any())
                    {
                        ta.Permission = 0;
                    }
                    else
                    {
                        ta.Permission = 1;
                    }
                    retList.Add(ta);
                }
                return retList;
            }
            catch (Exception)
            {

            }
            return null;
        }

        public List<long> GetAllInstanceByMember(Guid objectGuid, int objecttype, Guid teammemberId)
        {
            List<long> ret = new List<long>();
            try
            {
                var sw = new System.Diagnostics.Stopwatch();

                // 项目下的组
                var groups = _mainProjectDbContext.MPUserGroups.ToList();

                // 用户所在的用户组
                var memberGroups = _mainProjectDbContext.MPUserGroupMembers.AsNoTracking().Where(g => g.TeamMemberId == teammemberId).Select(s => s.UserGroupId).ToList();
                var memberGroupIdStr = memberGroups.Select(s => s.ToString()).ToList();

                // 只查需要的权限数据，避免全表 ToList
                var mpUseGroupAuths = _mainProjectDbContext.MPUserGroupAuths
                    .AsNoTracking()
                    .Where(a => a.ObjectType == objecttype
                             && a.ObjectId == objectGuid
                             && memberGroupIdStr.Contains(a.GroupOrMemberId))
                    .Select(a => new { a.InstanceId, a.AuthInfo, a.IsGroupOrTeamMember })
                    .ToList();

                // 用户组权限过滤（内存操作）
                var objectGroupAuth = mpUseGroupAuths;

                // 用户组
                var allGroupAuth = objectGroupAuth.Where(a => a.IsGroupOrTeamMember == 0).ToList();
                var groupobjectGroupAuth = objectGroupAuth.Where(a => a.IsGroupOrTeamMember == 0).Select(s => s.InstanceId).Distinct().ToList();

                // 融合优化：预构建字典+并行+只解析Permission字段
                var authDict = allGroupAuth
                    .GroupBy(a => a.InstanceId)
                    .ToDictionary(g => g.Key, g => g.ToList());
                var resultBag = new System.Collections.Concurrent.ConcurrentBag<long>();
                System.Threading.Tasks.Parallel.ForEach(groupobjectGroupAuth, instance =>
                {
                    if (!authDict.TryGetValue(instance, out var groupAuths))
                        return;
                    foreach (var authRecord in groupAuths)
                    {
                        if (!string.IsNullOrEmpty(authRecord.AuthInfo) && authRecord.AuthInfo.Contains("\"Permission\":1"))
                        {
                            try
                            {
                                using (var doc = System.Text.Json.JsonDocument.Parse(authRecord.AuthInfo))
                                {
                                    foreach (var element in doc.RootElement.EnumerateArray())
                                    {
                                        if (element.TryGetProperty("Permission", out var permProp) && permProp.GetInt32() == 1)
                                        {
                                            resultBag.Add(instance);
                                            return; // 只要有一个就够了，跳出
                                        }
                                    }
                                }
                            }
                            catch { /* 忽略单条解析异常，防止影响整体 */ }
                        }
                    }
                });

                ret = resultBag.Distinct().ToList();
                return ret;
            }
            catch (Exception ex)
            {
                //log.Info("objectGuid==>" + objectGuid + "|GetAllInstanceByMember err:" + ex.Message);
            }
            return ret;
        }

        public Dictionary<int, List<MPAuthInfo>> GetAllMembersAuthInfoByObjectId(Guid objectGuid, long nodeid, int objecttype)
        {
            //查询用户或用户组对于objectid对象及祖辈节点的权限列表
            //PbimLog log = new //PbimLog("Info");
            //log.Info("GetAllMembersAuthInfoByObjectId=>objectGuid=" + objectGuid + "|nodeid=" + nodeid + "|objecttype=" + objecttype);
            try
            {
                List<long> objIds = new List<long>();
                Dictionary<int, List<MPAuthInfo>> retAuthDic = new Dictionary<int, List<MPAuthInfo>>();
                //树节点权限
                //获取该子项目（模型）的所有节点
                var alltreenodes = _mainProjectDbContext.MPProjectTreeNodes.Where(t => t.subProjectld == objectGuid);
                var groups = _mainProjectDbContext.MPUserGroups.ToList();
                //递归获取所有上层节点
                //var objectNode = alltreenodes.FirstOrDefault(f => f.NodeId == nodeid);
                //if (objectNode != null)
                //{
                //    objIds.Add(objectNode.NodeId);

                //    while (objectNode != null && objectNode.ParentNodeId != 0)
                //    {
                //        objectNode = alltreenodes.FirstOrDefault(f => f.NodeId == objectNode.ParentNodeId);
                //        if (objectNode != null)
                //        {
                //            objIds.Add(objectNode.NodeId);
                //        }
                //    }
                //}
                objIds.Add(nodeid);
                var mpUseGroupAuths = _mainProjectDbContext.MPUserGroupAuths.ToList();
                //权限列表，权限对象id为所有父辈节点id
                var objectGroupAuth = mpUseGroupAuths.Where(
                    a => a.ObjectType == objecttype
                    && a.ObjectId == objectGuid
                    && objIds.Contains(a.InstanceId)
                ).ToList();

                var allGroupAuth = objectGroupAuth.Where(a => a.IsGroupOrTeamMember == 0).ToList();
                //用户组
                var groupobjectGroupAuth = objectGroupAuth.Where(a => a.IsGroupOrTeamMember == 0).Select(s => s.GroupOrMemberId).Distinct().ToList();

                foreach (var group in groupobjectGroupAuth)
                {
                    int groupid = int.Parse(group);
                    var gp = groups.FirstOrDefault(g => g.ID == groupid);
                    var groupsauth = allGroupAuth.Where(s => s.GroupOrMemberId == group).ToList();
                    var tempaulist = new List<MPAuthInfo>();
                    foreach (var ob in groupsauth)
                    {
                        List<MPAuthInfo> authList = JsonSerializer.Deserialize<List<MPAuthInfo>>(ob.AuthInfo);
                        if (authList.Any())
                        {
                            tempaulist.AddRange(authList);
                        }
                    }

                    List<MPAuthInfo> retList = new List<MPAuthInfo>();
                    List<string> authNameList = tempaulist.Select(s => s.AuthName).Distinct().ToList();
                    foreach (var authName in authNameList)
                    {
                        MPAuthInfo ta = new MPAuthInfo();
                        ta.AuthName = authName;
                        //log.Info("objectGuid==>" + objectGuid + "|GetAllMembersAuthInfoByObjectId=>AuthName:" + authName);
                        var noAuth = tempaulist.Where(a => a.AuthName == authName && a.Permission == 0).ToList();
                        var noper = noAuth.Select(s => s.Permission);
                        //log.Info("objectGuid==>" + objectGuid + "|GetAllMembersAuthInfoByObjectId=>Permissions:" + string.Join(",", noper));
                        var auths = tempaulist.Where(a => a.AuthName == authName).ToList();
                        var pers = auths.Select(s => s.Permission);
                        //log.Info("objectGuid==>" + objectGuid + "|GetAllMembersAuthInfoByObjectId=>Permissions:" + string.Join(",", pers));
                        if (noAuth.Any())
                        {
                            //log.Info("objectGuid==>" + objectGuid + "|GetAllMembersAuthInfoByObjectId=>Permissions:noAuth.Any() true");
                            ta.Permission = 0;
                        }
                        else
                        {
                            //log.Info("objectGuid==>" + objectGuid + "|GetAllMembersAuthInfoByObjectId=>Permissions:noAuth.Any() false");
                            ta.Permission = 1;
                        }
                        retList.Add(ta);
                    }

                    if (!retAuthDic.ContainsKey(gp.ID))
                    {
                        retAuthDic.Add(gp.ID, retList);
                    }
                }
                return retAuthDic;
            }
            catch (Exception)
            {

            }
            return null;
        }

        #endregion

        #region MPTeamProject
        public IQueryable<MPTeamProject> MPTeamProjects
        {
            get
            {
                return _mainProjectDbContext.MPTeamProjects.AsNoTracking();
            }
        }

        public bool AddMPTeamProjectList(List<MPTeamProject> mPTeamProjects)
        {
            _mainProjectDbContext.MPTeamProjects.AddRange(mPTeamProjects);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public MPTeamProject AddMPTeamProject(MPTeamProject mpTeamProject)
        {
            var select = _mainProjectDbContext.MPTeamProjects.Where(tp => tp.ID == mpTeamProject.ID);
            if (select.Any())
            {
                throw new Exception("服务器已存在相同的模型ID\n请尝试使用其它模型ID！");
            }
            select = _mainProjectDbContext.MPTeamProjects.Where(tp => tp.Name.ToLower() == mpTeamProject.Name.ToLower());
            if (select.Any())
            {
                throw new Exception("服务器已存在同名模型\n请尝试修改模型名称");
            }
            mpTeamProject.CreationTime = DateTime.Now;
            mpTeamProject = _mainProjectDbContext.MPTeamProjects.Add(mpTeamProject).Entity;
            _mainProjectDbContext.SaveChanges();

            return mpTeamProject;
        }

        public MPTeamProject RemoveMPTeamProject(Guid mpTeamProjectId)
        {
            var tpToDel = _mainProjectDbContext.MPTeamProjects.FirstOrDefault(tp => tp.ID == mpTeamProjectId);
            if (null != tpToDel)
            {
                MPTeamProject tp = _mainProjectDbContext.MPTeamProjects.Remove(tpToDel).Entity;
                _mainProjectDbContext.SaveChanges();
                return tp;
            }
            return null;
        }

        public bool UpdateMPTeamProject(MPTeamProject mpTeamProject)
        {
            var select = _mainProjectDbContext.MPTeamProjects.AsNoTracking().Where(mp => mp.ID == mpTeamProject.ID).FirstOrDefault();
            if (select != null)
            {
                select.Name = mpTeamProject.Name;
                select.Description = mpTeamProject.Description;
                select.Leader = mpTeamProject.Leader;
                select.Avatar = mpTeamProject.Avatar;
                select.StartTime = mpTeamProject.StartTime;
                select.EndTime = mpTeamProject.EndTime;
                select.Progress = mpTeamProject.Progress;
                _mainProjectDbContext.MPTeamProjects.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.Name).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.Description).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.Leader).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.Avatar).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.StartTime).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.EndTime).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.Progress).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            return true;
        }

        public bool UpdateMPTeamProjectIdAndName(Guid oldprojectid, Guid newprojectid, string newprojectname)
        {
            var select = _mainProjectDbContext.MPTeamProjects.AsNoTracking().Where(mp => mp.ID == oldprojectid).FirstOrDefault();
            if (select != null)
            {
                select.ID = newprojectid;
                select.Name = newprojectname;
                _mainProjectDbContext.MPTeamProjects.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.ID).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.Name).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            return true;
        }

        public bool CheckProjectName(String pName)
        {
            var select = _mainProjectDbContext.MPTeamProjects.Where(tp => tp.Name.ToLower() == pName.ToLower());
            if (select.Any())
            {
                return false;
            }
            return true;
        }

        #endregion

        #region MPFileDirectory
        public IQueryable<MPFileDirectory> MPFileDirectories
        {
            get
            {
                return _mainProjectDbContext.MPFileDirectories.AsNoTracking();
            }
        }
        public MPFileDirectory AddMPFileDirectorey(MPFileDirectory filedirectory)
        {
            var select = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ID == filedirectory.ID);
            if (select.Any())
            {
                throw new Exception("服务器已存在相同的文件夹ID\n请尝试使用其它文件夹ID！");
            }
            select = _mainProjectDbContext.MPFileDirectories.Where(tp => tp.Name.ToLower() == filedirectory.Name.ToLower() && tp.ParentID.ToString().ToLower() == filedirectory.ParentID.ToString().ToLower());
            if (select.Any())
            {
                throw new Exception("该项目下服务器已存在同名文件夹\n请尝试修改文件夹名称");
            }
            //mainproject.CreationTime = DateTime.Now;

            //增加文件夹排序字段
            if (filedirectory.OrderNo == 0)
            {
                int maxOrder = 0;
                var maxFile = _mainProjectDbContext.MPFileDirectories.Where(tp => tp.ParentID == filedirectory.ParentID).ToList();
                if (maxFile.Any())
                {
                    maxOrder = maxFile.Max(f => f.OrderNo);
                    maxOrder = maxOrder + 1;
                }
                filedirectory.OrderNo = maxOrder;
            }
            filedirectory = _mainProjectDbContext.MPFileDirectories.Add(filedirectory).Entity;
            _mainProjectDbContext.SaveChanges();
            return filedirectory;
        }
        public bool AddMPFileDirectoreyList(List<MPFileDirectory> fileDicList)
        {
            _mainProjectDbContext.MPFileDirectories.AddRange(fileDicList);
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool RemoveMPFileDirectorey(Guid fileId)
        {
            var toDel = _mainProjectDbContext.MPFileDirectories.FirstOrDefault(f => f.ID == fileId);
            if (null != toDel)
            {
                _mainProjectDbContext.MPFileDirectories.Remove(toDel);
            }
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool UpdateMPFileDirectoryOrderNo(Guid dirGuid, string upOrDown)
        {
            if (upOrDown == "up")
            {
                var select = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ID == dirGuid).FirstOrDefault();
                var perSelect = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ParentID == select.ParentID && mp.OrderNo < select.OrderNo).OrderByDescending(mp => mp.OrderNo).FirstOrDefault();
                if (perSelect != null)
                {
                    var selectorderno = select.OrderNo;
                    var perorderno = perSelect.OrderNo;
                    select.OrderNo = perorderno;
                    perSelect.OrderNo = selectorderno;
                    _mainProjectDbContext.MPFileDirectories.Attach(select);
                    _mainProjectDbContext.Entry(select).Property(a => a.OrderNo).IsModified = true;
                    //_mainProjectDbContext.SaveChanges();
                    _mainProjectDbContext.MPFileDirectories.Attach(perSelect);
                    _mainProjectDbContext.Entry(perSelect).Property(a => a.OrderNo).IsModified = true;
                    _mainProjectDbContext.SaveChanges();
                    return true;
                }

            }
            else
            {
                var select = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ID == dirGuid).FirstOrDefault();
                var nextSelect = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ParentID == select.ParentID && mp.OrderNo > select.OrderNo).OrderBy(mp => mp.OrderNo).FirstOrDefault();
                if (nextSelect != null)
                {
                    var selectorderno = select.OrderNo;
                    var nextorderno = nextSelect.OrderNo;
                    select.OrderNo = nextorderno;
                    nextSelect.OrderNo = selectorderno;
                    _mainProjectDbContext.MPFileDirectories.Attach(select);
                    _mainProjectDbContext.Entry(select).Property(a => a.OrderNo).IsModified = true;
                    //_mainProjectDbContext.SaveChanges();
                    _mainProjectDbContext.MPFileDirectories.Attach(nextSelect);
                    _mainProjectDbContext.Entry(nextSelect).Property(a => a.OrderNo).IsModified = true;
                    _mainProjectDbContext.SaveChanges();
                    return true;
                }
            }

            return true;
        }
        public bool UpdateMPFileDirectoryInfo(MPFileDirectory mpFileDirectory)
        {
            var select = _mainProjectDbContext.MPFileDirectories.Where(mp => mp.ID == mpFileDirectory.ID).FirstOrDefault();
            if (select != null)
            {
                select.Name = mpFileDirectory.Name;
                _mainProjectDbContext.MPFileDirectories.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.Name).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            return true;
        }

        public MPFileDirectory GetMPFileDirectory(Guid mpFileDirectoryId)
        {
            return _mainProjectDbContext.MPFileDirectories.FirstOrDefault(p => p.ID == mpFileDirectoryId);
        }

        public bool CheckMPFileDirectoryName(MPFileDirectory filedir)
        {
            var select = _mainProjectDbContext.MPFileDirectories.Where(tp => tp.Name.ToLower() == filedir.Name.ToLower() && tp.ParentID == filedir.ParentID);
            if (select.Any())
            {
                return false;
            }
            return true;
        }
        #endregion

        #region MPVolume
        public IQueryable<MPVolume> MPVolumes
        {
            get
            {
                return _mainProjectDbContext.MPVolumes.AsNoTracking();
            }
        }

        public MPVolume AddMPVolume(MPVolume mpVolume)
        {
            var select = _mainProjectDbContext.MPVolumes.Where(mp => mp.VolumeId == mpVolume.VolumeId);
            if (select.Any())
            {
                throw new Exception("服务器已存在相同的卷册ID\n请尝试使用其它卷册ID！");
            }
            select = _mainProjectDbContext.MPVolumes.Where(tp => tp.VolumeName.ToLower() == mpVolume.VolumeName.ToLower() && tp.FileDirectoryId == mpVolume.FileDirectoryId);
            if (select.Any())
            {
                throw new Exception("该项目下服务器已存在同名卷册\n请尝试修改卷册图档名称");
            }
            mpVolume = _mainProjectDbContext.MPVolumes.Add(mpVolume).Entity;
            _mainProjectDbContext.SaveChanges();
            return mpVolume;
        }

        public bool AddMPVolumeList(List<MPVolume> mpVolumeList)
        {
            _mainProjectDbContext.MPVolumes.AddRange(mpVolumeList);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        public bool RemoveMPVolume(Guid volumeId)
        {
            var select = _mainProjectDbContext.MPVolumes.Where(t => t.VolumeId == volumeId);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _mainProjectDbContext.MPVolumes.RemoveRange(select);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }

        #endregion

        #region MPVolumeVersion
        public IQueryable<MPVolumeVersion> MPVolumeVersions
        {
            get
            {
                return _mainProjectDbContext.MPVolumeVersions.AsNoTracking();
            }
        }

        public bool UpdateMPVolumeVersionFilePathInfo(MPVolumeVersion volVer)
        {
            var select = _mainProjectDbContext.MPVolumeVersions.Where(s => s.VolumeId == volVer.VolumeId && s.VerNo == volVer.VerNo).FirstOrDefault();
            if (select != null)
            {
                select.SavePath = volVer.SavePath;
                _mainProjectDbContext.MPVolumeVersions.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.SavePath).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            return true;
        }
        public bool UpdateMPDrawingPathWithNewProjectId(string mainpjId, string newpath, string oldprojectid, string newprojectid, string oldmainprjid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpdrawings set SubProjectId='" + newprojectid + "',FullPath = CONCAT('" + newpath + "',SUBSTRING_INDEX(FullPath, '" + oldmainprjid + "', -1)),FullPath=REPLACE(FullPath, '\\\\', \""+separator+"\") where LOCATE('" + oldmainprjid + "', FullPath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPDrawingPathWithNewProjectId===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPUserGroupMemberInfo(string mainpjId, string oldteammemberid, string newteammemberid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpusergroupmembers set TeamMemberId='" + newteammemberid + "' where TeamMemberId='" + oldteammemberid + "';");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPUserGroupMemberInfo===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPUserGroupAuthInfo(string mainpjId, string oldprojectid, string newprojectid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpusergroupauths set ObjectId='" + newprojectid + "' where ObjectId='" + oldprojectid + "';");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPUserGroupAuthInfo===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPReleaseConfigInfo(string mainpjId, string oldprojectid, string newprojectid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpreleaseconfigs set SourceSubProjectGuid='" + newprojectid + "' where SourceSubProjectGuid='" + oldprojectid + "';");
            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpreleaseconfigs set TargetSubProjectGuid='" + newprojectid + "' where TargetSubProjectGuid='" + oldprojectid + "';");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPReleaseConfigInfo===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPProjectTreeNodeSubprojectId(string mainpjId, string oldprojectid, string newprojectid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpprojecttreenodes set subProjectld='" + newprojectid + "' where subProjectld='" + oldprojectid + "'");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPProjectTreeNodeSubprojectId===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPLinkFilePathWithNewProjectId(string mainpjId, string newpath, string oldprojectid, string newprojectid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpcloudlinkfiles set ProjectId='" + newprojectid + "',"+
                "SavePath = CONCAT('" + newpath + "',SUBSTRING_INDEX(SavePath, '" + oldprojectid + "', -1)),SavePath=REPLACE(SavePath, '\\\\', \""+separator+"\") where LOCATE('" + oldprojectid + "', SavePath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPLinkFilePathWithNewProjectId===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPLinkFilePath(string mainpjId, string newpath)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpcloudlinkfiles set SavePath = CONCAT('" + newpath + "',SUBSTRING_INDEX(SavePath, 'LinkFile', -1)) where LOCATE('LinkFile', SavePath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPTreeNodeVersionInfo(string mainpjId, string oldlibGuid, string newlibGuid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mptreenodeversions set LibId='" + newlibGuid + "' where LibId='" + oldlibGuid + "'");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPTreeNodeVersionInfo===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPLibFilePath(string mainpjId, string newpath, string oldlibGuid, string newlibguid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mplibrarydatas set LibId = '" + newlibguid + "',ServerFilePath = CONCAT('" + newpath + "',SUBSTRING_INDEX(ServerFilePath, '" + oldlibGuid + "', -1)),ServerFilePath=REPLACE(ServerFilePath, '\\\\', \""+separator+"\") where LOCATE('" + oldlibGuid + "', ServerFilePath) > 0;");
            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mplibrarydatas set LibId = '" + newlibguid + "' where  LibId = '" + oldlibGuid + "';");
            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mplibrarydatahistories set LibId = '" + newlibguid + "' where  LibId = '" + oldlibGuid + "';");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPLibFilePath===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPLibFilePath(string mainpjId, string newpath)
        {

            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mplibrarydatas set ServerFilePath = CONCAT('" + newpath + "',SUBSTRING_INDEX(ServerFilePath, 'MPLibraryFile', -1)),ServerFilePath=REPLACE(ServerFilePath, '\\\\', \""+separator+"\") where LOCATE('MPLibraryFile', ServerFilePath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool AddMPVolumeVersion(MPVolumeVersion volVer)
        {
            if (_mainProjectDbContext.MPVolumeVersions.Any(s => s.VolumeId == volVer.VolumeId && s.VerNo == volVer.VerNo))
                return false;
            _mainProjectDbContext.MPVolumeVersions.Add(volVer);
            _mainProjectDbContext.SaveChanges();
            return true;
        }
        public bool AddMPVolumeVersionList(List<MPVolumeVersion> volVerList)
        {
            _mainProjectDbContext.MPVolumeVersions.AddRange(volVerList);
            _mainProjectDbContext.SaveChanges();
            return true;
        }

        #endregion


        #region MPProjectTreeNode
        public IQueryable<MPProjectTreeNode> MPProjectTreeNodes
        {
            get
            {
                return _mainProjectDbContext.MPProjectTreeNodes.AsNoTracking();
            }
        }
        public MPProjectTreeNode AddMPProjectTreeNode(MPProjectTreeNode mpProjectTreeNode)
        {
            var select = _mainProjectDbContext.MPProjectTreeNodes.Where(
                mp => mp.InstanceId == mpProjectTreeNode.InstanceId
                && mp.subProjectld == mpProjectTreeNode.subProjectld
                );
            if (select.Any())
            {
                throw new Exception("服务器已存在相同的节点ID\n请尝试使用其它节点ID！");
            }
            select = _mainProjectDbContext.MPProjectTreeNodes.Where(tp => tp.NodeName.ToLower() == mpProjectTreeNode.NodeName.ToLower() && tp.ParentNodeId == mpProjectTreeNode.ParentNodeId);
            if (select.Any())
            {
                throw new Exception("已存在同名节点\n请尝试修改节点名称");
            }
            mpProjectTreeNode = _mainProjectDbContext.MPProjectTreeNodes.Add(mpProjectTreeNode).Entity;
            _mainProjectDbContext.SaveChanges();
            return mpProjectTreeNode;
        }
        public bool RemoveMPProjectTreeNode(long instanceid)
        {
            var select = _mainProjectDbContext.MPProjectTreeNodes.Where(t => t.InstanceId == instanceid);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _mainProjectDbContext.MPProjectTreeNodes.RemoveRange(select);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }


        public bool WriteDataToLocalFile(long requestId, List<MPProjectTreeNode> addDatas, List<MPProjectTreeNode> modifyDatas,
                                    List<MPProjectTreeNode> deleteDatas)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveAddMPTreeNodeDatasToFile(addDatas, requestId);
                _bulkOperation.SaveAddMPTreeNodeDatasToFile(modifyDatas, requestId);
                _bulkOperation.SaveDeleteMPTreeNodeIdToFile(deleteDatas, requestId);
                
                return true;
            }

            return false;
        }

        public bool CheckInToDBFromLocalFile (long requestId)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveMPTreeNodeToDB(_mainProjectDbContext, requestId);

                return true;
            }

            return false;
        }

        public bool CheckInMPLibDataToDBFromLocalFile(long requestId)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveMPCataLogTreeNodeToDB(_mainProjectDbContext, requestId);
                _bulkOperation.SaveMPLibDataToDB(_mainProjectDbContext, requestId);
                _bulkOperation.SaveMPLibDataHistoryToDB(_mainProjectDbContext, requestId);

                return true;
            }

            return false;
        }
        #endregion

        #region MPLibraryInfo
        public IQueryable<MPLibraryInfo> MPLibraryInfos
        {
            get
            {
                return _mainProjectDbContext.MPLibraryInfoes.AsNoTracking();
            }
        }

        public bool RemoveMPLibraryInfo(Guid LibId)
        {
            var select = _mainProjectDbContext.MPLibraryInfoes.Where(t => t.LibId == LibId);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _mainProjectDbContext.MPLibraryInfoes.RemoveRange(select);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }
        public MPLibraryInfo AddMPLibraryInfo(MPLibraryInfo mpLibraryInfo)
        {
            var select = _mainProjectDbContext.MPLibraryInfoes.Where(mp => mp.LibId == mpLibraryInfo.LibId);
            if (select.Any())
            {
                throw new Exception("服务器已存在库ID\n请尝试使用其它库ID！");
            }
            select = _mainProjectDbContext.MPLibraryInfoes.Where(tp => tp.LibType == mpLibraryInfo.LibType);
            if (select.Any())
            {
                throw new Exception("相同类型的库只能有一个\n");
            }
            mpLibraryInfo.CreateTime = DateTime.Now;
            mpLibraryInfo = _mainProjectDbContext.MPLibraryInfoes.Add(mpLibraryInfo).Entity;
            _mainProjectDbContext.SaveChanges();
            return mpLibraryInfo;
        }

        public bool UpdateMPLibraryInfoGuid(Guid oldlibid, Guid newlibid)
        {
            var select = _mainProjectDbContext.MPLibraryInfoes.AsNoTracking().FirstOrDefault(l => l.LibId == oldlibid);
            if (select != null)
            {
                select.LibId = newlibid;
                _mainProjectDbContext.MPLibraryInfoes.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.LibId).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            else
            {
                return false;
            }
        }


        #endregion

        #region MPUserGroupLibAuth

        public IQueryable<MPUserGroupLibAuth> MPUserGroupLibAuths
        {
            get
            {
                return _mainProjectDbContext.MPUserGroupLibAuths.AsNoTracking();
            }
        }

        public bool SetMPUserGroupLibAuth(MPUserGroupLibAuth mpUserGroupLibAuth)
        {
            var select = _mainProjectDbContext.MPUserGroupLibAuths.Where(t => t.MPUserGroupId == mpUserGroupLibAuth.MPUserGroupId && t.LibType == mpUserGroupLibAuth.LibType).FirstOrDefault();
            if (select != null)
            {
                select.Permission = mpUserGroupLibAuth.Permission;
                select.AuthInfo = mpUserGroupLibAuth.AuthInfo;
                _mainProjectDbContext.MPUserGroupLibAuths.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.Permission).IsModified = true;
                _mainProjectDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
            else
            {
                _mainProjectDbContext.MPUserGroupLibAuths.Add(mpUserGroupLibAuth);
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }

        #endregion


        #region MPLibLock

        public IQueryable<MPLibLock> MPLibLocks
        {
            get
            {
                return _mainProjectDbContext.MPLibLocks.AsNoTracking();
            }
        }

        /// <summary>
        /// 批量锁定
        /// </summary>
        /// <param name="memberGuid"></param>
        /// <param name="locklibList"></param>
        /// <returns></returns>
        public bool LockMPLib(List<MPLibLock> locklibList)
        {
            var sw = new Stopwatch();
            sw.Start();
            if (locklibList == null || !locklibList.Any())
                return false;
            var dataIdSet = locklibList.GroupBy(x => x.LibId).Select(a => a.First()).ToList();

            var sb = new StringBuilder();
            sb.Append("REPLACE INTO `mpliblocks` (LockUserName,LibId,LockType) VALUES ");
            foreach (var v in dataIdSet)
            {
                sb.AppendFormat("({0},{1},{2}),", v.LockUserName, v.LibId, v.LockType);
            }
            var batchInsertStr = sb.ToString().TrimEnd(',');

            //提高批量插入性能
            _mainProjectDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {

                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(batchInsertStr);
                    dbContextTransaction.Commit();

                    sw.Stop();
                    //log.InfoFormat("LockMPLib==>插入{0}条数据，耗时{1}毫秒。", dataIdSet.Count, sw.ElapsedMilliseconds);
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    //log.Error("LockMPLib==>插入数据失败" + ex.Message);
                    throw ex;
                }

            }
        }
        /// <summary>
        /// 批量解锁
        /// </summary>
        /// <returns></returns>
        public bool UnlockMPLib(string memberName, List<Guid> unlockLibList)
        {
            var sw = new Stopwatch();
            sw.Start();
            //没有数据会造成后面的sql语句异常
            if (unlockLibList.Count <= 0) return true;
            HashSet<Guid> dataIdSet = new HashSet<Guid>(unlockLibList);
            //优化第一次创建联机项目，无需走下面的代码
            var isExist = _mainProjectDbContext.MPLibLocks.AsNoTracking().Any();
            if (!isExist) return true;

            var sb = new StringBuilder();
            var values = string.Join(",", dataIdSet);

            sb.Append("DELETE FROM `mpliblocks` WHERE LockUserName = {0} AND libid in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr, memberName);
                    dbContextTransaction.Commit();
                    sw.Stop();
                    //log.InfoFormat("UnlockMPLib==>删除{0}条数据，耗时{1}毫秒。", dataIdSet.Count, sw.ElapsedMilliseconds);
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    //log.Error("UnlockMPLib==>" + ex.Message);
                    throw ex;
                }
            }
        }
        #endregion


        #region MPCloudLinkFile

        public IQueryable<MPCloudLinkFile> MPLibCloudLinks
        {
            get
            {
                return _mainProjectDbContext.MPCloudLinkFiles.AsNoTracking();
            }
        }
        public bool AddMPCloudLinkFile(MPCloudLinkFile clf)
        {
            if (_mainProjectDbContext.MPCloudLinkFiles.Any(s => s.ProjectId == clf.ProjectId && s.VersionNo == clf.VersionNo))
                return false;
            clf.UploadTime = DateTime.Now;
            _mainProjectDbContext.MPCloudLinkFiles.Add(clf);
            _mainProjectDbContext.SaveChanges();
            return true;
        }


        #endregion

        #region MPReleaseConfig
        public IQueryable<MPReleaseConfig> MPLibReleaseConfigs
        {
            get
            {
                return _mainProjectDbContext.MPReleaseConfigs.AsNoTracking();
            }
        }
        public MPReleaseConfig AddMPReleaseConfig(MPReleaseConfig mpReleaseConfig)
        {
            var select = _mainProjectDbContext.MPReleaseConfigs.FirstOrDefault(
                r => r.SourceSubProjectGuid == mpReleaseConfig.SourceSubProjectGuid
                && r.TargetSubProjectGuid == mpReleaseConfig.TargetSubProjectGuid
                );
            if (select != null)
            {
                //更新
                select.TargetTreeNodeId = mpReleaseConfig.TargetTreeNodeId;
                _mainProjectDbContext.MPReleaseConfigs.Attach(select);
                _mainProjectDbContext.Entry(select).Property(a => a.TargetTreeNodeId).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return select;
            }
            else
            {
                mpReleaseConfig = _mainProjectDbContext.MPReleaseConfigs.Add(mpReleaseConfig).Entity;
                _mainProjectDbContext.SaveChanges();
                return mpReleaseConfig;
            }
        }

        public bool RemoveMPReleaseConfig(MPReleaseConfig mPReleaseConfig)
        {
            var select = _mainProjectDbContext.MPReleaseConfigs.FirstOrDefault(
                r => r.SourceSubProjectGuid == mPReleaseConfig.SourceSubProjectGuid
                && r.TargetSubProjectGuid == mPReleaseConfig.TargetSubProjectGuid
                );
            if (select != null)
            {
                _mainProjectDbContext.MPReleaseConfigs.Remove(select);
                _mainProjectDbContext.SaveChanges();
            }

            return true;
        }


        #endregion


        #region MPMessage
        public IQueryable<MPMessage> MPMessages
        {
            get
            {
                return _mainProjectDbContext.MPMessages.AsNoTracking();
            }
        }

        public bool UpdateMessageStateMul(List<int> messageIds)
        {
            if ((!messageIds.Any()) || messageIds.Count == 0)
            {
                return true;
            }
            var sb = new StringBuilder();
            var values = string.Join(",", messageIds);

            sb.Append("update mpmessages set msgstate=1 where MsgServerID in (");
            sb.Append(values);
            sb.Append(")");
            var updateStr = sb.ToString();
            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(updateStr);
                    dbContextTransaction.Commit();
                    ////log.Info("updateStr sql=>" + updateStr);
                    ////log.InfoFormat("UnlockDatasNew==>删除{0}条数据，耗时{1}毫秒。", dataIdSet.Count, sw.ElapsedMilliseconds);
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    //log.Info("UpdateMessageStateMul error" + ex.Message);
                }
            }
            return true;
        }

        public bool UpdateMessageState(MPMessage message)
        {
            var old = _mainProjectDbContext.MPMessages.FirstOrDefault(m => m.MsgServerID == message.MsgServerID);
            if (old == null)
            {
                return false;
            }
            else
            {
                old.MsgState = message.MsgState;
                _mainProjectDbContext.MPMessages.Attach(old);
                _mainProjectDbContext.Entry(old).Property(a => a.MsgState).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }
        public MPMessage AddMPMessage(MPMessage projectMessage)
        {
            if (null == projectMessage)
            {
                return null;
            }
            projectMessage.MsgCreateTime = DateTime.Now;
            projectMessage = _mainProjectDbContext.MPMessages.Add(projectMessage).Entity;
            _mainProjectDbContext.SaveChanges();

            return projectMessage;
        }
        public bool AddMPMessageList(List<MPMessage> projectMessage)
        {
            if (null == projectMessage)
            {
                return true;
            }
            _mainProjectDbContext.MPMessages.AddRange(projectMessage);
            _mainProjectDbContext.SaveChanges();

            return true;
        }
        #endregion

        #region MPDrawing

        public IQueryable<MPDrawing> MPDrawings
        {
            get
            {
                return _mainProjectDbContext.MPDrawings.AsNoTracking();
            }
        }

        public MPDrawing AddMPDrawing(MPDrawing drawing)
        {
            if (null == drawing)
            {
                return null;
            }
            drawing.CreateTime = DateTime.Now;
            drawing.UpdateTime = DateTime.Now;
            drawing = _mainProjectDbContext.MPDrawings.Add(drawing).Entity;
            _mainProjectDbContext.SaveChanges();

            return drawing;
        }

        public bool LockMPDrawing(Guid subProjectId, List<long> instanceid, string lockuser)
        {
            var sw = new Stopwatch();
            sw.Start();
            //没有数据会造成后面的sql语句异常
            if (instanceid.Count <= 0) return true;
            //优化第一次创建联机项目，无需走下面的代码

            var sb = new StringBuilder();
            var values = string.Join(",", instanceid);

            sb.Append("UPDATE `mpdrawings`  SET lockuser = '" + lockuser + "'  WHERE SubProjectId = '" + subProjectId.ToString().ToLower() + "' and InstanceId in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    sw.Stop();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UnlockMPDrawing(Guid subProjectGuid, List<long> instanceid, string lockuser)
        {
            var sw = new Stopwatch();
            sw.Start();
            //没有数据会造成后面的sql语句异常
            if (instanceid.Count <= 0) return true;
            //优化第一次创建联机项目，无需走下面的代码

            var sb = new StringBuilder();
            var values = string.Join(",", instanceid);

            sb.Append("UPDATE `mpdrawings`  SET lockuser = ''  WHERE SubProjectId = '" + subProjectGuid.ToString().ToLower() + "' and InstanceId in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    sw.Stop();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMPDrawing(Guid subprojectGuid, long instanceid, int versionNo, string fullpath, string fileMD5)
        {
            var old = _mainProjectDbContext.MPDrawings.FirstOrDefault(m => m.SubProjectId == subprojectGuid && m.InstanceId == instanceid);
            if (old == null)
            {
                return false;
            }
            else
            {
                old.VersionNo = versionNo;
                old.FullPath = fullpath;
                old.UpdateTime = DateTime.Now;
                old.FileMD5 = fileMD5;
                _mainProjectDbContext.MPDrawings.Attach(old);
                _mainProjectDbContext.Entry(old).Property(a => a.VersionNo).IsModified = true;
                _mainProjectDbContext.Entry(old).Property(a => a.FullPath).IsModified = true;
                _mainProjectDbContext.Entry(old).Property(a => a.UpdateTime).IsModified = true;
                _mainProjectDbContext.Entry(old).Property(a => a.FileMD5).IsModified = true;
                _mainProjectDbContext.SaveChanges();
                return true;
            }
        }

        #endregion

        #region 
        public IQueryable<MPTreeNodeVersion> MPTreeNodesVersions
        {
            get
            {
                return _mainProjectDbContext.MPTreeNodeVersions.AsNoTracking();
            }
        }
        public int SaveLibVersion(MPTreeNodeVersion versionData)
        {
            if (null == versionData)
                return -1;

            _mainProjectDbContext.MPTreeNodeVersions.Add(versionData);
            _mainProjectDbContext.SaveChanges();

            var addedversion =
                _mainProjectDbContext.ChangeTracker.Entries<MPTreeNodeVersion>()
                    .FirstOrDefault(entry => entry.State == EntityState.Unchanged);

            //if (addedversion != null) return addedversion.Entity.VersionNo;
            return versionData.VersionNo;
        }

        public IQueryable<MPCatalogTreeNode> MPCatalogTreeNodes
        {
            get
            {
                return _mainProjectDbContext.MPCatalogTreeNodes.AsNoTracking();
            }
        }

        public bool UpdateMPCataLogTreeNodeLibGuid(string mainpjId, string oldLibGuid, string newLibGuid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-mpdb-" + mainpjId + "`.mpcatalogtreenodes set LibId = '" + newLibGuid + "' where LibId = '" + oldLibGuid + "'");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _mainProjectDbContext.Database.BeginTransaction())
            {
                try
                {
                    _mainProjectDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine("UpdateMPCataLogTreeNodeLibGuid===============>" + ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool CheckInCLTreeNode(List<MPCatalogTreeNode> addDatas,
            List<MPCatalogTreeNode> modifyDatas,
            List<MPCatalogTreeNode> delDatas, Guid libGuid, int version)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveTreeNodes(_mainProjectDbContext, addDatas, version);
                _bulkOperation.ModifyTreeNodes(_mainProjectDbContext, modifyDatas, version);
                _bulkOperation.DeleteTreeNodes(_mainProjectDbContext, delDatas, libGuid);
                //bulkOperation.SaveHistoryTreeNodes(_mainProjectDbContext, addDatas, modifyDatas, delDatas, version);


                return true;
            }

            return false;
        }

        public IQueryable<MPLibraryData> MPLibraryDatas
        {
            get
            {
                return _mainProjectDbContext.MPLibraryDatas.AsNoTracking();
            }
        }
        public bool CheckInCLLibData(List<MPLibraryData> addDatas,
            List<MPLibraryData> modifyDatas,
            List<MPLibraryData> delDatas,
            Guid libGuid, int version)
        {
            if (_bulkOperation != null)
            {
                _bulkOperation.SaveLibDatas(_mainProjectDbContext, addDatas, version);
                _bulkOperation.ModifyLibDatas(_mainProjectDbContext, modifyDatas, version);
                _bulkOperation.DeleteLibDatas(_mainProjectDbContext, delDatas, libGuid);
                _bulkOperation.SaveHistoryLibData(_mainProjectDbContext, addDatas, modifyDatas, delDatas, version);

                return true;
            }

            return false;
        }
        public IQueryable<MPLibraryDataHistory> MPLibraryDataHistories
        {
            get
            {
                return _mainProjectDbContext.MPLibraryDataHistories.AsNoTracking();
            }
        }
        public bool UpdateLibDataFileServerPath(long dataId, string serverPath, string fileMD5)
        {
            var old = _mainProjectDbContext.MPLibraryDatas.FirstOrDefault(e => e.DataId == dataId);
            if (old != null)
            {
                old.ServerFilePath = serverPath;
                if (!string.IsNullOrEmpty(fileMD5))
                {
                    old.FileMD5 = fileMD5;
                }
                _mainProjectDbContext.MPLibraryDatas.Attach(old);
                _mainProjectDbContext.Entry(old).Property(a => a.ServerFilePath).IsModified = true;
                if (!string.IsNullOrEmpty(fileMD5))
                {
                    _mainProjectDbContext.Entry(old).Property(a => a.FileMD5).IsModified = true;
                }

                _mainProjectDbContext.SaveChanges();
                return true;
            }
            return false;
        }
        #endregion


        public bool Destroy()
        {
            return _mainProjectDbContext.Database.EnsureDeleted();
        }





        public bool Insert<T>(T t) where T : class
        {
            _mainProjectDbContext.Set<T>().Add(t);
            return _mainProjectDbContext.SaveChanges() > 0;
        }

        public bool Insert<T>(IEnumerable<T> tList) where T : class
        {
            _mainProjectDbContext.Set<T>().AddRange(tList);
            return _mainProjectDbContext.SaveChanges() > 0;
        }

        public void Update<T>(T t) where T : class
        {
            _mainProjectDbContext.Set<T>().Update(t);
            _mainProjectDbContext.SaveChanges();
        }

        public void Update<T>(IEnumerable<T> tList) where T : class
        {
            _mainProjectDbContext.Set<T>().UpdateRange(tList);
            _mainProjectDbContext.SaveChanges();
        }

        public void Delete<T>(T t) where T : class
        {
            _mainProjectDbContext.Set<T>().Remove(t);
            _mainProjectDbContext.SaveChanges();
        }

        public void Delete<T>(IEnumerable<T> tList) where T : class
        {
            _mainProjectDbContext.Set<T>().RemoveRange(tList);
            _mainProjectDbContext.SaveChanges();
        }

        public IQueryable<TResult> QueryFrom<TSource, TResult>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TResult>> projection) where TSource : class
        {
            var x = _mainProjectDbContext.Set<TSource>().Where(predicate).Select(projection);
            return x;
        }

        public IQueryable<TSource> QueryFrom<TSource>(Expression<Func<TSource, bool>> predicate = null) where TSource : class
        {
            if (predicate is null) return _mainProjectDbContext.Set<TSource>();
            var x = _mainProjectDbContext.Set<TSource>().Where(predicate);
            return x;
        }
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    (_mainProjectDbContext as IDisposable)?.Dispose();
                }

                disposedValue = true;
            }
        }
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        public bool WriteDataToLocalFile(long requestId, 
            List<MPCatalogTreeNode> addtreeDatas, 
            List<MPCatalogTreeNode> modifytreeDatas, 
            List<MPCatalogTreeNode> deltreeDatas, 
            List<MPLibraryData> addLibDatas, 
            List<MPLibraryData> modifyLibDatas, 
            List<MPLibraryData> delLibDatas, 
            Guid libGuid, 
            int verNo)
        {
            if (_bulkOperation != null)
            {
                int versionNo = verNo;//CurrentVersionNo;
                if (versionNo > -1)
                {
                    _bulkOperation.SaveAddCataLogTreeNodeToFile(addtreeDatas, requestId,verNo);
                    _bulkOperation.SaveModifyCataLogTreeNodeToFile(_mainProjectDbContext , modifytreeDatas, requestId,versionNo);
                    _bulkOperation.SaveDeleteCataLogTreeNodeIdToFile(deltreeDatas, requestId);
                    

                    _bulkOperation.SaveAddMPLibDataToFile(addLibDatas, requestId, versionNo);
                    _bulkOperation.SaveModifyMPLibDataToFile(_mainProjectDbContext, modifyLibDatas, requestId, versionNo);
                    _bulkOperation.SaveDeleteMPLibDataIdToFile(delLibDatas, requestId);
                    _bulkOperation.SaveMPLibDataHistoryToFile(_mainProjectDbContext, addLibDatas, modifyLibDatas, delLibDatas, versionNo, requestId);
                    return true;
                }
            }

            return false;
        }
    }
}
