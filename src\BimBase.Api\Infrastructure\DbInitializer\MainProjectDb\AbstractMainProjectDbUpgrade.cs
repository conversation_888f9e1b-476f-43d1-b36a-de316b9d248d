using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using BimBase.Api.Infrastructure.MainDomain;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb数据库升级抽象基类
    /// 继承通用数据库升级基类，提供MainProjectDb特定的升级功能
    /// </summary>
    public abstract class AbstractMainProjectDbUpgrade : AbstractDatabaseUpgrade<MainProjectDbContext>, IMainProjectDbUpgrade
    {
        protected AbstractMainProjectDbUpgrade(ILogger logger) : base(logger)
        {
        }
        
        /// <summary>
        /// 源版本
        /// </summary>
        public override abstract string FromVersion { get; }
        
        /// <summary>
        /// 目标版本
        /// </summary>
        public override abstract string ToVersion { get; }
        
        /// <summary>
        /// 升级描述
        /// </summary>
        public override abstract string Description { get; }
        
        /// <summary>
        /// 是否支持回滚（默认不支持）
        /// </summary>
        public override bool SupportsRollback => false;
        
        /// <summary>
        /// 执行升级
        /// </summary>
        public async Task UpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行MainProjectDb升级: {FromVersion} -> {ToVersion}");
            Logger.LogInformation($"升级描述: {Description}");
            
            try
            {
                await ExecuteUpgradeAsync(context);
                await UpdateVersionRecordAsync(context);
                
                Logger.LogInformation($"MainProjectDb升级完成: {FromVersion} -> {ToVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"MainProjectDb升级失败: {FromVersion} -> {ToVersion}");
                throw;
            }
        }
        
        /// <summary>
        /// 回滚升级
        /// </summary>
        public async Task RollbackAsync(MainProjectDbContext context)
        {
            if (!SupportsRollback)
            {
                throw new NotSupportedException($"升级 {FromVersion} -> {ToVersion} 不支持回滚");
            }
            
            Logger.LogInformation($"开始回滚MainProjectDb升级: {ToVersion} -> {FromVersion}");
            
            try
            {
                await ExecuteRollbackAsync(context);
                await UpdateVersionRecordForRollbackAsync(context);
                Logger.LogInformation($"MainProjectDb回滚完成: {ToVersion} -> {FromVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"MainProjectDb回滚失败: {ToVersion} -> {FromVersion}");
                throw;
            }
        }
        
        /// <summary>
        /// 执行具体的升级逻辑（子类实现）
        /// </summary>
        protected abstract Task ExecuteUpgradeAsync(MainProjectDbContext context);
        
        /// <summary>
        /// 执行具体的回滚逻辑（子类可选实现）
        /// </summary>
        protected virtual Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            throw new NotSupportedException("此升级不支持回滚");
        }
        
        /// <summary>
        /// 更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordAsync(MainProjectDbContext context)
        {
            try
            {
                // 检查版本记录是否已存在于数据库中
                var existingRecord = await context.DatabaseVersions
                    .AsNoTracking() // 使用AsNoTracking避免跟踪冲突
                    .FirstOrDefaultAsync(v => v.Version == ToVersion);
                
                if (existingRecord == null)
                {
                    // 检查是否已经在当前上下文中跟踪了相同版本的实体
                    var trackedEntity = context.ChangeTracker.Entries<MPDatabaseVersion>()
                        .FirstOrDefault(e => e.Entity.Version == ToVersion);
                    
                    if (trackedEntity == null)
                    {
                        var versionRecord = new BimBase.Api.Infrastructure.MainDomain.MPDatabaseVersion
                        {
                            Version = ToVersion,
                            UpgradeTime = DateTime.Now,
                            Description = Description,
                            UpgradedBy = "System"
                        };
                        
                        context.DatabaseVersions.Add(versionRecord);
                        Logger.LogInformation($"版本记录已添加到上下文: {ToVersion}");
                    }
                    else
                    {
                        Logger.LogInformation($"版本 {ToVersion} 已在上下文中跟踪，跳过添加");
                    }
                }
                else
                {
                    Logger.LogInformation($"版本 {ToVersion} 已存在于数据库中，跳过添加");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"更新版本记录失败: {ToVersion}");
                throw;
            }
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }
        
        /// <summary>
        /// 回滚时更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordForRollbackAsync(MainProjectDbContext context)
        {
            // 删除当前版本记录
            var currentVersionRecord = await context.DatabaseVersions
                .Where(v => v.Version == ToVersion)
                .FirstOrDefaultAsync();
                
            if (currentVersionRecord != null)
            {
                context.DatabaseVersions.Remove(currentVersionRecord);
                Logger.LogInformation($"版本记录已从上下文中删除: {ToVersion}");
            }
            else
            {
                Logger.LogInformation($"未找到版本记录: {ToVersion}，跳过删除");
            }
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }
        
        #region MainProjectDb特定辅助方法

        /// <summary>
        /// 检查列是否存在
        /// </summary>
        protected async Task<bool> ColumnExistsAsync(MainProjectDbContext context, string tableName, string columnName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND column_name = '{columnName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查索引是否存在
        /// </summary>
        protected async Task<bool> IndexExistsAsync(MainProjectDbContext context, string tableName, string indexName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.statistics 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND index_name = '{indexName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查表是否存在
        /// </summary>
        protected async Task<bool> TableExistsAsync(MainProjectDbContext context, string tableName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        /// <summary>
        /// 检查触发器是否存在
        /// </summary>
        protected async Task<bool> TriggerExistsAsync(MainProjectDbContext context, string triggerName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.triggers 
                WHERE trigger_schema = '{databaseName}' 
                  AND trigger_name = '{triggerName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        /// <summary>
        /// 安全添加列（如果不存在）
        /// </summary>
        protected async Task AddColumnIfNotExistsAsync(MainProjectDbContext context, string tableName, string columnName, string columnDefinition)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                var sql = $"ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnDefinition}";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已添加列: {tableName}.{columnName}");
            }
            else
            {
                Logger.LogInformation($"列已存在，跳过: {tableName}.{columnName}");
            }
        }

        /// <summary>
        /// 安全删除列（如果存在）
        /// </summary>
        protected async Task DropColumnIfExistsAsync(MainProjectDbContext context, string tableName, string columnName)
        {
            if (await ColumnExistsAsync(context, tableName, columnName))
            {
                var sql = $"ALTER TABLE `{tableName}` DROP COLUMN `{columnName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已删除列: {tableName}.{columnName}");
            }
            else
            {
                Logger.LogInformation($"列不存在，跳过删除: {tableName}.{columnName}");
            }
        }

        /// <summary>
        /// 安全修改列（如果存在）
        /// </summary>
        protected async Task ModifyColumnIfExistsAsync(MainProjectDbContext context, string tableName, string columnName, string newColumnDefinition)
        {
            if (await ColumnExistsAsync(context, tableName, columnName))
            {
                var sql = $"ALTER TABLE `{tableName}` MODIFY COLUMN `{columnName}` {newColumnDefinition}";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已修改列: {tableName}.{columnName}");
            }
            else
            {
                Logger.LogInformation($"列不存在，跳过修改: {tableName}.{columnName}");
            }
        }

        /// <summary>
        /// 安全创建索引（如果不存在）
        /// </summary>
        protected async Task CreateIndexIfNotExistsAsync(MainProjectDbContext context, string tableName, string indexName, string columnDefinition)
        {
            if (!await IndexExistsAsync(context, tableName, indexName))
            {
                var sql = $"CREATE INDEX `{indexName}` ON `{tableName}` ({columnDefinition})";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已创建索引: {tableName}.{indexName}");
            }
            else
            {
                Logger.LogInformation($"索引已存在，跳过创建: {tableName}.{indexName}");
            }
        }

        /// <summary>
        /// 安全删除索引（如果存在）
        /// </summary>
        protected async Task DropIndexIfExistsAsync(MainProjectDbContext context, string tableName, string indexName)
        {
            if (await IndexExistsAsync(context, tableName, indexName))
            {
                var sql = $"DROP INDEX `{indexName}` ON `{tableName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已删除索引: {tableName}.{indexName}");
            }
            else
            {
                Logger.LogInformation($"索引不存在，跳过删除: {tableName}.{indexName}");
            }
        }

        /// <summary>
        /// 安全创建表（如果不存在）
        /// </summary>
        protected async Task CreateTableIfNotExistsAsync(MainProjectDbContext context, string tableName, string tableDefinition)
        {
            if (!await TableExistsAsync(context, tableName))
            {
                var sql = $"CREATE TABLE `{tableName}` ({tableDefinition}) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已创建表: {tableName}");
            }
            else
            {
                Logger.LogInformation($"表已存在，跳过创建: {tableName}");
            }
        }

        /// <summary>
        /// 安全删除表（如果存在）
        /// </summary>
        protected async Task DropTableIfExistsAsync(MainProjectDbContext context, string tableName)
        {
            if (await TableExistsAsync(context, tableName))
            {
                var sql = $"DROP TABLE `{tableName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已删除表: {tableName}");
            }
            else
            {
                Logger.LogInformation($"表不存在，跳过删除: {tableName}");
            }
        }

        /// <summary>
        /// 创建或替换触发器
        /// </summary>
        protected async Task CreateOrReplaceTriggerAsync(MainProjectDbContext context, string triggerName, string triggerDefinition)
        {
            MySqlConnector.MySqlConnection connection = null;
            try
            {
                // 构建新的连接字符串，确保完全脱离连接池和事务状态
                var connectionString = context.Database.GetConnectionString();
                
                // 添加连接池相关参数，确保获取新的干净连接
                var connectionStringBuilder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString)
                {
                    Pooling = false, // 禁用连接池，确保获取全新连接
                    AutoEnlist = false // 禁用自动加入分布式事务
                };
                
                connection = new MySqlConnector.MySqlConnection(connectionStringBuilder.ConnectionString);
                await connection.OpenAsync();
                
                Logger.LogInformation($"升级过程中触发器连接状态 - 数据库: {connection.Database}, 状态: {connection.State}");

                // 先删除已存在的触发器
                using var dropCommand = connection.CreateCommand();
                dropCommand.CommandText = $"DROP TRIGGER IF EXISTS `{triggerName}`;";
                await dropCommand.ExecuteNonQueryAsync();
                Logger.LogInformation($"已删除旧触发器: {triggerName}");
                
                // 创建新触发器
                using var createCommand = connection.CreateCommand();
                createCommand.CommandText = triggerDefinition;
                await createCommand.ExecuteNonQueryAsync();
                Logger.LogInformation($"已创建触发器: {triggerName}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"创建触发器 {triggerName} 时发生错误");
                throw;
            }
            finally
            {
                if (connection != null)
                {
                    try
                    {
                        if (connection.State == System.Data.ConnectionState.Open)
                        {
                            await connection.CloseAsync();
                        }
                        await connection.DisposeAsync();
                        Logger.LogInformation($"升级过程中触发器连接已安全关闭");
                    }
                    catch (Exception ex)
                    {
                        Logger.LogWarning(ex, "关闭升级过程中触发器连接时发生错误");
                    }
                }
            }
        }

        /// <summary>
        /// 安全删除触发器（如果存在）
        /// </summary>
        protected async Task DropTriggerIfExistsAsync(MainProjectDbContext context, string triggerName)
        {
            if (await TriggerExistsAsync(context, triggerName))
            {
                var sql = $"DROP TRIGGER IF EXISTS `{triggerName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已删除触发器: {triggerName}");
            }
            else
            {
                Logger.LogInformation($"触发器不存在，跳过删除: {triggerName}");
            }
        }



        #endregion

        #region IMainProjectDbUpgrade 接口实现

        async Task IMainProjectDbUpgrade.ExecuteAsync()
        {
            // 这个方法会在升级管理器中被调用，但实际逻辑在UpgradeAsync中
            throw new NotImplementedException("请使用 UpgradeAsync(MainProjectDbContext context) 方法");
        }

        async Task IMainProjectDbUpgrade.RollbackAsync()
        {
            // 这个方法会在升级管理器中被调用，但实际逻辑在RollbackAsync中
            throw new NotImplementedException("请使用 RollbackAsync(MainProjectDbContext context) 方法");
        }

        string IMainProjectDbUpgrade.GetDescription()
        {
            return Description;
        }

        async Task<bool> IMainProjectDbUpgrade.ValidateAsync()
        {
            // 默认验证总是通过，子类可以重写
            return await Task.FromResult(true);
        }

        #endregion
    }
} 