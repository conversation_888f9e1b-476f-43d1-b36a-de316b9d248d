using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.LibDomain
{
    /// <summary>
    /// LibraryDb 版本管理表实体
    /// </summary>
    [Table("DatabaseVersion")]
    public class DatabaseVersion
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Version { get; set; }

        public DateTime UpgradedAt { get; set; }

        [StringLength(500)]
        public string Description { get; set; }
    }
} 