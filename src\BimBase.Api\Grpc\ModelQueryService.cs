﻿using AutoMapper;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using BimBase.Api.Protos.model;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pipelines.Sockets.Unofficial.Arenas;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using static BimBase.Api.Protos.model.GrpcModelQuery;

namespace BimBase.Api.Grpc
{
    public class TempClass
    {
        public long InstanceId;
        public OperationRecordType OperType;
        public int VersionNo;
    }
    public class RelationIdInstanceIDOperType
    {
        public long Id;
        public long InstanceId;
        public OperationRecordType OperType;
        public long VersionNo;
    }
    public class ModelQueryService : GrpcModelQueryBase
    {

        private readonly ITeamRepository _teamRepository;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        private readonly ILogger<ModelQueryService> _logger;


        private static object _locker = new object();
        private const string SCHEMAVERSION_2020630 = "1.0.17";
        private readonly string[] SCHEMAVERS = { "1.0.17", "1.0.18" };
        //private Infrastructure.TaskContrller _taskContrl;
        private const int limitLength = 500 * 1024 * 1024;
        private const int limitcount = 10000;//int.MaxValue;
        private const int limitretuncount = int.MaxValue;
        public ModelQueryService(ITeamRepository teamRepository
            , IMapper mapper
            , IAuthorityManager authorityManager
            , ILogger<ModelQueryService> logger)
        {
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _authorityManager = authorityManager ?? throw new ArgumentNullException(nameof(authorityManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            //_taskContrl = Infrastructure.TaskContrller.GetInstance;
        }


        public override async Task<DownloadModeldataByInstanceIDsResponse> DownloadModeldataByInstanceIDs(DownloadModeldataByInstanceIDsRequest request, ServerCallContext context)
        {
            var x = new DownloadModeldataByInstanceIDsResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            foreach (var k in request.DicInstanceIDs.Keys)
            {
                int vNO;
                if (request.DicInstanceIDs.TryGetValue(k, out vNO))
                {
                    var historydata = projectRepo.QueryFrom<Infrastructure.ModelDomain.HistoryData>(d => d.InstanceId == k && d.VersionNo <= vNO).OrderByDescending(c => c.VersionNo).FirstOrDefault();
                    var lockData = projectRepo.LockedComponents.Where(l => l.InstanceId == k).FirstOrDefault();
                    if (historydata != null)
                    {
                        if (lockData != null)
                        {
                            var modeldata = new GrpcModelData
                            {
                                InstanceId = historydata.InstanceId,
                                DomainClassName = historydata.DomainClassName,
                                ECSchemaName = historydata.ECSchemaName,
                                StoreyID = historydata.StoreyID,
                                Domain = historydata.Domain,
                                Data = Google.Protobuf.ByteString.CopyFrom(historydata.Data),
                                VersionNo = historydata.VersionNo,
                                StoreyGuid = historydata.StoreyGuid.ToString(),
                                LockUserID = lockData.LockUserId
                            };
                            historydata.Data = null;
                            x.IsSuccess = true;
                            x.ModelList.Add(modeldata);
                        }
                        else
                        {
                            var modeldata = new GrpcModelData
                            {
                                InstanceId = historydata.InstanceId,
                                DomainClassName = historydata.DomainClassName,
                                ECSchemaName = historydata.ECSchemaName,
                                StoreyID = historydata.StoreyID,
                                Domain = historydata.Domain,
                                Data = Google.Protobuf.ByteString.CopyFrom(historydata.Data),
                                VersionNo = historydata.VersionNo,
                                StoreyGuid = historydata.StoreyGuid.ToString()
                            };
                            historydata.Data = null;
                            x.IsSuccess = true;
                            x.ModelList.Add(modeldata);
                        }

                    }
                    else
                    {
                        return x;
                    }

                }
                else
                {
                    return x;
                }

            }

            return x;
        }

        private bool SecondDownloadDataFirstFromModeldata(Guid projectId, List<long> instanceIDs, int limitcount, int limitLength, int limitretuncount, int toVersion,
            out List<long> modelIds, out List<HistoryData> outModelDatas)
        {

            //PbimLog PbimLog = new PbimLog("Info");
            modelIds = new List<long>();
            outModelDatas = new List<HistoryData>();
            List<ModelData> listDatas = new List<ModelData>();
            try
            {
                var _modelManager = _teamRepository.GetProjectRepository(projectId);
                //分段下载第一次下载
                //PbimLog.Info("SecondDownloadDataFirstFromModeldata:===>add 第一次下载");
                //int dataLength = 0;
                int addcount = instanceIDs.Count;
                //int minCount = Math.Min(addGeometries.Count, limitcount);
                int n = addcount / limitcount;
                int m = addcount % limitcount;

                if (n > 0)
                {
                    for (int i = 0; i < n; i++)
                    {
                        List<long> add = instanceIDs.GetRange(i * limitcount, limitcount);
                        var dicAdd = _modelManager.Datas.Where(s => add.Contains(s.InstanceId) && s.VersionNo <= toVersion).Select(s => s.Id).ToList();
                        //dicAddGeo = dicAddGeo.Concat(dicAdd).ToDictionary(k => k.Key, k => k.Value);
                        modelIds.AddRange(dicAdd);
                    }
                }
                if (m > 0)
                {
                    List<long> add = instanceIDs.GetRange(n * limitcount, m);
                    var dicAdd = _modelManager.AllHistoryDatas.Where(s => add.Contains(s.InstanceId) && s.VersionNo <= toVersion).Select(s => s.Id).ToList();
                    modelIds.AddRange(dicAdd);
                }

                //开始分段
                int minAddCount = Math.Min(modelIds.Count, limitretuncount);
                int splitCount = 0;
                if (minAddCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int addGeoCount = minAddCount / j;
                        var addIds = modelIds.Take(addGeoCount).ToList();
                        var dataLength = _modelManager.Datas.Where(s => (addIds.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            splitCount = addGeoCount;
                            listDatas = dataLength;
                            go = false;
                        }
                    }
                }

                foreach (var item in listDatas)
                {
                    if (item != null)
                    {
                        var history = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                        outModelDatas.Add(history);
                    }

                }
                modelIds.RemoveRange(0, splitCount);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }
        private bool SecondDownloadDatasFirst(Guid projectId, List<long> instanceIDs, int limitcount, int limitLength, int limitretuncount, int toVersion,
            out List<long> modelIds, out List<HistoryData> outModelDatas)
        {

            //PbimLog PbimLog = new PbimLog("Info");
            modelIds = new List<long>();
            outModelDatas = new List<HistoryData>();
            try
            {
                var _modelManager = _teamRepository.GetProjectRepository(projectId);
                //分段下载第一次下载
                //PbimLog.Info("DownloadGeometryDataByVersion:===>add 第一次下载");
                Dictionary<long, List<RelationIdInstanceIDOperType>> dicAddGeo = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                //int dataLength = 0;
                int addcount = instanceIDs.Count;
                //int minCount = Math.Min(addGeometries.Count, limitcount);
                int n = addcount / limitcount;
                int m = addcount % limitcount;

                if (n > 0)
                {
                    for (int i = 0; i < n; i++)
                    {
                        List<long> add = instanceIDs.GetRange(i * limitcount, limitcount);
                        var dicAdd = _modelManager.AllHistoryDatas.Where(s => add.Contains(s.InstanceId) && s.VersionNo <= toVersion)
                            .Select(s => new RelationIdInstanceIDOperType { Id = s.Id, InstanceId = s.InstanceId, OperType = s.OperationRecordType, VersionNo = s.VersionNo })
                            .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                        dicAddGeo = dicAddGeo.Concat(dicAdd).ToDictionary(k => k.Key, k => k.Value);
                    }
                }
                if (m > 0)
                {
                    List<long> add = instanceIDs.GetRange(n * limitcount, m);
                    var dicAdd = _modelManager.AllHistoryDatas.Where(s => add.Contains(s.InstanceId) && s.VersionNo <= toVersion)
                            .Select(s => new RelationIdInstanceIDOperType { Id = s.Id, InstanceId = s.InstanceId, OperType = s.OperationRecordType, VersionNo = s.VersionNo })
                            .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                    dicAddGeo = dicAddGeo.Concat(dicAdd).ToDictionary(k => k.Key, k => k.Value);
                }
                foreach (var k in dicAddGeo.Keys)
                {
                    List<RelationIdInstanceIDOperType> listValue = dicAddGeo[k];
                    var maxAddGeo = listValue.OrderByDescending(s => s.VersionNo).FirstOrDefault();
                    if (maxAddGeo != null)
                    {
                        modelIds.Add(maxAddGeo.Id);
                    }
                }
                //开始分段
                int minAddCount = Math.Min(modelIds.Count, limitretuncount);
                int splitCount = 0;
                List<HistoryData> listHistory = new List<HistoryData>();
                if (minAddCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int addGeoCount = minAddCount / j;
                        var addIds = modelIds.Take(addGeoCount).ToList();
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (addIds.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            splitCount = addGeoCount;
                            listHistory = dataLength;
                            go = false;
                        }
                    }
                }
                outModelDatas = listHistory;
                modelIds.RemoveRange(0, splitCount);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        private bool SecondDownloadDatasFirstByDataIdAndVersion(Guid projectId, Dictionary<long, int> dicDataIdVersion, int limitcount, int limitLength, int limitretuncount,
            out List<long> modelIds, out List<GrpcOperationRecordType> modelOpertypes, out List<GrpcHistoryData> outModelDatas, out string message)
        {

            //PbimLog PbimLog = new PbimLog("Info");
            message = "";
            modelIds = new List<long>();
            modelOpertypes = new List<GrpcOperationRecordType>();
            outModelDatas = new List<GrpcHistoryData>();
            try
            {
                var _modelManager = _teamRepository.GetProjectRepository(projectId);
                //分段下载第一次下载
                //PbimLog.Info("DownloadGeometryDataByVersion:===>add 第一次下载");
                Dictionary<long, List<RelationIdInstanceIDOperType>> dicAddGeo = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                //int dataLength = 0;
                int addcount = dicDataIdVersion.Count;
                var instanceIDs = dicDataIdVersion.Keys.ToList();
                //int minCount = Math.Min(addGeometries.Count, limitcount);
                int n = addcount / limitcount;
                int m = addcount % limitcount;
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion dicDataIdVersion:{addcount}个");
                if (n > 0)
                {
                    for (int i = 0; i < n; i++)
                    {
                        List<long> add = instanceIDs.GetRange(i * limitcount, limitcount);
                        HashSet<long> hashIds = new HashSet<long>(add);
                        var dicAdd = _modelManager.AllHistoryDatas
                            .Where(s => hashIds.Contains(s.InstanceId))
                            .AsEnumerable()
                            .Where(s => s.VersionNo >= dicDataIdVersion[s.InstanceId])
                            .Select(s => new RelationIdInstanceIDOperType { Id = s.Id, InstanceId = s.InstanceId, OperType = s.OperationRecordType, VersionNo = s.VersionNo })
                            .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                        dicAddGeo = dicAddGeo.Concat(dicAdd).ToDictionary(k => k.Key, k => k.Value);
                    }
                }
                if (m > 0)
                {
                    List<long> add = instanceIDs.GetRange(n * limitcount, m);
                    HashSet<long> hashIds = new HashSet<long>(add);
                    var dicAdd = _modelManager.AllHistoryDatas
                        .Where(s => hashIds.Contains(s.InstanceId))
                        .AsEnumerable()
                        .Where(s => s.VersionNo >= dicDataIdVersion[s.InstanceId])
                        .Select(s => new RelationIdInstanceIDOperType { Id = s.Id, InstanceId = s.InstanceId, OperType = s.OperationRecordType, VersionNo = s.VersionNo })
                        .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                    dicAddGeo = dicAddGeo.Concat(dicAdd).ToDictionary(k => k.Key, k => k.Value);
                }
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion dicAddGeo:{dicAddGeo.Count}个");
                List<long> hdIdsList = new List<long>();
                List<OperationRecordType> hdOperTypeList = new List<OperationRecordType>();
                foreach (var l in dicAddGeo)
                {
                    List<RelationIdInstanceIDOperType> hdvalueList = l.Value;
                    if (hdvalueList.Count > 1)
                    {
                        var maxOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                        var minOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                        if (maxOper != null && minOper != null)
                        {
                            if (maxOper.OperType == OperationRecordType.Delete)
                            {
                                hdIdsList.Add(maxOper.Id);
                                hdOperTypeList.Add(maxOper.OperType);
                            }
                            else if (minOper.OperType == OperationRecordType.Add)
                            {
                                hdIdsList.Add(maxOper.Id);
                                hdOperTypeList.Add(OperationRecordType.Add);
                            }
                            else
                            {
                                hdIdsList.Add(maxOper.Id);
                                hdOperTypeList.Add(OperationRecordType.Modify);
                            }
                        }
                        //hdDomainIds.Add(maxOper.InstanceId);
                    }
                    else
                    {
                        var oper = hdvalueList.FirstOrDefault();
                        if (oper != null)
                        {
                            hdIdsList.Add(oper.Id);
                            hdOperTypeList.Add(oper.OperType);
                        }
                        //hdDomainIds.Add(oper.InstanceId);
                    }
                }

                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion hdIdsList:{hdIdsList.Count}个");

                //开始分段
                int minAddCount = Math.Min(hdIdsList.Count, limitcount);
                int splitCount = 0;
                List<HistoryData> listHistory = new List<HistoryData>();
                if (minAddCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int addGeoCount = minAddCount / j;
                        var addIds = hdIdsList.Take(addGeoCount).ToList();
                        HashSet<long> hashIds = new HashSet<long>(addIds);
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (hashIds.Contains(s.Id))).ToList();
                        _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion dataLength:{dataLength.Count}个");
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            splitCount = addGeoCount;
                            listHistory = dataLength;
                            go = false;
                        }
                    }
                }
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion listHistory:{listHistory.Count}个");
                outModelDatas = _mapper.Map<List<GrpcHistoryData>>(listHistory);
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion outModelDatas:{outModelDatas.Count}个");
                hdIdsList.RemoveRange(0, splitCount);
                hdOperTypeList.RemoveRange(0, splitCount);
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion hdIdsList RemoveRange后:{hdIdsList.Count}个");
                modelIds = hdIdsList;
                modelOpertypes = _mapper.Map<List<GrpcOperationRecordType>>(hdOperTypeList);
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion modelIds:{modelIds.Count}个");
            }
            catch (Exception ex)
            {
                message = ex.Message;
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion err:{ex.Message}");
                _logger.LogInformation($"SecondDownloadDatasFirstByDataIdAndVersion err:{ex.StackTrace}");

                return false;
            }
            return true;
        }

        private bool SecondDownloadBySession(Guid projectId, List<long> modelIds, int limitretuncount, int limitLength, out List<long> outModelIds, out List<HistoryData> outModelDatas)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            outModelIds = new List<long>();
            outModelDatas = new List<HistoryData>();
            List<HistoryData> listHistory = new List<HistoryData>();
            try
            {
                var _modelManager = _teamRepository.GetProjectRepository(projectId);
                int minAddCount = Math.Min(modelIds.Count, limitretuncount);
                int splitCount = 0;
                if (minAddCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int addGeoCount = minAddCount / j;
                        var addIds = modelIds.Take(addGeoCount).ToList();
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (addIds.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            splitCount = addGeoCount;
                            listHistory = dataLength;
                            go = false;
                        }
                    }
                }

                outModelDatas = listHistory;
                modelIds.RemoveRange(0, splitCount);
                outModelIds = modelIds;
            }
            catch (Exception ex)
            {

                throw ex;
            }
            return true;
        }


        private bool SecondDownloadByInstanceAndVersion(Guid projectId, List<long> modelIds, List<GrpcOperationRecordType> hdOperTypeList, int limitretuncount, int limitLength, out List<long> outModelIds, out List<GrpcOperationRecordType> outOpertypeList, out List<GrpcHistoryData> outModelDatas)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            outModelIds = new List<long>();
            outOpertypeList = new List<GrpcOperationRecordType>();
            outModelDatas = new List<GrpcHistoryData>();
            List<HistoryData> listHistory = new List<HistoryData>();
            try
            {
                var _modelManager = _teamRepository.GetProjectRepository(projectId);
                int minAddCount = Math.Min(modelIds.Count, limitretuncount);
                int splitCount = 0;
                if (minAddCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int addGeoCount = minAddCount / j;
                        var addIds = modelIds.Take(addGeoCount).ToList();
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (addIds.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            splitCount = addGeoCount;
                            listHistory = dataLength;
                            go = false;
                        }
                    }
                }

                outModelDatas = _mapper.Map<List<GrpcHistoryData>>(listHistory);
                modelIds.RemoveRange(0, splitCount);
                outModelIds = modelIds;
            }
            catch (Exception ex)
            {

                throw ex;
            }
            return true;
        }


        private bool DownLoadByCommon(Guid projectId, string username, int limitCount, int limitLength, Dictionary<int, int> dicDomainVer, bool isMapping, int toVerNo, List<int> currentUserVersionNoList,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList,
            out List<HistoryData> changedDatas,
            out List<HistoryRelationship> changedRelationships
            )
        {
            changedDatas = new List<HistoryData>();
            changedRelationships = new List<HistoryRelationship>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();


            List<long> hdIdsList = new List<long>();
            List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();
            List<long> hrIdsList = new List<long>();
            List<GrpcOperationRecordType> hrOperTypeList = new List<GrpcOperationRecordType>();

            int minCount = 0;
            //PbimLog PbimLog = new PbimLog("Info");
            List<RelationIdInstanceIDOperType> hrIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>();
            HashSet<long> relationshipIds = new HashSet<long>();
            Dictionary<long, List<RelationIdInstanceIDOperType>> dicHrIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            Dictionary<int, int> dicDomainToinversion = new Dictionary<int, int>();

            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            foreach (var k in dicDomainVer.Keys)
            {
                int newToVer = toVerNo;
                if (!dicDomainToinversion.TryGetValue(k, out newToVer))
                {
                    newToVer = toVerNo;
                }
                //
                HashSet<long> hdDomainIds = new HashSet<long>();

                //按专业获取数据
                int selectdVer = 0;
                //var dIsMapping = dicDomainIsMap[k];
                if (dicDomainVer.TryGetValue(k, out selectdVer))
                {
                    //PbimLog.Info(username + "DownloadVersionByDomainAndVersionNoWithLargeData:开始获取专业" + k + "的数据toVerNo=" + newToVer + "|beginversion=" + selectdVer);
                    //构件
                    Dictionary<long, List<RelationIdInstanceIDOperType>> hdIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                    if (!isMapping)
                    {
                        //todictionary()
                        hdIdInstanceIdOperType = _modelManager.AllHistoryDatas.Where(h => h.Domain == k && (!currentUserVersionNoList.Contains(h.VersionNo)) && (h.VersionNo > selectdVer && h.VersionNo <= newToVer))
                            .Select(g => (new RelationIdInstanceIDOperType { Id = g.Id, InstanceId = g.InstanceId, OperType = g.OperationRecordType, VersionNo = g.VersionNo }))
                            .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                    }
                    else
                    {

                        hdIdInstanceIdOperType = _modelManager.AllHistoryDatas.Where(h => h.Domain == k && (h.VersionNo > 0 && h.VersionNo <= newToVer))
                            .Select(g => (new RelationIdInstanceIDOperType { Id = g.Id, InstanceId = g.InstanceId, OperType = g.OperationRecordType, VersionNo = g.VersionNo }))
                            .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
                    }
                    //var hdInstanceId = hdIdInstanceIdOperType.Select(s => s.InstanceId).ToList();
                    //HashSet<long> hashInstanceId = new HashSet<long>(hdInstanceId);
                    foreach (var l in hdIdInstanceIdOperType)
                    {
                        List<RelationIdInstanceIDOperType> hdvalueList = l.Value;
                        if (hdvalueList.Count > 1)
                        {
                            var maxOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                            var minOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                            if (maxOper != null && minOper != null)
                            {
                                if (maxOper.OperType == OperationRecordType.Delete)
                                {
                                    hdIdsList.Add(maxOper.Id);
                                    hdOperTypeList.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), maxOper.OperType.ToString())); //(OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hdOperTypeList[i].ToString());
                                }
                                else if (minOper.OperType == OperationRecordType.Add)
                                {
                                    hdIdsList.Add(maxOper.Id);
                                    hdOperTypeList.Add(GrpcOperationRecordType.Add);
                                }
                                else
                                {
                                    hdIdsList.Add(maxOper.Id);
                                    hdOperTypeList.Add(GrpcOperationRecordType.Modify);
                                }
                            }
                            //hdDomainIds.Add(maxOper.InstanceId);
                        }
                        else
                        {
                            var oper = hdvalueList.FirstOrDefault();
                            if (oper != null)
                            {
                                hdIdsList.Add(oper.Id);
                                hdOperTypeList.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), oper.OperType.ToString()));
                            }
                            //hdDomainIds.Add(oper.InstanceId);
                        }
                        hdDomainIds.Add(l.Key);
                    }
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:专业" + k + "的数据个数=" + hdIdsList.Count);
                    //关系
                    //避免超过sql语句长度，分每次10000条
                    List<RelationIdInstanceIDOperType> AllhrObjects = new List<RelationIdInstanceIDOperType>();
                    Dictionary<long, List<RelationIdInstanceIDOperType>> dicInstanceIdType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();



                    if (hdDomainIds.Count > limitCount)
                    {
                        int n = hdDomainIds.Count / limitCount;
                        int m = hdDomainIds.Count % limitCount;
                        for (int i = 0; i < n; i++)
                        {
                            var tempList = hdDomainIds.Skip(i * limitCount).Take(limitCount);
                            List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                            //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                            if (!isMapping)
                            {
                                hrObjects = _modelManager.AllHistoryRelationships.
                                    Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                                    Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                    .ToList();//.ToList().GroupBy(s=>s.Id).ToDictionary(g=>g.Key,g=>g.ToList());
                            }
                            else
                            {
                                hrObjects = _modelManager.AllHistoryRelationships
                                    .Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                                    .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                    .ToList();
                                //.ToList().GroupBy(s=>s.Id).ToDictionary(g=>g.Key,g=>g.ToList());
                            }

                            //AllhrObjects.AddRange(hrObjects);
                            foreach (var item in hrObjects)
                            {
                                if (relationshipIds.Add(item.Id))
                                {
                                    if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                                    {
                                        dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                                    }
                                    else
                                    {
                                        List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                                        v.Add(item);
                                        dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                                    }
                                }
                            }
                        }
                        if (m > 0)
                        {
                            var tempList = hdDomainIds.Skip(n * limitCount).Take(m);
                            //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                            List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                            if (!isMapping)
                            {
                                hrObjects = _modelManager.AllHistoryRelationships.
                                    Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                                    Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                    .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                            }
                            else
                            {
                                hrObjects = _modelManager.AllHistoryRelationships
                                    .Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                                    .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                    .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                            }
                            foreach (var item in hrObjects)
                            {
                                if (relationshipIds.Add(item.Id))
                                {
                                    if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                                    {
                                        dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                                    }
                                    else
                                    {
                                        List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                                        v.Add(item);
                                        dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                        //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                        if (!isMapping)
                        {
                            hrObjects = _modelManager.AllHistoryRelationships.
                                Where(r => (hdDomainIds.Contains(r.SourceID) || hdDomainIds.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                                Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                        }
                        else
                        {
                            hrObjects = _modelManager.AllHistoryRelationships
                                .Where(r => (hdDomainIds.Contains(r.SourceID) || hdDomainIds.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                                .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                                .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                        }
                        foreach (var item in hrObjects)
                        {
                            if (relationshipIds.Add(item.Id))
                            {
                                if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                                {
                                    dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                                }
                                else
                                {
                                    List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                                    v.Add(item);
                                    dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                                }
                            }
                        }
                    }

                }
            }
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:hdIdsList：" + hdIdsList.Count + "|hdOperTypeList：" + hdOperTypeList.Count);
            //循环专业列表获取数据完毕，开始处理关系数据
            //组装Dictionay instanceid:List<RelationIdInstanceIDOperType>
            //Dictionary<long, List<RelationIdInstanceIDOperType>> dicHrIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            //dicHrIdInstanceIdOperType = hrIdInstanceIdOperType.ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());

            foreach (var l in dicHrIdInstanceIdOperType)
            {
                List<RelationIdInstanceIDOperType> hrValueList = l.Value;
                if (hrValueList.Count > 1)
                {
                    var maxOper = hrValueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                    var minOper = hrValueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                    if (maxOper != null && minOper != null)
                    {
                        if (maxOper.OperType == OperationRecordType.Delete)
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), maxOper.OperType.ToString()));
                        }
                        else if (minOper.OperType == OperationRecordType.Add)
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add(GrpcOperationRecordType.Add);
                        }
                        else
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add(GrpcOperationRecordType.Modify);
                        }
                    }
                }
                else
                {
                    var Oper = hrValueList.FirstOrDefault();
                    if (Oper != null)
                    {
                        hrIdsList.Add(Oper.Id);
                        hrOperTypeList.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), Oper.OperType.ToString()));
                    }
                }

            }
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:hrIdsList：" + hrIdsList.Count() + "|hrOperTypeList：" + hrOperTypeList.Count());
            //开始构件数据分段
            List<HistoryData> firstHdList = new List<HistoryData>();
            List<long> firstHdIdList = new List<long>();
            int hdEndSplit = 0;
            minCount = Math.Min(hdIdsList.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                bool go = true;
                int j = 1;
                while (go)
                {
                    int hdidscount = minCount / j;
                    var hdids = hdIdsList.Take(hdidscount).ToList();
                    var dataLength = _modelManager.AllHistoryDatas.Where(s => (hdids.Contains(s.Id))).ToList();
                    int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                    if (x > limitLength)
                    {
                        j = j * 2;
                    }
                    else
                    {
                        hdEndSplit = hdidscount;
                        firstHdList = dataLength;
                        firstHdIdList = hdids;
                        go = false;
                    }
                }
            }
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:首次构件分段数量hdEndSplit：" + hdEndSplit);
            if (hdEndSplit > 0)
            {
                for (int i = 0; i < firstHdIdList.Count(); i++)
                {
                    var id = firstHdIdList[i];
                    var change = firstHdList.Where(s => s.Id == id).FirstOrDefault();
                    if (change != null)
                    {
                        change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hdOperTypeList[i].ToString());
                        changedDatas.Add(change);
                    }
                }
                hdIdsList.RemoveRange(0, hdEndSplit);
                hdOperTypeList.RemoveRange(0, hdEndSplit);

            }
            outhdIdsList = hdIdsList;
            outhdOperTypeList = hdOperTypeList;
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载构件数量：" + hdIdsList.Count());
            //关系数据分段处理
            List<long> firstHrIdList = new List<long>();
            List<HistoryRelationship> firstHrList = new List<HistoryRelationship>();
            int hrEndSplit = 0;
            minCount = Math.Min(hrIdsList.Count, limitCount);

            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                bool go = true;
                int j = 1;
                while (go)
                {
                    int hridscount = minCount / j;
                    var hrids = hrIdsList.Take(hridscount).ToList();
                    var dataLength = _modelManager.AllHistoryRelationships.Where(s => (hrids.Contains(s.Id))).ToList();
                    int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                    if (x > limitLength)
                    {
                        j = j * 2;
                    }
                    else
                    {
                        hrEndSplit = hridscount;
                        firstHrIdList = hrids;
                        firstHrList = dataLength;
                        go = false;
                    }
                }
            }



            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:首次构件分段数量hrEndSplit：" + hrEndSplit);
            if (hrEndSplit > 0)
            {

                for (int i = 0; i < firstHrIdList.Count(); i++)
                {
                    var id = firstHrIdList[i];
                    var change = firstHrList.Where(s => s.Id == id).FirstOrDefault();
                    if (change != null)
                    {
                        change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hrOperTypeList[i].ToString());
                        changedRelationships.Add(change);
                    }
                }
                hrIdsList.RemoveRange(0, hrEndSplit);
                hrOperTypeList.RemoveRange(0, hrEndSplit);

            }
            outhrIdsList = hrIdsList;
            outhrOperTypeList = hrOperTypeList;
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载构件数量：" + outhrIdsList.Count());


            return true;
        }
        //映射到最新版本，下载构件和关系
        private bool DownLoadByRepository(Guid projectId, int limitCount, int limitLength,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList,
            out List<HistoryData> changedDatas,
            out List<HistoryRelationship> changedRelationships
            )
        {
            changedDatas = new List<HistoryData>();
            changedRelationships = new List<HistoryRelationship>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();


            List<long> hdIdsList = new List<long>();
            List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();
            List<long> hrIdsList = new List<long>();
            List<GrpcOperationRecordType> hrOperTypeList = new List<GrpcOperationRecordType>();

            int minCount = 0;

            //PbimLog PbimLog = new PbimLog("Info");
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            //构件
            //映射到最新版本，无需按专业取数据，直接取全部数据
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:映射下载 第一次 直接从modeldata和relationship表中取数据");
            List<RelationIdInstanceIDOperType> hrIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>();

            List<RelationIdInstanceIDOperType> hdIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>(); //
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            hdIdInstanceIdOperType = _modelManager.Datas.Select(g => (new RelationIdInstanceIDOperType { Id = g.Id, InstanceId = g.InstanceId, VersionNo = g.VersionNo, OperType = OperationRecordType.Add })).ToList();

            Parallel.ForEach(hdIdInstanceIdOperType, options, item =>
            {
                lock (_locker)
                {
                    if (item != null)
                    {
                        hdIdsList.Add(item.Id);
                        hdOperTypeList.Add(GrpcOperationRecordType.Add); //(OperationRecordType.Add);
                    }
                }
            });
            outhdIdsList = hdIdsList;
            outhdOperTypeList = hdOperTypeList;
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:outhdIdsList：" + outhdIdsList.Count() + "|outhdOperTypeList：" + outhdOperTypeList.Count());
            //关系

            hrIdInstanceIdOperType = _modelManager.Relationships.Select(s => new RelationIdInstanceIDOperType { Id = s.Id, InstanceId = s.InstanceId, VersionNo = s.VersionNo, OperType = OperationRecordType.Add }).ToList();

            Parallel.ForEach(hrIdInstanceIdOperType, options, item =>
            {
                lock (_locker)
                {
                    if (item != null)
                    {
                        hrIdsList.Add(item.Id);
                        hrOperTypeList.Add(GrpcOperationRecordType.Add);
                    }
                }
            });
            outhrIdsList = hrIdsList;
            outhrOperTypeList = hrOperTypeList;
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:outhrIdsList：" + outhrIdsList.Count() + "|outhrOperTypeList：" + outhrOperTypeList.Count());
            //开始构件数据分段
            List<long> firstHdIdList = new List<long>();
            List<ModelData> firstHdList = new List<ModelData>();
            int hdEndSplit = 0;
            //取limitecount 与 hdidslist.count min()
            minCount = Math.Min(hdIdsList.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                bool go = true;
                int j = 1;
                while (go)
                {
                    int hdidscount = minCount / j;
                    var hdids = hdIdsList.Take(hdidscount).ToList();
                    var dataLength = _modelManager.Datas.Where(s => (hdids.Contains(s.Id))).ToList();
                    int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                    if (x > limitLength)
                    {
                        j = j * 2;
                    }
                    else
                    {
                        hdEndSplit = hdidscount;
                        firstHdIdList = hdids;
                        firstHdList = dataLength;
                        go = false;
                    }
                }
            }

            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:首次构件分段数量endSplit：" + hdEndSplit);
            if (hdEndSplit > 0)
            {
                List<HistoryData> changes = new List<HistoryData>();
                Parallel.ForEach(firstHdList, options, item =>
                {
                    lock (_locker)
                    {
                        if (item != null)
                        {
                            var history = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                            changes.Add(history);
                        }
                    }
                });
                changedDatas.AddRange(changes);
                hdIdsList.RemoveRange(0, hdEndSplit);
                hdOperTypeList.RemoveRange(0, hdEndSplit);

            }
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载构件数量：" + hdIdsList.Count());
            //关系数据分段处理
            List<long> firstHrIdList = new List<long>();
            List<Relationship> firstHrList = new List<Relationship>();
            int hrEndSplit = 0;
            minCount = Math.Min(hrIdsList.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                bool go = true;
                int j = 1;
                while (go)
                {
                    int hridscount = minCount / j;
                    var hrids = hrIdsList.Take(hridscount).ToList();
                    var dataLength = _modelManager.Relationships.Where(s => (hrids.Contains(s.Id))).ToList();
                    int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                    if (x > limitLength)
                    {
                        j = j * 2;
                    }
                    else
                    {
                        hrEndSplit = hridscount;
                        firstHrIdList = hrids;
                        firstHrList = dataLength;
                        go = false;
                    }
                }
            }

            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:首次关系分段数量endSplit：" + hrEndSplit);
            if (hrEndSplit > 0)
            {
                List<HistoryRelationship> changes = new List<HistoryRelationship>();
                Parallel.ForEach(firstHrList, options, item =>
                {
                    lock (_locker)
                    {
                        if (item != null)
                        {
                            var history = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                            changes.Add(history);
                        }
                    }
                });
                changedRelationships.AddRange(changes);
                hrIdsList.RemoveRange(0, hrEndSplit);
                hrOperTypeList.RemoveRange(0, hrEndSplit);

            }
            //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载关系数量：" + hrIdsList.Count());
            //关系
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<DownLoadModelDataByDataIdAndVersionResponse> DownLoadModelDataByDataIdAndVersion(DownLoadModelDataByDataIdAndVersionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new DownLoadModelDataByDataIdAndVersionResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var dicDataIdAndVersion = request.DicInstanceIdVersion.ToDictionary();
            long requestId = request.RequestId;
            var toDownLoadList = request.ToDownLoadIdList.ToList();
            string username = currentUser.LoginName;
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            //var toVersion = request.ToVersion;
            List<GrpcHistoryData> modelList = new List<GrpcHistoryData>();
            ParallelOptions options = new ParallelOptions();
            try
            {
                if (toDownLoadList.Any())
                {
                    _logger.LogInformation("DownLoadModelDataByDataIdAndVersion 分段下载：ToDownLoadIdList数量：" + toDownLoadList.Count);
                    //session中有addkey：已有下载申请的分批下载
                    List<long> modelIds = request.ToDownLoadIdList.ToList();
                    List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();
                    hdOperTypeList = request.ModelOpertypeList.ToList();

                    List<long> outModelIds = new List<long>();
                    List<GrpcOperationRecordType> outhdOperTypeList = new List<GrpcOperationRecordType>();
                    //DownLoadGeometriesBySessionIds(modelIds, limitretuncount, limitLength, out outModelIds, out addGeoDatas);
                    SplitNoMappingData(projectId, modelIds, limitcount, hdOperTypeList, limitLength, options, out modelList, out outModelIds, out outhdOperTypeList);
                    _logger.LogInformation($"DownLoadModelDataByDataIdAndVersion 分段下载：SplitNoMappingData之后outModelIds：{outModelIds.Count}个");
                    if (outModelIds.Count > 0)
                    {
                        x.RequestId = requestId;
                        x.ToDownLoadIdList.AddRange(outModelIds);
                        x.ModelOpertypeList.AddRange(outhdOperTypeList);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
                else
                {
                    //var instanceIDs = request.InstanceIDs.ToList();
                    List<long> modelIds = new List<long>();
                    List<GrpcOperationRecordType> modelOpers = new List<GrpcOperationRecordType>();
                    //第一次下载
                    _logger.LogInformation($"DownLoadModelDataByDataIdAndVersion 第一次下载：DownloadDatasFirstByDataIdAndVersion：{toDownLoadList.Count}个");
                    //DownLoadGeometryFirst(addGeometries, limitcount, limitLength, limitretuncount, toVersion, out modelIds, out addGeoDatas);
                    string errmessage = "";
                    var ret = SecondDownloadDatasFirstByDataIdAndVersion(projectId, dicDataIdAndVersion, limitcount, limitLength, limitretuncount, out modelIds, out modelOpers, out modelList, out errmessage);
                    _logger.LogInformation($"DownLoadModelDataByDataIdAndVersion 第一次下载：DownloadDatasFirstByDataIdAndVersion之后outModelIds：{modelIds.Count}个");
                    if (!ret)
                    {
                        x.Message = errmessage;
                        x.IsSuccess = false;
                        return x;
                    }
                    if (modelIds.Count > 0)
                    {
                        //session保存
                        x.RequestId = requestId;
                        x.ToDownLoadIdList.AddRange(modelIds);
                        x.ModelOpertypeList.AddRange(modelOpers);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"DownLoadModelDataByDataIdAndVersion err：{ex.Message}");
                _logger.LogInformation($"DownLoadModelDataByDataIdAndVersion err：{ex.StackTrace}");
                x.Message = ex.Message;
                x.IsSuccess = false;
            }

            x.ModelList.AddRange(_mapper.Map<List<GrpcHistoryData>>(modelList));
            return x;
        }


        public override async Task<DownLoadModeldataByInstanceIDsLargeResponse> DownLoadModeldataByInstanceIDsLarge(DownLoadModeldataByInstanceIDsLargeRequest request, ServerCallContext context)
        {
            var x = new DownLoadModeldataByInstanceIDsLargeResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            long requestId = request.RequestId;
            var isMapping = request.IsMapping;
            var toDownLoadList = request.ToDownLoadList.ToList();
            string username = currentUser.LoginName;
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var toVersion = request.ToVersion;
            List<HistoryData> modelList = new List<HistoryData>();
            if (isMapping)
            {
                if (toDownLoadList.Any())
                {
                    //session中有secondDownloadKey：已有下载申请的分批下载
                    List<long> modelIds = toDownLoadList;

                    List<long> outModelIds = new List<long>();

                    SecondDownloadDataFirstFromModeldata(projectId, modelIds, limitcount, limitLength, limitretuncount, toVersion, out outModelIds, out modelList);
                    if (outModelIds.Count > 0)
                    {

                        x.RequestId = requestId;
                        x.ToDownLoadList.AddRange(outModelIds);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
                else
                {
                    List<long> modelIds = request.InstanceIDs.ToList();
                    List<long> outModelIds = new List<long>();
                    //分段下载第一次下载
                    SecondDownloadDataFirstFromModeldata(projectId, modelIds, limitcount, limitLength, limitretuncount, toVersion, out outModelIds, out modelList);
                    if (outModelIds.Count > 0)
                    {
                        //session保存
                        x.RequestId = requestId;
                        x.ToDownLoadList.AddRange(outModelIds);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
            }
            else
            {
                if (toDownLoadList.Any())
                {
                    //session中有addkey：已有下载申请的分批下载
                    List<long> modelIds = request.ToDownLoadList.ToList();

                    List<long> outModelIds = new List<long>();
                    //DownLoadGeometriesBySessionIds(modelIds, limitretuncount, limitLength, out outModelIds, out addGeoDatas);
                    SecondDownloadBySession(projectId, modelIds, limitretuncount, limitLength, out outModelIds, out modelList);
                    if (outModelIds.Count > 0)
                    {
                        x.RequestId = requestId;
                        x.ToDownLoadList.AddRange(outModelIds);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
                else
                {
                    var instanceIDs = request.InstanceIDs.ToList();
                    List<long> modelIds = new List<long>();
                    //分段下载第一次下载
                    //DownLoadGeometryFirst(addGeometries, limitcount, limitLength, limitretuncount, toVersion, out modelIds, out addGeoDatas);
                    SecondDownloadDatasFirst(projectId, instanceIDs, limitcount, limitLength, limitretuncount, toVersion, out modelIds, out modelList);
                    if (modelIds.Count > 0)
                    {
                        //session保存
                        x.RequestId = requestId;
                        x.ToDownLoadList.AddRange(modelIds);
                        x.IsSuccess = true;
                    }
                    else
                    {
                        requestId = 0;
                        x.IsSuccess = true;
                        x.RequestId = requestId;
                    }
                }
            }
            x.ModelList.AddRange(_mapper.Map<List<GrpcHistoryData>>(modelList));
            return x;
        }


        public override async Task<DownloadVersionByDomainAndVersionNoWithLargeDataResponse> DownloadVersionByDomainAndVersionNoWithLargeData(DownloadVersionByDomainAndVersionNoWithLargeDataRequest request, ServerCallContext context)
        {
            var x = new DownloadVersionByDomainAndVersionNoWithLargeDataResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            int minCount = 0;
            long requestId = request.RequestId;
            var isMapping = request.IsMapping;
            var toDownLoadList = request.ToDownLoadList.ToList();
            var modelOperTypeList = request.ModelOpertypeList.ToList();

            var toDownLoadHrList = request.ToDownLoadHrList.ToList();
            var hrDataOperTypeList = request.HrOpertypeList.ToList();
            string username = currentUser.LoginName;
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var toVersion = request.ToVersion;
            List<HistoryData> changedDatas = new List<HistoryData>();
            List<HistoryRelationship> changedRelationships = new List<HistoryRelationship>();
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            var currentUserVersion = projectRepo.Versions.Where(v => v.Author == username);
            int serverVersion = projectRepo.CurrentVersionNo;
            var currentUserVersionNoList = new List<int>();
            var dicDomainVer = request.DicDomainVer.ToDictionary();
            var toVerNo = request.ToVersion;
            if (currentUserVersion.Any())
            {
                currentUserVersionNoList = currentUserVersion.Select(v => v.VersionNo).ToList();
            }

            if (isMapping)
            {
                if (toDownLoadList.Any())
                {
                    //session中有secondDownloadKey：映射 第二次下载
                    List<long> hdIdsList = toDownLoadList;
                    List<GrpcOperationRecordType> hdOperTypeList = modelOperTypeList;
                    List<long> hrIdsList = toDownLoadHrList;  //historyrelationship 自增ID
                    List<GrpcOperationRecordType> hrOperTypeList = hrDataOperTypeList;   //historyrelationship操作类型
                    #region 映射
                    //映射
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData: 映射下在构件数据 hdIdsList：" + hdIdsList.Count() + "|hdOperTypeList：" + hdOperTypeList.Count());

                    //开始构件数据分段
                    int hdEndSplit = 0;
                    List<long> firstHdIdList = new List<long>();
                    List<ModelData> firstHdList = new List<ModelData>();
                    minCount = Math.Min(hdIdsList.Count, limitcount);
                    if (minCount > 0)
                    {
                        //数量超过10000，必分段
                        //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                        bool go = true;
                        int j = 1;
                        while (go)
                        {
                            int hdidscount = minCount / j;
                            var hdids = hdIdsList.Take(hdidscount).ToList();
                            var dataLength = projectRepo.Datas.Where(s => (hdids.Contains(s.Id))).ToList();
                            int y = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);
                            if (y > limitLength)
                            {
                                j = j * 2;
                            }
                            else
                            {
                                hdEndSplit = hdidscount;
                                firstHdIdList = hdids;
                                firstHdList = dataLength;
                                go = false;
                            }
                        }
                    }

                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:分段下载构件分段数量hdEndSplit：" + hdEndSplit);
                    if (hdEndSplit > 0)
                    {
                        //var firstHdIdList = hdIdsList.Take(hdEndSplit).ToList();
                        //var firstHdList = _modelManager.Datas.Where(s => firstHdIdList.Contains(s.Id)).ToList();
                        List<HistoryData> changes = new List<HistoryData>();
                        Parallel.ForEach(firstHdList, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var history = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                                    changes.Add(history);
                                }
                            }
                        });
                        changedDatas.AddRange(changes);
                        hdIdsList.RemoveRange(0, hdEndSplit);
                        hdOperTypeList.RemoveRange(0, hdEndSplit);

                    }
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
                    #endregion

                    #region 映射下载，关系数据分段处理
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:hrIdsList:" + hrIdsList.Count);
                    //映射下载，关系数据分段处理


                    int hrEndSplit = 0;
                    List<long> firstHrIdList = new List<long>();
                    List<Relationship> firstHrList = new List<Relationship>();
                    minCount = Math.Min(hrIdsList.Count, limitcount);
                    if (minCount > 0)
                    {
                        //数量超过10000，必分段
                        //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                        bool go = true;
                        int j = 1;
                        while (go)
                        {
                            int hridscount = minCount / j;
                            var hrids = hrIdsList.Take(hridscount).ToList();
                            var dataLength = projectRepo.Relationships.Where(s => (hrids.Contains(s.Id))).ToList();
                            int y = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                            if (y > limitLength)
                            {
                                j = j * 2;
                            }
                            else
                            {
                                hrEndSplit = hridscount;
                                firstHrIdList = hrids;
                                firstHrList = dataLength;
                                go = false;
                            }
                        }
                    }

                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:分段下载关系分段数量hrEndSplit：" + hrEndSplit);
                    if (hrEndSplit > 0)
                    {
                        //var firstHrIdList = hrIdsList.Take(hrEndSplit).ToList();
                        //var firstHrList = _modelManager.Relationships.Where(s => firstHrIdList.Contains(s.Id)).ToList();
                        List<HistoryRelationship> changes = new List<HistoryRelationship>();
                        Parallel.ForEach(firstHrList, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var history = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                    changes.Add(history);
                                }
                            }
                        });
                        changedRelationships.AddRange(changes);
                        hrIdsList.RemoveRange(0, hrEndSplit);
                        hrOperTypeList.RemoveRange(0, hrEndSplit);

                    }
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
                    #endregion


                    if (hdIdsList.Count > 0)
                    {
                        x.ToDownLoadList.AddRange(hdIdsList);
                        x.ModelOpertypeList.AddRange(hdOperTypeList);
                        x.IsSuccess = true;
                    }

                    if (hrIdsList.Count > 0)
                    {

                        x.ToDownLoadHrList.AddRange(hrIdsList);
                        x.HrOpertypeList.AddRange(hrOperTypeList);
                        //x.IsSuccess = true;
                    }
                    if (hrIdsList.Count <= 0 && hdIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }
                    x.IsSuccess = true;
                    x.RequestId = requestId;
                }
                else
                {

                    //映射第一次下载
                    List<long> hdIdsList = new List<long>();//toDownLoadList;
                    List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();//modelOperTypeList;
                    List<long> hrIdsList = new List<long>();//toDownLoadHrList;  //historyrelationship 自增ID
                    List<GrpcOperationRecordType> hrOperTypeList = new List<GrpcOperationRecordType>();//hrDataOperTypeList;   //historyrelationship操作类型
                    DownLoadByRepository(projectId, limitcount, limitLength, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);


                    if (hdIdsList.Count > 0)
                    {
                        x.ToDownLoadList.AddRange(hdIdsList);
                        x.ModelOpertypeList.AddRange(hdOperTypeList);
                        x.IsSuccess = true;
                    }

                    if (hrIdsList.Count > 0)
                    {

                        x.ToDownLoadHrList.AddRange(hrIdsList);
                        x.HrOpertypeList.AddRange(hrOperTypeList);
                        //x.IsSuccess = true;
                    }
                    if (hrIdsList.Count <= 0 && hdIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }
                    x.IsSuccess = true;
                    x.RequestId = requestId;
                }
            }
            else
            {
                if (toDownLoadList.Any())
                {
                    //session中有addkey：已有下载申请的分批下载 非映射，第二次下载
                    List<long> hdIdsList = toDownLoadList;
                    List<GrpcOperationRecordType> hdOperTypeList = modelOperTypeList;
                    List<long> hrIdsList = toDownLoadHrList;  //historyrelationship 自增ID
                    List<GrpcOperationRecordType> hrOperTypeList = hrDataOperTypeList;


                    #region 非映射
                    //开始构件数据分段
                    int hdEndSplit = 0;
                    List<long> firstHdIdList = new List<long>();
                    List<HistoryData> firstHdList = new List<HistoryData>();
                    minCount = Math.Min(hdIdsList.Count, limitcount);
                    if (minCount > 0)
                    {
                        //数量超过10000，必分段
                        //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                        bool go = true;
                        int j = 1;
                        while (go)
                        {
                            int hdidscount = minCount / j;
                            var hdids = hdIdsList.Take(hdidscount).ToList();
                            var dataLength = projectRepo.AllHistoryDatas.Where(s => (hdids.Contains(s.Id))).ToList();
                            int y = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                            if (y > limitLength)
                            {
                                j = j * 2;
                            }
                            else
                            {
                                hdEndSplit = hdidscount;
                                firstHdIdList = hdids;
                                firstHdList = dataLength;
                                go = false;
                            }
                        }
                    }

                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:分段下载构件数量hdEndSplit：" + hdEndSplit);
                    if (hdEndSplit > 0)
                    {
                        //var firstHdIdList = hdIdsList.Take(hdEndSplit).ToList();
                        //var firstHdList = _modelManager.AllHistoryDatas.Where(s => firstHdIdList.Contains(s.Id)).ToList();
                        for (int i = 0; i < firstHdIdList.Count(); i++)
                        {
                            var id = firstHdIdList[i];
                            var change = firstHdList.Where(s => s.Id == id).FirstOrDefault();
                            if (change != null)
                            {
                                change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hdOperTypeList[i].ToString());//(OperationRecordType)hdOperTypeList[i];
                                changedDatas.Add(change);
                            }
                        }
                        hdIdsList.RemoveRange(0, hdEndSplit);
                        hdOperTypeList.RemoveRange(0, hdEndSplit);

                    }

                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
                    #endregion

                    #region //非映射，关系数据分段处理
                    int hrEndSplit = 0;
                    List<long> firstHrIdList = new List<long>();
                    List<HistoryRelationship> firstHrList = new List<HistoryRelationship>();
                    minCount = Math.Min(hrIdsList.Count, limitcount);
                    if (minCount > 0)
                    {
                        //数量超过10000，必分段
                        //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                        bool go = true;
                        int j = 1;
                        while (go)
                        {
                            int hridscount = minCount / j;
                            var hrids = hrIdsList.Take(hridscount).ToList();
                            var dataLength = projectRepo.AllHistoryRelationships.Where(s => (hrids.Contains(s.Id))).ToList();
                            int y = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                            if (y > limitLength)
                            {
                                j = j * 2;
                            }
                            else
                            {
                                hrEndSplit = hridscount;
                                firstHrIdList = hrids;
                                firstHrList = dataLength;
                                go = false;
                            }
                        }
                    }

                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData：分段下载关系分段数量hrEndSplit：" + hrEndSplit);
                    if (hrEndSplit > 0)
                    {
                        for (int i = 0; i < firstHrIdList.Count(); i++)
                        {
                            var id = firstHrIdList[i];
                            var change = firstHrList.Where(s => s.Id == id).FirstOrDefault();
                            if (change != null)
                            {
                                change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hrOperTypeList[i].ToString()); //hrOperTypeList[i];
                                changedRelationships.Add(change);
                            }
                        }
                        hrIdsList.RemoveRange(0, hrEndSplit);
                        hrOperTypeList.RemoveRange(0, hrEndSplit);
                    }
                    //PbimLog.Info("DownloadVersionByDomainAndVersionNoWithLargeData:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
                    #endregion


                    if (hdIdsList.Count > 0)
                    {
                        x.ToDownLoadList.AddRange(hdIdsList);
                        x.ModelOpertypeList.AddRange(hdOperTypeList);
                        x.IsSuccess = true;
                    }

                    if (hrIdsList.Count > 0)
                    {

                        x.ToDownLoadHrList.AddRange(hrIdsList);
                        x.HrOpertypeList.AddRange(hrOperTypeList);
                        //x.IsSuccess = true;
                    }
                    if (hrIdsList.Count <= 0 && hdIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }
                    x.IsSuccess = true;
                    x.RequestId = requestId;
                }
                else
                {
                    //var instanceIDs = request.InstanceIDs.ToList();
                    //List<long> modelIds = new List<long>();
                    List<long> hdIdsList = toDownLoadList;
                    List<GrpcOperationRecordType> hdOperTypeList = modelOperTypeList;
                    List<long> hrIdsList = toDownLoadHrList;  //historyrelationship 自增ID
                    List<GrpcOperationRecordType> hrOperTypeList = hrDataOperTypeList;
                    //非映射第一次下载
                    DownLoadByCommon(projectId, username, limitcount, limitLength, dicDomainVer, isMapping, toVerNo, currentUserVersionNoList, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    //PbimLog.Info("不是映射到最新版本 :hdIdsList" + hdIdsList.Count + "|hrIdsList:" + hrIdsList.Count);
                    if (hdIdsList.Count() <= 0 && hrIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }

                    //分段下载第一次下载
                    //DownLoadGeometryFirst(addGeometries, limitcount, limitLength, limitretuncount, toVersion, out modelIds, out addGeoDatas);
                    //SecondDownloadDatasFirst(projectId, instanceIDs, limitcount, limitLength, limitretuncount, toVersion, out modelIds, out modelList);
                    if (hdIdsList.Count > 0)
                    {
                        x.ToDownLoadList.AddRange(hdIdsList);
                        x.ModelOpertypeList.AddRange(hdOperTypeList);
                        x.IsSuccess = true;
                    }

                    if (hrIdsList.Count > 0)
                    {

                        x.ToDownLoadHrList.AddRange(hrIdsList);
                        x.HrOpertypeList.AddRange(hrOperTypeList);
                        //x.IsSuccess = true;
                    }
                    if (hrIdsList.Count <= 0 && hdIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }
                    x.IsSuccess = true;
                    x.RequestId = requestId;
                }
            }
            x.ChangedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(changedDatas));

            return x;
        }

        public override async Task<DownloadVesionByDomainResponse> DownloadVesionByDomain(DownloadVesionByDomainRequest request, ServerCallContext context)
        {
            var x = new DownloadVesionByDomainResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var version = new Infrastructure.ModelDomain.SchemaVersion();
            bool status = projectRepo.GetProjectSchema(out version);
            string verStr = version.MajorVersion + "." + version.MinorVersion + "." +
                                              version.DevelopVersion;
            //根据schema版本号判断使用哪个版本的接口

            List<HistoryData> historyData = new List<HistoryData>();
            List<HistoryRelationship> relationships = null;
            string updateDomainList = string.Empty;
            var lockuserIds = new List<int?>();
            var resourceNames = new List<string>();


            var currentDatas = _mapper.Map<List<ModelData>>(request.CurrentDatas);
            var currentRelationships = _mapper.Map<List<Relationship>>(request.CurrentRelationships);

            if (SCHEMAVERS.Contains(verStr))
            {
                _logger.LogInformation("DownloadVesionByDomain_2020630:" + SCHEMAVERSION_2020630);
                historyData = DownloadVesionByDomain_2020630(projectRepo, projectId, currentUser.LoginName, request.DomainIds.ToList(), request.ToVerNo, currentDatas, currentRelationships, request.IsMapping, out relationships, out lockuserIds, out updateDomainList, out resourceNames);
            }
            else
            {
                _logger.LogInformation("DownloadVesionByDomain_Default:" + verStr);
                historyData = DownloadVesionByDomain_Default(projectRepo, projectId, currentUser.LoginName, request.DomainIds.ToList(), request.ToVerNo, currentDatas, currentRelationships, out relationships, out lockuserIds, out updateDomainList, out resourceNames);
            }

            x.HistoryDataList.AddRange(_mapper.Map<List<GrpcHistoryData>>(historyData));
            x.LockuserIds.AddRange(lockuserIds);
            x.RelationshipList.AddRange(_mapper.Map<List<GrpcHistoryRelationship>>(relationships));
            x.ResourceNames.AddRange(resourceNames);
            x.UpdateDomainList = updateDomainList;
            x.IsSuccess = true;

            return x;
        }


        private List<HistoryData> DownloadVesionByDomain_2020630(IProjectRepository projectRepository, Guid projectId, string userName
            , List<int> domainIds, int toVerNo
            , List<ModelData> currentDatas
            , List<Relationship> currentRelationships
            , bool isMapping
            , out List<HistoryRelationship> relationships
            , out List<int?> lockuserIds
            , out string updateDomainList
            , out List<string> resourceNames)
        {

            relationships = null;
            updateDomainList = "";
            List<HistoryData> changedDatas = new List<HistoryData>();
            List<HistoryRelationship> changedRelationships = new List<HistoryRelationship>();

            lockuserIds = new List<int?>();
            resourceNames = new List<string>();
            List<int?> exceptCurrentUserIds = new List<int?>();
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            var waitHandle = new AutoResetEvent(false);
            try
            {
                Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;
                if (toVerNo > 0 && toVerNo <= projectRepository.CurrentVersionNo)
                {
                    //if (_taskContrl != null)
                    //{
                    //    _logger.LogInformation("DownloadVesionByDomain:_taskContrl:======>true");
                    //    if (_taskContrl.RegistHanlder(waitHandle))
                    //        waitHandle.WaitOne();
                    //}
                    var mem = projectRepository.GetProjectMember(GetMemberIdByLoginName, userName);

                    List<ModelData> targetDatas;
                    List<Relationship> targetRelationships;

                    var projectAllModelsInCache = new List<ModelData>();//CacheHelper.FromCacheModelData(projectId) ?? _modelManager.Datas.ToList();
                    var projectAllRelationShipInCache = new List<Relationship>();// CacheHelper.FromCacheRelationShip(projectId) ?? _modelManager.Relationships.ToList();
                    if (CacheHelper.IsRuning && CacheHelper.HasMemory)
                    {
                        projectAllModelsInCache = CacheHelper.FromCacheModelData(projectId) ?? projectRepository.Datas.ToList();
                        projectAllRelationShipInCache = CacheHelper.FromCacheRelationShip(projectId) ?? projectRepository.Relationships.ToList();
                    }
                    else
                    {
                        projectAllModelsInCache = projectRepository.Datas.ToList();
                        projectAllRelationShipInCache = projectRepository.Relationships.ToList();
                    }

                    //如果是最新版本直接获取
                    //int domainCurrentVersionNo = _modelManager.VersionDomains.Where(d=>d.Domain==domainIds);
                    if (toVerNo == projectRepository.CurrentVersionNo)
                    {
                        _logger.LogInformation("DownloadVesionByDomain:开始获取最新数据；" + string.Join(",", domainIds) + "CurrentVersionNo:" + projectRepository.CurrentVersionNo);
                        var dataIds = projectAllModelsInCache.Where(d => domainIds.Contains(d.Domain)).Select(d => d.InstanceId).ToList();
                        //ModelQuery modelQuery = new ModelQuery(_modelManager);

                        var hashids = new HashSet<long>(dataIds);
                        var dataIndexes = projectAllModelsInCache
                            .Where(d => hashids.Contains(d.InstanceId))
                            .Select(index =>
                                    new
                                    {
                                        index.InstanceId,
                                        index.VersionNo
                                    }).ToList();

                        var datahashset = new HashSet<long>(dataIndexes.Select(d => d.InstanceId));

                        var targetDataDictionary = new Dictionary<long, int>();

                        Parallel.ForEach(dataIndexes, options, item =>
                        {
                            lock (_locker)
                            {
                                targetDataDictionary.Add(item.InstanceId, item.VersionNo);
                            }
                        });

                        foreach (var data in currentDatas)
                        {
                            if (targetDataDictionary.ContainsKey(data.InstanceId))
                            {
                                int targetVersion;
                                targetDataDictionary.TryGetValue(data.InstanceId, out targetVersion);
                                if (targetVersion != data.VersionNo)
                                {
                                    var targetData = projectAllModelsInCache.SingleOrDefault(d => d.InstanceId == data.InstanceId);

                                    if (targetData != null)
                                    {
                                        var historydata = targetData.CreateHistoryData(OperationRecordType.Modify, targetVersion);
                                        changedDatas.Add(historydata);
                                    }
                                }

                                targetDataDictionary.Remove(data.InstanceId);
                            }
                            else
                            {
                                var historydata = data.CreateHistoryData(OperationRecordType.Delete, 0);
                                changedDatas.Add(historydata);
                            }
                        }

                        var x = projectAllModelsInCache.Where(a => targetDataDictionary.Keys.Contains(a.InstanceId));
                        Parallel.ForEach(x, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var historydata = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                                    changedDatas.Add(historydata);
                                }
                            }
                        });
                        //获得变更的关系
                        var relationIndexes = projectAllRelationShipInCache
                                                                        .Select(index => new
                                                                        {
                                                                            index.InstanceId,
                                                                            index.VersionNo,
                                                                            index.Type,
                                                                            index.SourceID,
                                                                            index.TargetID
                                                                        }).ToList();

                        relationIndexes = relationIndexes.Where(rs => (hashids.Contains(rs.SourceID))).ToList();
                        var relhashset = new HashSet<long>(relationIndexes.Select(s => s.InstanceId));

                        var targetRelDictionary = new Dictionary<long, int>();

                        Parallel.ForEach(relationIndexes, options, item =>
                        {
                            lock (_locker)
                            {
                                targetRelDictionary.Add(item.InstanceId, item.VersionNo);
                            }
                        });

                        //第一次创建联机项目，currentRelationships为null 或者 count=0
                        foreach (var relationship in currentRelationships)
                        {
                            if (targetRelDictionary.ContainsKey(relationship.InstanceId))
                            {
                                int targetVersion;
                                targetRelDictionary.TryGetValue(relationship.InstanceId, out targetVersion);


                                targetRelDictionary.Remove(relationship.InstanceId);
                            }
                            else
                            {
                                var historyRelationship = relationship.CreateHistoryRelationship(OperationRecordType.Delete, 0);
                                changedRelationships.Add(historyRelationship);
                            }
                        }
                        var ships = projectAllRelationShipInCache.Where(d => targetRelDictionary.Keys.Contains(d.InstanceId));
                        Parallel.ForEach(ships, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var historyrelationship = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                    changedRelationships.Add(historyrelationship);
                                }
                            }
                        });
                    }
                    else
                    {
                        bool status = VersionTool.CalculateVersionDatas(projectRepository,
                                                                        domainIds,
                                                                        toVerNo,
                                                                        out targetDatas,
                                                                        out targetRelationships,
                                                                        projectId);
                        if (!status) return null;

                        status = ChangeSetTool.GetChangedSet(currentDatas,
                                                            currentRelationships,
                                                            targetDatas,
                                                            targetRelationships,
                                                            out changedDatas,
                                                            out changedRelationships);

                        if (!status) return null;
                    }

                    Dictionary<long, int?> lockDictionary = new Dictionary<long, int?>();

                    Dictionary<long, string> resourceDictionary = new Dictionary<long, string>();


                    var lockedDatas = projectRepository.LockedComponents.ToList();
                    var allResourceClasses = projectRepository.ResourceClasses.ToList();
                    Parallel.ForEach(lockedDatas, options, item =>
                    {
                        lock (_locker)
                        {
                            if (!lockDictionary.ContainsKey(item.InstanceId))
                                lockDictionary.Add(item.InstanceId, item.LockUserId);
                        }
                    });
                    Parallel.ForEach(allResourceClasses, options, item =>
                    {
                        lock (_locker)
                        {
                            if (!resourceDictionary.ContainsKey(item.InstanceId))
                            {
                                resourceDictionary.Add(item.InstanceId, item.ResourceName);
                            }
                        }
                    });
                    List<long> curUserLockInstanceIds = new List<long>();
                    List<long> resourceClassIds = projectRepository.ResourceClasses.Select(c => c.InstanceId).ToList();
                    foreach (var data in changedDatas)
                    {
                        int? lockstatus;
                        if (lockDictionary.TryGetValue(data.InstanceId, out lockstatus))
                        {
                            lockuserIds.Add(lockstatus);

                            if (lockstatus == mem.ID)
                            {
                                curUserLockInstanceIds.Add(data.InstanceId);
                            }
                            if (lockstatus != mem.ID || resourceClassIds.Contains(data.InstanceId))
                            {
                                exceptCurrentUserIds.Add(lockstatus);
                            }
                        }
                        else
                        {
                            lockuserIds.Add(null);
                            exceptCurrentUserIds.Add(null);
                        }
                        string resourceName = "";
                        if (resourceDictionary.TryGetValue(data.InstanceId, out resourceName))
                        {
                            resourceNames.Add(resourceName);
                        }
                        else
                        {
                            resourceNames.Add("");
                        }
                    }

                    List<long> exceptDownloadIds = curUserLockInstanceIds.Except(resourceClassIds).ToList();
                    List<HistoryData> exceptModeldata = changedDatas.Where(c => exceptDownloadIds.Contains(c.InstanceId)).ToList();
                    relationships = changedRelationships;
                    var ret = changedDatas;

                    //将该用户锁定的非资源类数据排除掉，不返回给客户端
                    if (curUserLockInstanceIds.Any() && !isMapping)
                    {
                        ret = changedDatas.Except(exceptModeldata).ToList();
                        lockuserIds = exceptCurrentUserIds;
                    }

                    var domainList = changedDatas.Select(d => d.Domain).Distinct();
                    //排查问题使用，非参数值所传专业数据被下载
                    //var query = ret.Select(a => !domainIds.Contains(a.Domain)).ToList();
                    updateDomainList = "下载专业列表：" + string.Join(",", domainList);
                    _logger.LogInformation(updateDomainList);

                    return ret;
                }



            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }

            return null;
        }


        private List<HistoryData> DownloadVesionByDomain_Default(IProjectRepository projectRepository, Guid projectId, string userName
            , List<int> domainIds, int toVerNo, List<ModelData> currentDatas
            , List<Relationship> currentRelationships, out List<HistoryRelationship> relationships
            , out List<int?> lockuserIds, out string updateDomainList
            , out List<string> resourceNames)
        {
            _logger.LogInformation("DownloadVesionByDomain:begin============================");
            bool isFirst = false;
            if (!currentDatas.Any() && !currentRelationships.Any())
            {
                isFirst = true;
            }
            Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;

            relationships = null;
            updateDomainList = "";
            List<HistoryData> changedDatas = new List<HistoryData>();
            List<HistoryRelationship> changedRelationships = new List<HistoryRelationship>();

            lockuserIds = new List<int?>();
            resourceNames = new List<string>();
            List<int?> exceptCurrentUserIds = new List<int?>();
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            _logger.LogInformation("DownloadVesionByDomain:Environment.ProcessorCount:======>" + Environment.ProcessorCount);
            var waitHandle = new AutoResetEvent(false);
            try
            {
                //username = SessionManager.GetCurrentSession().UserName;
                //add by asdf 2018-02-27 获取缓存使用
                //var projectId = SessionManager.GetCurrentSession().ProjectGuid;
                //Console.Write(projectId);
                if (toVerNo > 0 && toVerNo <= projectRepository.CurrentVersionNo)
                {
                    //if (_taskContrl != null)
                    //{
                    //    _logger.LogInformation("DownloadVesionByDomain:_taskContrl:======>true");
                    //    if (_taskContrl.RegistHanlder(waitHandle))
                    //        waitHandle.WaitOne();
                    //}
                    //var mem = _projectManager.GetProjectMember(username);
                    var mem = projectRepository.GetProjectMember(GetMemberIdByLoginName, userName);
                    List<ModelData> targetDatas;
                    List<Relationship> targetRelationships;

                    var projectAllModelsInCache = new List<ModelData>();//CacheHelper.FromCacheModelData(projectId) ?? _modelManager.Datas.ToList();
                    var projectAllRelationShipInCache = new List<Relationship>();// CacheHelper.FromCacheRelationShip(projectId) ?? _modelManager.Relationships.ToList();
                    if (CacheHelper.IsRuning && CacheHelper.HasMemory)
                    {
                        projectAllModelsInCache = CacheHelper.FromCacheModelData(projectId) ?? projectRepository.Datas.ToList();
                        projectAllRelationShipInCache = CacheHelper.FromCacheRelationShip(projectId) ?? projectRepository.Relationships.ToList();
                    }
                    else
                    {
                        projectAllModelsInCache = projectRepository.Datas.ToList();
                        projectAllRelationShipInCache = projectRepository.Relationships.ToList();
                    }




                    //如果是最新版本直接获取
                    //int domainCurrentVersionNo = _modelManager.VersionDomains.Where(d=>d.Domain==domainIds);
                    if (toVerNo == projectRepository.CurrentVersionNo)
                    {
                        _logger.LogInformation("DownloadVesionByDomain:开始获取最新数据；" + string.Join(",", domainIds) + "CurrentVersionNo:" + projectRepository.CurrentVersionNo);
                        var dataIds = projectAllModelsInCache.Where(d => domainIds.Contains(d.Domain)).Select(d => d.InstanceId).ToList();
                        //ModelQuery modelQuery = new ModelQuery(_modelManager);
                        var hashids = new HashSet<long>(dataIds);
                        var dataIndexes = projectAllModelsInCache
                            .Where(d => hashids.Contains(d.InstanceId))
                            .Select(index =>
                                    new
                                    {
                                        index.InstanceId,
                                        index.VersionNo
                                    }).ToList();

                        var datahashset = new HashSet<long>(dataIndexes.Select(d => d.InstanceId));

                        var targetDataDictionary = new Dictionary<long, int>();

                        Parallel.ForEach(dataIndexes, options, item =>
                        {
                            lock (_locker)
                            {
                                targetDataDictionary.Add(item.InstanceId, item.VersionNo);
                            }
                        });

                        foreach (var data in currentDatas)
                        {
                            if (targetDataDictionary.ContainsKey(data.InstanceId))
                            {
                                int targetVersion;
                                targetDataDictionary.TryGetValue(data.InstanceId, out targetVersion);
                                if (targetVersion != data.VersionNo)
                                {
                                    var targetData = projectAllModelsInCache.SingleOrDefault(d => d.InstanceId == data.InstanceId);

                                    if (targetData != null)
                                    {
                                        var historydata = targetData.CreateHistoryData(OperationRecordType.Modify, targetVersion);
                                        changedDatas.Add(historydata);
                                    }
                                }

                                targetDataDictionary.Remove(data.InstanceId);
                            }
                            else
                            {
                                var historydata = data.CreateHistoryData(OperationRecordType.Delete, 0);
                                changedDatas.Add(historydata);
                            }
                        }

                        var x = projectAllModelsInCache.Where(a => targetDataDictionary.Keys.Contains(a.InstanceId));
                        Parallel.ForEach(x, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var historydata = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                                    changedDatas.Add(historydata);
                                }
                            }
                        });
                        //获得变更的关系
                        var relationIndexes = projectAllRelationShipInCache
                                                                        .Select(index => new
                                                                        {
                                                                            index.InstanceId,
                                                                            index.VersionNo,
                                                                            index.Type,
                                                                            index.SourceID,
                                                                            index.TargetID
                                                                        }).ToList();

                        relationIndexes = relationIndexes.Where(rs => (hashids.Contains(rs.SourceID))).ToList();
                        var relhashset = new HashSet<long>(relationIndexes.Select(s => s.InstanceId));

                        var targetRelDictionary = new Dictionary<long, int>();

                        Parallel.ForEach(relationIndexes, options, item =>
                        {
                            lock (_locker)
                            {
                                targetRelDictionary.Add(item.InstanceId, item.VersionNo);
                            }
                        });

                        //第一次创建联机项目，currentRelationships为null 或者 count=0
                        foreach (var relationship in currentRelationships)
                        {
                            if (targetRelDictionary.ContainsKey(relationship.InstanceId))
                            {
                                int targetVersion;
                                targetRelDictionary.TryGetValue(relationship.InstanceId, out targetVersion);


                                targetRelDictionary.Remove(relationship.InstanceId);
                            }
                            else
                            {
                                var historyRelationship = relationship.CreateHistoryRelationship(OperationRecordType.Delete, 0);
                                changedRelationships.Add(historyRelationship);
                            }
                        }
                        var ships = projectAllRelationShipInCache.Where(d => targetRelDictionary.Keys.Contains(d.InstanceId));
                        Parallel.ForEach(ships, options, item =>
                        {
                            lock (_locker)
                            {
                                if (item != null)
                                {
                                    var historyrelationship = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                    changedRelationships.Add(historyrelationship);
                                }
                            }
                        });
                    }
                    else
                    {
                        bool status = VersionTool.CalculateVersionDatas(projectRepository,
                                                                        domainIds,
                                                                        toVerNo,
                                                                        out targetDatas,
                                                                        out targetRelationships,
                                                                        projectId);
                        if (!status) return null;

                        status = ChangeSetTool.GetChangedSet(currentDatas,
                                                            currentRelationships,
                                                            targetDatas,
                                                            targetRelationships,
                                                            out changedDatas,
                                                            out changedRelationships);

                        if (!status) return null;
                    }

                    Dictionary<long, int?> lockDictionary = new Dictionary<long, int?>();

                    Dictionary<long, string> resourceDictionary = new Dictionary<long, string>();


                    var lockedDatas = projectRepository.LockedComponents.AsNoTracking().ToList();
                    var allResourceClasses = projectRepository.ResourceClasses.AsNoTracking().ToList();
                    Parallel.ForEach(lockedDatas, options, item =>
                    {
                        lock (_locker)
                        {
                            if (!lockDictionary.ContainsKey(item.InstanceId))
                                lockDictionary.Add(item.InstanceId, item.LockUserId);
                        }
                    });
                    Parallel.ForEach(allResourceClasses, options, item =>
                    {
                        lock (_locker)
                        {
                            if (!resourceDictionary.ContainsKey(item.InstanceId))
                            {
                                resourceDictionary.Add(item.InstanceId, item.ResourceName);
                            }
                        }
                    });
                    List<long> curUserLockInstanceIds = new List<long>();
                    List<long> resourceClassIds = projectRepository.ResourceClasses.AsNoTracking().Select(c => c.InstanceId).ToList();
                    foreach (var data in changedDatas)
                    {
                        int? lockstatus;
                        if (lockDictionary.TryGetValue(data.InstanceId, out lockstatus))
                        {
                            lockuserIds.Add(lockstatus);

                            if (lockstatus == mem.ID)
                            {
                                curUserLockInstanceIds.Add(data.InstanceId);
                            }
                            if (lockstatus != mem.ID || resourceClassIds.Contains(data.InstanceId))
                            {
                                exceptCurrentUserIds.Add(lockstatus);
                            }
                        }
                        else
                        {
                            lockuserIds.Add(null);
                            exceptCurrentUserIds.Add(null);
                        }
                        string resourceName = "";
                        if (resourceDictionary.TryGetValue(data.InstanceId, out resourceName))
                        {
                            resourceNames.Add(resourceName);
                        }
                        else
                        {
                            resourceNames.Add("");
                        }
                    }

                    List<long> exceptDownloadIds = curUserLockInstanceIds.Except(resourceClassIds).ToList();
                    List<HistoryData> exceptModeldata = changedDatas.Where(c => exceptDownloadIds.Contains(c.InstanceId)).ToList();
                    relationships = changedRelationships;
                    var ret = changedDatas;

                    //将该用户锁定的非资源类数据排除掉，不返回给客户端
                    if (curUserLockInstanceIds.Any() && !isFirst)
                    {
                        ret = changedDatas.Except(exceptModeldata).ToList();
                        lockuserIds = exceptCurrentUserIds;
                    }

                    var domainList = changedDatas.Select(d => d.Domain).Distinct();
                    //排查问题使用，非参数值所传专业数据被下载
                    //var query = ret.Select(a => !domainIds.Contains(a.Domain)).ToList();
                    updateDomainList = "下载专业列表：" + string.Join(",", domainList);
                    _logger.LogInformation(updateDomainList);

                    return ret;
                }



            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
            }

            return null;
        }


        public override async Task<GetCurrentVersionNoResponse> GetCurrentVersionNo(GetCurrentVersionNoRequest request, ServerCallContext context)
        {
            var x = new GetCurrentVersionNoResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            x.CurrentVersionNo = projectRepo.CurrentVersionNo;
            x.IsSuccess = true;
            return x;
        }


        public override async Task<GetInitialMarkResponse> GetInitialMark(GetInitialMarkRequest request, ServerCallContext context)
        {
            var x = new GetInitialMarkResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            x.HasUploadedData = projectRepo.AllHistoryDatas.Any(dat => dat.Domain == request.Domain);
            x.IsSuccess = true;
            return x;
        }



        public override async Task<DownloadStoreyDataByVersionNoResponse> DownloadStoreyDataByVersionNo(DownloadStoreyDataByVersionNoRequest request, ServerCallContext context)
        {
            var x = new DownloadStoreyDataByVersionNoResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var relationships = new List<HistoryRelationship>();
            var dicStoreyVersion = new Dictionary<int, int>();
            var lockuserIds = new List<int?>();
            List<int?> exceptCurrentUserIds = new List<int?>();
            List<HistoryData> modeldatas = new List<HistoryData>();
            List<HistoryRelationship> changedRelationships = new List<HistoryRelationship>();
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var currentDatas = _mapper.Map<List<ModelData>>(request.CurrentDatas);
            var currentRelationships = _mapper.Map<List<Relationship>>(request.CurrentRelationships);

            if (request.ToVerNo > 0 && request.ToVerNo <= projectRepo.CurrentVersionNo)
            {
                _logger.LogInformation("DownloadStoreyDataByVersionNo:===>toVerNo:" + request.ToVerNo.ToString() + "|CurrentVersionNo:" + projectRepo.CurrentVersionNo);

                Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;

                var mem = projectRepo.GetProjectMember(GetMemberIdByLoginName, currentUser.LoginName);
                var projectAllModelsInCache = new List<ModelData>();
                var projectAllRelationShipInCache = new List<Relationship>();
                if (CacheHelper.IsRuning && CacheHelper.HasMemory)
                {
                    projectAllModelsInCache = CacheHelper.FromCacheModelData(projectId) ?? projectRepo.Datas.ToList();
                    projectAllRelationShipInCache = CacheHelper.FromCacheRelationShip(projectId) ?? projectRepo.Relationships.ToList();
                }
                else
                {
                    projectAllModelsInCache = projectRepo.Datas.ToList();
                    projectAllRelationShipInCache = projectRepo.Relationships.ToList();
                }

                //如果是最新版本直接获取
                if (request.ToVerNo == projectRepo.CurrentVersionNo)
                {
                    _logger.LogInformation("DownloadStoreyDataByVersionNo:开始获取最新数据；dataDomainClassName:" + string.Join(",", request.DataDomainClassName) + "relationDomainClassName:" + string.Join(",", request.RelationDomainClassName));
                    var dataIds = projectAllModelsInCache.Where(d => request.DataDomainClassName.Contains(d.DomainClassName)).Select(d => d.InstanceId).ToList();
                    //ModelQuery modelQuery = new ModelQuery(_modelManager);
                    #region 获得变更的楼层构件信息
                    var hashids = new HashSet<long>(dataIds);
                    var dataIndexes = projectAllModelsInCache
                        .Where(d => hashids.Contains(d.InstanceId))
                        .Select(index =>
                                new
                                {
                                    index.InstanceId,
                                    index.VersionNo
                                }).ToList();

                    var datahashset = new HashSet<long>(dataIndexes.Select(d => d.InstanceId));

                    var targetDataDictionary = new Dictionary<long, int>();

                    Parallel.ForEach(dataIndexes, options, item =>
                    {
                        lock (_locker)
                        {
                            targetDataDictionary.Add(item.InstanceId, item.VersionNo);
                        }
                    });


                    foreach (var data in currentDatas)
                    {
                        if (targetDataDictionary.ContainsKey(data.InstanceId))
                        {
                            int targetVersion;
                            targetDataDictionary.TryGetValue(data.InstanceId, out targetVersion);
                            if (targetVersion != data.VersionNo)
                            {
                                var targetData = projectAllModelsInCache.SingleOrDefault(d => d.InstanceId == data.InstanceId);

                                if (targetData != null)
                                {
                                    var historydata = targetData.CreateHistoryData(OperationRecordType.Modify, targetVersion);
                                    modeldatas.Add(historydata);
                                }
                            }

                            targetDataDictionary.Remove(data.InstanceId);
                        }
                        else
                        {
                            var historydata = data.CreateHistoryData(OperationRecordType.Delete, 0);
                            modeldatas.Add(historydata);
                        }
                    }

                    var modelDatas = projectAllModelsInCache.Where(a => targetDataDictionary.Keys.Contains(a.InstanceId));
                    Parallel.ForEach(modelDatas, options, item =>
                    {
                        lock (_locker)
                        {
                            if (item != null)
                            {
                                var historydata = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                                modeldatas.Add(historydata);
                            }
                        }
                    });
                    #endregion

                    #region 获得变更的关系
                    var relationIndexes = projectAllRelationShipInCache.Where(d => request.RelationDomainClassName.Contains(d.DomainClassName)).Select(index => new
                    {
                        index.InstanceId,
                        index.VersionNo,
                        index.Type,
                        index.SourceID,
                        index.TargetID
                    }).ToList();
                    //var relhashset = new HashSet<long>(relationIndexes);

                    var targetRelDictionary = new Dictionary<long, int>();

                    Parallel.ForEach(relationIndexes, options, item =>
                    {
                        lock (_locker)
                        {
                            targetRelDictionary.Add(item.InstanceId, item.VersionNo);
                        }
                    });

                    //第一次创建联机项目，currentRelationships为null 或者 count=0
                    foreach (var relationship in currentRelationships)
                    {
                        if (targetRelDictionary.ContainsKey(relationship.InstanceId))
                        {
                            int targetVersion;
                            targetRelDictionary.TryGetValue(relationship.InstanceId, out targetVersion);
                            if (targetVersion != relationship.VersionNo)
                            {
                                var targetRelationship = projectAllRelationShipInCache.SingleOrDefault(d => d.InstanceId == relationship.InstanceId);

                                if (targetRelationship != null)
                                {
                                    var relationshipData = targetRelationship.CreateHistoryRelationship(OperationRecordType.Modify, targetVersion);
                                    changedRelationships.Add(relationshipData);
                                }
                            }

                            targetRelDictionary.Remove(relationship.InstanceId);
                        }
                        else
                        {
                            var historyRelationship = relationship.CreateHistoryRelationship(OperationRecordType.Delete, 0);
                            changedRelationships.Add(historyRelationship);
                        }
                    }
                    var ships = projectAllRelationShipInCache.Where(d => targetRelDictionary.Keys.Contains(d.InstanceId));
                    Parallel.ForEach(ships, options, item =>
                    {
                        lock (_locker)
                        {
                            if (item != null)
                            {
                                var historyrelationship = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                changedRelationships.Add(historyrelationship);
                            }
                        }
                    });
                    #endregion
                }
                else
                {
                    var targetDatas = new List<ModelData>();
                    var targetRelationships = new List<Relationship>();
                    bool status = VersionTool.CalculateVersionDatas(projectRepo,
                                                                    request.ToVerNo,
                                                                    out targetDatas,
                                                                    out targetRelationships,
                                                                    projectId);
                    if (!status) return null;
                    var changedDatas = new List<HistoryData>();
                    var targetDatasByStorey = targetDatas.Where(d => request.DataDomainClassName.Contains(d.DomainClassName)).ToList();
                    var targetRelationshipsByStorey = targetRelationships.Where(d => request.RelationDomainClassName.Contains(d.DomainClassName)).ToList();
                    status = ChangeSetTool.GetChangedSet(currentDatas,
                                                        currentRelationships,
                                                        targetDatasByStorey,
                                                        targetRelationshipsByStorey,
                                                        out changedDatas,
                                                        out changedRelationships);
                    modeldatas = changedDatas;

                    if (!status) return null;
                }

                var lockStoreys = projectRepo.StoreyLocks.ToList();
                foreach (var st in lockStoreys)
                {
                    if (!dicStoreyVersion.ContainsKey(st.Domain))
                    {
                        dicStoreyVersion.Add(st.Domain, st.VersionNo);
                    }
                }

                Dictionary<long, int?> lockDictionary = new Dictionary<long, int?>();
                var lockedDatas = projectRepo.LockedComponents.AsNoTracking().ToList();
                Parallel.ForEach(lockedDatas, options, item =>
                {
                    lock (_locker)
                    {
                        if (!lockDictionary.ContainsKey(item.InstanceId))
                            lockDictionary.Add(item.InstanceId, item.LockUserId);
                    }
                });

                List<long> curUserLockInstanceIds = new List<long>();
                foreach (var data in modeldatas)
                {
                    int? lockstatus;
                    if (lockDictionary.TryGetValue(data.InstanceId, out lockstatus))
                    {
                        lockuserIds.Add(lockstatus);

                        if (lockstatus == mem.ID)
                        {
                            curUserLockInstanceIds.Add(data.InstanceId);
                        }
                        if (lockstatus != mem.ID)
                        {
                            exceptCurrentUserIds.Add(lockstatus);
                        }
                    }
                    else
                    {
                        lockuserIds.Add(null);
                        exceptCurrentUserIds.Add(null);
                    }
                }

                List<HistoryData> exceptModeldata = modeldatas.Where(c => curUserLockInstanceIds.Contains(c.InstanceId)).ToList();
                //将该用户锁定的数据排除掉，不返回给客户端
                if (curUserLockInstanceIds.Any() && !request.IsMapping)
                {
                    modeldatas = modeldatas.Except(exceptModeldata).ToList();
                    lockuserIds = exceptCurrentUserIds;
                }
                relationships = changedRelationships;
                _logger.LogInformation("DownloadStoreyDataByVersionNo:modeldatas数量:" + modeldatas.Count);
                var mId = modeldatas.Select(s => s.InstanceId);
                var mCls = modeldatas.Select(s => s.DomainClassName);
                var rId = relationships.Select(r => r.InstanceId);
                var rCls = relationships.Select(r => r.DomainClassName);
                _logger.LogInformation("DownloadStoreyDataByVersionNo:开始获取最新数据；modeldatas:" + string.Join(",", mId) + "|" + string.Join(",", mCls) + "relationships:" + string.Join(",", rId) + "|" + string.Join(",", rCls));

                x.IsSuccess = true;
                x.DicStoreyVersion.Add(dicStoreyVersion);
                x.HistoryDataList.AddRange(_mapper.Map<List<GrpcHistoryData>>(modeldatas));
                x.LockuserIds.AddRange(lockuserIds);
                x.RelationshipList.AddRange(_mapper.Map<List<GrpcHistoryRelationship>>(relationships));

            }

            return x;
        }


        public override async Task<GetUndownloadVersionInfomationResponse> GetUndownloadVersionInfomation(GetUndownloadVersionInfomationRequest request, ServerCallContext context)
        {
            var x = new GetUndownloadVersionInfomationResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            List<int> vList = new List<int>();
            var verInfoList = new List<VersionInformation>();
            var domainVerNoList = new List<GrpcDomainVersions>();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var version = new Infrastructure.ModelDomain.SchemaVersion();
            bool status = projectRepo.GetProjectSchema(out version);

            if (status)
            {
                string verStr = version.MajorVersion + "." + version.MinorVersion + "." +
                                      version.DevelopVersion;
                //根据schema版本号判断使用哪个版本的接口
                if (SCHEMAVERS.Contains(verStr))
                {
                    _logger.LogInformation("GetUndownloadVersionInfomation_2020630:" + SCHEMAVERSION_2020630);
                    verInfoList = GetUndownloadVersionInfomation_2020630(projectRepo, request.DomainIds.ToList(), out domainVerNoList);
                }
                else
                {
                    _logger.LogInformation("GetUndownloadVersionInfomation_Default:" + verStr);
                    verInfoList = GetUndownloadVersionInfomation_Default(projectRepo, request.DomainIds.ToList(), out domainVerNoList);
                }

                x.IsSuccess = true;
                x.DomainVerNoList.AddRange(domainVerNoList);
                x.VersionInformationList.AddRange(_mapper.Map<List<GrpcVersionInformation>>(verInfoList));
            }
            return x;
        }

        //默认版本接口
        private List<VersionInformation> GetUndownloadVersionInfomation_Default(IProjectRepository projectRepository, List<int> domainIds, out List<GrpcDomainVersions> domainVerNoList)
        {
            List<int> vList = new List<int>();
            List<VersionInformation> verInfoList = new List<VersionInformation>();
            domainVerNoList = new List<GrpcDomainVersions>();
            for (int i = 0; i < domainIds.Count; i++)
            {
                GrpcDomainVersions dv = new GrpcDomainVersions();
                int domainId = domainIds[i];
                var domainVerNo = (from vd in projectRepository.VersionDomains
                                   join v in projectRepository.Versions
                                   on vd.VersionNo equals v.VersionNo
                                   where vd.Domain == domainId
                                   orderby vd.VersionNo
                                   select vd.VersionNo
                                  ).ToList();
                vList.AddRange(domainVerNo);

                dv.DomainId = domainId;
                dv.DomainVerNos.AddRange(domainVerNo);
                domainVerNoList.Add(dv);
            }
            var verNoList = new HashSet<int>(vList);
            _logger.LogInformation("GetUndownloadVersionInfomation_Default==>verNoList:" + string.Join(",", verInfoList));
            List<VersionData> rtVersions = new List<VersionData>();
            rtVersions
                    = (from ver in projectRepository.Versions
                       where verNoList.Contains(ver.VersionNo)
                       orderby ver.VersionNo
                       select ver).ToList();

            verInfoList.AddRange(rtVersions);
            return verInfoList;
        }
        //2020630版本使用接口，对应SchemaVersion为1.0.16
        private List<VersionInformation> GetUndownloadVersionInfomation_2020630(IProjectRepository projectRepository, List<int> domainIds, out List<GrpcDomainVersions> domainVerNoList)
        {
            List<int> vList = new List<int>();
            List<VersionInformation> verInfoList = new List<VersionInformation>();
            domainVerNoList = new List<GrpcDomainVersions>();
            for (int i = 0; i < domainIds.Count; i++)
            {
                GrpcDomainVersions dv = new GrpcDomainVersions();
                int domainId = domainIds[i];
                var domainVerNo = (from vd in projectRepository.VersionDomains
                                   join v in projectRepository.Versions
                                   on vd.VersionNo equals v.VersionNo
                                   where vd.Domain == domainId
                                   orderby vd.VersionNo
                                   select vd.VersionNo
                                  ).ToList();
                vList.AddRange(domainVerNo);

                dv.DomainId = domainId;
                dv.DomainVerNos.AddRange(domainVerNo);
                domainVerNoList.Add(dv);
            }
            var verNoList = new HashSet<int>(vList);
            _logger.LogInformation("GetUndownloadVersionInfomation_2020630==>verNoList:" + string.Join(",", verNoList));
            List<VersionData> rtVersions = new List<VersionData>();
            rtVersions
                    = (from ver in projectRepository.Versions
                       where verNoList.Contains(ver.VersionNo)
                       orderby ver.VersionNo
                       select ver).ToList();

            verInfoList.AddRange(rtVersions);
            return verInfoList;
        }


        public override async Task<GetLockedUserIdByInstanceIdResponse> GetLockedUserIdByInstanceId(GetLockedUserIdByInstanceIdRequest request, ServerCallContext context)
        {
            var x = new GetLockedUserIdByInstanceIdResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var y = projectRepo.LockedComponents
                                .Where(a => request.Ids.Contains(a.InstanceId))
                                .Select(b => new
                                {
                                    InstanceId = b.InstanceId,
                                    LockedUserId = b.LockUserId
                                }).ToList();

            y.ForEach(a => x.InstanceIdAsKeyLockedUserIdAsValue.Add(new GrpcKeyValuePair { Key = a.InstanceId, Value = a.LockedUserId }));
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 锁定楼层查看
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetStoreyLockResponse> GetStoreyLock(GetStoreyLockRequest request, ServerCallContext context)
        {
            var x = new GetStoreyLockResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            //  var projectId = await _teamRepository.GetCurrentUserProjectIdAsync(request.SessionId);
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            //if (projectId != Guid.Empty)
            //{
            //    x.Message = $"ProjectId 有误！";
            //    return x;
            //}
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;
            var mem = projectRepo.GetProjectMember(GetMemberIdByLoginName, currentUser.LoginName);
            if (mem == null)
            {
                return x;
            }
            var storeyLocks = projectRepo.StoreyLocks.Where(s => s.LockUserId != null).ToList();

            foreach (var storeyLock in storeyLocks)
            {
                var domainStoreyLock = new GrpcDomainStoreyLock();
                domainStoreyLock.Id = storeyLock.Id;
                domainStoreyLock.Domain = storeyLock.Domain;

                var member = projectRepo.Members.SingleOrDefault(m => m.ID == storeyLock.LockUserId);
                if (null == member)
                {
                    domainStoreyLock.UserName = "未知用户";
                }
                else
                {
                    var teamMember = _teamRepository.Members.SingleOrDefault(m => m.ID == member.TeamMemberID);
                    if (null == teamMember)
                        domainStoreyLock.UserName = "未知用户";
                    else
                        domainStoreyLock.UserName = teamMember.LoginName;
                }

                x.DomainStoreyLockList.Add(domainStoreyLock);
            }
            x.IsSuccess = true;
            return x;
        }


        public override async Task<GetStoreyVersionNoResponse> GetStoreyVersionNo(GetStoreyVersionNoRequest request, ServerCallContext context)
        {
            var x = new GetStoreyVersionNoResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var storeyLocks = projectRepo.StoreyLocks;
            foreach (var sl in storeyLocks)
            {
                if (!x.DicStoreyVersionNo.ContainsKey(sl.Domain))
                {
                    x.DicStoreyVersionNo.Add(sl.Domain, sl.VersionNo);
                }
            }
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetLockModelDatasResponse> GetLockModelDatas(GetLockModelDatasRequest request, ServerCallContext context)
        {
            var x = new GetLockModelDatasResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var retLockDatas = new List<LockedComponents>();
            List<LockedComponents> allLockedDatas = projectRepo.LockedComponents.AsNoTracking().ToList();
            if (null != request.LockInstanceIds && request.LockInstanceIds.Any())
            {
                retLockDatas = allLockedDatas.Where(ld => request.LockInstanceIds.Contains(ld.InstanceId)).ToList();
            }
            else
            {
                retLockDatas = allLockedDatas;
            }


            x.LockedComponents.AddRange(_mapper.Map<List<GrpcLockedComponents>>(retLockDatas));
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetLockIndexResponse> GetLockIndex(GetLockIndexRequest request, ServerCallContext context)
        {
            var x = new GetLockIndexResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var datas = projectRepo.LockedComponents.Where(d => request.Domains.Contains(d.Domain)).OrderBy(prj => prj.InstanceId);
            foreach (var data in datas)
            {
                x.InstanceIdAsKeyLockUserIdAsValue.Add(data.InstanceId, data.LockUserId);
            }
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetStoreyListdDatasResponse> GetStoreyListdDatas(GetStoreyListdDatasRequest request, ServerCallContext context)
        {
            var x = new GetStoreyListdDatasResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var datas = new List<ModelData>();
            if (CacheHelper.IsRuning)
            {
                if (CacheHelper.ProjectInCache(projectId))
                {
                    datas = CacheHelper.FromCacheModelData(projectId).Where(dat => dat.DomainClassName.Contains("StoreyList")).ToList();
                }
                else
                {
                    datas = projectRepo.Datas.Where(dat => dat.DomainClassName.Contains("StoreyList")).ToList();
                }
            }
            else
            {
                datas = projectRepo.Datas.Where(dat => dat.DomainClassName.Contains("StoreyList")).ToList();
            }

            if (datas.Any())
            {
                x.ModelDataList.AddRange(_mapper.Map<List<GrpcModelData>>(datas));
                x.IsSuccess = true;
            }


            return x;
        }

        public override async Task<GetNewestVersionByDomainResponse> GetNewestVersionByDomain(GetNewestVersionByDomainRequest request, ServerCallContext context)
        {
            var x = new GetNewestVersionByDomainResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var otherUVersions = from l in projectRepo.Versions
                                 join d in projectRepo.VersionDomains
                                 on l.VersionNo equals d.VersionNo
                                 where l.IsComplete > 0 && l.Author != currentUser.LoginName
                                 select d;


            foreach (var domain in request.Domains)
            {
                var lastVersion = 0;
                var domainVersion = projectRepo.VersionDomains.Where(vd => vd.Domain == domain);
                if (domainVersion.Any())
                {
                    lastVersion = domainVersion.Max(v => v.VersionNo);
                }

                x.LastVersions.Add(lastVersion);

                var perDomainVersion = 0;
                if (otherUVersions.Any())
                {
                    var versions = otherUVersions.Where(data => data.Domain == domain);
                    if (versions.Any())
                    {
                        perDomainVersion = versions.Max(version => version.VersionNo);

                    }
                    x.PerDomainVersions.Add(perDomainVersion);
                }
                else
                {
                    x.PerDomainVersions.Add(perDomainVersion);
                }
            }
            _logger.LogInformation(currentUser.LoginName + "GetNewestVersionByDomain ==>domain:" + string.Join(",", request.Domains) + "======>perDomainVersions:" + string.Join(",", x.PerDomainVersions) + "=======>lastVersions:" + string.Join(",", x.LastVersions));
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 锁定构件查看
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetLockUserListResponse> GetLockUserList(GetLockUserListRequest request, ServerCallContext context)
        {
            var x = new GetLockUserListResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            //var projectId = await _teamRepository.GetCurrentUserProjectIdAsync(request.SessionId);
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            //if (projectId != Guid.Empty)
            //{
            //    x.Message = $"ProjectId 有误！";
            //    return x;
            //}
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var users = projectRepo.Members;
            foreach (var user in users)
            {
                int count = projectRepo.LockedComponents.Count(dat => dat.LockUserId == user.ID);
                if (count > 0)
                {
                    var member = _teamRepository.Members.SingleOrDefault(mbr => mbr.ID == user.TeamMemberID);

                    if (member != null)
                    {
                        var lockUsers = new GrpcLockUsers();
                        string domainClassDisplayName = "";
                        var query = (from lc in projectRepo.LockedComponents.AsNoTracking() where lc.LockUserId == user.ID select lc).ToList();
                        var lcInstanceID = query.Select(t => t.InstanceId).ToList();
                        var modelLocked = (from ml in projectRepo.Datas.AsNoTracking() where lcInstanceID.Contains(ml.InstanceId) select ml).ToList();
                        var clsID = new HashSet<string>(modelLocked.Select(t => t.DomainClassName).ToList());
                        int realCount = 0;
                        foreach (var item in clsID)
                        {
                            int clsCount = modelLocked.Count(dat => dat.DomainClassName == item);
                            realCount += clsCount;
                            int domainID = modelLocked.FirstOrDefault(m => m.DomainClassName == item).Domain;
                            if (clsCount > 0)
                            {
                                switch (domainID)
                                {
                                    case -1: domainClassDisplayName = ""; break;
                                    case 0: domainClassDisplayName = "通用"; break;
                                    case 1: domainClassDisplayName = "建筑"; break;
                                    case 2: domainClassDisplayName = "结构"; break;
                                    case 3: domainClassDisplayName = "暖通"; break;
                                    case 4: domainClassDisplayName = "给排水"; break;
                                    case 5: domainClassDisplayName = "电气"; break;
                                    case 6: domainClassDisplayName = "装配式"; break;
                                    case 11: domainClassDisplayName = "机电通用"; break;
                                    case 16: domainClassDisplayName = "建筑机电通用"; break;
                                    case 17: domainClassDisplayName = "结构设计"; break;
                                    default: domainClassDisplayName = "其他"; break;
                                }
                                if (member != null)
                                {
                                    var lockUserinfo = new GrpcLockUsers
                                    {
                                        Type = "branch",
                                        Text = member.DisplayName,
                                        Name = member.LoginName,
                                        Expanded = true,
                                        Checked = false,

                                        UserId = user.ID,
                                        UserName = member.LoginName,
                                        Displayname = domainClassDisplayName,
                                        ClassID = domainID,
                                        DomainClassName = item,
                                        ClassName = item,
                                        Count = clsCount
                                    };
                                    lockUsers.Children.Add(lockUserinfo);
                                }

                            }
                        }

                        lockUsers.Type = "root";
                        lockUsers.Text = member.DisplayName;
                        lockUsers.Name = member.LoginName;
                        lockUsers.Expanded = true;
                        lockUsers.Checked = false;

                        lockUsers.UserId = user.ID;
                        lockUsers.Displayname = "";
                        lockUsers.Count = realCount;
                        x.LockUserList.Add(lockUsers);
                        x.IsSuccess = true;
                    }

                }
            }

            return x;
        }

        public override async Task<GetLockedResourceListResponse> GetLockedResourceList(GetLockedResourceListRequest request, ServerCallContext context)
        {
            var x = new GetLockedResourceListResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;
            var mem = projectRepo.GetProjectMember(GetMemberIdByLoginName, currentUser.LoginName);

            var query = (from lc in projectRepo.LockedComponents.AsNoTracking() where lc.LockUserId == mem.ID select lc).ToList();
            var lcInstanceID = query.Select(t => t.InstanceId).ToList();
            var resourceLocked = (from ml in projectRepo.ResourceClasses.AsNoTracking() where lcInstanceID.Contains(ml.InstanceId) select ml).ToList();
            x.LockedResourceList.AddRange(_mapper.Map<List<GrpcResourceClass>>(resourceLocked));
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetVersionInfomationHistoryDataResponse> GetVersionInfomationHistoryData(GetVersionInfomationHistoryDataRequest request, ServerCallContext context)
        {
            var x = new GetVersionInfomationHistoryDataResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var rtDatas = (from hdata in projectRepo.AllHistoryDatas where hdata.VersionNo == request.VersionNo select hdata).ToList();
            Dictionary<string, int> DomainClassIDs = new Dictionary<string, int>();
            HashSet<OperationRecordType> operTypes = new HashSet<OperationRecordType>(rtDatas.Select(r => r.OperationRecordType));
            foreach (var rtdata in rtDatas)
            {
                if (!DomainClassIDs.Keys.Contains(rtdata.DomainClassName))
                {
                    DomainClassIDs.Add(rtdata.DomainClassName, rtdata.Domain);
                }
            }
            foreach (var o in operTypes)
            {
                var hds = new GrpcHistoryDataList();
                string domainDisplayName = "";
                string operStr = "";
                switch (o)
                {
                    case OperationRecordType.Add:
                        operStr = "添加";
                        break;
                    case OperationRecordType.Delete:
                        operStr = "删除";
                        break;
                    case OperationRecordType.Modify:
                        operStr = "修改";
                        break;
                    case OperationRecordType.Unchange:
                        operStr = "无变更";
                        break;
                    default:
                        operStr = "";
                        break;
                }
                foreach (var domainClassID in DomainClassIDs)
                {
                    switch (domainClassID.Value)
                    {
                        case -1: domainDisplayName = ""; break;
                        case 0: domainDisplayName = "通用"; break;
                        case 1: domainDisplayName = "建筑"; break;
                        case 2: domainDisplayName = "结构"; break;
                        case 3: domainDisplayName = "暖通"; break;
                        case 4: domainDisplayName = "给排水"; break;
                        case 5: domainDisplayName = "电气"; break;
                        case 6: domainDisplayName = "装配式"; break;
                        case 11: domainDisplayName = "机电通用"; break;
                        case 16: domainDisplayName = "建筑机电通用"; break;
                        case 17: domainDisplayName = "结构设计"; break;
                        default: domainDisplayName = "其他"; break;
                    }
                    int operNum = (from dom in rtDatas where (dom.DomainClassName == domainClassID.Key && dom.OperationRecordType == o) select dom).ToList().Count;
                    if (operNum > 0)
                    {
                        var hdataSt = new GrpcHistoryDataList
                        {
                            Type = "branch",
                            Text = "",//((int)o).ToString(),
                            Name = "",
                            Leaf = true,
                            Expanded = false,
                            VersionNo = request.VersionNo,
                            Domain = domainClassID.Value,
                            DomainClassName = domainClassID.Key,
                            DomainClassDisplayName = domainClassID.Key,
                            DomainClassNum = operNum,//(from dom in rtDatas where (dom.DomainClassName == domainClassID.Key && dom.OperationRecordType == o) select dom).ToList().Count,
                            Author = domainDisplayName
                        };
                        hds.Children.Add(hdataSt);
                    }

                }
                hds.Type = "root";
                hds.Text = operStr;
                hds.Name = operStr;
                hds.Expanded = true;

                hds.DomainClassNum = (from dom in rtDatas where (dom.OperationRecordType == o) select dom).ToList().Count;
                x.HdList.Add(hds);
            }
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetVersionInformationHistoryRelationshipResponse> GetVersionInformationHistoryRelationship(GetVersionInformationHistoryRelationshipRequest request, ServerCallContext context)
        {
            var x = new GetVersionInformationHistoryRelationshipResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var rtDatas = (from hdata in projectRepo.AllHistoryRelationships where hdata.VersionNo == request.VersionNo select hdata).ToList();
            Dictionary<string, int> DomainClassIDs = new Dictionary<string, int>();
            HashSet<OperationRecordType> operTypes = new HashSet<OperationRecordType>(rtDatas.Select(r => r.OperationRecordType));
            foreach (var rtdata in rtDatas)
            {
                if (!DomainClassIDs.Keys.Contains(rtdata.DomainClassName))
                {
                    DomainClassIDs.Add(rtdata.DomainClassName, (int)rtdata.Type);
                }
            }
            foreach (var o in operTypes)
            {
                var hds = new GrpcHistoryDataList();
                string operStr = "";
                switch (o)
                {
                    case OperationRecordType.Add:
                        operStr = "添加";
                        break;
                    case OperationRecordType.Delete:
                        operStr = "删除";
                        break;
                    case OperationRecordType.Modify:
                        operStr = "修改";
                        break;
                    case OperationRecordType.Unchange:
                        operStr = "无变更";
                        break;
                    default:
                        operStr = "";
                        break;
                }
                foreach (var domainClassID in DomainClassIDs)
                {
                    int operNum = (from dom in rtDatas where (dom.DomainClassName == domainClassID.Key && dom.OperationRecordType == o) select dom).ToList().Count;
                    if (operNum > 0)
                    {
                        var hdataSt = new GrpcHistoryDataList
                        {
                            Type = "branch",
                            Text = "",//((int)o).ToString(),
                            Name = "",
                            Leaf = true,
                            Expanded = false,
                            VersionNo = request.VersionNo,
                            Domain = domainClassID.Value,
                            DomainClassName = domainClassID.Key,
                            DomainClassDisplayName = domainClassID.Key,

                            DomainClassNum = (from dom in rtDatas where (dom.DomainClassName == domainClassID.Key && dom.OperationRecordType == o) select dom).ToList().Count,

                        };
                        hds.Children.Add(hdataSt);
                    }

                }
                hds.Type = "root";
                hds.Text = operStr;
                hds.Name = operStr;
                hds.Expanded = true;

                hds.DomainClassNum = (from dom in rtDatas where (dom.OperationRecordType == o) select dom).ToList().Count;
                x.HdList.Add(hds);
            }

            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetVersionInfomationHistoryResponse> GetVersionInfomationHistory(GetVersionInfomationHistoryRequest request, ServerCallContext context)
        {
            var x = new GetVersionInfomationHistoryResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            IProjectRepository projectRepo = null;
            if (request.EndTime.CompareTo(request.StartTime) < 0)
            {
                var tmp = request.StartTime;
                request.StartTime = request.EndTime;
                request.EndTime = tmp;
            }

            var startTime = request.StartTime.ToDateTime();
            var endTime = request.EndTime.ToDateTime();

            List<VersionData> rtVersions = new List<VersionData>();
            if (!string.IsNullOrEmpty(request.PID))
            {
                projectRepo = _teamRepository.GetProjectRepository(Guid.Parse(request.PID));
            }
            else
            {
                projectRepo = _teamRepository.GetProjectRepository(projectId);
            }
            //找到项目下所有的版本数据
            rtVersions
                    = (from ver in projectRepo.Versions
                       where ver.Time > startTime && ver.Time < endTime
                       orderby ver.Time descending
                       select ver).ToList();//.Skip((page - 1) * limit).Take(limit).ToList();

            if (rtVersions.Count > 0)
            {
                foreach (var data in rtVersions)
                {
                    var history = new GrpcVersionDataHistory();
                    history.VersionNo = data.VersionNo;
                    history.Author = data.Author;
                    history.Description = data.Description;
                    history.Time = Timestamp.FromDateTime(data.Time);
                    history.Domains = "";


                    //找到版本下所有的专业
                    List<int> domains = (from Mdata in projectRepo.VersionDomains
                                         where Mdata.VersionNo == data.VersionNo
                                         select Mdata.Domain).Distinct().ToList();
                    if (domains.Count > 0)
                    {
                        foreach (var dms in domains)
                        {
                            history.Domains += dms.ToString() + "|";
                        }
                    }

                    //找到发布人头像
                    var member = (from m in _teamRepository.Members
                                  where m.LoginName == data.Author
                                  select m).FirstOrDefault();
                    if (member != null)
                    {
                        history.AuthorAvatar = member.Avatar;
                    }
                    x.VersionhistoryList.Add(history);
                }
            }

            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetVersionInfomationResponse> GetVersionInfomation(GetVersionInfomationRequest request, ServerCallContext context)
        {
            var x = new GetVersionInfomationResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            if (request.EndTime.CompareTo(request.StartTime) < 0)
            {
                var tmp = request.StartTime;
                request.StartTime = request.EndTime;
                request.EndTime = tmp;
            }

            var startTime = request.StartTime.ToDateTime();
            var endTime = request.EndTime.ToDateTime();

            var rtVersions
                    = (from ver in projectRepo.Versions
                       where ver.Time > startTime && ver.Time < endTime
                             && (request.Author.Equals(null) || ver.Author == request.Author)
                       select new GrpcVersionInformation
                       {
                           Author = ver.Author,
                           Description = ver.Description,
                           IsComplete = ver.IsComplete,
                           ReleaseType = (GrpcReleaseType)System.Enum.Parse(typeof(GrpcReleaseType), ver.ReleaseType.ToString()), //(GrpcReleaseType)ver.ReleaseType,
                           VersionNo = ver.VersionNo,
                           Time = Timestamp.FromDateTime(ver.Time)
                       }).ToList();

            if (rtVersions.Count > 0)
            {
                foreach (var data in rtVersions)
                {
                    if (data.ReleaseType != request.Type)
                        continue;
                    if (request.Domain != null && !projectRepo.VersionDomains.Any(v => v.VersionNo == data.VersionNo && v.Domain == request.Domain))
                        continue;

                    x.VersionInformationList.Add(data);
                }
            }

            return x;
        }

        public override async Task<CheckSingletonClassResponse> CheckSingletonClass(CheckSingletonClassRequest request, ServerCallContext context)
        {
            var x = new CheckSingletonClassResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var singletonClasses = _mapper.Map<List<SingletonClass>>(request.SingletonClasses);
            bool result = projectRepo.CheckSingletonClass(singletonClasses, out var conflictSingletonClasses);
            x.IsSuccess = result;
            if (result)
            {
                x.ConflictSingletonClasses.AddRange(_mapper.Map<List<GrpcSingletonClass>>(conflictSingletonClasses));
            }
            return x;
        }

        public override async Task<CheckResourceClassByResourceNameResponse> CheckResourceClassByResourceName(CheckResourceClassByResourceNameRequest request, ServerCallContext context)
        {
            var x = new CheckResourceClassByResourceNameResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;

            var mem = projectRepo.GetProjectMember(GetMemberIdByLoginName, currentUser.LoginName);
            List<ModelData> needLockModeldataList = new List<ModelData>();
            List<int?> lockUserIds;
            bool result = projectRepo.CheckResourceClassNew(mem.ID, request.ResourceClasses.ToList(), out var conflictResourceClasses, out needLockModeldataList, out lockUserIds);
            if (result)
            {
                //资源无冲突时，需把要添加的资源类构件锁定
                var lockedComponents = (needLockModeldataList.Select(m => new LockedComponents
                {
                    InstanceId = m.InstanceId,
                    Domain = m.Domain,
                    LockUserId = mem.ID,
                    LockedAt = DateTime.Now
                })).ToList();
                projectRepo.LockDatasNew(mem, lockedComponents);
            }
            else
            {
                //资源类有锁定冲突时，返回锁定用户列表
                var teamMemberIds = projectRepo.Members.Where(member => lockUserIds.Contains(member.ID)).Select(member => member.TeamMemberID).ToList();
                var memberNames = _teamRepository.Members.Where(member => teamMemberIds.Contains(member.ID)).Select(member => member.LoginName).ToList();
                foreach (var name in memberNames)
                {
                    x.ConflictUserNames.Add(name);
                }
            }

            x.IsSuccess = true;
            return x;
        }

        public override async Task<CheckResourceClassConfilictResponse> CheckResourceClassConfilict(CheckResourceClassConfilictRequest request, ServerCallContext context)
        {
            var x = new CheckResourceClassConfilictResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            Func<string, Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;

            var mem = projectRepo.GetProjectMember(GetMemberIdByLoginName, currentUser.LoginName);

            List<ModelData> needLockModeldataList = new List<ModelData>();
            List<int?> lockUserIds;

            var resourceClasses = _mapper.Map<List<ResourceClass>>(request.ResourceClasses);
            bool result = projectRepo.CheckResourceClass(mem.ID, resourceClasses, out var conflictResourceClasses, out needLockModeldataList, out lockUserIds);
            if (result)
            {
                //资源无冲突时，需把要添加的资源类构件锁定
                var lockedComponents = (needLockModeldataList.Select(m => new LockedComponents
                {
                    InstanceId = m.InstanceId,
                    Domain = m.Domain,
                    LockUserId = mem.ID,
                    LockedAt = DateTime.Now
                })).ToList();
                projectRepo.LockDatasNew(mem, lockedComponents);
            }
            else
            {
                //资源类有锁定冲突时，返回锁定用户列表
                var teamMemberIds = projectRepo.Members.Where(member => lockUserIds.Contains(member.ID)).Select(member => member.TeamMemberID).ToList();
                var memberNames = _teamRepository.Members.Where(member => teamMemberIds.Contains(member.ID)).Select(member => member.LoginName).ToList();
                foreach (var name in memberNames)
                {
                    x.ConflictUserNames.Add(name);
                }
            }
            x.ConflictResourceClasses.AddRange(_mapper.Map<List<GrpcResourceClass>>(conflictResourceClasses));
            x.IsSuccess = true;
            return x;

        }

        public override async Task<GetProjectSchemaResponse> GetProjectSchema(GetProjectSchemaRequest request, ServerCallContext context)
        {
            var x = new GetProjectSchemaResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            bool status = projectRepo.GetProjectSchema(out var version);
            x.IsSuccess = status;
            x.Version = _mapper.Map<GrpcSchemaVersion>(version);
            return x;
        }
        private void SplitMappingData(Guid projectId, List<long> hdIdsList,
            int limitCount, List<GrpcOperationRecordType> hdOperTypeList,
            int limitLength, ParallelOptions options,
            out List<GrpcHistoryData> changedDatas,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            //PbimLog.Info("SplitMappingData: 映射下在构件数据 hdIdsList：" + hdIdsList.Count() + "|hdOperTypeList：" + hdOperTypeList.Count());
            changedDatas = new List<GrpcHistoryData>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            //开始构件数据分段
            int realDataLenth = 0;
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            do
            {
                int hdEndSplit = 0;
                List<long> firstHdIdList = new List<long>();
                List<ModelData> firstHdList = new List<ModelData>();
                int minCount = Math.Min(hdIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hdidscount = minCount / j;
                        var hdids = hdIdsList.Take(hdidscount).ToList();
                        HashSet<long> setHdids = new HashSet<long>(hdids);
                        var dataLength = _modelManager.Datas.Where(s => (setHdids.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);
                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hdEndSplit = hdidscount;
                            firstHdIdList = hdids;
                            firstHdList = dataLength;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }

                //PbimLog.Info("SplitMappingData:分段下载构件分段数量hdEndSplit：" + hdEndSplit);
                if (hdEndSplit > 0)
                {
                    //var firstHdIdList = hdIdsList.Take(hdEndSplit).ToList();
                    //var firstHdList = _modelManager.Datas.Where(s => firstHdIdList.Contains(s.Id)).ToList();
                    //List<HistoryData> changes = new List<HistoryData>();
                    //Parallel.ForEach(firstHdList, options, item =>
                    //{
                    //    lock (_locker)
                    //    {
                    //        if (item != null)
                    //        {
                    //            var history = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                    //            changes.Add(history);
                    //        }
                    //    }
                    //});
                    var changes = firstHdList
                        .AsParallel()
                        .WithDegreeOfParallelism(Environment.ProcessorCount)
                        .Select(item =>
                        {
                            lock (_locker)
                            {
                                return item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                            }
                        })
                        .ToList();
                    limitLength = limitLength - realDataLenth;
                    realDataLenth = 0;
                    changedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(changes));
                    hdIdsList.RemoveRange(0, hdEndSplit);
                    hdOperTypeList.RemoveRange(0, hdEndSplit);

                }
            } while (limitLength > 1 * 1024 * 1024 && realDataLenth < limitLength && hdIdsList.Count > 0);

            outhdIdsList = hdIdsList;
            outhdOperTypeList = hdOperTypeList;

            //PbimLog.Info("SplitMappingData:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
        }


        private void SplitMappingDataTemp(Guid projectId, List<long> hdIdsList,
            int limitCount, List<GrpcOperationRecordType> hdOperTypeList,
            int limitLength, ParallelOptions options,
            out List<GrpcHistoryData> changedDatas,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            //PbimLog.Info("SplitMappingData: 映射下在构件数据 hdIdsList：" + hdIdsList.Count() + "|hdOperTypeList：" + hdOperTypeList.Count());
            changedDatas = new List<GrpcHistoryData>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            //开始构件数据分段
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            int FixedBatchSize = 10000;
            int MaxBatchSizeBytes = 500 * 1024 * 1024;
            int skipCount = (int)hdIdsList.FirstOrDefault(); // 记录已跳过数据的位置 
            var validData = new List<ModelData>();

            while (true)
            {
                // 1. 分页查询数据（直接按位置取）
                var batch = _modelManager.Datas
                    .OrderBy(s=>s.Id)
                    .Skip(skipCount)
                    .Take(FixedBatchSize)
                    .ToList();

                if (!batch.Any()) break;

                // 2. 计算本批数据总量 
                int batchSize = batch.Where(d => d.Data != null).Sum(d => d.Data.Length);

                // 3. 容量检查 
                if (validData.Sum(d => d.Data.Length) + batchSize > MaxBatchSizeBytes)
                {
                    // 1. 计算剩余可用容量
                    int remainingSpace = MaxBatchSizeBytes - validData.Sum(d => d.Data.Length);

                    // 2. 预计算可接受的数据量（优化关键点）
                    var acceptableItems = batch
                        .Select(item => new { Item = item, Size = item.Data.Length })
                        .Aggregate(new List<ModelData>(), (list, x) =>
                        {
                            if (list.Sum(i => i.Data.Length) + x.Size <= remainingSpace)
                                list.Add(x.Item);
                            return list;
                        });

                    // 3. 批量添加 
                    validData.AddRange(acceptableItems);
                    skipCount += acceptableItems.Count;
                    break;
                }
                else
                {
                    validData.AddRange(batch);
                    skipCount += batch.Count;
                }
            }

            // 4. 处理结果数据（示例）
            var datachanges = validData
                .AsParallel()
                .WithDegreeOfParallelism(Environment.ProcessorCount)
                .Select(item =>
                {
                    lock (_locker)
                    {
                        return item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                    }
                })
                .ToList();
            changedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(datachanges));
            outhdIdsList.Add(skipCount);

            //outhdIdsList = hdIdsList;
            //outhdOperTypeList = hdOperTypeList;

            _logger.LogInformation("SplitMappingData:本次下载数量："+ changedDatas.Count);
        }


        private void SplitNoMappingData(Guid projectId, List<long> hdIdsList,
            int limitCount, List<GrpcOperationRecordType> hdOperTypeList,
            int limitLength, ParallelOptions options,
            out List<GrpcHistoryData> changedDatas,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            _logger.LogInformation("SplitNoMappingData: 映射下在构件数据 hdIdsList：" + hdIdsList.Count() + "|hdOperTypeList：" + hdOperTypeList.Count());
            changedDatas = new List<GrpcHistoryData>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            //开始构件数据分段
            int realDataLenth = 0;
            do
            {
                int hdEndSplit = 0;
                List<long> firstHdIdList = new List<long>();
                List<HistoryData> firstHdList = new List<HistoryData>();
                int minCount = Math.Min(hdIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hdidscount = minCount / j;
                        var hdids = hdIdsList.Take(hdidscount).ToList();
                        HashSet<long> setHdids = new HashSet<long>(hdids);
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (setHdids.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);
                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hdEndSplit = hdidscount;
                            firstHdIdList = hdids;
                            firstHdList = dataLength;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }

                _logger.LogInformation("SplitNoMappingData:分段下载构件分段数量hdEndSplit：" + hdEndSplit);
                if (hdEndSplit > 0)
                {
                    for (int i = 0; i < firstHdIdList.Count(); i++)
                    {
                        var id = firstHdIdList[i];
                        var change = firstHdList.Where(s => s.Id == id).FirstOrDefault();
                        if (change != null)
                        {
                            change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hdOperTypeList[i].ToString());
                            changedDatas.Add(_mapper.Map<GrpcHistoryData>(change));
                        }
                    }
                    limitLength = limitLength - realDataLenth;
                    realDataLenth = 0;
                    hdIdsList.RemoveRange(0, hdEndSplit);
                    hdOperTypeList.RemoveRange(0, hdEndSplit);

                }
                _logger.LogInformation("SplitNoMappingData:limitLength：" + limitLength);
            } while (limitLength > 1 * 1024 * 1024 && realDataLenth < limitLength && hdIdsList.Count > 0);

            outhdIdsList = hdIdsList;
            outhdOperTypeList = hdOperTypeList;

            _logger.LogInformation("SplitNoMappingData end:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
        }

        private void SplitMappingHRData(Guid projectId, List<long> hrIdsList,
            int limitCount, List<GrpcOperationRecordType> hrOperTypeList,
            int limitLength, ParallelOptions options,
            out List<GrpcHistoryRelationship> changedRelationships,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            //PbimLog.Info("SplitMappingHRData:hrIdsList:" + hrIdsList.Count);
            changedRelationships = new List<GrpcHistoryRelationship>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            //映射下载，关系数据分段处理
            int realDataLenth = 0;
            do
            {
                int hrEndSplit = 0;
                List<long> firstHrIdList = new List<long>();
                List<Relationship> firstHrList = new List<Relationship>();
                int minCount = Math.Min(hrIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hridscount = minCount / j;
                        var hrids = hrIdsList.Take(hridscount).ToList();
                        var dataLength = _modelManager.Relationships
                            //.Where(s => (hrids.Contains(s.Id)))
                            .InRange(s => s.Id, 10000, hrids)
                            .ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hrEndSplit = hridscount;
                            firstHrIdList = hrids;
                            firstHrList = dataLength;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }

                //PbimLog.Info("SplitMappingHRData:分段下载关系分段数量hrEndSplit：" + hrEndSplit);
                if (hrEndSplit > 0)
                {
                    //var firstHrIdList = hrIdsList.Take(hrEndSplit).ToList();
                    //var firstHrList = _modelManager.Relationships.Where(s => firstHrIdList.Contains(s.Id)).ToList();
                    List<HistoryRelationship> changes = new List<HistoryRelationship>();
                    Parallel.ForEach(firstHrList, options, item =>
                    {
                        lock (_locker)
                        {
                            if (item != null)
                            {
                                var history = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                changes.Add(history);
                            }
                        }
                    });
                    limitLength = limitLength - realDataLenth;
                    realDataLenth = 0;
                    changedRelationships.AddRange(_mapper.Map<List<GrpcHistoryRelationship>>(changes));
                    hrIdsList.RemoveRange(0, hrEndSplit);
                    hrOperTypeList.RemoveRange(0, hrEndSplit);

                }

            } while (limitLength > 1 * 1024 * 1024 && realDataLenth < limitLength && hrIdsList.Count > 0);

            outhrIdsList = hrIdsList;
            outhrOperTypeList = hrOperTypeList;

            //PbimLog.Info("SplitMappingHRData:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
        }
        private void SplitNoMappingHRData(Guid projectId, List<long> hrIdsList,
            int limitCount, List<GrpcOperationRecordType> hrOperTypeList,
            int limitLength, ParallelOptions options,
            out List<GrpcHistoryRelationship> changedRelationships,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList)
        {
            //PbimLog PbimLog = new PbimLog("Info");
            //PbimLog.Info("SplitNoMappingHRData:hrIdsList:" + hrIdsList.Count);
            changedRelationships = new List<GrpcHistoryRelationship>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();
            int realDataLenth = 0;
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            do
            {
                int hrEndSplit = 0;
                List<long> firstHrIdList = new List<long>();
                List<HistoryRelationship> firstHrList = new List<HistoryRelationship>();
                int minCount = Math.Min(hrIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hridscount = minCount / j;
                        var hrids = hrIdsList.Take(hridscount).ToList();
                        var dataLength = _modelManager.AllHistoryRelationships
                            //.Where(s => (hrids.Contains(s.Id)))
                            .InRange(s => s.Id, 10000, hrids)
                            .ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hrEndSplit = hridscount;
                            firstHrIdList = hrids;
                            firstHrList = dataLength;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }

                //PbimLog.Info("SplitNoMappingHRData：分段下载关系分段数量hrEndSplit：" + hrEndSplit);
                if (hrEndSplit > 0)
                {
                    for (int i = 0; i < firstHrIdList.Count(); i++)
                    {
                        var id = firstHrIdList[i];
                        var change = firstHrList.Where(s => s.Id == id).FirstOrDefault();
                        if (change != null)
                        {
                            change.OperationRecordType = (OperationRecordType)System.Enum.Parse(typeof(OperationRecordType), hrOperTypeList[i].ToString());
                            changedRelationships.Add(_mapper.Map<GrpcHistoryRelationship>(change));
                        }
                    }
                    limitLength = limitLength - realDataLenth;
                    realDataLenth = 0;
                    hrIdsList.RemoveRange(0, hrEndSplit);
                    hrOperTypeList.RemoveRange(0, hrEndSplit);
                }
            } while (limitLength > 1 * 1024 * 1024 && realDataLenth < limitLength && hrIdsList.Count > 0);
            outhrIdsList = hrIdsList;
            outhrOperTypeList = hrOperTypeList;

            //PbimLog.Info("SplitNoMappingHRData:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
        }
        private bool DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange(Guid projectId, string username,
            int limitCount, int limitLength,
            int selectdVer, int newToVer,
            bool isMapping, List<int> currentUserVersionNoList,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList,
            out List<GrpcHistoryData> changedDatas,
            out List<GrpcHistoryRelationship> changedRelationships
            )
        {
            changedDatas = new List<GrpcHistoryData>();
            changedRelationships = new List<GrpcHistoryRelationship>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();


            List<long> hdIdsList = new List<long>();
            List<OperationRecordType> hdOperTypeList = new List<OperationRecordType>();
            List<long> hrIdsList = new List<long>();
            List<OperationRecordType> hrOperTypeList = new List<OperationRecordType>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            int minCount = 0;
            //PbimLog PbimLog = new PbimLog("Info");
            List<RelationIdInstanceIDOperType> hrIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>();
            HashSet<long> relationshipIds = new HashSet<long>();
            Dictionary<long, List<RelationIdInstanceIDOperType>> dicHrIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            Dictionary<int, int> dicDomainToinversion = new Dictionary<int, int>();

            HashSet<long> hdDomainIds = new HashSet<long>();
            //HashSet<>
            //构件
            Dictionary<long, List<RelationIdInstanceIDOperType>> hdIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            Dictionary<long, List<RelationIdInstanceIDOperType>> delInstanceidOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            Dictionary<long, List<RelationIdInstanceIDOperType>> addInstanceidOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:selectdVer=" + selectdVer.ToString() + "|newToVer=" + newToVer.ToString());
            if (!isMapping)
            {
                //todictionary()
                hdIdInstanceIdOperType = _modelManager.AllHistoryDatas.Where(h => (!currentUserVersionNoList.Contains(h.VersionNo)) && (h.VersionNo > selectdVer && h.VersionNo <= newToVer))
                    .Select(g => (new RelationIdInstanceIDOperType { Id = g.Id, InstanceId = g.InstanceId, OperType = g.OperationRecordType, VersionNo = g.VersionNo }))
                    .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());

            }
            else
            {

                hdIdInstanceIdOperType = _modelManager.AllHistoryDatas.Where(h => (h.VersionNo > 0 && h.VersionNo <= newToVer))
                    .Select(g => (new RelationIdInstanceIDOperType { Id = g.Id, InstanceId = g.InstanceId, OperType = g.OperationRecordType, VersionNo = g.VersionNo }))
                    .ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());
            }
            //var hdInstanceId = hdIdInstanceIdOperType.Select(s => s.InstanceId).ToList();
            //HashSet<long> hashInstanceId = new HashSet<long>(hdInstanceId);
            foreach (var l in hdIdInstanceIdOperType)
            {
                List<RelationIdInstanceIDOperType> hdvalueList = l.Value;
                if (hdvalueList.Count > 1)
                {
                    var maxOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                    var minOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                    if (maxOper != null && minOper != null)
                    {
                        if (maxOper.OperType == OperationRecordType.Delete)
                        {
                            hdIdsList.Add(maxOper.Id);
                            hdOperTypeList.Add(maxOper.OperType);
                        }
                        else if (minOper.OperType == OperationRecordType.Add)
                        {
                            hdIdsList.Add(maxOper.Id);
                            hdOperTypeList.Add(OperationRecordType.Add);
                        }
                        else
                        {
                            hdIdsList.Add(maxOper.Id);
                            hdOperTypeList.Add(OperationRecordType.Modify);
                        }
                    }
                    //hdDomainIds.Add(maxOper.InstanceId);
                }
                else
                {
                    var oper = hdvalueList.FirstOrDefault();
                    if (oper != null)
                    {
                        hdIdsList.Add(oper.Id);
                        hdOperTypeList.Add(oper.OperType);
                    }
                    //hdDomainIds.Add(oper.InstanceId);
                }
                hdDomainIds.Add(l.Key);
            }
            foreach (var l in delInstanceidOperType)
            {
                List<RelationIdInstanceIDOperType> hdvalueList = l.Value;
                var oper = hdvalueList.FirstOrDefault();
                if (oper != null)
                {
                    hdIdsList.Add(oper.Id);
                    hdOperTypeList.Add(oper.OperType);
                }
                hdDomainIds.Add(l.Key);
            }
            foreach (var l in addInstanceidOperType)
            {
                List<RelationIdInstanceIDOperType> hdvalueList = l.Value;
                if (hdvalueList.Count > 1)
                {
                    var maxOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                    var minOper = hdvalueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                    if (maxOper != null && minOper != null)
                    {
                        if (maxOper.OperType == OperationRecordType.Delete)
                        {

                        }
                        else if (minOper.OperType == OperationRecordType.Add)
                        {
                            hdIdsList.Add(maxOper.Id);
                            hdOperTypeList.Add(OperationRecordType.Add);
                            hdDomainIds.Add(l.Key);
                        }
                        else
                        {
                            hdIdsList.Add(maxOper.Id);
                            hdOperTypeList.Add(OperationRecordType.Add);
                            hdDomainIds.Add(l.Key);
                        }
                    }
                }
                else
                {
                    var oper = hdvalueList.FirstOrDefault();
                    if (oper != null)
                    {
                        if (oper.OperType != OperationRecordType.Delete)
                        {
                            hdIdsList.Add(oper.Id);
                            hdOperTypeList.Add(OperationRecordType.Add);
                            hdDomainIds.Add(l.Key);
                        }

                    }
                }

            }

            //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:数据个数=" + hdIdsList.Count);
            //关系
            //避免超过sql语句长度，分每次10000条
            List<RelationIdInstanceIDOperType> AllhrObjects = new List<RelationIdInstanceIDOperType>();
            Dictionary<long, List<RelationIdInstanceIDOperType>> dicInstanceIdType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();



            if (hdDomainIds.Count > limitCount)
            {
                int n = hdDomainIds.Count / limitCount;
                int m = hdDomainIds.Count % limitCount;
                for (int i = 0; i < n; i++)
                {
                    var tempList = hdDomainIds.Skip(i * limitCount).Take(limitCount);
                    List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                    //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                    if (!isMapping)
                    {
                        hrObjects = _modelManager.AllHistoryRelationships.
                            Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                            Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                            .ToList();//.ToList().GroupBy(s=>s.Id).ToDictionary(g=>g.Key,g=>g.ToList());
                    }
                    else
                    {
                        hrObjects = _modelManager.AllHistoryRelationships
                            .Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                            .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                            .ToList();
                        //.ToList().GroupBy(s=>s.Id).ToDictionary(g=>g.Key,g=>g.ToList());
                    }

                    //AllhrObjects.AddRange(hrObjects);
                    foreach (var item in hrObjects)
                    {
                        if (relationshipIds.Add(item.Id))
                        {
                            if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                            {
                                dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                            }
                            else
                            {
                                List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                                v.Add(item);
                                dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                            }
                        }
                    }
                }
                if (m > 0)
                {
                    var tempList = hdDomainIds.Skip(n * limitCount).Take(m);
                    //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                    List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                    if (!isMapping)
                    {
                        hrObjects = _modelManager.AllHistoryRelationships.
                            Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                            Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                            .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                    }
                    else
                    {
                        hrObjects = _modelManager.AllHistoryRelationships
                            .Where(r => (tempList.Contains(r.SourceID) || tempList.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                            .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                            .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                    }
                    foreach (var item in hrObjects)
                    {
                        if (relationshipIds.Add(item.Id))
                        {
                            if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                            {
                                dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                            }
                            else
                            {
                                List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                                v.Add(item);
                                dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                            }
                        }
                    }
                }
            }
            else
            {
                List<RelationIdInstanceIDOperType> hrObjects = new List<RelationIdInstanceIDOperType>();
                //Dictionary<long, List<RelationIdInstanceIDOperType>> hrObjects = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
                if (!isMapping)
                {
                    hrObjects = _modelManager.AllHistoryRelationships.
                        Where(r => (hdDomainIds.Contains(r.SourceID) || hdDomainIds.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer) && (!currentUserVersionNoList.Contains(r.VersionNo))).
                        Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                        .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                }
                else
                {
                    hrObjects = _modelManager.AllHistoryRelationships
                        .Where(r => (hdDomainIds.Contains(r.SourceID) || hdDomainIds.Contains(r.TargetID)) && (r.VersionNo > selectdVer && r.VersionNo <= newToVer))
                        .Select(a => new RelationIdInstanceIDOperType { Id = a.Id, InstanceId = a.InstanceId, OperType = a.OperationRecordType, VersionNo = a.VersionNo })
                        .ToList();//.ToList().GroupBy(s => s.Id).ToDictionary(g => g.Key, g => g.ToList());
                }
                foreach (var item in hrObjects)
                {
                    if (relationshipIds.Add(item.Id))
                    {
                        if (dicHrIdInstanceIdOperType.ContainsKey(item.InstanceId))
                        {
                            dicHrIdInstanceIdOperType[item.InstanceId].Add(item);
                        }
                        else
                        {
                            List<RelationIdInstanceIDOperType> v = new List<RelationIdInstanceIDOperType>();
                            v.Add(item);
                            dicHrIdInstanceIdOperType.Add(item.InstanceId, v);
                        }
                    }
                }
            }
            //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:hdIdsList：" + hdIdsList.Count + "|hdOperTypeList：" + hdOperTypeList.Count);
            //循环专业列表获取数据完毕，开始处理关系数据
            //组装Dictionay instanceid:List<RelationIdInstanceIDOperType>
            //Dictionary<long, List<RelationIdInstanceIDOperType>> dicHrIdInstanceIdOperType = new Dictionary<long, List<RelationIdInstanceIDOperType>>();
            //dicHrIdInstanceIdOperType = hrIdInstanceIdOperType.ToList().GroupBy(s => s.InstanceId).ToDictionary(g => g.Key, g => g.ToList());

            foreach (var l in dicHrIdInstanceIdOperType)
            {
                List<RelationIdInstanceIDOperType> hrValueList = l.Value;
                if (hrValueList.Count > 1)
                {
                    var maxOper = hrValueList.Where(h => h.InstanceId == l.Key).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                    var minOper = hrValueList.Where(h => h.InstanceId == l.Key).OrderBy(d => d.VersionNo).FirstOrDefault();
                    if (maxOper != null && minOper != null)
                    {
                        if (maxOper.OperType == OperationRecordType.Delete)
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add(maxOper.OperType);
                        }
                        else if (minOper.OperType == OperationRecordType.Add)
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add(OperationRecordType.Add);
                        }
                        else
                        {
                            hrIdsList.Add(maxOper.Id);
                            hrOperTypeList.Add(OperationRecordType.Modify);
                        }
                    }
                }
                else
                {
                    var Oper = hrValueList.FirstOrDefault();
                    if (Oper != null)
                    {
                        hrIdsList.Add(Oper.Id);
                        hrOperTypeList.Add(Oper.OperType);
                    }
                }

            }
            //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:hrIdsList：" + hrIdsList.Count() + "|hrOperTypeList：" + hrOperTypeList.Count());
            //开始构件数据分段
            int realDataLenth = 0;
            int limitHDLenth = limitLength;
            do
            {
                List<HistoryData> firstHdList = new List<HistoryData>();
                List<long> firstHdIdList = new List<long>();
                int hdEndSplit = 0;
                minCount = Math.Min(hdIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hdidscount = minCount / j;
                        var hdids = hdIdsList.Take(hdidscount).ToList();
                        HashSet<long> setHdids = new HashSet<long>(hdids);
                        var dataLength = _modelManager.AllHistoryDatas.Where(s => (setHdids.Contains(s.Id))).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitHDLenth)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hdEndSplit = hdidscount;
                            firstHdList = dataLength;
                            firstHdIdList = hdids;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }
                //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:首次构件分段数量hdEndSplit：" + hdEndSplit);
                if (hdEndSplit > 0)
                {
                    for (int i = 0; i < firstHdIdList.Count(); i++)
                    {
                        var id = firstHdIdList[i];
                        var change = firstHdList.Where(s => s.Id == id).FirstOrDefault();
                        if (change != null)
                        {
                            change.OperationRecordType = hdOperTypeList[i];
                            changedDatas.Add(_mapper.Map<GrpcHistoryData>(change));
                        }
                    }
                    hdIdsList.RemoveRange(0, hdEndSplit);
                    hdOperTypeList.RemoveRange(0, hdEndSplit);

                }
                limitHDLenth = limitHDLenth - realDataLenth;
                realDataLenth = 0;
                outhdIdsList = hdIdsList;
                outhdOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hdOperTypeList);
                //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:剩余待下载构件数量：" + hdIdsList.Count());
                //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:limitLength：" + limitHDLenth);
            } while (realDataLenth < limitHDLenth && hdIdsList.Count > 0 && limitHDLenth > 1 * 1024 * 1024);
            //关系数据分段处理
            int limitDRLenth = limitLength;
            int realHRDataLenth = 0;
            do
            {
                List<long> firstHrIdList = new List<long>();
                List<HistoryRelationship> firstHrList = new List<HistoryRelationship>();
                int hrEndSplit = 0;
                minCount = Math.Min(hrIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hridscount = minCount / j;
                        var hrids = hrIdsList.Take(hridscount).ToList();
                        var dataLength = _modelManager.AllHistoryRelationships.InRange(s => s.Id, 10000, hrids).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitDRLenth)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hrEndSplit = hridscount;
                            firstHrIdList = hrids;
                            firstHrList = dataLength;
                            realHRDataLenth = realHRDataLenth + x;
                            go = false;
                        }
                    }
                }
                //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:首次关系分段数量hrEndSplit：" + hrEndSplit);
                if (hrEndSplit > 0)
                {

                    for (int i = 0; i < firstHrIdList.Count(); i++)
                    {
                        var id = firstHrIdList[i];
                        var change = firstHrList.Where(s => s.Id == id).FirstOrDefault();
                        if (change != null)
                        {
                            change.OperationRecordType = hrOperTypeList[i];
                            changedRelationships.Add(_mapper.Map<GrpcHistoryRelationship>(change));
                        }
                    }
                    hrIdsList.RemoveRange(0, hrEndSplit);
                    hrOperTypeList.RemoveRange(0, hrEndSplit);

                }
                limitDRLenth = limitDRLenth - realHRDataLenth;
                realHRDataLenth = 0;
                outhrIdsList = hrIdsList;
                outhrOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hrOperTypeList);
                //PbimLog.Info("DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange:剩余待下载关系数量：" + outhrIdsList.Count());
            } while (realHRDataLenth < limitDRLenth && hrIdsList.Count > 0 && limitDRLenth > 1 * 1024 * 1024);




            return true;
        }
        /// <summary>
        /// 按数据集id列表映射到最新版本，下载构件和关系
        /// </summary>
        /// <param name="limitCount"></param>
        /// <param name="limitLength"></param>
        /// <param name="worksetIdList"></param>
        /// <param name="outhdIdsList"></param>
        /// <param name="outhdOperTypeList"></param>
        /// <param name="outhrIdsList"></param>
        /// <param name="outhrOperTypeList"></param>
        /// <param name="changedDatas"></param>
        /// <param name="changedRelationships"></param>
        /// <returns></returns>
        private bool DownLoadByRepositoryByWorkSetIdList(Guid projectId, int limitCount, int limitLength,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList,
            out List<GrpcHistoryData> changedDatas,
            out List<GrpcHistoryRelationship> changedRelationships)
        {
            changedDatas = new List<GrpcHistoryData>();
            changedRelationships = new List<GrpcHistoryRelationship>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();


            List<long> hdIdsList = new List<long>();
            List<OperationRecordType> hdOperTypeList = new List<OperationRecordType>();
            List<long> hrIdsList = new List<long>();
            List<OperationRecordType> hrOperTypeList = new List<OperationRecordType>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            int minCount = 0;

            //PbimLog PbimLog = new PbimLog("Info");
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            //构件
            //映射到最新版本，无需按专业取数据，直接取全部数据
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:映射下载 第一次 直接从modeldata和relationship表中取数据");
            List<RelationIdInstanceIDOperType> hrIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>();

            List<RelationIdInstanceIDOperType> hdIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>(); //

            hdIdInstanceIdOperType = _modelManager.ModelDataVersionTrackers.Select(
                d => new RelationIdInstanceIDOperType
                {
                    Id = d.DataId,
                    InstanceId = d.InstanceId,
                    VersionNo = d.MaxVersion,
                    OperType = OperationRecordType.Add
                }
                ).ToList();
                
            

            Parallel.ForEach(hdIdInstanceIdOperType, options, item =>
            {
                lock (_locker)
                {
                    if (item != null)
                    {
                        hdIdsList.Add(item.Id);
                        hdOperTypeList.Add(OperationRecordType.Add);
                    }
                }
            });
            outhdIdsList = hdIdsList;
            outhdOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hdOperTypeList);
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:outhdIdsList：" + outhdIdsList.Count() + "|outhdOperTypeList：" + outhdOperTypeList.Count());
            //关系

            hrIdInstanceIdOperType = _modelManager.Relationships
                .GroupBy(p => p.InstanceId)
                .Select(g => new
                {
                    MaxVersion = g.Max(x => x.VersionNo),
                    InstanceId = g.Key
                })
                .Join(_modelManager.Relationships,
                    g => new { g.InstanceId, g.MaxVersion },
                    d => new { d.InstanceId, MaxVersion = d.VersionNo },
                    (g, d) => new RelationIdInstanceIDOperType
                    {
                        Id = d.Id,
                        InstanceId = d.InstanceId,
                        VersionNo = d.VersionNo,
                        OperType = OperationRecordType.Add
                    })
                .ToList();
            Parallel.ForEach(hrIdInstanceIdOperType, options, item =>
            {
                lock (_locker)
                {
                    if (item != null)
                    {
                        hrIdsList.Add(item.Id);
                        hrOperTypeList.Add(OperationRecordType.Add);
                    }
                }
            });
            outhrIdsList = hrIdsList;
            outhrOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hrOperTypeList);
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:outhrIdsList：" + outhrIdsList.Count() + "|outhrOperTypeList：" + outhrOperTypeList.Count());

            //开始构件数据分段
            int realDataLenth = 0;
            int limitHDLenth = limitLength;
            do
            {
                List<long> firstHdIdList = new List<long>();
                List<ModelData> firstHdList = new List<ModelData>();
                int hdEndSplit = 0;
                //取limitecount 与 hdidslist.count min()
                minCount = Math.Min(hdIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hdidscount = minCount / j;
                        var hdids = hdIdsList.Take(hdidscount).ToList();
                        HashSet<long> setHdids = new HashSet<long>(hdids);
                        var dataLength = _modelManager.Datas
                            .Where(s => setHdids.Contains(s.Id))
                            .Select(s => new ModelData 
                            { 
                                Id = s.Id, 
                                InstanceId = s.InstanceId, 
                                Data = s.Data, 
                                DomainClassName = s.DomainClassName,
                                VersionNo = s.VersionNo,
                                ECSchemaName = s.ECSchemaName,
                                StoreyID = s.StoreyID,
                                Domain = s.Domain,
                                StoreyGuid = s.StoreyGuid,
                                LockUserID = s.LockUserID  // 显式包含但不包含导航属性LockUser
                                // 关键：不包含 s.LockUser 导航属性，只包含 s.LockUserID 外键
                            })
                            .ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hdEndSplit = hdidscount;
                            firstHdIdList = hdids;
                            firstHdList = dataLength;
                            realDataLenth = realDataLenth + x;
                            go = false;
                        }
                    }
                }
                if (hdEndSplit > 0)
                {

                    //changedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(datachanges));


                    //List<HistoryData> changes = new List<HistoryData>();
                    //Parallel.ForEach(firstHdList, options, item =>
                    //{
                    //    lock (_locker)
                    //    {
                    //        if (item != null)
                    //        {
                    //            var history = item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                    //            changes.Add(history);
                    //        }
                    //    }
                    //});
                    var changes = firstHdList
                        .AsParallel()
                        .WithDegreeOfParallelism(Environment.ProcessorCount)
                        .Select(item =>
                        {
                            lock (_locker)
                            {
                                return item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                            }
                        })
                        .ToList();
                    limitHDLenth = limitHDLenth - realDataLenth;
                    realDataLenth = 0;
                    changedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(changes));
                    hdIdsList.RemoveRange(0, hdEndSplit);
                    hdOperTypeList.RemoveRange(0, hdEndSplit);
                }

                //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:剩余待下载构件数量：" + hdIdsList.Count());
            } while (limitHDLenth > 1 * 1024 * 1024 && realDataLenth < limitHDLenth && hdIdsList.Count > 0);


            //关系数据分段处理
            int realHRDataLenth = 0;
            int limitHRLenth = limitLength;
            do
            {
                List<long> firstHrIdList = new List<long>();
                List<Relationship> firstHrList = new List<Relationship>();
                int hrEndSplit = 0;
                minCount = Math.Min(hrIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hridscount = minCount / j;
                        var hrids = hrIdsList.Take(hridscount).ToList();
                        var dataLength = _modelManager.Relationships.InRange(s => s.Id, 10000, hrids).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hrEndSplit = hridscount;
                            firstHrIdList = hrids;
                            firstHrList = dataLength;
                            realHRDataLenth = realHRDataLenth + x;
                            go = false;
                        }
                    }
                }

                //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:首次关系分段数量endSplit：" + hrEndSplit);
                if (hrEndSplit > 0)
                {




                    List<HistoryRelationship> changes = new List<HistoryRelationship>();
                    Parallel.ForEach(firstHrList, options, item =>
                    {
                        lock (_locker)
                        {
                            if (item != null)
                            {
                                var history = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                changes.Add(history);
                            }
                        }
                    });
                    limitHRLenth = limitHRLenth = realHRDataLenth;
                    realHRDataLenth = 0;
                    changedRelationships.AddRange(_mapper.Map<List<GrpcHistoryRelationship>>(changes));
                    hrIdsList.RemoveRange(0, hrEndSplit);
                    hrOperTypeList.RemoveRange(0, hrEndSplit);

                }
                //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:剩余待下载关系数量：" + hrIdsList.Count());
            } while (limitHRLenth > 1024 * 1024 && hrIdsList.Count > 0 && realHRDataLenth < limitHRLenth);

            //关系
            return true;
        }


        /// <summary>
        /// 中核效率优化临时函数
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="limitCount"></param>
        /// <param name="limitLength"></param>
        /// <param name="outhdIdsList"></param>
        /// <param name="outhdOperTypeList"></param>
        /// <param name="outhrIdsList"></param>
        /// <param name="outhrOperTypeList"></param>
        /// <param name="changedDatas"></param>
        /// <param name="changedRelationships"></param>
        /// <returns></returns>
        private bool DownLoadByRepositoryByWorkSetIdListTemp(Guid projectId, int limitCount, int limitLength,
            out List<long> outhdIdsList,
            out List<GrpcOperationRecordType> outhdOperTypeList,
            out List<long> outhrIdsList,
            out List<GrpcOperationRecordType> outhrOperTypeList,
            out List<GrpcHistoryData> changedDatas,
            out List<GrpcHistoryRelationship> changedRelationships)
        {
            changedDatas = new List<GrpcHistoryData>();
            changedRelationships = new List<GrpcHistoryRelationship>();
            outhdIdsList = new List<long>();
            outhdOperTypeList = new List<GrpcOperationRecordType>();
            outhrIdsList = new List<long>();
            outhrOperTypeList = new List<GrpcOperationRecordType>();


            List<long> hdIdsList = new List<long>();
            List<OperationRecordType> hdOperTypeList = new List<OperationRecordType>();
            List<long> hrIdsList = new List<long>();
            List<OperationRecordType> hrOperTypeList = new List<OperationRecordType>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            int minCount = 0;

            //PbimLog PbimLog = new PbimLog("Info");
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            //构件
            //映射到最新版本，无需按专业取数据，直接取全部数据
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:映射下载 第一次 直接从modeldata和relationship表中取数据");
            List<RelationIdInstanceIDOperType> hrIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>();

            List<RelationIdInstanceIDOperType> hdIdInstanceIdOperType = new List<RelationIdInstanceIDOperType>(); //


            //hdIdInstanceIdOperType = _modelManager.ModelDataVersionTrackers.Select(
            //    d => new RelationIdInstanceIDOperType
            //    {
            //        Id = d.DataId,
            //        InstanceId = d.InstanceId,
            //        VersionNo = d.MaxVersion,
            //        OperType = OperationRecordType.Add
            //    }
            //    ).ToList();



            //Parallel.ForEach(hdIdInstanceIdOperType, options, item =>
            //{
            //    lock (_locker)
            //    {
            //        if (item != null)
            //        {
            //            hdIdsList.Add(item.Id);
            //            hdOperTypeList.Add(OperationRecordType.Add);
            //        }
            //    }
            //});

            //outhdIdsList = hdIdsList;
            //outhdOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hdOperTypeList);
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:outhdIdsList：" + outhdIdsList.Count() + "|outhdOperTypeList：" + outhdOperTypeList.Count());
            //关系

            hrIdInstanceIdOperType = _modelManager.Relationships
                .GroupBy(p => p.InstanceId)
                .Select(g => new
                {
                    MaxVersion = g.Max(x => x.VersionNo),
                    InstanceId = g.Key
                })
                .Join(_modelManager.Relationships,
                    g => new { g.InstanceId, g.MaxVersion },
                    d => new { d.InstanceId, MaxVersion = d.VersionNo },
                    (g, d) => new RelationIdInstanceIDOperType
                    {
                        Id = d.Id,
                        InstanceId = d.InstanceId,
                        VersionNo = d.VersionNo,
                        OperType = OperationRecordType.Add
                    })
                .ToList();
            Parallel.ForEach(hrIdInstanceIdOperType, options, item =>
            {
                lock (_locker)
                {
                    if (item != null)
                    {
                        hrIdsList.Add(item.Id);
                        hrOperTypeList.Add(OperationRecordType.Add);
                    }
                }
            });
            outhrIdsList = hrIdsList;
            outhrOperTypeList = _mapper.Map<List<GrpcOperationRecordType>>(hrOperTypeList);
            //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:outhrIdsList：" + outhrIdsList.Count() + "|outhrOperTypeList：" + outhrOperTypeList.Count());

            //开始构件数据分段
            //int realDataLenth = 0;
            int limitHDLenth = limitLength;

            // 常量定义 
            int FixedBatchSize = 10000;
            int MaxBatchSizeBytes = 500 * 1024 * 1024;
            int skipCount = 0; // 记录已跳过数据的位置 
            var validData = new List<ModelData>();

            while (true)
            {
                // 1. 分页查询数据（直接按位置取）
                var batch = _modelManager.Datas
                    .OrderBy(d => d.Id)
                    .Skip(skipCount)
                    .Take(FixedBatchSize)
                    .ToList();

                if (!batch.Any()) break;

                // 2. 计算本批数据总量 
                int batchSize = batch.Where(d=>d.Data!=null).Sum(d => d.Data.Length);

                // 3. 容量检查 
                if (validData.Sum(d => d.Data.Length) + batchSize > MaxBatchSizeBytes)
                {
                    // 1. 计算剩余可用容量
                    int remainingSpace = MaxBatchSizeBytes - validData.Sum(d => d.Data.Length);

                    // 2. 预计算可接受的数据量（优化关键点）
                    var acceptableItems = batch
                        .Select(item => new { Item = item, Size = item.Data.Length })
                        .Aggregate(new List<ModelData>(), (list, x) =>
                        {
                            if (list.Sum(i => i.Data.Length) + x.Size <= remainingSpace)
                                list.Add(x.Item);
                            return list;
                        });

                    // 3. 批量添加 
                    validData.AddRange(acceptableItems);
                    skipCount += acceptableItems.Count;
                    break;
                }
                else
                {
                    validData.AddRange(batch);
                    skipCount += batch.Count;
                }
            }

            // 4. 处理结果数据（示例）
            var datachanges = validData
                .AsParallel()
                .WithDegreeOfParallelism(Environment.ProcessorCount)
                .Select(item =>
                {
                    lock (_locker)
                    {
                        return item.CreateHistoryData(OperationRecordType.Add, item.VersionNo);
                    }
                })
                .ToList();
            changedDatas.AddRange(_mapper.Map<List<GrpcHistoryData>>(datachanges));
            outhdIdsList.Add(skipCount);
            _logger.LogInformation("DownLoadByRepositoryByWorkSetIdList：本次下载数量：" + changedDatas.Count());

            //关系数据分段处理
            int realHRDataLenth = 0;
            int limitHRLenth = limitLength;
            do
            {
                List<long> firstHrIdList = new List<long>();
                List<Relationship> firstHrList = new List<Relationship>();
                int hrEndSplit = 0;
                minCount = Math.Min(hrIdsList.Count, limitCount);
                if (minCount > 0)
                {
                    //数量超过10000，必分段
                    //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                    bool go = true;
                    int j = 1;
                    while (go)
                    {
                        int hridscount = minCount / j;
                        var hrids = hrIdsList.Take(hridscount).ToList();
                        var dataLength = _modelManager.Relationships.InRange(s => s.Id, 10000, hrids).ToList();
                        int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                        if (x > limitLength)
                        {
                            j = j * 2;
                        }
                        else
                        {
                            hrEndSplit = hridscount;
                            firstHrIdList = hrids;
                            firstHrList = dataLength;
                            realHRDataLenth = realHRDataLenth + x;
                            go = false;
                        }
                    }
                }

                //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:首次关系分段数量endSplit：" + hrEndSplit);
                if (hrEndSplit > 0)
                {
                    List<HistoryRelationship> changes = new List<HistoryRelationship>();
                    Parallel.ForEach(firstHrList, options, item =>
                    {
                        lock (_locker)
                        {
                            if (item != null)
                            {
                                var history = item.CreateHistoryRelationship(OperationRecordType.Add, item.VersionNo);
                                changes.Add(history);
                            }
                        }
                    });
                    limitHRLenth = limitHRLenth = realHRDataLenth;
                    realHRDataLenth = 0;
                    changedRelationships.AddRange(_mapper.Map<List<GrpcHistoryRelationship>>(changes));
                    hrIdsList.RemoveRange(0, hrEndSplit);
                    hrOperTypeList.RemoveRange(0, hrEndSplit);

                }
                //PbimLog.Info("DownLoadByRepositoryByWorkSetIdList:剩余待下载关系数量：" + hrIdsList.Count());
            } while (limitHRLenth > 1024 * 1024 && hrIdsList.Count > 0 && realHRDataLenth < limitHRLenth);

            //关系
            return true;
        }

        public override async Task<DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersionResponse> DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion(DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.RequestId);
            var x = new DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersionResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            long requestId = request.RequestId;
            var isMapping = request.IsMapping;
            var toDownLoadList = request.ToDownLoadList.ToList();
            string username = currentUser.LoginName;
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var clientVersionList = request.ClientVersionList.ToList();
            int toVerNo = projectRepo.CurrentVersionNo;
            var allVersions = projectRepo.Versions.Select(v => v.VersionNo).ToList();
            var currentUserVersionNoList = clientVersionList;
            _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:currentUserVersionNoList==" + string.Join(",", clientVersionList));
            var downloadVersionList = allVersions.Except(clientVersionList).ToList();
            int localVerNo = 0;
            List<HistoryData> modelList = new List<HistoryData>();
            List<GrpcHistoryData> changedDatas = new List<GrpcHistoryData>();
            List<GrpcHistoryRelationship> changedRelationships = new List<GrpcHistoryRelationship>();
            if (toDownLoadList.Any())
            {
                //构件 
                List<long> hdIdsList = new List<long>();
                hdIdsList = toDownLoadList;
                List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();
                hdOperTypeList = request.ModelOpertypeList.ToList();
                #region  正常流程
                //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion==》正常流程");
                if (isMapping)
                {
                    #region 映射
                    //映射

                    SplitMappingData(projectId, hdIdsList, limitcount, hdOperTypeList, limitLength, options, out changedDatas, out hdIdsList, out hdOperTypeList);
                    //SplitMappingDataTemp(projectId, hdIdsList, limitcount, hdOperTypeList, limitLength, options, out changedDatas, out hdIdsList, out hdOperTypeList);

                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
                    #endregion

                }
                else
                {
                    #region 非映射
                    //开始构件数据分段

                    SplitNoMappingData(projectId, hdIdsList, limitcount, hdOperTypeList, limitLength, options, out changedDatas, out hdIdsList, out hdOperTypeList);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:剩余待下载构件数量hdIdsList：" + hdIdsList.Count());
                    #endregion
                }
                #endregion
                if (hdIdsList.Count > 0)
                {
                    x.ToDownLoadList.AddRange(hdIdsList);
                    x.ModelOpertypeList.AddRange(hdOperTypeList);
                    x.IsSuccess = true;
                }

                //if (changedDatas.Count > 0)
                //{
                //    x.ToDownLoadList.AddRange(hdIdsList);
                //    x.ModelOpertypeList.AddRange(hdOperTypeList);
                //    x.IsSuccess = true;
                //}



                //关系
                List<long> hrIdsList = new List<long>();  //historyrelationship 自增ID
                hrIdsList = request.ToDownLoadHrList.ToList();
                List<GrpcOperationRecordType> hrOperTypeList = new List<GrpcOperationRecordType>();   //historyrelationship操作类型
                hrOperTypeList = request.HrOpertypeList.ToList();
                #region 正常流程
                if (isMapping)
                {
                    #region 映射下载，关系数据分段处理

                    SplitMappingHRData(projectId, hrIdsList, limitcount, hrOperTypeList, limitLength, options, out changedRelationships, out hrIdsList, out hrOperTypeList);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
                    #endregion
                }
                else
                {
                    #region //非映射，关系数据分段处理
                    SplitNoMappingHRData(projectId, hrIdsList, limitcount, hrOperTypeList, limitLength, options, out changedRelationships, out hrIdsList, out hrOperTypeList);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:剩余待下载关系数量hrIdsList：" + hrIdsList.Count());
                    #endregion
                }
                #endregion
                if (hrIdsList.Count > 0)
                {

                    x.ToDownLoadHrList.AddRange(hrIdsList);
                    x.HrOpertypeList.AddRange(hrOperTypeList);
                    //x.IsSuccess = true;
                }
                if (hrIdsList.Count <= 0 && hdIdsList.Count <= 0)
                {
                    requestId = 0;
                }
                //if (hrIdsList.Count <= 0 && changedDatas.Count <= 0)
                //{
                //    requestId = 0;
                //}
                x.IsSuccess = true;
                x.RequestId = requestId;
            }
            else
            {
                //第一次下载的数据

                List<long> hdIdsList = new List<long>(); //historydata 自增ID的list
                List<GrpcOperationRecordType> hdOperTypeList = new List<GrpcOperationRecordType>();  //opertype historydata操作类型 list

                List<long> hrIdsList = new List<long>();  //historyrelationship 自增ID
                List<GrpcOperationRecordType> hrOperTypeList = new List<GrpcOperationRecordType>();   //historyrelationship操作类型

                #region //正常流程
                if (isMapping)
                {

                    //映射到最新版本，无需按专业取数据，直接取全部数据

                    //DownLoadByRepository(limitCount, limitLength, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion  映射下载，参数 :localVerNo=" + localVerNo.ToString() + "|toVerNo=" + toVerNo.ToString() + "|currentUserVersionNoList=" + string.Join(",", currentUserVersionNoList) + "|wsgmulwsidlist=" + string.Join(",", wsgmulwsidlist));
                    DownLoadByRepositoryByWorkSetIdList(projectId, limitcount, limitLength, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    //DownLoadByRepositoryByWorkSetIdListTemp(projectId, limitcount, limitLength, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);

                    if (hdIdsList.Count() <= 0 && hrIdsList.Count <= 0)
                    {
                        requestId = 0;
                    }
                    //if (changedDatas.Count() <= 0 && hrIdsList.Count <= 0)
                    //{
                    //    requestId = 0;
                    //}
                }
                else
                {
                    //不是映射到最新版本
                    //DownLoadByCommonWorkSet(username, limitCount, limitLength, localVerNo, toVerNo, isMapping, currentUserVersionNoList, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion  非映射下载，参数 :localVerNo=" + localVerNo.ToString() + "|toVerNo=" + toVerNo.ToString() + "|currentUserVersionNoList=" + string.Join(",", currentUserVersionNoList) + "|wsgmulwsidlist=" + string.Join(",", wsgmulwsidlist));
                    //DownLoadByCommonWorkSetByWorkSetIdList(username, limitCount, limitLength, localVerNo, toVerNo, isMapping, currentUserVersionNoList,wsgmulwsidlist, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    DownLoadByCommonWorkSetByWorkSetIdListAndWorksetChange(projectId, username, limitcount, limitLength, localVerNo, toVerNo, isMapping, currentUserVersionNoList, out hdIdsList, out hdOperTypeList, out hrIdsList, out hrOperTypeList, out changedDatas, out changedRelationships);
                    //PbimLog.Info("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion不是映射到最新版本 :hdIdsList" + hdIdsList.Count + "|hrIdsList:" + hrIdsList.Count);

                }
                #endregion

                if (hdIdsList.Count > 0)
                {
                    x.ToDownLoadList.AddRange(hdIdsList);
                    x.ModelOpertypeList.AddRange(hdOperTypeList);
                    x.IsSuccess = true;
                }
                if (hrIdsList.Count > 0)
                {

                    x.ToDownLoadHrList.AddRange(hrIdsList);
                    x.HrOpertypeList.AddRange(hrOperTypeList);
                    //x.IsSuccess = true;
                }
                if (hdIdsList.Count() <= 0 && hrIdsList.Count <= 0)
                {
                    requestId = 0;
                }
                var queryNodeDataList = changedDatas.Where(d => d.DomainClassName == "BimBaseTreeNodeData").Select(d => d.InstanceId).ToList();
                var curTP = _teamRepository.Projects.Where(s => s.ID == projectId).FirstOrDefault();
                _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:mPProjectTreeNodes begin:queryNodeDataList:" + queryNodeDataList.Count);
                if (queryNodeDataList.Any())
                {
                    var mainprojectGuid = Guid.Parse(curTP.MainProjectID);
                    var mainManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
                    if (mainManager != null)
                    {
                        List<MPProjectTreeNode> mPProjectTreeNodes = new List<MPProjectTreeNode>();
                        int limitNum = 10000;
                        if (queryNodeDataList.Count > limitNum)
                        {
                            int n = queryNodeDataList.Count / limitNum;
                            int m = queryNodeDataList.Count % limitNum;
                            for (int i = 0; i < n; i++)
                            {
                                var tempList = queryNodeDataList.GetRange(i * limitNum, limitNum);
                                HashSet<long> setqueryNodeDataList = new HashSet<long>(tempList);
                                var rDMs = mainManager.MPProjectTreeNodes.Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList();

                                if (rDMs.Any())
                                {
                                    mPProjectTreeNodes.AddRange(rDMs);
                                }
                            }
                            if (m > 0)
                            {
                                var tempList = queryNodeDataList.GetRange(n * limitNum, m);
                                HashSet<long> setqueryNodeDataList = new HashSet<long>(tempList);
                                var rDMs = mainManager.MPProjectTreeNodes.Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList(); //_modelManager.Datas.Where();

                                if (rDMs.Any())
                                {
                                    mPProjectTreeNodes.AddRange(rDMs);
                                }
                            }
                        }
                        else
                        {
                            //var tempList = sourceIds.GetRange(n * limitNum, m);
                            HashSet<long> setqueryNodeDataList = new HashSet<long>(queryNodeDataList);
                            var rDMs = mainManager.MPProjectTreeNodes.Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList();

                            if (rDMs.Any())
                            {
                                mPProjectTreeNodes.AddRange(rDMs);
                            }
                        }
                        x.MPProjectTreeNodes.AddRange(_mapper.Map<List<GrpcMPProjectTreeNode>>(mPProjectTreeNodes));
                        _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion:mPProjectTreeNodes:" + x.MPProjectTreeNodes.Count);
                    }

                }
                x.IsSuccess = true;
                x.RequestId = requestId;
            }
            x.DownloadVersionList.AddRange(downloadVersionList);
            _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion DownloadVersionList:" + string.Join(",", x.DownloadVersionList));
            if (changedDatas.Any())
            {
                _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion changedatas:" + changedDatas.Count);
                x.ChangedDatas.AddRange(changedDatas);
            }
            if (changedRelationships.Any())
            {
                _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion changedRelationships:" + changedRelationships.Count);
                x.ChangedRelationships.AddRange(changedRelationships);
            }

            if (x.MPProjectTreeNodes.Count > 0)
            {
                var findRootNodeList = x.MPProjectTreeNodes.Where(k => k.BPDataKey == -1).ToList();
                if (findRootNodeList.Count > 0 && changedDatas.Count > 0)
                {
                    foreach (var item in findRootNodeList)
                    {
                        var findRootData = changedDatas.FirstOrDefault(k => k.InstanceId == item.InstanceId);
                        if (findRootData != null)
                        {
                            _logger.LogInformation("DownloadVersionByWorkSetGroupMultipleIdwithTreeNodeExcClientVersion root InstanceId:" + findRootData.InstanceId +" version: " + findRootData.VersionNo);
                        }
                    }
                }
            }

            return x;
        }

        public override async Task<GrpcResult> CheckIsAllVersion(CheckIsAllVersionRequest request, ServerCallContext context)
        {
            var x = new GrpcResult { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (!Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数： {request.SubProjectGuid} 有误！";
                return x;
            }
            var clientVersionList = request.ClientVersionList.ToList();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var allServerVersion = projectRepo.Versions.Select(v => v.VersionNo).ToList();
            var exc = allServerVersion.Except(clientVersionList).ToList();
            if (exc.Any())
            {
                //log.Info("CheckIsAllVersion exc:" + string.Join(",", exc));
                x.IsSuccess = false;
                return x;
            }
            //log.Info("CheckIsAllVersion 客户端已获取全部版本");
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetUndownloadVersionInfomationAllDomainResponse> GetUndownloadVersionInfomationAllDomain(GetUndownloadVersionInfomationAllDomainRequest request, ServerCallContext context)
        {
            var x = new GetUndownloadVersionInfomationAllDomainResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            List<int> vList = new List<int>();
            var verInfoList = new List<VersionInformation>();
            var domainVerNoList = new List<GrpcDomainVersions>();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            List<VersionData> rtVersions = projectRepo.Versions.ToList();

            verInfoList.AddRange(rtVersions);

            x.IsSuccess = true;
            x.VersionInformationList.AddRange(_mapper.Map<List<GrpcVersionInformation>>(verInfoList));
            return x;
        }

        public override async Task<Protos.model.GetVersionDatasResponse> GetVersionDatas(Protos.model.GetVersionDatasRequest request, ServerCallContext context)
        {
            var x = new Protos.model.GetVersionDatasResponse();
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            if (request.EndTime.CompareTo(request.StartTime) < 0)
            {
                var tmp = request.StartTime;
                request.StartTime = request.EndTime;
                request.EndTime = tmp;
            }
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            //找到项目下所有的版本数据
            var rtVersions
                    = (from ver in projectRepo.Versions
                       where ver.Time > request.StartTime.ToDateTime()
                       && ver.Time < request.EndTime.ToDateTime()
                       select ver).ToList();

            x.IsSuccess = true;
            x.VersionDataList.AddRange(_mapper.Map<RepeatedField<GrpcVersionData>>(rtVersions));
            return x;
        }

        public override async Task<GrpcResult> IsNewestVersionCompareOther(IsNewestVersionCompareOtherRequest request, ServerCallContext context)
        {
            var x = new GrpcResult { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var username = currentUser.LoginName;
            var localVersion = request.LocalVersion;
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            var otheruserVersions = _modelManager.Versions.Where(v => v.Author != currentUser.LoginName).ToList();
            var allVersions = _modelManager.Versions.Where(v => v.Author != username).ToList();
            if (allVersions != null && allVersions.Any())
            {
                var lastOtherVersion = allVersions.Max(a => a.VersionNo);
                //PbimLog.Info(username + "其他用户已上传的最大版本号==>" + lastOtherVersion);
                if (lastOtherVersion > localVersion)
                {
                    return x;
                }
                else
                {
                    x.IsSuccess = true;
                    return x;
                }
            }
            else
            {
                x.IsSuccess = true;
                return x;
            }

        }

        /// <summary>
        /// 🔴 原版GetHistoryDataSplit方法 - 保留用于紧急还原
        /// ⚠️ 已停用：存在严重性能问题（重复全表扫描、代码重复、查询效率低）
        /// 📋 替换方案：GetHistoryDataSplitOptimized（已验证完全兼容）
        /// 🔄 还原方法：如需紧急还原，将调用处改回此方法即可
        /// </summary>
        private List<TempClass> GetHistoryDataSplit_BACKUP_FOR_ROLLBACK(Guid projectId, List<long> hdIdsList, int limitNum)
        {
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            List<TempClass> outHistoryList = new List<TempClass>();

            if (hdIdsList.Count > limitNum)
            {
                int n = hdIdsList.Count / limitNum;
                int m = hdIdsList.Count % limitNum;
                for (int i = 0; i < n; i++)
                {
                    var tempList = hdIdsList.GetRange(i * limitNum, limitNum);
                    HashSet<long> setqueryNodeDataList = new HashSet<long>(tempList);
                    var rDMs = _modelManager.AllHistoryDatas
                        .Select(s => new TempClass
                        {
                            InstanceId = s.InstanceId,
                            VersionNo = s.VersionNo,
                            OperType = s.OperationRecordType
                        }).Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList();

                    if (rDMs.Any())
                    {
                        outHistoryList.AddRange(rDMs);
                    }
                }
                if (m > 0)
                {
                    var tempList = hdIdsList.GetRange(n * limitNum, m);
                    HashSet<long> setqueryNodeDataList = new HashSet<long>(tempList);
                    var rDMs = _modelManager.AllHistoryDatas
                        .Select(s => new TempClass
                        {
                            InstanceId = s.InstanceId,
                            VersionNo = s.VersionNo,
                            OperType = s.OperationRecordType
                        })
                        .Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList(); //_modelManager.Datas.Where();

                    if (rDMs.Any())
                    {
                        outHistoryList.AddRange(rDMs);
                    }
                }
            }
            else
            {
                //var tempList = sourceIds.GetRange(n * limitNum, m);
                HashSet<long> setqueryNodeDataList = new HashSet<long>(hdIdsList);
                var rDMs = _modelManager.AllHistoryDatas
                    .Select(s => new TempClass
                    {
                        InstanceId = s.InstanceId,
                        VersionNo = s.VersionNo,
                        OperType = s.OperationRecordType
                    })
                    .Where(s => setqueryNodeDataList.Contains(s.InstanceId)).ToList();

                if (rDMs.Any())
                {
                    outHistoryList.AddRange(rDMs);
                }
            }
            return outHistoryList;
        }

        /// <summary>
        /// 🟢 优化版GetHistoryDataSplit方法 - 解决性能瓶颈
        /// 主要改进：
        /// 1. 一次性查询，避免重复全表扫描
        /// 2. 先过滤再投影，提升查询效率
        /// 3. 使用InRangeFixed扩展方法，支持大量ID查询
        /// 4. 简化代码逻辑，提升可维护性
        /// </summary>
        private List<TempClass> GetHistoryDataSplitOptimized(Guid projectId, List<long> hdIdsList, int limitNum)
        {
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            
            if (!hdIdsList.Any())
            {
                return new List<TempClass>();
            }

            // ✅ 优化1：一次性查询，避免分批重复扫描全表
            // ✅ 优化2：先过滤再投影，减少内存使用
            // ✅ 优化3：使用InRangeFixed处理大量ID
            
            List<TempClass> result;
            
            if (hdIdsList.Count <= limitNum) // 使用传入的limitNum参数，保持与原版逻辑一致
            {
                // 小批量数据：直接使用EF Core的Contains优化
                var hdIdsSet = hdIdsList.ToHashSet(); // 使用HashSet提升Contains性能
                result = _modelManager.AllHistoryDatas
                    .Where(s => hdIdsSet.Contains(s.InstanceId)) // 先过滤
                    .Select(s => new TempClass // 再投影
                    {
                        InstanceId = s.InstanceId,
                        VersionNo = s.VersionNo,
                        OperType = s.OperationRecordType
                    })
                    .ToList();
            }
            else
            {
                // 大批量数据：使用InRangeFixed扩展方法
                result = _modelManager.AllHistoryDatas
                    .InRangeFixed(s => s.InstanceId, limitNum, hdIdsList)
                    .Select(s => new TempClass
                    {
                        InstanceId = s.InstanceId,
                        VersionNo = s.VersionNo,
                        OperType = s.OperationRecordType
                    })
                    .ToList();
            }

            return result;
        }


        public override async Task<GetVersionDataByInstanceListResponse> GetVersionDataByInstanceList(GetVersionDataByInstanceListRequest request, ServerCallContext context)
        {
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();
            var stepStopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            var x = new GetVersionDataByInstanceListResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            // 性能监控点1：用户认证查询
            stepStopwatch.Restart();
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            _logger.LogInformation($"GetVersionDataByInstanceList - 用户认证查询耗时：{stepStopwatch.ElapsedMilliseconds}ms");
            
            if (string.IsNullOrWhiteSpace(request.SubProjectGuid) || !Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数 SubProjectGuid 有误！";
                return x;
            }
            var username = currentUser.LoginName;
            List<CompVersionInfo> versionData = new List<CompVersionInfo>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            
            // 性能监控点2：集合转换
            stepStopwatch.Restart();
            var instanceIdList = request.InstanceIdList.ToList();
            // 删除未使用的HashSet创建，节省内存和CPU
            // HashSet<long> hsidlist = new HashSet<long>(instanceIdList);
            _logger.LogInformation($"GetVersionDataByInstanceList - 输入参数：{request.InstanceIdList.Count}个InstanceID，集合转换耗时：{stepStopwatch.ElapsedMilliseconds}ms");

            // 性能监控点3：历史数据查询
            stepStopwatch.Restart();
            var historyList = GetHistoryDataSplitOptimized(projectId, instanceIdList, 10000);
            _logger.LogInformation($"GetVersionDataByInstanceList - GetHistoryDataSplitOptimized查询耗时：{stepStopwatch.ElapsedMilliseconds}ms，返回{historyList?.Count ?? 0}条历史数据");

            if (historyList == null)
            {
                x.Message = "未找到该构件的版本记录";
                return x;
            }

            // 性能监控点4：版本数据全表查询（可能是最大瓶颈）
            stepStopwatch.Restart();
            var rtVersions = _modelManager.Versions.ToDictionary(g => g.VersionNo, g => g);
            _logger.LogInformation($"GetVersionDataByInstanceList - 版本数据全表查询耗时：{stepStopwatch.ElapsedMilliseconds}ms，加载{rtVersions.Count}个版本到字典");
            
            // 性能优化：注释掉分组查询，因为数据分析显示99.99%的InstanceId都只有一个版本
            // 分组操作耗时350ms但只减少4条数据，性价比极低
            // var retHistoryList = historyList.GroupBy(p => p.InstanceId).SelectMany(g => g.OrderByDescending(x => x.VersionNo).Take(1)).ToList();
            _logger.LogInformation($"GetVersionDataByInstanceList - 跳过分组查询，直接处理{historyList.Count}条历史数据（性能优化：节省约350ms）");
            
            // 性能监控点5：数据处理循环 - 直接使用historyList
            stepStopwatch.Restart();
            int processedCount = 0;
            int validVersionCount = 0;
            int enumParseCount = 0;
            int mapperCount = 0;
            
            foreach (var item in historyList) // 性能优化：直接使用historyList，避免不必要的分组操作
            {
                processedCount++;
                var vdata = new VersionData();
                rtVersions.TryGetValue(item.VersionNo, out vdata);
                if (vdata != null)
                {
                    validVersionCount++;
                    
                    // 性能瓶颈：字符串到枚举转换，每次都要解析
                    enumParseCount++;
                    var operationType = (GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), item.OperType.ToString());
                    
                    // 性能瓶颈：AutoMapper映射
                    mapperCount++;
                    var mappedVersionData = _mapper.Map<GrpcVersionData>(vdata);
                    
                    CompVersionInfo comp = new CompVersionInfo
                    {
                        InstanceId = item.InstanceId,
                        OperationType = operationType,
                        VersionNo = item.VersionNo,
                        VersionData = mappedVersionData
                    };
                    //_logger.LogInformation(currentUser.LoginName + "==>GetVersionDataByInstanceList Versions end" + request.MainprojectGuid+"|instanceid:"+comp.InstanceId+"|ver:"+comp.VersionNo);
                    versionData.Add(comp);
                }
            }
            _logger.LogInformation($"GetVersionDataByInstanceList - 数据处理循环耗时：{stepStopwatch.ElapsedMilliseconds}ms，处理{processedCount}条记录，有效版本{validVersionCount}个，枚举转换{enumParseCount}次，Mapper映射{mapperCount}次");
            
            x.VersionData.AddRange(versionData);
            x.IsSuccess = true;
            
            totalStopwatch.Stop();
            _logger.LogInformation($"GetVersionDataByInstanceList总耗时：{totalStopwatch.ElapsedMilliseconds}ms，输入{request.InstanceIdList.Count}个ID，返回{versionData.Count}条数据");
            
            return x;
        }

        public override async Task<GetVersionDataByInstanceIdResponse> GetVersionDataByInstanceId(GetVersionDataByInstanceIdRequest request, ServerCallContext context)
        {
            var x = new GetVersionDataByInstanceIdResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.SubProjectGuid) || !Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数 SubProjectGuid 有误！";
                return x;
            }
            var username = currentUser.LoginName;
            List<CompVersionInfo> versionData = new List<CompVersionInfo>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            var instanceid = request.Instanceid;
            var historyList = _modelManager.AllHistoryDatas.Where(h => h.InstanceId == instanceid).OrderBy(h => h.VersionNo).ToList();
            if (historyList == null)
            {
                x.Message = "未找到该构件的版本记录";
                return x;
            }
            //找到项目下所有的版本数据
            List<VersionData> rtVersions
                            = _modelManager.Versions.ToList();
            foreach (var item in historyList)
            {
                var vdata = rtVersions.FirstOrDefault(v => v.VersionNo == item.VersionNo);
                if (vdata != null)
                {
                    CompVersionInfo comp = new CompVersionInfo
                    {
                        InstanceId = item.InstanceId,
                        OperationType = (GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), item.OperationRecordType.ToString()),
                        VersionNo = item.VersionNo,
                        VersionData = _mapper.Map<GrpcVersionData>(vdata)
                    };
                    versionData.Add(comp);
                }
            }
            x.VersionData.AddRange(versionData);
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetLockedInfoResponse> GetLockedInfo(GetLockedInfoRequest request, ServerCallContext context)
        {
            var x = new GetLockedInfoResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.SubProjectGuid) || !Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数 SubProjectGuid 有误！";
                return x;
            }
            var username = currentUser.LoginName;
            List<LockCompInfo> result = new List<LockCompInfo>();
            List<LockedComponents> alllockedList = new List<LockedComponents>();
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            alllockedList = _modelManager.LockedComponents.ToList();
            var allteammemberlist = _teamRepository.Members.ToDictionary(g => g.ID, g => g);
            var memberlist = _modelManager.Members.ToDictionary(g => g.ID, g => g);

            foreach (var l in alllockedList)
            {
                ProjectMember mem = new ProjectMember();
                memberlist.TryGetValue(l.LockUserId, out mem);
                if (mem != null)
                {
                    TeamMember teammember = new TeamMember();
                    allteammemberlist.TryGetValue(mem.TeamMemberID, out teammember);
                    if (teammember != null)
                    {
                        var ret = new LockCompInfo
                        {
                            InstanceId = l.InstanceId,
                            MemberId = teammember.ID.ToString(),
                            LockType = l.LockType
                        };
                        result.Add(ret);
                    }
                }
            }
            x.IsSuccess = true;
            x.LockInfoList.Add(result);
            return x;
        }


        public override async Task<GetDomainVersionsResponse> GetDomainVersions(GetDomainVersionsRequest request, ServerCallContext context)
        {
            var x = new GetDomainVersionsResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.SubProjectGuid) || !Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数 SubProjectGuid 有误！";
                return x;
            }
            List<int> vList = new List<int>();
            var domainVerNoList = new List<GrpcDomainVersions>();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            var domainIds = new List<int>();
            foreach (var domain in domainIds)
            {
                GrpcDomainVersions dv = new GrpcDomainVersions();
                var domainVerNo = projectRepo.VersionDomains.Where(vd => vd.Domain == domain).Select(v => v.VersionNo).ToList();

                dv.DomainId = domain;
                dv.DomainVerNos.AddRange(domainVerNo);
                domainVerNoList.Add(dv);
            }
            x.IsSuccess = true;
            x.DomainVerNoList.AddRange(_mapper.Map<List<GrpcDomainVersions>>(domainVerNoList));
            return x;
        }

        public override async Task<GetCreaterByInstanceIdsResponse> GetCreaterByInstanceIds(GetCreaterByInstanceIdsRequest request, ServerCallContext context)
        {
            var x = new GetCreaterByInstanceIdsResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.SubProjectGuid) || !Guid.TryParse(request.SubProjectGuid, out var projectId))
            {
                x.Message = $"参数 SubProjectGuid 有误！";
                return x;
            }
            List<int> vList = new List<int>();
            var domainVerNoList = new List<GrpcDomainVersions>();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var instanceIds = request.InstanceIds.ToList();
            
            Dictionary<long, int> temp;
            if (instanceIds.Count <= 10000) // 使用与InRange调用一致的limitNum值
            {
                // 小批量：使用EF Core的Contains优化
                var instanceIdsSet = instanceIds.ToHashSet();
                temp = projectRepo.AllHistoryDatas
                    .Where(s => s.OperationRecordType == OperationRecordType.Add && instanceIdsSet.Contains(s.InstanceId))
                    .ToList()
                    .GroupBy(s => s.InstanceId)
                    .ToDictionary(g => g.Key, g => g.FirstOrDefault().VersionNo);
            }
            else
            {
                // 大批量：使用InRangeFixed
                temp = projectRepo.AllHistoryDatas
                    .Where(s => s.OperationRecordType == OperationRecordType.Add)
                    .InRangeFixed(s => s.InstanceId, 10000, instanceIds)
                    .ToList()
                    .GroupBy(s => s.InstanceId)
                    .ToDictionary(g => g.Key, g => g.FirstOrDefault().VersionNo);
            }

            Dictionary<long, string> createrUserId = temp.Join(projectRepo.Versions, h => h.Value, v => v.VersionNo, (h, v) => new { instanceid = h.Key, createuserid = v.Author }).ToDictionary(g => g.instanceid, g => g.createuserid);
            x.IsSuccess = true;
            x.CreaterUserId.Add(createrUserId);
            return x;
        }
        /// <summary>
        /// 构件统计
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetDomainCountResponse> GetDomainCount(GetDomainCountRequest request, ServerCallContext context)
        {
            var x = new GetDomainCountResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            string username = string.Empty;
            username = currentUser.LoginName;
            //var projectId = await _teamRepository.GetCurrentUserProjectIdAsync(request.SessionId);
            //if (projectId != Guid.Empty)
            //{
            //    x.Message = $"ProjectId 有误！";
            //    return x;
            //}
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var _modelManager = _teamRepository.GetProjectRepository(projectId);
            var domainCls = _modelManager.Datas.ToList().GroupBy(d => new { d.DomainClassName, d.Domain }).Select(g => (new GrpcDomainModelCount { DCName = g.Key.DomainClassName, DomainId = g.Key.Domain, InstanceCount = g.Count() })).ToList();
            x.GrpcDomainModelCount.AddRange(domainCls);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 网页端操作历史，包含文件级协同和构件协同，文件级协同显示是否里程碑版本
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>

        public override async Task<GetVersionInfomationHistoryForWebResponse> GetVersionInfomationHistoryForWeb(GetVersionInfomationHistoryForWebRequest request, ServerCallContext context)
        {
            var x = new GetVersionInfomationHistoryForWebResponse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.PID) || !Guid.TryParse(request.PID, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            IProjectRepository projectRepo = null;
            if (request.EndTime.CompareTo(request.StartTime) < 0)
            {
                var tmp = request.StartTime;
                request.StartTime = request.EndTime;
                request.EndTime = tmp;
            }
            var startTime = request.StartTime.ToDateTime();
            var endTime = request.EndTime.ToDateTime();
            List<VersionData> rtVersions = new List<VersionData>();
            if (!string.IsNullOrEmpty(request.PID))
            {
                projectRepo = _teamRepository.GetProjectRepository(Guid.Parse(request.PID));
            }
            else
            {
                projectRepo = _teamRepository.GetProjectRepository(projectId);
            }

            List<GrpcVersionDataHistoryForAllType> versionhistory = new List<GrpcVersionDataHistoryForAllType>();

            var tp = _teamRepository.Projects.FirstOrDefault(t => t.ID == Guid.Parse(request.PID));
            //找到项目下所有的版本数据
            rtVersions
                    = (from ver in projectRepo.Versions
                       where ver.Time > startTime && ver.Time < endTime
                       orderby ver.Time descending
                       select ver).ToList();//.Skip((page - 1) * limit).Take(limit).ToList();
            if (rtVersions.Count > 0)
            {
                foreach (var data in rtVersions)
                {
                    GrpcVersionDataHistoryForAllType history = new GrpcVersionDataHistoryForAllType();
                    history.VersionNo = data.VersionNo;
                    history.Author = data.Author;
                    history.Description = data.Description;
                    history.Time = Timestamp.FromDateTime(data.Time);
                    history.Domains = "";
                    history.Type = tp.ProjectType;

                    if (tp.ProjectType == 0)
                    {
                        history.IsMileStoneVersion = 0;
                    }
                    else
                    {
                        //文件级协同，查找里程碑版本
                        var MfVersion = projectRepo.MilestoneFiles.FirstOrDefault(m => m.VersionNo == data.ModelFileVerNo);
                        if (MfVersion != null)
                        {
                            history.IsMileStoneVersion = 1;
                        }
                        else
                        {
                            history.IsMileStoneVersion = 0;
                        }
                    }

                    //找到版本下所有的专业
                    List<int> domains = (from Mdata in projectRepo.VersionDomains
                                         where Mdata.VersionNo == data.VersionNo
                                         select Mdata.Domain).Distinct().ToList();
                    if (domains.Count > 0)
                    {
                        foreach (var dms in domains)
                        {
                            history.Domains += dms.ToString() + "|";
                        }
                    }

                    //找到发布人头像
                    var member = (from m in _teamRepository.Members
                                  where m.LoginName == data.Author
                                  select m).FirstOrDefault();
                    if (member != null)
                    {
                        history.AuthorAvatar = member.Avatar;
                    }
                    x.GrpcVersionDataHistoryForAllType.Add(history);
                    versionhistory.Add(history);
                }
            }
            return x;
        }

        /// <summary>
        /// 根据ClassName排序
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcDomainClassNameCountResonse> GetDomainClassNameCount(GrpcDomainClassNameCountRequest request, ServerCallContext context)
        {
            var x = new GrpcDomainClassNameCountResonse { IsSuccess = false, Message = string.Empty };
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            try
            {
            IProjectRepository projectRepo = _teamRepository.GetProjectRepository(projectId);

                // 使用汇总表直接查询，避免group by操作
                var data = await projectRepo.ModelDatasDomainClassSummaries
                    .Select(s => new GrpcDomainClassNameCount
            {
                        DomainClassName = s.DomainClassName,
                        Count = s.Count
                    })
                    .OrderBy(x => x.DomainClassName)
                    .ToListAsync();

                x.Data.AddRange(data);
            x.IsSuccess = true;
            x.Message = "获取成功";
            }
            catch (Exception ex)
            {
                x.Message = $"查询失败: {ex.Message}";
                _logger.LogError(ex, "GetDomainClassNameCount查询失败，ProjectId: {ProjectId}", projectId);
            }
                        
            return x;
        }
    }
}
