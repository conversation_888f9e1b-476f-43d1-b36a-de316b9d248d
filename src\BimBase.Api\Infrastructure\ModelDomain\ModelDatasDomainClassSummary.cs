using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// 模型数据域类名统计表
    /// </summary>
    [Table("modeldatas_domain_class_summary")]
    public class ModelDatasDomainClassSummary
    {
        /// <summary>
        /// 域类名
        /// </summary>
        [Key]
        [Column("DomainClassName")]
        [StringLength(190)]
        public string DomainClassName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Column("cnt")]
        public int Count { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; }

        public ModelDatasDomainClassSummary()
        {
            
        }
    }
} 