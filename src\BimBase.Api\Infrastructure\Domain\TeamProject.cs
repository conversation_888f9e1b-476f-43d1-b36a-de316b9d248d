﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    public class TeamProject
    {
        public TeamProject()
        {
            ID = Guid.NewGuid();
            EnableAuthority = false;
        }
        /// <summary>
        /// 项目的唯一标识
        /// </summary>
        /// <remarks>以此GUID确认本地的项目数据是否从属于服务器上的特定项目。</remarks>
        [Key]
        
        public Guid ID { get; set; }
        /// <summary>
        /// 用于让用户识别项目的名字
        /// </summary>
        
        public string Name
        {
            get;
            set;
        }

        /// <summary>
        /// 项目的详细描述信息
        /// </summary>
        
        public String Description
        {
            get;
            set;
        }

        /// <summary>
        /// 负责人
        /// </summary>
        
        public string Leader
        {
            get;
            set;
        }

        
        public String Avatar
        {
            get;
            set;
        }

        
        public DateTime StartTime
        {
            get;
            set;
        }

        
        public DateTime EndTime
        {
            get;
            set;
        }
        /// <summary>
        /// 项目进度描述
        /// </summary>
        
        public String Progress
        {
            get;
            set;
        }

        /// <summary>
        /// 拓展属性
        /// </summary>
        
        public String ExtendProperty
        {
            get;
            set;
        }

        
        public DateTime CreationTime
        {
            get;
            set;
        }

        
        public bool EnableAuthority
        {
            get;
            set;
        }

        
        public string FilePath { get; set; }


        
        public string ParentProjectID { get; set; }


        /// <summary>
        /// 所属项目id
        /// </summary>
        
        public string MainProjectID { get; set; }
        /// <summary>
        /// 所属文件目录id
        /// </summary>
        
        public string FileDirectoryID { get; set; }

        
        public string CreateUser { get; set; }

        
        public int RoleGroupId { get; set; }

        
        public string RoleString { get; set; }
        /// <summary>
        /// 0:构件级模型  1:文件级协同
        /// </summary>
        
        public int ProjectType { get; set; }
        /// <summary>
        /// 罗列模型参与者，只读，只能通过ITeamMember对象修改其参与的项目。
        /// </summary>
        //virtual public List<TeamMember> Members { get; set; }

    }

    public class commonfiles
    {
        [Key]
        public Int32 Id { get; set; }

        public string FileName { get; set; }

        public int VersionNo { get; set; }

        public string SavePath { get; set; }

        public long FileSize { get; set; }

        public DateTime Time { get; set; }

        public string Description { get; set; }

        public string FileType { get; set; }
    }

    public class cloudlinkfiles
    {
        [Key]
        public Int32 Id { get; set; }

        public string ProjectId { get; set; }

        public string ProjectName { get; set; }

        public string FileName { get; set; }

        public int VersionNo { get; set; }

        public string SavePath { get; set; }

        public long FileSize { get; set; }

        public DateTime UploadTime { get; set; }

        public string Description { get; set; }

        public string FileType { get; set; }

        public string UserName { get; set; }
    }

    public class IpMap
    {
        [Key]
        public int Id { get; set; }
        public string Address { get; set; }
        public string Ip { get; set; }
        public int Port { get; set; }


    }

    /// <summary>
    /// 卷册图档类
    /// </summary>
    public class Volume
    {
        public Volume()
        {
            CreateTime = DateTime.Now;
        }

        /// <summary>
        /// 主键Guid
        /// </summary>
        [Key]
        public Guid VolumeId { get; set; }

        /// <summary>
        /// 卷册图档名称
        /// </summary>
        public string VolumeName { get; set; }

        /// <summary>
        /// 所属项目id
        /// </summary>
        public Guid MainProjectId { get; set; }

        /// <summary>
        /// 文件夹目录id
        /// </summary>
        public Guid FileDirectoryId { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        public string CreateUser { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 预留扩展字段
        /// </summary>
        public string ExtendStr { get; set; }

    }


    /// <summary>
    /// 卷册图档版本记录表
    /// </summary>
    public class VolumeVersion
    {
         
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        /// <summary>
        /// 卷册Guid
        /// </summary>
         
        public Guid VolumeId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
         
        public string FileName { get; set; }


        /// <summary>
        /// 版本描述
        /// </summary>
         
        public string Description { get; set; }

        /// <summary>
        /// 存储路径
        /// </summary>
         
        public string SavePath { get; set; }

        /// <summary>
        /// 文件类型（扩展名）
        /// </summary>
         
        public string FileType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
         
        public long FileSize { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
         
        public int VerNo { get; set; }
        /// <summary>
        /// 版本上传者
        /// </summary>
         
        public string UploadUser { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
         
        public DateTime UploadTime { get; set; }
        /// <summary>
        /// 预留扩展字段
        /// </summary>
         
        public string ExtendStr { get; set; }
    }

    public class TeamProjectModelLock
    {
        [Key]
        public int Id { get; set; }

         
        public Guid ProjectGuid { get; set; }

         
        public string ModelLockUser { get; set; }
    }
    [XmlRoot(Namespace = "")]
    public class RepositoryInformation
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public long RepId { get; set; }

         
        public long value { get; set; }
    }
    [XmlRoot(Namespace = "")]
    public class MainProjectUserGroupMember
    {
        [Key]
        public long Id { get; set; }
         
        public Guid MainProjectId { get; set; }

        /// <summary>
        /// 用户组ID
        /// </summary>
         
        public int UserGroupId { get; set; }
        /// <summary>
        /// 用户Guid
        /// </summary>
         
        public Guid TeamMemberId { get; set; }
        /// <summary>
        /// 项目中用户组名称
        /// </summary>
        [DataMember]
        public string UserGroupName { get; set; }
    }


    public class CacheMainproject
    {
        [Key]
        public long Id { get; set; }
         
        public Guid MainprojectId { get; set; }

         
        public DateTime CacheTime { get; set; }
    }
}
