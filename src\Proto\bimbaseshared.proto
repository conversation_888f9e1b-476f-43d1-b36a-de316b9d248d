syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

package bimbase.api;

message SessionRequest{
	string sessionId = 1;
}

message GrpcResult{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	google.protobuf.Any result = 4;
}


message RoleDto{
	string id = 1;
	string name = 2;
	int32 status = 3;
	int32 type = 4;
}


message AuthInfoDto{
	string authId = 1;
	string parentId = 2;
	string name = 3;
	string code = 4;
	string type = 5;
	string permission = 6;
}


enum TemplateType
{
    TEAMROLE = 0;
    TEAMMEMBER = 1;
    PROJECTROLE = 2;
    PROJECTMEMBER = 3;
    DOMAIN = 4;
    DOMIANCLASS = 5;
    RELEASE = 6;
    WORKGROUP = 7;
}

//初始化项目时使用的模板配置
message TemplateForInit{
	string teamRole = 1;
	string teamMember = 2;
	string projectRole = 3;
	string projectMember = 4;
	string releaseinformation = 5;
	string workGroup = 6;
	string domainClass = 7;
}


//目录下存在的所有模板配置名称的列表
message TemplateCacheList{
	repeated string teamRoleNames = 1;
	repeated string teamMemberNames = 2;
	repeated string projectRoleNames = 3;
	repeated string projectMemberNames = 4;
	repeated string releaseNames = 5;
	repeated string workGroups = 6;
	repeated string domainClassNames = 7;
	repeated string mainProjectMemberAndPermissionNames = 8;
}


message GrpcTeamMember{
	string id = 1;
	string loginName = 2;
	string passwordMD5 = 3;
	string displayName = 4;
	int32 color = 5;
	string email = 6;
	string department = 7;
	string avatar = 8;
	string createId = 9;
	bytes avatarData = 10;
	string avatarExtType = 11;
	//repeated GrpcTeamProject projects = 12;
}
message GrpcTeamProject{
	string id = 1;
	string name = 2;
	string description = 3;
	string leader = 4;
	string avatar = 5;
	google.protobuf.Timestamp startTime = 6;
	google.protobuf.Timestamp endTime = 7;
	string progress = 8;
	string ExtendProperty = 9;
	google.protobuf.Timestamp creationTime = 10;
	bool enableAuthority = 11;
	//repeated GrpcTeamMember Members = 12;
	string filePath = 12;
	string parentProjectId = 13;
	string mainProjectId = 14;
	string fileDirectoryID = 15;
	string createUser = 16;
	int32 projectType = 17;
}
message GrpcTeamAuth{
	int32 id = 1;
	string authName = 2;
	int32 authType = 3;
	int32 permission = 4;
}
message GrpcTeamGroupAuth{
	int32 id = 1;
	string groupOrMemberId = 2;
	int32 isGroupOrTeamMember = 3;
	string mainProjectId = 4;
	int32 objectType = 5;
	string objectId = 6;
}
message GrpcTeamUserGroup{
	int32 id = 1;
	string teamMemberId = 2;
	int32 groupId = 3;    
}
message GrpcMainProject{
	string id = 1;
	string name = 2;
	string description = 3;
	google.protobuf.Timestamp creationTime = 4;
	string createUser = 5;
	int32 roleGroupId = 6;
	string roleString = 7;
	google.protobuf.Timestamp lastUpdateTime = 8;
	bytes extendData = 9;
	string extendStr = 10;
	int32 mainProjectType = 11;
	string shortName = 12;
	string designStage = 13;
	string engineerType = 14;
	int32 isDelete = 15;
}

message GrpcVolume{
	string VolumeId = 1;
	string VolumeName = 2;
	string MainProjectId = 3;
	string FileDirectoryId = 4;
	string CreateUser = 5;
	google.protobuf.Timestamp CreateTime = 6;
	string ExtendStr = 7;
}

message GrpcTeamGroup{
	int32 id = 1;
	string groupName = 2;
	string description = 3;
}
enum GrpcReleaseType{
	None = 0;
	MileStone = 1;
}

message GrpcVersionData{
	int32 versionNo = 1;
	string author = 2;
	string description = 3;
	google.protobuf.Timestamp time = 4;
	GrpcReleaseType releaseType = 5;
	int32 isComplete = 6;
}


message GrpcSchemaVersion{
	int32 id = 1;
	int32 majorVersion = 2;
	int32 minorVersion = 3;
	int32 developVersion = 4;
	string sdkVersion = 5;
	bytes schemaData = 6;
}


message GrpcProjectMember{
	int32 id = 1;
	string teamMemberGuid = 2;
	string loginName = 3;
	string displayName = 4;
	int32 color = 5;
	string avatar = 6;
	int32 isProjectAdmin = 7;
}


message GrpcModelData{
	int64 instanceId = 1;
	string domainClassName = 2;
	string eCSchemaName = 3;
	int64 storeyID = 4;
	int32 domain = 5;
	bytes data = 6;
	int32 versionNo = 7;
	string storeyGuid = 8;
	google.protobuf.Int32Value LockUserID = 9;
}
message GrpcMPUserGroupAuth{
	int32 id = 1;
	string GroupOrMemberId = 2;
	int32 IsGroupOrTeamMember = 3;
	string AuthInfo = 4;
	int32 ObjectType = 5;
	string ObjectId = 6;
	int64 InstanceId = 7;
	int64 TreeId = 8;
}
enum GrpcLibType{
	/// <summary>
    /// 元件类型等级库
    /// </summary>
    enCatalog_Lib = 0;
    /// <summary>
    /// 参数化构件库
    /// </summary>
    enPara_Comp_Lib = 1;
    /// <summary>
    /// 图纸和报告库
    /// </summary>
    enDraw_Report_Lib = 2;
    /// <summary>
    /// 设计配置库
    /// </summary>
    enDesign_Config_Lib = 3;
	/// <summary>
    /// 公共资源库
    /// </summary>
    enPublic_Resource_Lib = 4;
	/// <summary>
    /// 类型属性库
    /// </summary>
    enType_Attribute_Lib = 5;
}

message GrpcMPUserGroupLibAuth{
	int32 id = 1;
	int32 MPUserGroupId = 2;
	GrpcLibType LibType = 3;
	int32 Permission = 4;
	string AuthInfo = 5;
	string ExtendStr = 6;
}
message GrpcHistoryData{
	//--------------------BasicModelDataInfomation  ----------//
	int64 id = 1;
	int64 instanceId = 2;
	bytes data = 3;
	string domainClassName = 4;
	int32 versionNo = 5;
	string eCSchemaName = 6;
	//--------------------BasicModelDataInfomation  ----------//
	int64 storeyID = 7;
	int32 domain = 8;
	GrpcOperationRecordType operationRecordType = 9;
	string storeyGuid = 10;
}

enum GrpcOperationRecordType{
	Add = 0;
    Delete = 1;
    Modify = 2;
    Unchange = 3;
}

message GrpcRelationship{
	int64 id = 1;
	int64 instanceId = 2;
	bytes data = 3;
	string domainClassName = 4;
	int32 versionNo = 5;
	string eCSchemaName = 6;
	int64 sourceID = 7;
	string sourceDomainClassName = 8;
	string SourceECSchemaName = 9;
	int64 targetID = 10;
	string targetDomainClassName = 11;
	string targetECSchemaName = 12;
	GrpcRelationshipType type = 13;
	bool isForward = 14;
}

enum GrpcRelationshipType
{
    Contain = 0;
    Assemble = 1;
    Dependence = 2;
    Reference = 3;
    Incidence = 4;
}

message GrpcHistoryRelationship{
	int64 id = 1;
	int64 instanceId = 2;
	bytes data = 3;
	string domainClassName = 4;
	int32 versionNo = 5;
	string eCSchemaName = 6;
	int64 sourceID = 7;
	string sourceDomainClassName = 8;
	string SourceECSchemaName = 9;
	int64 targetID = 10;
	string targetDomainClassName = 11;
	string targetECSchemaName = 12;
	GrpcRelationshipType type = 13;
	bool isForward = 14;
	GrpcOperationRecordType operationRecordType = 15;
}



message GrpcVersionInformation{
	int32 versionNo = 1;
	string author = 2;
	string description = 3;
	google.protobuf.Timestamp time = 4;
	GrpcReleaseType releaseType = 5;
	int32 isComplete = 6;
}

message GrpcDomainVersions{
	int32 domainId = 1;
	repeated int32 domainVerNos = 2;
}


message GrpcLockedComponents{
	int64 id = 1;
	int32 domain = 2;
	int64 instanceId = 3;
	int32 lockUserId = 4;
	google.protobuf.Timestamp LockedAt = 5;
}


message GrpcResourceClass{
    int64 id = 1;
    string schemaName = 2;
    string domainClassName = 3;
    int64 instanceId = 4;
    string resourceName = 5;
}

message GrpcSingletonClass{
    int64 id = 1;
    string schemaName = 2;
    string domainClassName = 3;
    int64 instanceId = 4;
}

message GrpcFileDirectory{
	string id = 1;
	string name = 2;
	string mainprojectId = 3;
	string parentId = 4;
	string createUser = 5;
	int32 roleGroupId = 6;
	string roleString = 7;
	int32 type = 8;
	int32 orderNo = 9;
}

message GrpcMPLibraryInfo{
	string LibId = 1;
	string LibName = 2;
	string LibDescription = 3;
	string CreateUser = 4;
	GrpcLibType LibType = 5;
	string ExtendStr = 6;
	google.protobuf.Timestamp CreateTime = 7;
}

message GrpcProvideVersionInfo{
	int32 Id = 1;
	string VersionName = 2;
	string ShowName = 3;
	int32 CheckState = 4;
	int32 ProvideItemCount = 5;
	string SubVersionName = 6;
	string CreateUser = 7;
	google.protobuf.Timestamp CreateTime = 8;
	string ProvidePublishUser = 9;
	google.protobuf.Timestamp ProvidePublishTime = 10;
	int32 ProvideDomain = 11;
	int32 AcceptDomain = 12;
	string AcceptPublishUser = 13;
	google.protobuf.Timestamp AcceptPublishTime = 14;
	string Note = 15;
	int32 CountersignMark = 16;
	string Mark = 17;
}

message GrpcBPExObjectPublish{
	int64 Id = 1;
	string ProvideUser = 2;
	string ProvideTime = 3;
	int32 ProvideState = 4;
	string ProvideNotes = 5;
	string AcceptUser = 6;
	string AcceptTime = 7;
	int32 AcceptState = 8;
	string StructNotes = 9;
	int32 Domain = 10;
	string LoadName = 11;
	string GUID = 12;
	string ExtendField = 13;
	int32 Type = 14;
	string VersionName = 15;
	string SubVersionName = 16;
}

message GrpcBPExObject{
	int64 Id = 1;
	string ProvideUser = 2;
	string ProvideTime = 3;
	int32 ProvideState = 4;
	string ProvideNotes = 5;
	string AcceptUser = 6;
	string AcceptTime = 7;
	int32 AcceptState = 8;
	string StructNotes = 9;
	int32 Domain = 10;
	string LoadName = 11;
	string GUID = 12;
	string ExtendField = 13;
	int32 Type = 14;
}
message GrpcMPUserGroup{
	int32 ID = 1;
	string UserGroupName = 2;
	string Description = 3;
	string ExtendStr = 4;
}
message GrpcMPUserGroupMember{
	int32 ID = 1;
	int32 UserGroupId = 2;
	string TeamMemberId = 3;
}
message GrpcMPTeamProject{
	string id = 1;
	string name = 2;
	string description = 3;
	string leader = 4;
	string avatar = 5;
	google.protobuf.Timestamp startTime = 6;
	google.protobuf.Timestamp endTime = 7;
	string progress = 8;
	string ExtendProperty = 9;
	google.protobuf.Timestamp creationTime = 10;
	bool enableAuthority = 11;
	string filePath = 12;
	string parentProjectId = 13;
	string fileDirectoryID = 14;
	string createUser = 15;
	int32 projectType = 16;
}

message GrpcMPAuthInfo{
	string AuthDisplayName= 1;
    string AuthName = 2;
    int32 Permission = 3;
}
message GrpcMPProjectTreeNode{
	int32 ID = 1;
	int64 NodeId = 2;
	int64 InstanceId = 3;
	int64 TreeId = 4;
	int64 ParentNodeId = 5;
	int32 NodeType = 6;
	string NodeName = 7;
	int64 bPDataKey = 8;
	int64 modelnfoKey = 9;
	string subProjectld = 10;
	int32 level = 11;
	string indexCode = 12;
	string ConflictScope = 13;
}
message GrpcMPLibraryData{
	int32 ID = 1;
	int64 DataId = 2;
	string SchemaName = 3;
	string ClassName = 4;
	string LibId = 5;
	GrpcLibType TreeType = 6;
	bytes Data = 7;
	string ServerFilePath = 8;
	string FileMD5 = 9;
	int32 VersionNo = 10;
}
message GrpcMPCloudLinkFile{
	int32 Id = 1;
	string ProjectId = 2;
	string ProjectName = 3;
	string FileName = 4;
	string SavePath = 5;
	int32 VersionNo = 6;
	int64 FileSize = 7;
	google.protobuf.Timestamp UploadTime = 8;
	string Description = 9;
	string FileType = 10;
	string UserName = 11;
}
message GrpcMPReleaseConfig{
	int32 ID = 1;
    string SourceSubProjectGuid = 2;
    string TargetSubProjectGuid = 3;
    int64 TargetTreeNodeId = 4;
}
message GrpcMPMessage{
	int32 MsgServerID = 1;
    string FromUser = 2;
    string ToUser = 3;
    int32 MPUserGroupId = 4;
    string MsgTextContent = 5;
    int32 MsgState = 6;
    int32 MsgType = 7;
    google.protobuf.Timestamp MsgCreateTime = 8;
    bytes MsgExData = 9;
    string SubProjectId = 10;
}
message GrpcMPDrawing{
	string DrawingID = 1;
    string SubProjectId = 2;
    int64 InstanceId = 3;
    string FullPath = 4;
    string FileName = 5;
    google.protobuf.Timestamp  CreateTime = 6;
    google.protobuf.Timestamp  UpdateTime = 7;
    int32 VersionNo = 8;
    string LockUser = 9;
    string FileMD5 = 10;
}
message GrpcMPCatalogTreeNode{
	int32 ID  = 1;
    int64 NodeId  = 2;
    int64 InstanceId  = 3;
    int64 TreeId  = 4;
    int64 ParentNodeId  = 5;
    int32 NodeType  = 6;
    string NodeName  = 7;
    int64 bPDataKey  = 8;
    int64 modelnfoKey  = 9;
    GrpcLibType TreeType  = 10;
    string LibId  = 11;
    int32 VersionNo  = 12;
}
message GrpcMPLibraryDataHistory{
        
	int32 ID = 1;
	int64 DataId = 2;
	string SchemaName = 3;
	string ClassName = 4;
	string LibId = 5;
	GrpcLibType TreeType = 6;
	bytes Data = 7;
	string ServerFilePath = 8;
	string FileMD5 = 9;
	int32 VersionNo = 10;
	GrpcOperationRecordType OperationType = 11;
}
message GrpcCLCatalogTreeNode{
	int32 ID  = 1;
    int64 NodeId  = 2;
    int64 InstanceId  = 3;
    int64 TreeId  = 4;
    int64 ParentNodeId  = 5;
    int32 NodeType  = 6;
    string NodeName  = 7;
    int64 bPDataKey  = 8;
    int64 modelnfoKey  = 9;
    GrpcLibType TreeType  = 10;
    string LibId  = 11;
    int32 VersionNo  = 12;
}
message GrpcCLLibraryData{
	int32 ID = 1;
	int64 DataId = 2;
	string SchemaName = 3;
	string ClassName = 4;
	string LibId = 5;
	GrpcLibType TreeType = 6;
	bytes Data = 7;
	string ServerFilePath = 8;
	string FileMD5 = 9;
	int32 VersionNo = 10;
}
message GrpcCLLibraryDataHistory{
	int32 ID = 1;
	int64 DataId = 2;
	string SchemaName = 3;
	string ClassName = 4;
	string LibId = 5;
	GrpcLibType TreeType = 6;
	bytes Data = 7;
	string ServerFilePath = 8;
	string FileMD5 = 9;
	int32 VersionNo = 10;
	GrpcOperationRecordType OperationType = 11;
}
message GrpcCLCompanyLibInfo{
	string LibId = 1;
	string LibName = 2;
	string LibDescription = 3;
	string CreateUser = 4;
	google.protobuf.Timestamp CreateTime = 5;
	GrpcLibType LibType = 6;
	string ExtendStr = 7;
}

message ModelFileTransfer{
	int64 Length = 1;
	bytes Data = 2;
	int64 Offset = 3;
	string KeyToken = 4;
	string FileType = 5;
	string Description = 6;
}

message GrpcMainProjectUserGroupMember{
	int64 Id = 1;
    string MainProjectId =2;
    int32 UserGroupId = 3;
    string TeamMemberId = 4;
	string UserGroupName = 5;
}

message GrpcNodeNameCheckConfig{
	int64 Id = 1;
	string NodeTypeName = 2;  //节点类型名称
	int32 NodeTypeNum = 3;    //类型编号
	int32 CheckType = 4;      //冲突判断范围 全局/同级节点
}

message GrcpConflictNodeList{
	int32 CheckType = 1;     //冲突判断范围 全局/同级节点
	int64 NodeId = 2;        //冲突节点的nodeid
	string OldNodeName = 3;  //冲突节点的原节点name
	string NewNodeName = 4;  //自动修改后节点name
	int64  ParentNodeId = 5; //父节点nodeid（当冲突判断范围为全局时，此值为空，否则为父节点ID）
	repeated int64 ConflitNodeIdList = 6;  //所有冲突的节点NodeId
}

