namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志相关常量定义，包括队列、交换机、路由键、属性名等
    /// </summary>
    public static class MQLogConstants
    {
        // RabbitMQ相关
        public const string EXCHANGE_NAME = "cooperate-default";
        public const string EXCHANGE_TYPE = "topic";
        // 队列名称
        public const string INTERFACE_QUEUE = "cooperate.interface.base";
        public const string ERROR_QUEUE = "cooperate.error";
        // 日志类型
        public static class LogType
        {
            public const string INTERFACE = "interface";
            public const string PERFORMANCE = "perform";
            public const string INFO = "info";
        }
        // 日志级别
        public static class LogLevel
        {
            public const string DEBUG = "debug";
            public const string INFO = "info";
            public const string ERROR = "error";
        }
        // 路由键
        public static class RoutingKeys
        {
            public const string INTERFACE_DEBUG = "log.interface.debug";
            public const string INTERFACE_INFO = "log.interface.info";
            public const string INTERFACE_ERROR = "log.interface.error";
            public const string PERFORMANCE_DEBUG = "log.perform.debug";
            public const string PERFORMANCE_INFO = "log.perform.info";
            public const string PERFORMANCE_ERROR = "log.perform.error";
            public const string INFO_DEBUG = "log.info.debug";
            public const string INFO_INFO = "log.info.info";
            public const string INFO_ERROR = "log.info.error";
            public const string ERROR_ALL = "log.*.error";
        }
        // 日志属性名（用于JSON序列化）
        public static class PropertyNames
        {
            public const string RequestId = "requestId";
            public const string SessionId = "sessionId";
            public const string UserId = "userId";
            public const string UserName = "userName";
            public const string StatusCode = "statusCode";
            public const string TotalMilliseconds = "totalMilliseconds";
            public const string ClientIp = "clientIp";
            public const string ClientVersion = "clientVersion";
            public const string StartTime = "startTime";
            public const string EndTime = "endTime";
            public const string ServerIp = "serverIp";
            public const string ServerName = "serverName";
            public const string InterfaceName = "interfaceName";
            public const string LogType = "logType";
            public const string LogLevel = "logLevel";
        }
    }
} 