using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v1升级到v2
    /// 主要变更：
    /// 1. ClientModuleVersions表添加ProjectId、ClientIp、UserId、UserName字段
    /// 2. loginsession表添加ClientGuid字段
    /// 3. mainprojects表添加status字段
    /// 4. 创建客户端版本兼容表 ClientVersionCompatibility
    /// 5. 创建相关索引
    /// </summary>
    public class TeamDbUpgrade_v1_to_v2 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v1";
        public override string TargetVersion => "v2";
        public override string Description => "为ClientModuleVersions表添加ProjectId、ClientIp、UserId、UserName字段，为loginsession表添加ClientGuid字段，为mainprojects表添加status字段，创建客户端版本兼容表";

        public TeamDbUpgrade_v1_to_v2(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v1 到 v2 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(TeamDbContext context)
        {
            // 1. 为ClientModuleVersions表添加字段
            await AddColumnIfNotExistsAsync(context, "ClientModuleVersions", "ProjectId", "VARCHAR(50) NULL");
            await AddColumnIfNotExistsAsync(context, "ClientModuleVersions", "ClientIp", "VARCHAR(50) NULL");
            await AddColumnIfNotExistsAsync(context, "ClientModuleVersions", "UserId", "VARCHAR(50) NULL");
            await AddColumnIfNotExistsAsync(context, "ClientModuleVersions", "UserName", "VARCHAR(100) NULL");

            // 2. 为其他表添加字段
            await AddColumnIfNotExistsAsync(context, "loginsession", "ClientGuid", "CHAR(36) NULL");
            await AddColumnIfNotExistsAsync(context, "mainprojects", "status", "CHAR(30) NULL");

            // 3. 创建客户端版本兼容表
            await CreateClientVersionCompatibilityTableAsync(context);

            // 4. 插入初始客户端版本兼容性配置数据
            await InsertInitialCompatibilityDataAsync(context);

            // 5. 更新MainProject表现有数据
            await UpdateMainProjectDataAsync(context);

            // 6. 创建索引
            await CreateIndexIfNotExistsAsync(context, "ClientModuleVersions", "IX_ClientModuleVersions_ProjectId", "ProjectId");

            // 7. 不在此处调用SaveChangesAsync，由外层事务统一处理
            Logger.LogInformation("升级步骤执行完成，等待外层事务统一保存");
        }

        /// <summary>
        /// 创建客户端版本兼容表
        /// </summary>
        private async Task CreateClientVersionCompatibilityTableAsync(TeamDbContext context)
        {
            if (!await TableExistsAsync(context, "clientversioncompatibility"))
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE `clientversioncompatibility` (
                        `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                        `ClientId` varchar(40) NULL COMMENT '客户端ID',
                        `ClientVersion` varchar(40) NULL COMMENT '客户端版本',
                        `MinCompatibleVersion` varchar(40) NULL COMMENT '可兼容的最小版本',
                        PRIMARY KEY (`ID`),
                        INDEX `IDX_CLIENT_ID` (`ClientId`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户端版本兼容性配置表';
                ");
                Logger.LogInformation("成功创建 clientversioncompatibility 表");
            }
            else
            {
                Logger.LogInformation("clientversioncompatibility 表已存在，跳过创建");
            }
        }

        /// <summary>
        /// 插入初始客户端版本兼容性配置数据
        /// </summary>
        private async Task InsertInitialCompatibilityDataAsync(TeamDbContext context)
        {
            // 检查是否已存在PKPM-Plant的配置
            var existingConfig = await context.ClientVersionCompatibilities
                .FirstOrDefaultAsync(c => c.ClientId == "PKPM-Plant" && c.ClientVersion == "BIMBase-2025R01.01");

            if (existingConfig == null)
            {
                context.ClientVersionCompatibilities.Add(new Domain.ClientVersionCompatibility
                {
                    ClientId = "PKPM-Plant",
                    ClientVersion = "BIMBase-2025R01.01",
                    MinCompatibleVersion = "BIMBase-2025R01.01"
                });
                // 不在这里调用SaveChangesAsync，让外层事务统一处理
                Logger.LogInformation("准备插入PKPM-Plant客户端版本兼容性配置");
            }
            else
            {
                Logger.LogInformation("PKPM-Plant客户端版本兼容性配置已存在，跳过插入");
            }
        }

        /// <summary>
        /// 更新MainProject表现有数据
        /// </summary>
        private async Task UpdateMainProjectDataAsync(TeamDbContext context)
        {
            // 更新所有MainProject记录的status、clientId和ClientVersion字段
            var updateSql = @"
                UPDATE mainprojects 
                SET 
                    status = 'success',
                    clientId = 'PKPM-Plant',
                    ClientVersion = 'BIMBase-2025R01.01'
                WHERE 
                    (status IS NULL OR status = '') AND
                    (clientId IS NULL OR clientId = '') AND
                    (ClientVersion IS NULL OR ClientVersion = '')";

            var rowsAffected = await context.Database.ExecuteSqlRawAsync(updateSql);
            Logger.LogInformation($"成功更新MainProject表 {rowsAffected} 条记录的默认值");
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 1. 删除索引
            await DropIndexIfExistsAsync(context, "ClientModuleVersions", "IX_ClientModuleVersions_ProjectId");

            // 2. 删除客户端版本兼容表
            if (await TableExistsAsync(context, "clientversioncompatibility"))
            {
                await context.Database.ExecuteSqlRawAsync("DROP TABLE `clientversioncompatibility`;");
                Logger.LogInformation("成功删除 clientversioncompatibility 表");
            }

            // 3. 删除其他表的字段
            await DropColumnIfExistsAsync(context, "mainprojects", "status");
            await DropColumnIfExistsAsync(context, "loginsession", "ClientGuid");

            // 4. 删除ClientModuleVersions表的字段（按相反顺序）
            await DropColumnIfExistsAsync(context, "ClientModuleVersions", "UserName");
            await DropColumnIfExistsAsync(context, "ClientModuleVersions", "UserId");
            await DropColumnIfExistsAsync(context, "ClientModuleVersions", "ClientIp");
            await DropColumnIfExistsAsync(context, "ClientModuleVersions", "ProjectId");

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 