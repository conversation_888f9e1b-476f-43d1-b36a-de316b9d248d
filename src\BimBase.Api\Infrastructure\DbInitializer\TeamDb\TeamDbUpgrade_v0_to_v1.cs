using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v0升级到v1
    /// 主要变更：
    /// 1. MainProjects表添加ClientVersion和clientId字段
    /// 2. 创建ClientModuleVersions表
    /// </summary>
    public class TeamDbUpgrade_v0_to_v1 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v0";
        public override string TargetVersion => "v1";
        public override string Description => "添加客户端版本管理相关表和字段";
        
        /// <summary>
        /// 回滚时不更新版本表，因为会删除整个DatabaseVersion表
        /// </summary>
        protected override bool ShouldUpdateVersionOnRollback => false;

        public TeamDbUpgrade_v0_to_v1(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v0 到 v1 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(TeamDbContext context)
        {
            // 1. 添加ClientVersion列到MainProjects表
            await AddColumnIfNotExistsAsync(context, "MainProjects", "ClientVersion", "LONGTEXT NULL");

            // 2. 添加clientId列到MainProjects表
            await AddColumnIfNotExistsAsync(context, "MainProjects", "clientId", "VARCHAR(50) NULL");

            // 3. 创建 DatabaseVersion 表
            await CreateDatabaseVersionTableAsync(context);

            // 4. 创建ClientModuleVersions表
            await CreateClientModuleVersionsTableAsync(context);

            // 5. 创建loginsession表
            await CreateLogSessionTableAsync(context);
        }

        /// <summary>
        /// 创建 DatabaseVersion 表
        /// </summary>
        private async Task CreateDatabaseVersionTableAsync(TeamDbContext context)
        {
            if (!await TableExistsAsync(context, "DatabaseVersion"))
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE `DatabaseVersion` (
                        `Id` INT AUTO_INCREMENT PRIMARY KEY,
                        `Version` VARCHAR(20) NOT NULL,
                        `UpgradedAt` DATETIME NOT NULL,
                        `Description` VARCHAR(255) NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ");
                Logger.LogInformation("成功创建 DatabaseVersion 表");
            }
            else
            {
                Logger.LogInformation("DatabaseVersion 表已存在，跳过创建");
            }
        }

        /// <summary>
        /// 创建 ClientModuleVersions 表
        /// </summary>
        private async Task CreateClientModuleVersionsTableAsync(TeamDbContext context)
        {
            if (!await TableExistsAsync(context, "ClientModuleVersions"))
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE `ClientModuleVersions` (
                        `Id` int(11) NOT NULL AUTO_INCREMENT,
                        `ClientId` varchar(50) NOT NULL,
                        `ModuleId` varchar(50) NOT NULL,
                        `Version` varchar(50) NOT NULL,
                        `CreatedAt` datetime(6) NOT NULL,
                        `UpdatedAt` datetime(6) NOT NULL,
                        PRIMARY KEY (`Id`),
                        KEY `IX_ClientModuleVersions_ClientId` (`ClientId`),
                        KEY `IX_ClientModuleVersions_ModuleId` (`ModuleId`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ");
                Logger.LogInformation("成功创建ClientModuleVersions表");
            }
            else
            {
                Logger.LogInformation("ClientModuleVersions表已存在，跳过创建");
            }
        }


        private async Task CreateLogSessionTableAsync(TeamDbContext context)
        {
            if (!await TableExistsAsync(context, "loginsession"))
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE IF NOT EXISTS `loginsession` (
                      `ID` varchar(45)  CHARACTER SET utf8mb4  NOT NULL,
                      `TeamMember_ID` char(36) CHARACTER SET utf8mb4  NOT NULL,
                      `SessionId` longtext CHARACTER SET utf8mb4 ,
                      `ProjectGuid` char(36) CHARACTER SET utf8mb4  NOT NULL,
                      `LoginAt` datetime(6) NOT NULL,
                      PRIMARY KEY (`ID`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ;
                ");
                Logger.LogInformation("loginsession");
            }
            else
            {
                Logger.LogInformation("loginsession，跳过创建");
            }
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 1. 删除ClientModuleVersions表
            if (await TableExistsAsync(context, "ClientModuleVersions"))
            {
                await context.Database.ExecuteSqlRawAsync("DROP TABLE `ClientModuleVersions`;");
                Logger.LogInformation("成功删除ClientModuleVersions表");
            }

            // 2. 删除DatabaseVersion表
            if (await TableExistsAsync(context, "DatabaseVersion"))
            {
                await context.Database.ExecuteSqlRawAsync("DROP TABLE `DatabaseVersion`;");
                Logger.LogInformation("成功删除DatabaseVersion表");
                
                // 清除 EF Core 对 DatabaseVersion 实体的变更追踪
                // 这样可以避免在后续 SaveChangesAsync 时尝试操作已删除的表
                var trackedEntries = context.ChangeTracker.Entries()
                    .Where(e => e.Entity.GetType().Name == "DatabaseVersion")
                    .ToList();
                
                foreach (var entry in trackedEntries)
                {
                    entry.State = EntityState.Detached;
                }
                
                Logger.LogInformation("已清除 DatabaseVersion 表相关的 EF Core 变更追踪");
            }

            // 3. 删除MainProjects表的clientId字段
            await DropColumnIfExistsAsync(context, "MainProjects", "clientId");

            // 4. 删除MainProjects表的ClientVersion字段
            await DropColumnIfExistsAsync(context, "MainProjects", "ClientVersion");

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 