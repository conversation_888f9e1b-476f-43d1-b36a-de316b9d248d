﻿using BimBase.Api.Infrastructure.LibDomain;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System;
using BimBase.Api.Infrastructure.MainDomain;

namespace BimBase.Api.Infrastructure
{
    public interface ILibraryBulkOperation
    {
        void SaveTreeNodes(DbContext m_db, List<CLCatalogTreeNode> recordDatas, int version);

        void ModifyTreeNodes(DbContext m_db, List<CLCatalogTreeNode> recordDatas, int version);

        void DeleteTreeNodes(DbContext m_db, List<CLCatalogTreeNode> delDatas, Guid libId);

        void SaveHistoryTreeNodes(DbContext context, List<CLCatalogTreeNode> addDatas, List<CLCatalogTreeNode> modifyDatas, List<CLCatalogTreeNode> delDatas, int? version);
        void SaveLibDatas(DbContext m_db, List<CLLibraryData> recordDatas, int version);
        void ModifyLibDatas(DbContext m_db, List<CLLibraryData> recordDatas, int version);
        void DeleteLibDatas(DbContext m_db, List<CLLibraryData> delDatas, Guid libId);
        void SaveHistoryLibData(DbContext context, List<CLLibraryData> addDatas, List<CLLibraryData> modifyDatas, List<CLLibraryData> delDatas, int? version);
        void saveLockLibDatas(DbContext m_db, List<CLTreeNodeLock> recordDatas);

        void SaveDeleteCLLibDataIdToFile(List<CLLibraryData> deltreeDatas, long requestId);

        void SaveCLLibDataHistoryToFile(DbContext context, List<CLLibraryData> addLibDatas, List<CLLibraryData> modifyLibDatas, List<CLLibraryData> delLibDatas, int versionNo, long requestId);

        void SaveAddCLLibDataToFile(List<CLLibraryData> recordDatas, long requestId, int version);

        void SaveModifyCLLibDataToFile(DbContext context, List<CLLibraryData> recordDatas, long requestId, int version);
        void SaveAddCataLogTreeNodeToFile(List<CLCatalogTreeNode> recordDatas, long requestId, int version);

        void SaveDeleteCataLogTreeNodeIdToFile(List<CLCatalogTreeNode> deltreeDatas, long requestId);
        void SaveModifyCataLogTreeNodeToFile(DbContext context, List<CLCatalogTreeNode> recordDatas, long requestId, int version);


        void SaveCLCataLogTreeNodeToDB(DbContext context, long requestId);

        void SaveCLLibDataToDB(DbContext context, long requestId);
        void SaveCLLibDataHistoryToDB(DbContext context, long requestId);
    }
}
