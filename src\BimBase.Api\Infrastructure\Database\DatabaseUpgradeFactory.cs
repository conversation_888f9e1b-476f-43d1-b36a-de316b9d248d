using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using BimBase.Api.Infrastructure;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级工厂，用于获取数据库升级路径和升级实例
    /// </summary>
    public class DatabaseUpgradeFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ICollection<Type> _upgradeTypes;
        private readonly Dictionary<string, Dictionary<string, Type>> _upgradeTypeCache;
        
        public DatabaseUpgradeFactory(IServiceProvider serviceProvider, IServiceScopeFactory serviceScopeFactory)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _upgradeTypes = DiscoverUpgradeTypes();
            _upgradeTypeCache = BuildUpgradeTypeCache();
        }
        
        /// <summary>
        /// 获取从源版本到目标版本的升级类型路径
        /// </summary>
        /// <param name="sourceVersion">源版本</param>
        /// <param name="targetVersion">目标版本</param>
        /// <returns>升级类型列表</returns>
        public List<Type> GetUpgradeTypePath(string sourceVersion, string targetVersion)
        {
            if (string.IsNullOrEmpty(sourceVersion))
                throw new ArgumentNullException(nameof(sourceVersion));
            
            if (string.IsNullOrEmpty(targetVersion))
                throw new ArgumentNullException(nameof(targetVersion));
            
            if (sourceVersion == targetVersion)
                return new List<Type>();
            
            // 使用预先构建的版本图，而不是在运行时尝试创建实例
            var versionGraph = BuildVersionGraphFromCache();
            
            // 使用广度优先搜索查找最短路径
            var path = FindShortestPath(versionGraph, sourceVersion, targetVersion);
            
            if (path == null || !path.Any())
                throw new InvalidOperationException($"无法找到从 {sourceVersion} 到 {targetVersion} 的升级路径");
            
            // 收集升级类型
            var upgradeTypes = new List<Type>();
            
            for (int i = 0; i < path.Count - 1; i++)
            {
                var currentVersion = path[i];
                var nextVersion = path[i + 1];
                
                if (!_upgradeTypeCache.ContainsKey(currentVersion) || 
                    !_upgradeTypeCache[currentVersion].ContainsKey(nextVersion))
                {
                    throw new InvalidOperationException($"无法找到从 {currentVersion} 到 {nextVersion} 的升级实现");
                }
                
                var upgradeType = _upgradeTypeCache[currentVersion][nextVersion];
                upgradeTypes.Add(upgradeType);
            }
            
            return upgradeTypes;
        }
        
        /// <summary>
        /// 创建升级实例（在指定的作用域内）
        /// </summary>
        public IDatabaseUpgrade CreateUpgradeInstance(Type upgradeType, IServiceProvider scopedProvider)
        {
            // 使用带参数的CreateInstance方法
            return (IDatabaseUpgrade)ActivatorUtilities.CreateInstance(scopedProvider, upgradeType);
        }
        
        /// <summary>
        /// 检索所有实现了IDatabaseUpgrade接口的类型
        /// </summary>
        private ICollection<Type> DiscoverUpgradeTypes()
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .Where(t => typeof(IDatabaseUpgrade).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract)
                .ToList();
        }
        
        /// <summary>
        /// 构建升级类型缓存，避免运行时创建实例
        /// </summary>
        private Dictionary<string, Dictionary<string, Type>> BuildUpgradeTypeCache()
        {
            var cache = new Dictionary<string, Dictionary<string, Type>>();
            
            // 创建一个临时作用域来解析版本信息
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var scopedProvider = scope.ServiceProvider;
                
                foreach (var type in _upgradeTypes)
                {
                    // 创建临时实例以获取版本信息
                    var tempInstance = CreateUpgradeInstance(type, scopedProvider);
                    var sourceVersion = tempInstance.SourceVersion;
                    var targetVersion = tempInstance.TargetVersion;
                    
                    if (!cache.ContainsKey(sourceVersion))
                    {
                        cache[sourceVersion] = new Dictionary<string, Type>();
                    }
                    
                    cache[sourceVersion][targetVersion] = type;
                }
            }
            
            return cache;
        }
        
        /// <summary>
        /// 从缓存构建版本图，用于查找升级路径
        /// </summary>
        private Dictionary<string, List<string>> BuildVersionGraphFromCache()
        {
            var graph = new Dictionary<string, List<string>>();
            
            foreach (var sourceVersion in _upgradeTypeCache.Keys)
            {
                if (!graph.ContainsKey(sourceVersion))
                {
                    graph[sourceVersion] = new List<string>();
                }
                
                foreach (var targetVersion in _upgradeTypeCache[sourceVersion].Keys)
                {
                    graph[sourceVersion].Add(targetVersion);
                    
                    // 确保目标版本也在图中，即使它没有出度
                    if (!graph.ContainsKey(targetVersion))
                    {
                        graph[targetVersion] = new List<string>();
                    }
                }
            }
            
            return graph;
        }
        
        /// <summary>
        /// 使用广度优先搜索查找最短路径
        /// </summary>
        private List<string> FindShortestPath(Dictionary<string, List<string>> graph, string start, string end)
        {
            if (!graph.ContainsKey(start) || !graph.ContainsKey(end))
                return null;
            
            var queue = new Queue<string>();
            var visited = new HashSet<string>();
            var predecessors = new Dictionary<string, string>();
            
            queue.Enqueue(start);
            visited.Add(start);
            
            while (queue.Count > 0)
            {
                var current = queue.Dequeue();
                
                if (current == end)
                    break;
                
                foreach (var neighbor in graph[current])
                {
                    if (!visited.Contains(neighbor))
                    {
                        queue.Enqueue(neighbor);
                        visited.Add(neighbor);
                        predecessors[neighbor] = current;
                    }
                }
            }
            
            if (!predecessors.ContainsKey(end) && start != end)
                return null;
            
            // 重建路径
            var path = new List<string>();
            var current2 = end;
            
            while (current2 != start)
            {
                path.Add(current2);
                current2 = predecessors[current2];
            }
            
            path.Add(start);
            path.Reverse();
            
            return path;
        }
    }
} 