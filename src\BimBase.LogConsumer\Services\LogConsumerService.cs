using System;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using BimBase.LogConsumer.Constants;
using BimBase.LogConsumer.Data;
using BimBase.LogConsumer.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace BimBase.LogConsumer.Services
{
    /// <summary>
    /// 日志消费服务
    /// 
    /// 消息格式约定：
    /// 1. Exchange: c-default (Topic类型)
    /// 2. 路由键:
    ///    - API日志: log.api
    ///    - 性能日志: log.performance
    /// 3. 消息内容: JSON格式
    ///    - API日志: 使用 pbimlogs 表结构
    ///    - 性能日志: 使用 performance_logs 表结构
    /// 
    /// 处理逻辑：
    /// 1. 分别处理API和gRPC日志
    /// 2. 根据日志类型进行不同的处理
    /// 3. 保存到数据库
    /// 4. 确认消息处理完成
    /// </summary>
    public class LogConsumerService : BackgroundService
    {
        private readonly ILogger<LogConsumerService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IServiceProvider _serviceProvider;
        private IConnection _connection;
        private IModel _channel;
        private readonly string _exchangeName;
        private readonly string _interfaceQueue;
        private readonly string _errorQueue;
        private bool _enableDbSync;

        public LogConsumerService(
            ILogger<LogConsumerService> logger,
            IConfiguration configuration,
            IServiceProvider serviceProvider,
            IServiceScopeFactory scopeFactory)
        {
            _logger = logger;
            _configuration = configuration;
            _serviceProvider = serviceProvider;
            _scopeFactory = scopeFactory;
            _exchangeName = LogConstants.EXCHANGE_NAME;
            _interfaceQueue = LogConstants.INTERFACE_QUEUE;
            _errorQueue = LogConstants.ERROR_QUEUE;
            _enableDbSync = _configuration.GetValue<bool>("EnableDbSync");
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                InitializeRabbitMQ();
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while starting LogConsumerService");
                throw;
            }
        }

        private void InitializeRabbitMQ()
        {
            var factory = new ConnectionFactory
            {
                HostName = _configuration["RabbitMQ:HostName"],
                UserName = _configuration["RabbitMQ:UserName"],
                Password = _configuration["RabbitMQ:Password"],
                VirtualHost = _configuration["RabbitMQ:VirtualHost"],
                Port = int.Parse(_configuration["RabbitMQ:Port"])
            };

            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();

            // 声明 Exchange
            _channel.ExchangeDeclare(
                exchange: _exchangeName,
                type: ExchangeType.Topic,
                durable: true,
                autoDelete: false);

            // 声明并绑定接口日志队列
            _channel.QueueDeclare(
                queue: _interfaceQueue,
                durable: true,
                exclusive: false,
                autoDelete: false);

            _channel.QueueBind(
                queue: _interfaceQueue,
                exchange: _exchangeName,
                routingKey: "log.interface.#");

            // 声明并绑定错误日志队列
            _channel.QueueDeclare(
                queue: _errorQueue,
                durable: true,
                exclusive: false,
                autoDelete: false);

            _channel.QueueBind(
                queue: _errorQueue,
                exchange: _exchangeName,
                routingKey: "log.*.error");

            // 配置消费者
            var interfaceConsumer = new EventingBasicConsumer(_channel);
            interfaceConsumer.Received += async (model, ea) =>
            {
                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);
                var routingKey = ea.RoutingKey;

                try
                {
                    await ProcessInterfaceLogAsync(message, routingKey);
                    _channel.BasicAck(ea.DeliveryTag, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing interface log message");
                    _channel.BasicNack(ea.DeliveryTag, false, true);
                }
            };

            var errorConsumer = new EventingBasicConsumer(_channel);
            errorConsumer.Received += async (model, ea) =>
            {
                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);
                var routingKey = ea.RoutingKey;

                try
                {
                    await ProcessErrorLogAsync(message, routingKey);
                    _channel.BasicAck(ea.DeliveryTag, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing error log message");
                    _channel.BasicNack(ea.DeliveryTag, false, true);
                }
            };

            // 启动消费者
            _channel.BasicConsume(queue: _interfaceQueue, autoAck: false, consumer: interfaceConsumer);
            _channel.BasicConsume(queue: _errorQueue, autoAck: false, consumer: errorConsumer);
        }

        private async Task ProcessInterfaceLogAsync(string message, string routingKey)
        {
            if (!_enableDbSync)
            {
                _logger.LogInformation("EnableDbSync is false, skip writing interface log to database.");
                return;
            }
            var log = JsonSerializer.Deserialize<MQInterfaceLog>(message);
            if (log == null)
            {
                throw new JsonException("Failed to deserialize interface log message");
            }

            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<LogDbContext>();

            // 检查是否已存在相同 RequestId 的记录
            var existingLog = await dbContext.MQInterfaceLogs
                .FirstOrDefaultAsync(l => l.RequestId == log.RequestId);

            if (existingLog == null)
            {
                // 新记录
                dbContext.MQInterfaceLogs.Add(log);
            }
            else
            {
                // 更新现有记录
                existingLog.ResponseStatusCode = log.ResponseStatusCode;
                existingLog.ResponseTime = log.ResponseTime;
                existingLog.IsSuccess = log.IsSuccess;
                existingLog.ErrorMessage = log.ErrorMessage;
                existingLog.SourceClassName = log.SourceClassName;
                existingLog.SourceMethodName = log.SourceMethodName;
                existingLog.AdditionalData = log.AdditionalData;
            }

            await dbContext.SaveChangesAsync();
            _logger.LogInformation("Processed interface log: {RequestId}", log.RequestId);
        }

        private async Task ProcessErrorLogAsync(string message, string routingKey)
        {
            if (!_enableDbSync)
            {
                _logger.LogInformation("EnableDbSync is false, skip writing error log to database.");
                return;
            }
            var log = JsonSerializer.Deserialize<MQErrorLog>(message);
            if (log == null)
            {
                throw new JsonException("Failed to deserialize error log message");
            }

            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<LogDbContext>();

            // 生成 ErrorId（如果未提供）
            if (string.IsNullOrEmpty(log.ErrorId))
            {
                log.ErrorId = Guid.NewGuid().ToString("N");
            }

            dbContext.MQErrorLogs.Add(log);
            await dbContext.SaveChangesAsync();

            _logger.LogInformation("Processed error log: {ErrorId}", log.ErrorId);
        }

        public override void Dispose()
        {
            _channel?.Dispose();
            _connection?.Dispose();
            base.Dispose();
        }
    }
} 