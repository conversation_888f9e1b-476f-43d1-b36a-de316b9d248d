using System;
using System.Linq;
using System.Threading.Tasks;
using BimBase.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using System.Data.Common;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Config;
using Microsoft.Extensions.Options;
using BimBase.Api.Infrastructure.DbInitializer;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级服务，负责管理和执行数据库升级
    /// </summary>
    public class DatabaseUpgradeService
    {
        private readonly DatabaseUpgradeFactory _upgradeFactory;
        private readonly ILogger<DatabaseUpgradeService> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private const string InitialVersion = "v0";
        private readonly Func<DbConnection, Guid, IMainProjectRepository> _mainProjectRepositoryFactory;
        private readonly DatabaseVersioningOptions _versioningOptions;
        
        private enum DatabaseActionType
        {
            None,
            Upgrade,
            Downgrade
        }

        /// <summary>
        /// 数据库升级服务构造函数
        /// </summary>
        /// <param name="upgradeFactory">数据库升级工厂</param>
        /// <param name="logger">日志服务</param>
        /// <param name="serviceScopeFactory">服务作用域工厂</param>
        /// <param name="mainProjectRepositoryFactory">主项目仓储工厂函数</param>
        /// <param name="versioningOptions">数据库版本配置选项</param>
        public DatabaseUpgradeService(
            DatabaseUpgradeFactory upgradeFactory, 
            ILogger<DatabaseUpgradeService> logger,
            IServiceScopeFactory serviceScopeFactory,
            Func<DbConnection,Guid,IMainProjectRepository> mainProjectRepositoryFactory,
            IOptions<DatabaseVersioningOptions> versioningOptions)
        {
            _upgradeFactory = upgradeFactory ?? throw new ArgumentNullException(nameof(upgradeFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _mainProjectRepositoryFactory = mainProjectRepositoryFactory ?? throw new ArgumentNullException(nameof(mainProjectRepositoryFactory));
            _versioningOptions = versioningOptions?.Value ?? throw new ArgumentNullException(nameof(versioningOptions));
        }
        
        /// <summary>
        /// 根据配置自动应用数据库版本控制（升级或降级）。
        /// </summary>
        public async Task ApplyDatabaseVersioningAsync(string defaultTargetVersion = "v1")
        {
            var targetVersion = !string.IsNullOrEmpty(_versioningOptions.TargetVersion) 
                                ? _versioningOptions.TargetVersion 
                                : defaultTargetVersion;           

            await InitializeVersionTableAsync();
            var currentVersion = await GetCurrentVersionAsync() ?? InitialVersion;

            _logger.LogInformation($"数据库当前版本: {currentVersion}，目标版本: {targetVersion}");

            if (currentVersion == targetVersion)
            {
                _logger.LogInformation($"数据库已经是目标版本 {targetVersion}，无需任何操作。");
                return;
            }

            var actionType = await DetermineDatabaseActionAsync(currentVersion, targetVersion);

            switch (actionType)
            {
                case DatabaseActionType.Upgrade:
                    _logger.LogInformation($"检测到可行的升级路径，开始升级数据库从版本 {currentVersion} 到 {targetVersion}");
                    await UpgradeDatabaseAsync(targetVersion);
                    _logger.LogInformation($"数据库升级完成，当前版本: {targetVersion}");
                    break;
                case DatabaseActionType.Downgrade:
                    _logger.LogInformation($"检测到可行的降级路径，开始降级数据库从版本 {currentVersion} 到 {targetVersion}");
                    await DowngradeDatabaseAsync(targetVersion);
                    _logger.LogInformation($"数据库降级完成，当前版本: {targetVersion}");
                    break;
                case DatabaseActionType.None:
                    _logger.LogWarning($"无法找到从版本 {currentVersion} 到 {targetVersion} 的升级或降级路径。请检查数据库升级脚本和版本配置。");
                    break;
            }
        }

        private Task<DatabaseActionType> DetermineDatabaseActionAsync(string currentVersion, string targetVersion)
        {
            // 尝试获取直接升级路径
            try
            {
                var upgradePath = _upgradeFactory.GetUpgradeTypePath(currentVersion, targetVersion);
                if (upgradePath != null && upgradePath.Any())
                {
                    return Task.FromResult(DatabaseActionType.Upgrade);
                }
            }
            catch (InvalidOperationException)
            {
                _logger.LogInformation($"没有找到从 {currentVersion} 到 {targetVersion} 的直接升级路径。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查从 {currentVersion} 到 {targetVersion} 的升级路径时发生意外错误。");
                // 发生未知错误，视为无法确定路径
                return Task.FromResult(DatabaseActionType.None);
            }

            // 尝试获取反向升级路径 (用于判断是否可降级)
            try
            {
                var reverseUpgradePath = _upgradeFactory.GetUpgradeTypePath(targetVersion, currentVersion);
                if (reverseUpgradePath != null && reverseUpgradePath.Any())
                {
                    // 找到反向路径，意味着可以降级
                    return Task.FromResult(DatabaseActionType.Downgrade);
                }
            }
            catch (InvalidOperationException)
            {
                _logger.LogInformation($"没有找到从 {targetVersion} 到 {currentVersion} 的升级路径 (用于判断是否可降级)。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查从 {targetVersion} 到 {currentVersion} 的升级路径 (用于判断是否可降级) 时发生意外错误。");
                // 发生未知错误，视为无法确定路径
                 return Task.FromResult(DatabaseActionType.None);
            }
            
            return Task.FromResult(DatabaseActionType.None);
        }
        
        /// <summary>
        /// 获取数据库当前版本
        /// </summary>
        public async Task<string> GetCurrentVersionAsync()
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<TeamDbContext>();
            
            // 检查数据库版本表是否存在
            var tableExists = await CheckVersionTableExistsAsync(dbContext);
            
            if (!tableExists)
                return null;
            
            // 获取最新的版本记录
            var latestVersion = await dbContext.Set<DatabaseVersion>()
                .OrderByDescending(v => v.Id)
                .FirstOrDefaultAsync();
            
            return latestVersion?.Version ?? null;
        }
        
        /// <summary>
        /// 初始化数据库版本表
        /// </summary>
        public async Task InitializeVersionTableAsync()
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<TeamDbContext>();
            
            // 检查数据库版本表是否存在
            var tableExists = await CheckVersionTableExistsAsync(dbContext);
            
            if (!tableExists)
            {
                // 确保版本表存在
                await EnsureVersionTableExistsAsync(dbContext);
                
                // 插入初始版本记录
                dbContext.Set<DatabaseVersion>().Add(new DatabaseVersion
                {
                    Version = InitialVersion,
                    UpgradedAt = DateTime.Now,
                    Description = "初始数据库版本"
                });
                
                await dbContext.SaveChangesAsync();
                _logger.LogInformation($"数据库版本表初始化完成，当前版本: {InitialVersion}");
            }
        }
        
        /// <summary>
        /// 升级数据库到指定版本
        /// </summary>
        /// <param name="targetVersion">目标版本</param>
        public async Task UpgradeDatabaseAsync(string targetVersion)
        {
            if (string.IsNullOrEmpty(targetVersion))
                throw new ArgumentNullException(nameof(targetVersion));
            
            // 获取当前版本
            var currentVersion = await GetCurrentVersionAsync();
            
            // 如果当前没有版本记录，则初始化版本表
            if (currentVersion == null)
            {
                await InitializeVersionTableAsync();
                currentVersion = InitialVersion;
            }
            
            // 如果已经是目标版本，则无需升级
            if (currentVersion == targetVersion)
            {
                _logger.LogInformation($"数据库已经是目标版本: {targetVersion}，无需升级");
                return;
            }
            
            _logger.LogInformation($"开始数据库升级，当前版本: {currentVersion}，目标版本: {targetVersion}");
            
            // 获取升级类型路径
            var upgradeTypePath = _upgradeFactory.GetUpgradeTypePath(currentVersion, targetVersion);
            
            if (!upgradeTypePath.Any())
            {
                _logger.LogInformation($"没有找到从 {currentVersion} 到 {targetVersion} 的升级路径");
                return;
            }
            
            // 创建新的作用域以确保服务在整个升级过程中可用
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var scopedProvider = scope.ServiceProvider;
                
                // 执行每一步升级
                foreach (var upgradeType in upgradeTypePath)
                {
                    try
                    {
                        // 在当前作用域中创建升级实例
                        var upgrade = _upgradeFactory.CreateUpgradeInstance(upgradeType, scopedProvider);
                        
                        // 执行升级
                        await upgrade.UpgradeAsync();
                        _logger.LogInformation($"数据库成功升级到版本: {upgrade.TargetVersion}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"数据库升级失败: 升级类型 {upgradeType.Name}");
                        throw;
                    }
                }
            }
            
            _logger.LogInformation($"数据库升级完成，当前版本: {targetVersion}");
        }
        
        public Task UpgradeMainProjectDatabaseAsync(Guid projectId)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<TeamDbContext>();
            var mainProjectRepository = _mainProjectRepositoryFactory(dbContext.Database.GetDbConnection(), projectId);
            return Task.CompletedTask;
        }
       
        /// <summary>
        /// 降级数据库到指定版本
        /// </summary>
        /// <param name="targetVersion">目标版本</param>
        public async Task DowngradeDatabaseAsync(string targetVersion)
        {
            if (string.IsNullOrEmpty(targetVersion))
                throw new ArgumentNullException(nameof(targetVersion));
            
            // 获取当前版本
            var currentVersion = await GetCurrentVersionAsync();
            
            // 如果当前没有版本记录，则无法降级
            if (currentVersion == null)
            {
                _logger.LogWarning("无法降级：数据库未初始化");
                return;
            }
            
            // 如果已经是目标版本，则无需降级
            if (currentVersion == targetVersion)
            {
                _logger.LogInformation($"数据库已经是目标版本: {targetVersion}，无需降级");
                return;
            }
            
            _logger.LogInformation($"开始数据库降级，当前版本: {currentVersion}，目标版本: {targetVersion}");
            
            // 获取降级路径（从当前版本到目标版本，但需要反向执行）
            var upgradeTypePath = _upgradeFactory.GetUpgradeTypePath(targetVersion, currentVersion);
            
            if (upgradeTypePath == null || !upgradeTypePath.Any())
            {
                _logger.LogInformation($"没有找到从 {currentVersion} 到 {targetVersion} 的降级路径");
                return;
            }
            
            // 反转升级路径，得到降级路径
            var downgradeTypePath = new List<Type>(upgradeTypePath);
            downgradeTypePath.Reverse();
            
            // 创建新的作用域以确保服务在整个降级过程中可用
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var scopedProvider = scope.ServiceProvider;
                
                // 执行每一步降级
                foreach (var upgradeType in downgradeTypePath)
                {
                    try
                    {
                        // 在当前作用域中创建升级实例
                        var upgrade = _upgradeFactory.CreateUpgradeInstance(upgradeType, scopedProvider);
                        
                        // 执行降级
                        await upgrade.RollbackAsync();
                        _logger.LogInformation($"数据库成功降级：{upgrade.TargetVersion} -> {upgrade.SourceVersion}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"数据库降级失败: 升级类型 {upgradeType.Name}");
                        throw;
                    }
                }
            }
            
            _logger.LogInformation($"数据库降级完成，当前版本: {targetVersion}");
        }
        
        /// <summary>
        /// 检查数据库版本表是否存在
        /// </summary>
        private async Task<bool> CheckVersionTableExistsAsync(DbContext dbContext)
        {
            try
            {
                // 尝试查询版本表，如果不存在会抛出异常
                await dbContext.Set<DatabaseVersion>().FirstOrDefaultAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 确保数据库版本表存在
        /// </summary>
        private async Task EnsureVersionTableExistsAsync(DbContext dbContext)
        {
            // 创建数据库版本表
            // 使用MySQL兼容的语法
            await dbContext.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS `DatabaseVersion` (
                    `Id` INT AUTO_INCREMENT PRIMARY KEY,
                    `Version` VARCHAR(20) NOT NULL,
                    `UpgradedAt` DATETIME NOT NULL,
                    `Description` VARCHAR(255) NULL
                );
            ");
        }
    }
} 