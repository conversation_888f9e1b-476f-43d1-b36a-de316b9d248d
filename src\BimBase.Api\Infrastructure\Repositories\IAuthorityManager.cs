﻿using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Repositories
{
    public interface IAuthorityManager
    {
        /// <summary>
        /// 添加一个全局角色
        /// </summary>
        /// <param name="role">全局角色信息</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool AddRole(GrpcRole role, out string returnMessage);

        /// <summary>
        /// 添加项目角色
        /// </summary>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="role">角色的详细信息</param>
        /// <param name="creator">创建者Guid</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool AddProjectRole(string projectGuid, GrpcRole role, Guid creator, out string returnMessage);
        /// <summary>
        /// 删除项目相关角色信息
        /// </summary>
        /// <param name="id">项目Id</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns></returns>
        bool DeleteProject(Guid id, out string returnMessage);
        /// <summary>
        /// 删除全局角色
        /// </summary>
        /// <param name="roleList">角色列表</param>
        /// <param name="operateMember">操作者Guid</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool DeleteRoles(List<Guid> roleList, Guid operateMember, out string returnMessage);

        /// <summary>
        /// 删除项目角色
        /// </summary>
        /// <param name="roleList">项目角色列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool DeleteProjectRoles(List<string> roleList, out string returnMessage);

        /// <summary>
        /// 删除角色授权信息
        /// </summary>
        /// <param name="roleId">角色Id</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool DeleteRoleAuth(string roleId, out string returnMessage);
        /// <summary>
        /// 为角色设定权限
        /// </summary>
        /// <param name="roleGuid">角色Guid</param>
        /// <param name="authInfos">权限列表</param>
        /// <param name="operateMember">操作者Guid</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GiveRoleAuth(string roleGuid, List<GrpcAuthInfo> authInfos, Guid operateMember, out string returnMessage);

        /// <summary>
        /// 设定角色用户
        /// </summary>
        /// <param name="roleGuid">角色Guid</param>
        /// <param name="userGuids">用户Guid列表</param>
        /// <param name="operateMember">操作者Guid</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GiveRoleUser(string roleGuid, List<Guid> userGuids, Guid operateMember, out string returnMessage);

        /// <summary>
        /// 移除角色成员
        /// </summary>
        /// <param name="roleId">角色Id</param>
        /// <param name="userIds"></param>
        /// <param name="returnMessage"></param>
        /// <returns></returns>
        bool DeleteRoleUser(string roleId, List<string> userIds, out string returnMessage);
        /// <summary>
        /// 设定项目角色
        /// </summary>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="roleGuid">角色Guid</param>
        /// <param name="operateMember">操作者Guid</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool ProjectGiveRole(string projectGuid, Guid roleGuid, Guid operateMember, out string returnMessage);

        /// <summary>
        /// 设置项目用户
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="projecId"></param>
        /// <param name="roles"></param>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="member">团队成员</param>
        /// <param name="operateMember">操作者Guid</param>
        /// <param name="isAdmin">是否设为管理员</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        //bool ProjectGiveUser(Guid projectGuid, TeamMember member, bool isAdmin, Guid operateMember, out string returnMessage);

        /// <summary>
        /// 获取所有全局权限
        /// </summary>
        /// <param name="authInfos">权限列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        //bool GetAllGlobalAuth(out List<AuthInfo> authInfos, out string returnMessage);

        /// <summary>
        /// 获取当前用户的权限
        /// </summary>
        /// <param name="memberId">当前用户Id</param>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="authInfos">用户的角色列表和全局权限列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetProjectUserAuth(Guid memberId, Guid projectGuid, out List<GrpcAuthInfo> authInfos, out string returnMessage);


        /// <summary>
        /// 获取指定角色权限
        /// </summary>
        /// <param name="roleId">角色Guid</param>
        /// <param name="authInfos">权限列表</param>
        /// <returns>是否成功</returns>
        bool GetRoleAuthorities(string roleId, out List<GrpcAuthInfo> authInfos);


        /// <summary>
        /// 获取用户对应项目下的所有角色
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="projecId"></param>
        /// <param name="roles"></param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetRoleInfoByUserIdAndProjectId(Guid userId, Guid projecId, out List<RoleDto> roles, out string returnMessage);

        /// <summary>
        /// 获取项目下的角色列表
        /// </summary>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="roles">返回的角色列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetProjectRole(string projectGuid, out List<GrpcRole> roles, out string returnMessage);

        /// <summary>
        /// 用户创建的角色列表
        /// </summary>
        /// <param name="memberGuid">用户Guid</param>
        /// <param name="roles">返回的角色列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetOwnRoleList(string memberGuid, out List<RoleDto> roles, out string returnMessage);

        /// <summary>
        /// 获取角色的权限列表
        /// </summary>
        /// <param name="roleGuid">权限Guid</param>
        /// <param name="projectGuid">项目Guid</param>
        /// <param name="authInfos">返回的权限列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetRoleAuth(string roleGuid, string projectGuid, out List<GrpcAuthInfo> authInfos, out string returnMessage);

        /// <summary>
        /// 获取角色的用户列表
        /// </summary>
        /// <param name="roleId">角色Guid</param>
        /// <param name="memberGuids">返回用户的Guid列表</param>
        /// <param name="returnMessage">返回的错误信息</param>
        /// <returns>是否成功</returns>
        bool GetRoleUsers(string roleId, out List<Guid> memberGuids, out string returnMessage);
    }
}
