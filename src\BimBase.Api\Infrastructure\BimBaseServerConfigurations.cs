﻿using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Common;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public static class BimBaseServerConfigurations
    {
        internal static readonly String CurrentConfigPath;
        internal static readonly String CurrentAssemblyPath;

        static BimBaseServerConfigurations()
        {
            //CurrentConfigPath = Assembly.GetExecutingAssembly().Location;
            //if (CurrentConfigPath.StartsWith("file:///"))
            //{
            //    CurrentConfigPath = CurrentConfigPath.Substring("file:///".Length);
            //}
            //CurrentConfigPath = CurrentConfigPath.Replace('/', '\\');
            //CurrentConfigPath = CurrentConfigPath.Remove(CurrentConfigPath.LastIndexOf('\\'));
            //CurrentAssemblyPath = CurrentConfigPath + "\\ServicePlugins\\";
            //CurrentConfigPath += "\\Configurations\\";

            //if (!Directory.Exists(CurrentConfigPath))
            //    Directory.CreateDirectory(CurrentConfigPath);
            //SystemConfig = BimBaseServerXmlSerializer.FromXmlFile<SystemConfig>(CurrentConfigPath + "SystemConfig.config");
            //CachePolicyConfig = BimBaseServerXmlSerializer.FromXmlFile<CachePolicyConfig>(CurrentConfigPath + "CachePolicyConfig.config");
        }

        public static SystemConfig SystemConfig { get; set; }
        public static CachePolicyConfig CachePolicyConfig { get; set; }
        public static bool SaveSystemConfig()
        {
            return BimBaseServerXmlSerializer.ToXmlFile<SystemConfig>(SystemConfig, CurrentConfigPath + "SystemConfig.config");
        }
        public static bool SaveCachePolicyConfig()
        {
            return BimBaseServerXmlSerializer.ToXmlFile<CachePolicyConfig>(CachePolicyConfig, CurrentConfigPath + "CachePolicyConfig.config");
        }
        public static T ReadConfiguration<T>(string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.FromXmlFile<T>(CurrentConfigPath + xmlPath);
        }
        public static bool WriteConfiguration<T>(T obj, string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.ToXmlFile<T>(obj, CurrentConfigPath + xmlPath);
        }

        public static T ReadCustomConfiguration<T>(string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.FromXmlFile<T>(xmlPath);
        }
        public static bool WriteCustomConfiguration<T>(T obj, string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.ToXmlFile<T>(obj, xmlPath);
        }
    }
}
