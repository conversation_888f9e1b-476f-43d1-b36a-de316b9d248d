﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPVolume
    {
        public MPVolume()
        {
            CreateTime = DateTime.Now;
        }

        /// <summary>
        /// 主键Guid
        /// </summary>
        
        [Key]
        public Guid VolumeId { get; set; }

        /// <summary>
        /// 卷册图档名称
        /// </summary>
        
        public string VolumeName { get; set; }

        /// <summary>
        /// 文件夹目录id
        /// </summary>
        
        public Guid FileDirectoryId { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        
        public string CreateUser { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 预留扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }

    }

    /// <summary>
    /// 卷册图档版本记录表
    /// </summary>
    public class MPVolumeVersion
    {
        
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        /// <summary>
        /// 卷册Guid
        /// </summary>
        
        public Guid VolumeId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        
        public string FileName { get; set; }


        /// <summary>
        /// 版本描述
        /// </summary>
        
        public string Description { get; set; }

        /// <summary>
        /// 存储路径
        /// </summary>
        
        public string SavePath { get; set; }

        /// <summary>
        /// 文件类型（扩展名）
        /// </summary>
        
        public string FileType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        
        public long FileSize { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        
        public int VerNo { get; set; }
        /// <summary>
        /// 版本上传者
        /// </summary>
        
        public string UploadUser { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        
        public DateTime UploadTime { get; set; }
        /// <summary>
        /// 预留扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }
    }
}
