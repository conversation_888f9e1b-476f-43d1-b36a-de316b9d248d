using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class ModelDbContextFactory : IDesignTimeDbContextFactory<ModelDbContext>
    {
        public ModelDbContext CreateDbContext(string[] args)
        {
            if (args.Count()>0)
            {
                var connectionString = args[0];
                var optionsBuilder = new DbContextOptionsBuilder<ModelDbContext>()
                    .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

                return new ModelDbContext(optionsBuilder.Options);
            }
            else
            {
                var connectionString = "server=localhost; port=3307; database=PKPM-PBIMServer-ModelDB; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;";
                var optionsBuilder = new DbContextOptionsBuilder<ModelDbContext>()
                    .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

                return new ModelDbContext(optionsBuilder.Options);
            }
        }
    }
} 