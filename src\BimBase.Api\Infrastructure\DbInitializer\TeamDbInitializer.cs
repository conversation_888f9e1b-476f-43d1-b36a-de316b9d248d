using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.DbInitializer.TeamDb;
using Microsoft.EntityFrameworkCore.Migrations.Operations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public static class TeamDbInitializer
    {
        private const string InitialVersion = "v0";
        
        /// <summary>
        /// 初始化数据库并执行版本管理
        /// </summary>
        public static void Initialize(TeamDbContext context, IServiceProvider serviceProvider)
        {
            try
            {
                // 同步调用异步方法（在应用启动时可以接受）
                InitializeAsync(context, serviceProvider).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                // 记录错误并重新抛出，让应用启动失败
                Console.WriteLine($"数据库初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 异步初始化数据库并执行版本管理
        /// </summary>
        public static async Task InitializeAsync(TeamDbContext context, IServiceProvider serviceProvider)
        {
            var logger = GetLogger(serviceProvider);
            
            try
            {
                logger.LogInformation("开始数据库初始化和版本管理...");
                
                // 1. 确保数据库存在，并根据是否新创建决定是否需要初始数据
                var databaseCreated = await EnsureDatabaseExistsAsync(context, logger);
                
                // 2. 只有在数据库新创建时才创建初始数据
                if (databaseCreated)
                {
                    await EnsureInitialDataAsync(context, logger);
                }
                
                // 3. 执行版本管理 - 使用全局配置提供者
                await ExecuteVersionManagementAsync(context, logger);
                
                logger.LogInformation("数据库初始化和版本管理完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "数据库初始化过程中发生错误");
                await HandleRollbackAsync(context, logger, ex);
                throw;
            }
        }
        
        /// <summary>
        /// 确保数据库存在
        /// </summary>
        /// <returns>如果数据库是新创建的返回true，已存在返回false</returns>
        private static async Task<bool> EnsureDatabaseExistsAsync(TeamDbContext context, ILogger logger)
        {
            logger.LogInformation("检查数据库是否存在...");
            
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                logger.LogInformation("数据库已创建");
            }
            else
            {
                logger.LogInformation("数据库已存在");
            }
            
            return created;
        }
        
        /// <summary>
        /// 确保初始数据存在
        /// </summary>
        private static async Task EnsureInitialDataAsync(TeamDbContext context, ILogger logger)
        {
            // 检查是否已有数据
            if (await context.TeamMembers.AnyAsync())
            {
                logger.LogInformation("检测到已有数据，跳过初始数据创建");
                return;
            }
            
            logger.LogInformation("创建初始数据...");
            await CreateInitialDataAsync(context, logger);
        }
        
        /// <summary>
        /// 创建初始数据（管理员、角色等）
        /// </summary>
        private static async Task CreateInitialDataAsync(TeamDbContext context, ILogger logger)
        {
            logger.LogInformation("开始创建初始数据...");

            // 添加默认管理员角色
            TeamGroup adgroup = new TeamGroup();
            // 添加系统管理员
            TeamMember mbr = new TeamMember();
            mbr.ID = BuildinAdministrators.BuildInSystemAdminGuid;
            mbr.DisplayName = mbr.LoginName = BuildinAdministrators.BuildInSystemAdminLoginName;
            mbr.PasswordMD5 = UtilityHelper.CreatePasswordMD5(BuildinAdministrators.BuildInSystemAdminPassword);
            mbr.Avatar = BuildinAdministrators.BuildInAdministratorAvatar;
            context.TeamMembers.Add(mbr);

            // 添加团队管理员
            mbr = new TeamMember();
            mbr.ID = BuildinAdministrators.BuildInAdministratorGuid;
            mbr.DisplayName = mbr.LoginName = BuildinAdministrators.BuildInTeamAdministratorLoginName;
            mbr.PasswordMD5 = UtilityHelper.CreatePasswordMD5(BuildinAdministrators.BuildInTeamAdministratorPassword);
            mbr.Avatar = BuildinAdministrators.BuildInAdministratorAvatar;
            context.TeamMembers.Add(mbr);

            // 添加模型管理员
            mbr = new TeamMember();
            mbr.ID = BuildinAdministrators.BuildInSystemModelAdminGuid;
            mbr.DisplayName = mbr.LoginName = BuildinAdministrators.BuildInSystemModelAdminLoginName;
            mbr.PasswordMD5 = UtilityHelper.CreatePasswordMD5(BuildinAdministrators.BuildInSystemModelAdminPassword);
            mbr.Avatar = BuildinAdministrators.BuildInAdministratorAvatar;
            context.TeamMembers.Add(mbr);

            TeamGroup tg = new TeamGroup();
            tg.GroupName = "管理员";
            var x = context.TeamGroups.Add(tg);
            await context.SaveChangesAsync();
            adgroup = x.Entity;
            
            if (adgroup.Id > 0)
            {
                TeamUserGroup tug = new TeamUserGroup
                {
                    GroupId = adgroup.Id,
                    TeamMemberId = BuildinAdministrators.BuildInSystemAdminGuid
                };
                context.TeamUserGroups.Add(tug);
                tug = new TeamUserGroup
                {
                    GroupId = adgroup.Id,
                    TeamMemberId = BuildinAdministrators.BuildInAdministratorGuid
                };
                context.TeamUserGroups.Add(tug);
                tug = new TeamUserGroup
                {
                    GroupId = adgroup.Id,
                    TeamMemberId = BuildinAdministrators.BuildInSystemModelAdminGuid
                };
                context.TeamUserGroups.Add(tug);
            }

            // 初始化版本信息，直接插入 v2 版本
            // 先检查 DatabaseVersion 表是否存在
            if (await DatabaseVersionTableExistsAsync(context))
            {
                if (!await context.DatabaseVersions.AnyAsync())
                {
                    context.DatabaseVersions.Add(new DatabaseVersion
                    {
                        Version = "v2",
                        UpgradedAt = DateTime.Now,
                        Description = "初始化数据库到 v2 版本，包含客户端版本管理和兼容性配置"
                    });
                    logger.LogInformation("添加初始版本信息: v2");
                }
            }
            else
            {
                logger.LogInformation("DatabaseVersion 表不存在，跳过版本信息初始化");
            }

            // 初始化客户端版本兼容性配置数据
            if (!await context.ClientVersionCompatibilities.AnyAsync())
            {
                context.ClientVersionCompatibilities.Add(new ClientVersionCompatibility
                {
                    ClientId = "PKPM-Plant",
                    ClientVersion = "BIMBase-2025R01.01",
                    MinCompatibleVersion = "BIMBase-2025R01.01"
                });
                logger.LogInformation("添加初始客户端版本兼容性配置: PKPM-Plant");
            }

            await context.SaveChangesAsync();
            logger.LogInformation("初始数据和版本信息创建完成");
        }
        
        /// <summary>
        /// 获取日志记录器
        /// </summary>
        private static ILogger GetLogger(IServiceProvider serviceProvider)
        {
            // 从服务提供者获取 ILogger，就像 DatabaseUpgradeService 一样
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            return loggerFactory.CreateLogger("TeamDbInitializer");
        }
        
        /// <summary>
        /// 执行版本管理
        /// </summary>
        private static async Task ExecuteVersionManagementAsync(TeamDbContext context, ILogger logger)
        {
            logger.LogInformation("开始执行数据库版本管理...");
            
            // 使用全局配置提供者获取配置
            if (!DatabaseVersioningOptionsProvider.ShouldAutoExecute())
            {
                logger.LogInformation("数据库自动版本控制已禁用，跳过执行");
                return;
            }
            
            // 确保版本表存在
            await EnsureVersionTableAsync(context, logger);
            
            // 获取当前版本
            var currentVersion = await GetCurrentVersionAsync(context, logger);
            var targetVersion = DatabaseVersioningOptionsProvider.GetTargetVersion();
            
            logger.LogInformation($"当前版本: {currentVersion}, 目标版本: {targetVersion}");
            
            if (currentVersion == targetVersion)
            {
                logger.LogInformation("数据库已经是目标版本，无需升级");
                return;
            }
            
            // 使用新的升级管理器
            await ExecuteUpgradePathWithManagerAsync(context, logger, currentVersion, targetVersion);
        }
        
        /// <summary>
        /// 使用升级管理器执行升级路径
        /// </summary>
        private static async Task ExecuteUpgradePathWithManagerAsync(TeamDbContext context, ILogger logger, string currentVersion, string targetVersion)
        {
            logger.LogInformation($"开始使用升级管理器执行升级路径: {currentVersion} -> {targetVersion}");
            
            var upgradeManager = new TeamDbUpgradeManager(logger);
            
            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                List<ITeamDbUpgrade> upgradePath;
                
                // 根据版本关系计算升级或回滚路径
                if (IsUpgrade(currentVersion, targetVersion))
                {
                    upgradePath = upgradeManager.CalculateUpgradePath(currentVersion, targetVersion);
                    await upgradeManager.ExecuteUpgradePathAsync(context, upgradePath);
                }
                else
                {
                    var rollbackPath = upgradeManager.CalculateRollbackPath(currentVersion, targetVersion);
                    await upgradeManager.ExecuteRollbackPathAsync(context, rollbackPath);
                }
                
                // 统一保存所有更改（包括版本记录更新）
                await context.SaveChangesAsync();
                await transaction.CommitAsync();
                logger.LogInformation("升级路径执行完成");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                logger.LogError(ex, "升级过程中发生错误，已回滚");
                throw;
            }
        }
        
        /// <summary>
        /// 判断是否为升级操作（简单的版本比较）
        /// </summary>
        private static bool IsUpgrade(string currentVersion, string targetVersion)
        {
            // 简单的版本比较逻辑
            var currentVersionNumber = GetVersionNumber(currentVersion);
            var targetVersionNumber = GetVersionNumber(targetVersion);
            
            return targetVersionNumber > currentVersionNumber;
        }
        
        /// <summary>
        /// 获取版本号（用于比较）
        /// </summary>
        private static int GetVersionNumber(string version)
        {
            return version switch
            {
                "v0" => 0,
                "v1" => 1,
                "v2" => 2,
                "v3" => 3,
                "v4" => 4,
                "v5" => 5,
                "v6" => 6,
                "v7" => 7,
                "v8" => 8,
                _ => -1
            };
        }
        
        /// <summary>
        /// 确保版本表存在
        /// </summary>
        private static async Task EnsureVersionTableAsync(TeamDbContext context, ILogger logger)
        {
            // 使用已有的表存在性检查方法，避免不必要的异常日志
            if (await DatabaseVersionTableExistsAsync(context))
            {
                logger.LogInformation("版本管理相关表已存在");
            }
            else
            {
                logger.LogInformation("版本管理相关表不存在，将在升级过程中创建");
            }
        }
        
        /// <summary>
        /// 获取当前数据库版本
        /// </summary>
        private static async Task<string> GetCurrentVersionAsync(TeamDbContext context, ILogger logger)
        {
            try
            {
                // 先检查 DatabaseVersions 表是否存在
                if (!await DatabaseVersionTableExistsAsync(context))
                {
                    logger.LogInformation("DatabaseVersion 表不存在，使用初始版本");
                    return InitialVersion;
                }

                var latestVersion = await context.DatabaseVersions
                    .OrderByDescending(v => v.Id)
                    .FirstOrDefaultAsync();
                
                var version = latestVersion?.Version ?? InitialVersion;
                logger.LogInformation($"检测到当前数据库版本: {version}");
                return version;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "检测数据库版本时发生异常，使用初始版本");
                return InitialVersion;
            }
        }

        /// <summary>
        /// 检查 DatabaseVersions 表是否存在
        /// </summary>
        private static async Task<bool> DatabaseVersionTableExistsAsync(TeamDbContext context)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = 'DatabaseVersion'";

                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 处理回滚
        /// </summary>
        private static async Task HandleRollbackAsync(TeamDbContext context, ILogger logger, Exception originalException)
        {
            try
            {
                logger.LogWarning("尝试执行数据库回滚操作...");
                // 这里可以添加具体的回滚逻辑
                // 例如删除部分创建的表、恢复数据等
                logger.LogInformation("回滚操作完成");
            }
            catch (Exception rollbackException)
            {
                logger.LogError(rollbackException, "回滚操作也失败了");
            }
        }
    }
} 