﻿using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class BPExObjectPublish
    {
        
        [Key]
        public Int64 Id { get; set; }
        /// <summary>
        /// 提资人
        /// </summary>
        
        public string ProvideUser { get; set; }
        /// <summary>
        /// 提资日期
        /// </summary>
        
        public string ProvideTime
        {
            get;
            set;
        }


        /// <summary>
        /// 提资状态
        /// </summary>
        
        public int ProvideState { get; set; }
        /// <summary>
        /// 提资备注
        /// </summary>
        
        public string ProvideNotes { get; set; }
        /// <summary>
        /// 收资人
        /// </summary>
        
        public string AcceptUser { get; set; }

        /// <summary>
        /// 反资日期
        /// </summary>
        
        public string AcceptTime
        {
            get;
            set;
        }
        /// <summary>
        /// 收资状态
        /// </summary>
        
        public int AcceptState { get; set; }

        /// <summary>
        /// 结构备注
        /// </summary>
        
        public string StructNotes { get; set; }
        /// <summary>
        /// 专业
        /// </summary>
        
        public int Domain { get; set; }
        /// <summary>
        /// 荷载名称
        /// </summary>
        
        public string LoadName { get; set; }
        /// <summary>
        /// GUID
        /// </summary>
        
        public Guid GUID { get; set; }
        /// <summary>
        /// 大版本号，发布时生成大版本
        /// </summary>
        
        public int MainVersion { get; set; }
        /// <summary>
        /// 小版本，更新时不生成大版本，生成小版本
        /// </summary>
        
        public int SubVersion { get; set; }
        /// <summary>
        /// 扩展字段
        /// </summary>
        
        public string ExtendField { get; set; }

        /// <summary>
        /// 提资类型
        /// </summary>
        
        public int Type { get; set; }

        
        public string VersionName { get; set; }
        
        public string SubVersionName { get; set; }
    }


    /// <summary>
    /// 版本信息表
    /// </summary>
    public class ProvideVersionInfo
    {
        
        [Key]
        public int Id { get; set; }
        //大版本名称
        
        public string VersionName { get; set; }
        
        public string ShowName { get; set; }

        
        public int MainVersion { get; set; }

        
        public int SubVersion { get; set; }

        
        public int CheckState { get; set; }
        //提资物数量
        
        public int ProvideItemCount { get; set; }
        //小版本名称
        
        public string SubVersionName { get; set; }
        //创建人
        
        public string CreateUser { get; set; }
        //创建时间
        
        public DateTime CreateTime { get; set; }
        //提资发布人（需与创建人相同）
        
        public string ProvidePublishUser { get; set; }
        //提资发布时间
        
        public DateTime ProvidePublishTime { get; set; }
        //提资专业
        
        public int ProvideDomain { get; set; }
        //收资专业
        
        public int AcceptDomain { get; set; }
        //收资发布人
        
        public string AcceptPublishUser { get; set; }
        //收资发布时间
        
        public DateTime AcceptPublishTime { get; set; }
        //批注（json格式字符串）
        
        public string Note { get; set; }
        //设计阶段
        
        public int DesignState { get; set; }
        //会签标记
        
        public int CountersignMark { get; set; }

        //备注
        
        public string Mark { get; set; }

    }
}
