syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos.Library";
import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";

package bimbase.api.library;
enum GrpcCLCommandType
{
    ADD = 0;
    DEL = 1;
    MOD = 2;
}
message CLLibTreeNodeDataCommand{
	GrpcCLCommandType Type = 1;
    repeated GrpcCLCatalogTreeNode TreeNodeList = 2;
}
message CLLibraryDataCommand{
	GrpcCLCommandType Type = 1;
	repeated GrpcCLLibraryData LibDataList = 2;
}
message UploadLibraryTreeNodeDataRequest{
	string sessionId = 1;
	repeated string libGuidList = 2;
	repeated CLLibTreeNodeDataCommand treeNodes = 3;
	repeated CLLibraryDataCommand libDatas = 4;
}
message UploadLibraryTreeNodeDataResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	map<string,int32> version = 4;
}

message UploadLibraryTreeNodeDataLargeRequest{
	string sessionId = 1;
	int64 uploadRequestId = 2;
	bool isLastUpload = 3;
	repeated string libGuidList = 4;
	repeated CLLibTreeNodeDataCommand treeNodes = 5;
	repeated CLLibraryDataCommand libDatas = 6;
	map<string,int32> libVersion = 7;
}
message UploadLibraryTreeNodeDataLargeResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	map<string,int32> version = 4;
}
message CreateCompanyLibraryInfoRequest{
	string sessionId = 1;
	GrpcCLCompanyLibInfo cLCompanyLibInfo = 2;
}
message CreateCompanyLibraryInfoResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcCLCompanyLibInfo cLCompanyLibInfo = 4;
}
message LockInstanceIdWithVersion{
	int64 InstanceId = 1;
	int32 ClientVersion = 2;
}
message DicLockInstanceIdWithVersion{
	int64 treeId = 1;
	repeated LockInstanceIdWithVersion lockInstanceList = 2;
}
message LockLibraryElementByTreeListRequest{
	string sessionId = 1;
	repeated int64 treeIdList = 2;
	repeated DicLockInstanceIdWithVersion dicSingleinstanceid = 3;
	repeated DicLockInstanceIdWithVersion dicShareLockInstanceId = 4;
	string mainprojectGuid = 5;
}
message LockLibraryElementRequest{
	string sessionId = 1;
	string libGuid = 2;
	map<int64,int32> dicSingleinstanceid = 3;
	map<int64,int32> dicShareLockInstanceId = 4;
}
message UnlockTreeidInstance{
	int64 treeId = 1;
	repeated int64 InstanceIdList = 2;
}
message UnLockLibraryElementByTreeListRequest{
	string sessionId = 1;
	repeated int64 treeIdList = 2;
	repeated UnlockTreeidInstance unLockElementList = 3;
}
message UnLockLibraryElementRequest{
	string sessionId = 1;
	string libGuid = 2;
	repeated int64 unLockElementList = 3;
}
message UploadLibFileDataRequest{
	string sessionId = 1;
	string libGuid = 2;
	int64 dataId = 3;
	bytes fileData = 4;
	string fileName = 5;
}
message UploadLibFileDataWithMD5Request{
	string sessionId = 1;
	string libGuid = 2;
	int64 dataId = 3;
	bytes fileData = 4;
	string fileName = 5;
	string fileMD5 = 6;
}
message CombineLibFileRequest{
	string sessionId = 1;
	string libGuid = 2;
	int64 dataId = 3;
	bytes fileData = 4;
	string fileName = 5;
	string fileMD5 = 6;
}
service GrpcLibraryManagement {
	rpc UploadLibraryTreeNodeData(UploadLibraryTreeNodeDataRequest) returns (UploadLibraryTreeNodeDataResponse){
		option (google.api.http) = {
			post: "/v1/library/UploadLibraryTreeNodeData"
			body: "*"
		};
	}
	rpc CreateCompanyLibraryInfo(CreateCompanyLibraryInfoRequest) returns (CreateCompanyLibraryInfoResponse){
		option (google.api.http) = {
			post: "/v1/CreateCompanyLibraryInfo"
			body: "*"
		};
	}
	rpc UnLockLibByUser(SessionRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/UnLockLibByUser"
			body: "*"
		};
	}

	rpc LockLibraryElementByTreeList(LockLibraryElementByTreeListRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/LockLibraryElementByTreeList"
			body: "*"
		};
	}

	rpc LockLibraryElement(LockLibraryElementRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/LockLibraryElement"
			body: "*"
		};
	}
	rpc UnLockLibraryElementByTreeList(UnLockLibraryElementByTreeListRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/UnLockLibraryElementByTreeList"
			body: "*"
		};
	}
	rpc UnLockLibraryElement(UnLockLibraryElementRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/UnLockLibraryElement"
			body: "*"
		};
	}
	rpc UploadLibFileData(UploadLibFileDataRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/UploadLibFileData"
			body: "*"
		};
	}
	rpc UploadLibFileDataWithMD5(UploadLibFileDataWithMD5Request) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/library/UploadLibFileDataWithMD5"
			body: "*"
		};
	}
	rpc CombineLibFile(CombineLibFileRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/library/CombineLibFile"
			body: "*"
		};
	}

	rpc UploadLibraryTreeNodeDataLarge(UploadLibraryTreeNodeDataLargeRequest) returns (UploadLibraryTreeNodeDataLargeResponse){
		option (google.api.http) = {
			post: "/v1/library/UploadLibraryTreeNodeDataLarge"
			body: "*"
		};
	}
}