# BIMBase 性能优化全面指南

## 🚀 立即执行的排查步骤

### 1. 运行性能排查脚本
```powershell
.\performance_check.ps1
```

### 2. 重点关注指标
- **CPU使用率** > 80% 需要优化
- **内存使用率**：虽然系统内存充足，但要关注容器内存限制
- **数据库连接数**：接近max_connections需要优化
- **慢查询**：执行时间 > 2秒的查询

## 🔧 数据库层面优化

### A. 替换MySQL配置文件
1. 备份当前配置：`cp my.cnf my.cnf.backup`
2. 使用优化后的配置：`cp my.cnf.optimized my.cnf`
3. 重启数据库容器：`docker-compose restart teamdata libdata logdata authdata`

### B. 数据库索引优化
```sql
-- 检查缺少索引的查询
SELECT * FROM sys.statements_with_runtimes_in_95th_percentile;

-- 检查从未使用的索引
SELECT * FROM sys.schema_unused_indexes;

-- 检查重复索引
SELECT * FROM sys.schema_redundant_indexes;
```

### C. 数据库连接池优化
在 `docker-compose.override.yml` 中为每个数据库容器添加资源限制：

```yaml
  teamdata:
    # ... existing config ...
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

## 📊 应用层面优化

### A. 连接字符串优化
修改连接字符串，添加连接池参数：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "server=team-db;port=3306;database=pkpm-pbimserver-teamdb;user=root;password=*******;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=10;Max Pool Size=100;Pooling=true;Connection Lifetime=300;Connection Reset=true;SslMode=None;",
    "LoggerConnection": "server=log-db;port=3306;database=bimbase-logdb;user=root;password=*******;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=5;Max Pool Size=50;Pooling=true;Connection Lifetime=300;Connection Reset=true;SslMode=None;",
    "LibraryConnection": "server=lib-db;port=3306;database=pkpm-pbimserver-librarydb;user=root;password=*******;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=5;Max Pool Size=50;Pooling=true;Connection Lifetime=300;Connection Reset=true;SslMode=None;"
  }
}
```

### B. Redis缓存配置优化
在 `docker-compose.override.yml` 中优化Redis配置：
```yaml
  redis:
    image: redis:7-alpine
    container_name: redis-db
    command: [
      "redis-server", 
      "--appendonly", "yes",
      "--maxmemory", "2gb",
      "--maxmemory-policy", "allkeys-lru",
      "--tcp-keepalive", "60",
      "--timeout", "300"
    ]
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### C. 应用容器资源优化
```yaml
  bimbase.api:
    # ... existing config ...
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    environment:
      # ... existing env vars ...
      - DOTNET_gcServer=1
      - DOTNET_gcConcurrent=1
      - DOTNET_GCHeapCount=4
```

## 🏗️ 架构层面优化

### A. 读写分离
考虑为频繁读取的数据库配置主从复制：
```yaml
  teamdata-slave:
    image: mysql:8.0
    container_name: team-db-slave
    command: [
      "--server-id=2",
      "--read-only=1",
      "--log-bin=mysql-bin",
      "--relay-log=relay-log"
    ]
```

### B. 缓存策略
1. **Redis分布式缓存**：缓存频繁查询的数据
2. **应用级缓存**：使用MemoryCache缓存计算结果
3. **HTTP缓存**：为静态资源设置合适的缓存头

### C. 异步处理
对于耗时操作，使用后台服务处理：
```csharp
services.AddHostedService<LogProcessingService>();
services.AddSingleton<IBackgroundTaskQueue, BackgroundTaskQueue>();
```

## 📈 监控和告警

### A. 性能监控
```yaml
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
```

### B. 健康检查优化
```yaml
  bimbase.api:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
```

## 🔍 常见性能瓶颈排查

### 1. CPU瓶颈
- 检查是否有死循环或过度计算
- 优化算法复杂度
- 使用异步编程减少线程阻塞

### 2. 内存瓶颈
- 检查内存泄漏
- 优化大对象处理
- 及时释放资源

### 3. 数据库瓶颈
- 慢查询优化
- 索引优化
- 分库分表

### 4. 网络瓶颈
- 减少网络调用次数
- 数据压缩
- CDN使用

## 📋 执行清单

- [ ] 运行性能排查脚本
- [ ] 更新MySQL配置文件
- [ ] 优化数据库连接字符串
- [ ] 添加容器资源限制
- [ ] 配置Redis缓存策略
- [ ] 设置性能监控
- [ ] 定期检查慢查询日志
- [ ] 优化业务代码中的性能热点

## ⚠️ 注意事项

1. **分步骤执行**：不要一次性修改所有配置
2. **备份数据**：修改前务必备份数据库
3. **监控变化**：每次优化后观察性能指标变化
4. **负载测试**：在测试环境进行压力测试
5. **回滚准备**：准备快速回滚方案

执行这些优化后，您的系统性能应该会有显著提升。建议先执行性能排查脚本，根据具体结果再有针对性地进行优化。 