using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Linq;
using BimBase.Api.Infrastructure.Grpc;
// ========== MQ日志功能新增依赖 ==========
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using BimBase.Api.Infrastructure.MQLog;

namespace BimBase.Api.Infrastructure.Middleware
{
    /// <summary>
    /// 响应完成监控中间件
    /// 监控从请求开始到响应完全发送给客户端的整个过程
    /// 同时负责初始化RequestContext
    /// 
    /// 【功能说明】
    /// - 原有功能：HTTP层面监控、RequestContext初始化、性能分析、响应完成事件监控
    /// - 新增功能：MQ请求日志记录（request阶段）
    /// </summary>
    public class ResponseCompletionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ResponseCompletionMiddleware> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IConfiguration _configuration;
        private readonly bool _enableMQLogging;

        public ResponseCompletionMiddleware(
            RequestDelegate next,
            ILogger<ResponseCompletionMiddleware> logger,
            IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration)
        {
            _next = next;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _configuration = configuration;
            _enableMQLogging = configuration.GetValue<bool>("LogSettings:MQLogging:Enabled", false);
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (!IsGrpcRequest(context))
            {
                await _next(context);
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            var method = context.Request.Path.Value;
            var startTime = DateTime.Now;

            var requestContext = RequestContext.Initialize();
            requestContext.RequestId = context.TraceIdentifier;
            requestContext.StartTime = startTime;
            requestContext.MethodName = method;

            ExtractBasicHttpInfo(context, requestContext);

            _logger.LogInformation($"[中间件] 开始处理请求: {method}, RequestId: {requestContext.RequestId}, 开始时间: {startTime:HH:mm:ss.fff}");

            if (_enableMQLogging)
            {
                _ = Task.Run(async () => await LogRequestToMQAsync(context, requestContext, startTime));
            }

            var responseCompleted = false;
            context.Response.OnCompleted(() =>
            {
                if (!responseCompleted)
                {
                    responseCompleted = true;
                    stopwatch.Stop();
                    var totalTime = stopwatch.ElapsedMilliseconds;
                    var completedTime = DateTime.Now;

                    _logger.LogInformation($"[中间件] 响应发送完成: {method}, RequestId: {requestContext.RequestId}, " +
                        $"总耗时: {totalTime}ms, 响应状态: {context.Response.StatusCode}, " +
                        $"完成时间: {completedTime:HH:mm:ss.fff}");

                    if (context.Response.Headers.TryGetValue("processing-time-ms", out var processingTimeValues))
                    {
                        if (double.TryParse(processingTimeValues.FirstOrDefault(), out var grpcProcessingTime))
                        {
                            var frameworkOverhead = totalTime - grpcProcessingTime;
                            _logger.LogInformation($"[性能分析] {method}, RequestId: {requestContext.RequestId}, " +
                                $"gRPC业务处理: {grpcProcessingTime}ms, " +
                                $"框架+网络开销: {frameworkOverhead}ms, " +
                                $"开销占比: {(frameworkOverhead / totalTime * 100):F1}%");
                        }
                    }

                    // 新增：记录中间件阶段日志
                    if (_enableMQLogging)
                    {
                        try
                        {
                            _logger.LogInformation($"[MQ日志] 开始入队 Middle 阶段日志, RequestId: {requestContext.RequestId}, 耗时: {totalTime}ms");
                            var responseLog = new MQInterfaceLog
                            {
                                RequestId = requestContext.RequestId,
                                LogStage = "middle",
                                MiddleTotalMilliseconds = totalTime,
                                MiddleResponseTime = completedTime,
                                ServerIp = GetServerIp()
                            };
                            var scope = _serviceScopeFactory.CreateScope();
                            var logWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                            // 使用 Task.Run 来处理异步操作，避免在同步回调中使用 await
                            _ = Task.Run(async () => 
                            {
                                try
                                {
                                    await logWriterService.WriteInterfaceLogAsync(responseLog);
                                    _logger.LogInformation($"[MQ日志] Middle 阶段日志入队完成, RequestId: {requestContext.RequestId}");
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"[MQ日志] Middle 阶段日志入队失败: {requestContext.RequestId}, 异常: {ex.Message}");
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            // MQ日志记录失败不影响主流程
                            _logger.LogError(ex, $"[MQ日志] Middle 阶段日志入队失败: {requestContext.RequestId}, 异常: {ex.Message}");
                        }
                    }
                }
                return Task.CompletedTask;
            });

            try
            {
                await _next(context);

                var processingTime = stopwatch.ElapsedMilliseconds;
                var processedTime = DateTime.Now;
                _logger.LogInformation($"[中间件] 响应处理完成: {method}, RequestId: {requestContext.RequestId}, " +
                    $"处理耗时: {processingTime}ms, 响应状态: {context.Response.StatusCode}, " +
                    $"处理完成时间: {processedTime:HH:mm:ss.fff}");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, $"[中间件] 请求处理异常: {method}, RequestId: {requestContext.RequestId}, 耗时: {stopwatch.ElapsedMilliseconds}ms");
                throw;
            }
            finally
            {
                RequestContext.Clear();
            }
        }

        // ========== MQ日志功能新增方法：记录请求阶段日志 ==========
        /// <summary>
        /// 记录MQ请求日志（request阶段）
        /// 来源：从MQGrpcLogInterceptor.cs转移过来的请求日志记录功能
        /// 功能：在请求开始时记录基础信息，包括RequestId、请求时间、接口名称等
        /// 注意：这里无法获取用户信息和请求参数，这些信息在gRPC拦截器中补充
        /// </summary>
        private async Task LogRequestToMQAsync(HttpContext context, RequestContext requestContext, DateTime startTime)
        {
            try
            {
                _logger.LogInformation($"[MQ日志] 开始入队 Request 阶段日志, RequestId: {requestContext.RequestId}, 接口: {requestContext.MethodName}");
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var logWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();

                    // 构建请求日志对象（只包含HTTP层面可获取的信息）
                    var requestLog = new MQInterfaceLog
                    {
                        RequestId = requestContext.RequestId,
                        RequestTime = startTime,
                        InterfaceName = requestContext.MethodName,
                        HttpMethod = "gRPC",
                        RequestParams = null, // 中间件无法获取gRPC请求参数
                        ClientIp = requestContext.ClientIp,
                        UserId = null, // 中间件无法获取用户信息
                        UserName = null,
                        LogType = "interface",
                        LogLevel = "info",
                        ProjectId = null, // 这些信息在gRPC拦截器中补充
                        ProjectName = null,
                        SessionId = null,
                        ServerIp = GetServerIp(),
                        ServerName = Environment.MachineName,
                        // AppName = AppDomain.CurrentDomain.FriendlyName,
                        AppName = _configuration["LogSettings:AppName"],
                        AppVersion = _configuration["LogSettings:AppVersion"],
                        Environment = _configuration["LogSettings:Environment"],
                        AddTime = DateTime.Now,
                        ResponseTime = null,
                        IsSuccess = null,
                        ResponseStatusCode = null,
                        TotalMilliseconds = null,
                        MiddleTotalMilliseconds = null,
                        MiddleResponseTime = null,
                        ErrorMessage = null,
                        SourceClassName = null,
                        SourceMethodName = null,
                        LogStage = "request" // 标识为请求阶段
                    };

                    await logWriterService.WriteInterfaceLogAsync(requestLog);
                    _logger.LogInformation($"[MQ日志] Request 阶段日志入队完成, RequestId: {requestContext.RequestId}");
                }
            }
            catch (OutOfMemoryException ex)
            {
                // 内存不足时只记录警告，不影响主流程
                _logger.LogWarning($"[MQ日志] 系统内存不足，Request 阶段日志记录失败: {requestContext.RequestId}, 异常: {ex.Message}");
            }
            catch (Exception ex)
            {
                // MQ日志记录失败不影响主流程
                _logger.LogError(ex, $"[MQ日志] Request 阶段日志入队失败: {requestContext.RequestId}, 异常: {ex.Message}");
            }
        }

        private void ExtractBasicHttpInfo(HttpContext context, RequestContext requestContext)
        {
            // 优先从 header 读取 client-ip
            if (context.Request.Headers.TryGetValue("client-ip", out var clientIp) && !string.IsNullOrEmpty(clientIp))
            {
                requestContext.ClientIp = clientIp.ToString();
                requestContext.SetItem("ClientIpSource", "Header");
            }
            else
            {
                var remoteIp = context.Connection.RemoteIpAddress?.ToString();
                if (!string.IsNullOrEmpty(remoteIp))
                {
                    requestContext.ClientIp = remoteIp;
                    requestContext.SetItem("ClientIpSource", "HttpContext");
                }
            }

            if (context.Request.Headers.TryGetValue("User-Agent", out var userAgent))
            {
                requestContext.SetItem("UserAgent", userAgent.ToString());
            }

            requestContext.SetItem("Protocol", context.Request.Protocol);
            requestContext.SetItem("Scheme", context.Request.Scheme);
            requestContext.SetItem("Host", context.Request.Host.ToString());
        }

        private static bool IsGrpcRequest(HttpContext context)
        {
            return context.Request.ContentType?.StartsWith("application/grpc") == true ||
                   context.Request.Headers.ContainsKey("grpc-encoding") ||
                   context.Request.Path.StartsWithSegments("/bimbase.api");
        }

        private string GetServerIp()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }
    }
}