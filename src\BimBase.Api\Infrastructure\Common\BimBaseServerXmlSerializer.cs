﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Common
{
    internal static class BimBaseServerXmlSerializer
    {
        internal static bool ToXmlFile<T>(T obj, String strXmlFileName) where T : class
        {
            XmlSerializer xmlser = new XmlSerializer(typeof(T), new XmlRootAttribute { Namespace = null });
            using (TextWriter sw = new StreamWriter(strXmlFileName))
            {
                var xmlSN = new XmlSerializerNamespaces();
                xmlSN.Add("", "");
                xmlser.Serialize(sw, obj);
            }
            return true;
        }

        internal static T FromXmlFile<T>(String strXmlFileName) where T : class
        {
            XmlSerializer xmlser = new XmlSerializer(typeof(T), new XmlRootAttribute { Namespace = null });
            using (XmlTextReader sr = new XmlTextReader(strXmlFileName))
            {
                return xmlser.Deserialize(sr) as T;
            }
        }
    }
}
