using System;
using System.Collections.Generic;
using System.Threading;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// 请求上下文，存储当前请求的上下文信息，类似Java的ThreadLocal
    /// </summary>
    public class RequestContext
    {
        private static readonly AsyncLocal<RequestContext> _current = new AsyncLocal<RequestContext>();
        
        /// <summary>
        /// 获取当前请求的上下文
        /// </summary>
        public static RequestContext Current 
        {
            get => _current.Value;
            private set => _current.Value = value;
        }
        
        /// <summary>
        /// 初始化一个新的请求上下文
        /// </summary>
        /// <returns>新创建的请求上下文</returns>
        public static RequestContext Initialize()
        {
            var context = new RequestContext();
            Current = context;
            return context;
        }
        
        /// <summary>
        /// 清除当前请求上下文
        /// </summary>
        public static void Clear()
        {
            Current = null;
        }
        
        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 请求开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 请求方法名
        /// </summary>
        public string MethodName { get; set; }
        
        /// <summary>
        /// 请求用户ID
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        
        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }
        
        /// <summary>
        /// 请求客户端ID
        /// </summary>
        public string ClientId { get; set; }
        
        /// <summary>
        /// 客户端版本
        /// </summary>
        public string ClientVersion { get; set; }
        
        /// <summary>
        /// 客户端IP地址
        /// </summary>
        public string ClientIp { get; set; }
        
        /// <summary>
        /// 额外数据字典
        /// </summary>
        public Dictionary<string, object> Items { get; } = new Dictionary<string, object>();
        
        /// <summary>
        /// 获取数据项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>数据值</returns>
        public T GetItem<T>(string key)
        {
            if (Items.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            
            return default;
        }
        
        /// <summary>
        /// 设置数据项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public void SetItem<T>(string key, T value)
        {
            Items[key] = value;
        }
    }
} 