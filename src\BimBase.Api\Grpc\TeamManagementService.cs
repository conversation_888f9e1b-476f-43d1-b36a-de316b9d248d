﻿using AutoMapper;
using AutoMapper.Execution;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Ocsp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Transactions;
using static BimBase.Api.Protos.GrpcTeamManagement;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using BimBase.Api.Infrastructure.MainDomain;
using System.Text;
using Google.Protobuf;
using MySqlConnector;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Hosting;
using System.Xml.Linq;
using Microsoft.Data.SqlClient;
using Humanizer;
using System.Security.Policy;
using Microsoft.CodeAnalysis.CSharp;
using System.Runtime.InteropServices;
using static StackExchange.Redis.Role;
using static Org.BouncyCastle.Math.EC.ECCurve;
using Microsoft.CodeAnalysis;
using static Google.Rpc.Context.AttributeContext.Types;
using BimBase.Api.Infrastructure.Grpc;
using Microsoft.Extensions.DependencyInjection;

namespace BimBase.Api.Grpc
{
    #region Helper Classes 

    public class ProjectData
    {
        public MainProjects MainProject { get; set; }
        public List<TeamProject> TeamProjects { get; set; }
        public List<Guid> TeamProjectIds { get; set; }
        public List<FileDirectory> FileDirectories { get; set; }
        public List<Volume> Volumes { get; set; }
        public List<MainProjectUserGroupMember> MainProjectUserGroupMembers { get; set; }
        public List<MainProjectUserGroupLibAuth> MainProjectUserGroupLibAuths { get; set; }
        public List<MainProjectUserGroupAuth> MainProjectUserGroupAuths { get; set; }
        public List<MainProjectTeamGroup> MainProjectTeamGroups { get; set; }
        public List<TeamMember> TeamMembers { get; set; }
        public List<TeamUserGroup> TeamUserGroups { get; set; }
        public List<TeamGroup> TeamGroups { get; set; }

        public List<ClientModuleVersion> clientModuleVersions { get; set; }
        public IMainProjectRepository MainProjectManager { get; set; }
    }

    #endregion
    public class uploadmodeldatafilelist
    {
        public string savefilename;
        public string deletefilename;
        public string modifyfilename;
    }
    public class TeamManagementService : GrpcTeamManagementBase
    {
        private readonly UrlsConfig _urls;
        private readonly ITeamRepository _teamRepository;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        private readonly ILogger<TeamManagementService> _logger;
        private readonly ILibraryRepository _libRepository;//_libRepository;

        private const string DomainClass = "默认构件类型";
        private const string ProjectMember = "项目默认成员模板";
        private const string ProjectRole = "默认角色模板";
        private const string Releaseinformation = "默认可发布类型模板";
        private const string WorkGroup = "默认工作组";
        private readonly char separator = Path.DirectorySeparatorChar;

        public TeamManagementService(IOptions<UrlsConfig> config, ITeamRepository teamRepository, ILibraryRepository libRepository
            , IMapper mapper
            , IAuthorityManager authorityManager
            , ILogger<TeamManagementService> logger)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _libRepository = libRepository ?? throw new ArgumentNullException(nameof(libRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _authorityManager = authorityManager ?? throw new ArgumentNullException(nameof(authorityManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        static string GetAssemblyDirectory()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                var location = Assembly.GetExecutingAssembly().Location;
                if (location.StartsWith("file:///"))
                {
                    location = location.Substring("file:///".Length);
                }
                // 这里不需要替换分隔符
                var dir = Path.GetDirectoryName(location);
                return dir ?? string.Empty;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                var options = serviceProvider.GetService<IOptions<UrlsConfig>>();
                return options?.Value?.LoadDataSavePath; //Environment.GetEnvironmentVariable("MY_DOCUMENTS");
            }
            throw new PlatformNotSupportedException("Unsupported operating system");
        }
        public const int CHUNK_STRING_LENGTH = 30000;
        private void ExecuteDbCommand(StringBuilder dataSb, string filename)
        {
            //Guid fileGuid = Guid.NewGuid();
            string foldPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                foldPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                foldPath = _urls.LoadDataSavePath;//Environment.GetEnvironmentVariable("MY_DOCUMENTS");
            }
            string datafilePath = foldPath + separator + "data" + filename + ".txt";
            datafilePath = datafilePath.Replace("\\", separator.ToString());
            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                fileStream.Position = fileStream.Length;
                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {

                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());
                }
            }


        }

        private string readCommand(string filename)
        {
            //Guid fileGuid = Guid.NewGuid();
            string foldPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                foldPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                foldPath = _urls.LoadDataSavePath;//Environment.GetEnvironmentVariable("MY_DOCUMENTS");
            }
            string datafilePath = foldPath + separator + "data" + filename + ".txt";
            datafilePath = datafilePath.Replace("\\", separator.ToString());
            string strLine = "";
            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {

                using (StreamReader sr = new StreamReader(fileStream))
                {

                    strLine = sr.ReadToEnd();
                }
            }

            return strLine;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        [AllowAnonymous]
        public override Task<TestTeamResponse> TestTeam(TestTeamRequest request, ServerCallContext context)
        {
            //string sessionId = request.SessionId;
            var x = new TestTeamResponse();
            //`pkpm-pbimserver-mpdb-e1428bec-e8d4-4206-8de9-fd8c474b9ffe`
            var mainProjectDBName = "test";
            var sqlpath = "E:\\pkpm-pbimserver-modeldb-fa1aaf96-623e-41d2-acd5-bd03926448a3.sql";
            var conf = UtilityHelper.GetMysqlConnectionConfig();
            var databaseIp = conf.IP;
            var databasePort = conf.Port;
            var dbusername = conf.User;
            var dbpassword = conf.Password;
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "mysql",  // mysql 命令
                    WorkingDirectory = "C:\\Program Files\\MySQL\\MySQL Server 5.6\\bin\\",
                    Arguments = $" -h {databaseIp} -u {dbusername} -p{dbpassword} {mainProjectDBName} < {sqlpath}",
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardError = true,
                    RedirectStandardInput = true
                };

                using (var process = Process.Start(processInfo))
                {
                    //string StandardError = process.StandardError.ReadToEnd();

                    process.WaitForExit();
                    //if (StandardError!="")
                    //{
                    //    Console.WriteLine(StandardError);
                    //}

                }
                Console.WriteLine("Database backup completed successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while backing up the database: {ex.Message}");
            }
            //var save = request.Saveobject;
            //StringBuilder sbsave = new StringBuilder();
            //sbsave.Append(string.Join(",", save));
            //string savename = "save_" + sessionId;
            //ExecuteDbCommand(sbsave, savename.ToString());

            //var delete = request.Deleteobject;
            //StringBuilder sbdelete = new StringBuilder();
            //sbdelete.Append(string.Join(",", delete));
            //string deletename = "delete_" + sessionId;
            //ExecuteDbCommand(sbdelete, deletename.ToString());

            //string message = readCommand(savename);
            x.IsSuccess = true;
            x.Message = "message";
            return Task.FromResult(x);
        }


        public override async Task<CreateProjectResponse> CreateProject(CreateProjectRequest request, ServerCallContext context)
        {
            var x = new CreateProjectResponse();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (request.Project is null || request.Template is null || String.IsNullOrEmpty(request.Project.Name))
                return x;
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            Infrastructure.Domain.TeamProject prj;
            string userName = string.Empty;

            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                request.Project.Id = Guid.NewGuid().ToString();

                //Infrastructure.Domain.TeamProject teamProject;

                prj = _teamRepository.AddProject(_mapper.Map<Infrastructure.Domain.TeamProject>(request.Project));

                teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

                userName = teamMember.LoginName;


                _teamRepository.AddMemberToProject(teamMember, prj, true);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                _teamRepository.AddMemberToProject(teamMember, prj);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                _teamRepository.AddMemberToProject(teamMember, prj);
                scope.Complete();
            }

            string releaseinformation = request.Template.Releaseinformation;
            string domaincls = request.Template.DomainClass;
            string role = request.Template.ProjectRole;
            string mem = request.Template.ProjectMember;
            string workGroup = request.Template.WorkGroup;

            //构件模板取默认定义
            if (String.IsNullOrEmpty(domaincls))
                domaincls = DomainClass;

            var projectRepo = _teamRepository.GetProjectRepository(prj.ID);

            if (!String.IsNullOrEmpty(releaseinformation))
            {
                CreateInfoConfig(prj.ID, releaseinformation);
            }

            string message;

            if (!String.IsNullOrEmpty(role))
            {
                CreateProjectRoleConfig(prj.ID, userName, role, out message);
            }


            var defaultMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == userName);
            if (!String.IsNullOrEmpty(mem))
            {
                AddDefaultUserToRole(prj, mem, defaultMember, out message);
            }

            x.IsSuccess = true;
            x.Project = _mapper.Map<GrpcTeamProject>(prj);
            return x;
        }


        private bool CreateInfoConfig(Guid projectGuid, string templateName)
        {
            var projectRepo = _teamRepository.GetProjectRepository(projectGuid);
            var infos = BimBaseServerTemplates.ReadTemplateFile<List<ReleaseInformation>>(BimBaseServerTemplates.ReleaseRoot + templateName + ".tplt");
            if (infos != null)
            {
                foreach (var info in infos)
                {
                    projectRepo.AddReleaseInformation(info);
                }
                return true;
            }
            return false;
        }

        private bool CreateProjectRoleConfig(Guid projectGuid, string username, string templateName, out string message)
        {
            message = string.Empty;

            var roleTemplates = BimBaseServerTemplates.ReadTemplateFile<List<RoleTemplate>>(BimBaseServerTemplates.ProjectRoleRoot + templateName + ".tplt");
            var teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == username);
            if (null == teamMember)
            {
                message = "用户登录失效";
                return false;
            }

            if (roleTemplates != null)
            {
                var defaultRole = roleTemplates.SingleOrDefault(r => r.Name == "项目默认角色");

                if (null == defaultRole)
                {
                    var defaultRoleTemplate = BimBaseServerConfigurations.ReadConfiguration<RoleTemplate>("RoleTemplate.config");
                    GrpcRole defRole = new GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = defaultRoleTemplate.Name,
                        Status = defaultRoleTemplate.Status,
                        Type = defaultRoleTemplate.Type
                    };


                    var ret = _authorityManager.AddProjectRole(projectGuid.ToString(), defRole, teamMember.ID, out message);

                    if (!ret)
                        return false;

                    ret = _authorityManager.GiveRoleAuth(defRole.Id, defaultRoleTemplate.AuthInfos, teamMember.ID, out message);
                    if (!ret)
                        return false;
                }
                foreach (var roleTemplate in roleTemplates)
                {
                    GrpcRole role = new GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = roleTemplate.Name,
                        Status = roleTemplate.Status,
                        Type = roleTemplate.Type
                    };

                    var ret = _authorityManager.AddProjectRole(projectGuid.ToString(), role, teamMember.ID, out message);
                    if (!ret)
                        return false;

                    ret = _authorityManager.GiveRoleAuth(role.Id, roleTemplate.AuthInfos, teamMember.ID, out message);
                    if (!ret)
                        return false;
                }
                return true;
            }
            return false;

        }
        private bool AddDefaultUserToRole(Infrastructure.Domain.TeamProject prj, string templateName, Infrastructure.Domain.TeamMember defaultMember, out string message)
        {
            var sw = new Stopwatch();
            sw.Start();
            List<GrpcRole> roles;
            message = "";
            var ret = _authorityManager.GetProjectRole(prj.ID.ToString(), out roles, out message);
            if (!ret)
                return false;
            var role = roles.SingleOrDefault(r => r.Name == "项目默认角色");

            var members = BimBaseServerTemplates.ReadTemplateFile<List<Infrastructure.Domain.TeamMember>>(BimBaseServerTemplates.ProjectMemberRoot + templateName + ".tplt");
            if (members != null)
            {
                var memList = _teamRepository.Members.ToList().FindAll(mem => (members.Exists(member => member.LoginName == mem.LoginName)));
                foreach (var member in memList)
                {
                    _teamRepository.AddMemberToProject(member, prj);

                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { member.ID },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                sw.Stop();
                //PbimLog.Info("AddDefaultUserToRole===>AddMemberToProject====>" + sw.ElapsedMilliseconds);
                sw.Restart();
                if (!members.Exists(m => m.LoginName == defaultMember.LoginName))
                {
                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { defaultMember.ID },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                if (!members.Exists(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName))
                {
                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { BuildinAdministrators.BuildInAdministratorGuid },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                sw.Stop();
                return true;
            }
            return false;
        }

        public override async Task<CreateMainProjectResponse> CreateMainProject(CreateMainProjectRequest request, ServerCallContext context)
        {
            var x = new CreateMainProjectResponse();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (request.MainProject is null || String.IsNullOrEmpty(request.MainProject.Name))
            {
                x.Message = $"参数不能为空";
                return x;
            }


            Infrastructure.Domain.MainProject prj;
            string userName = string.Empty;
            var paraMainproject = request.MainProject;
            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                request.MainProject.Id = Guid.NewGuid().ToString();
                MainProject disctinctmp = _teamRepository.MainProjects.FirstOrDefault(m => m.Name.ToLower() == request.MainProject.Name.ToLower());
                if (disctinctmp != null)
                {
                    x.Message = "已存在同名项目";
                    return x;
                }

                var clientVersion = GrpcContextAccessor.GetClientVersion();
                var clientId = GrpcContextAccessor.GetClientId();
                var status = "success";
                var serverMainproject = _mapper.Map<Infrastructure.Domain.MainProject>(paraMainproject);
                serverMainproject.ClientVersion = clientVersion;
                serverMainproject.clientId = clientId;
                serverMainproject.status = status;
                prj = _teamRepository.AddMainProject(serverMainproject);

                teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);


                _teamRepository.AddMemberToMainProject(teamMember, prj);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                _teamRepository.AddMemberToMainProject(teamMember, prj);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                _teamRepository.AddMemberToMainProject(teamMember, prj);
                x.MainProject = _mapper.Map<GrpcMainProject>(prj);
                scope.Complete();
            }

            //var mainProjectRepo = _teamRepository.GetMainProjectRepository(prj.ID);

            return x;
        }

        public override async Task<CreateProjectResponse> CreateProjectWithSchemaVersion(CreateProjectWithSchemaVersionRequest request, ServerCallContext context)
        {
            var x = new CreateProjectResponse();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (request.Project is null || request.Template is null || String.IsNullOrEmpty(request.Project.Name))
                return x;

            Infrastructure.Domain.TeamProject prj;
            string userName = string.Empty;

            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                request.Project.Id = Guid.NewGuid().ToString();
                prj = _teamRepository.AddProject(_mapper.Map<Infrastructure.Domain.TeamProject>(request.Project));

                var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
                if (teamMember == null)
                {
                    return x;
                }

                _teamRepository.AddMemberToProject(teamMember, prj, true);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                _teamRepository.AddMemberToProject(teamMember, prj);

                teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                _teamRepository.AddMemberToProject(teamMember, prj);
                scope.Complete();
            }


            string releaseinformation = request.Template.Releaseinformation;
            string domaincls = request.Template.DomainClass;
            string role = request.Template.ProjectRole;
            string mem = request.Template.ProjectMember;
            string workGroup = request.Template.WorkGroup;

            //构件模板取默认定义
            if (String.IsNullOrEmpty(domaincls))
                domaincls = DomainClass;

            var projectRepo = _teamRepository.GetProjectRepository(prj.ID);

            if (!String.IsNullOrEmpty(releaseinformation))
            {
                CreateInfoConfig(prj.ID, releaseinformation);
            }

            string message;

            if (!String.IsNullOrEmpty(role))
            {
                CreateProjectRoleConfig(prj.ID, userName, role, out message);
            }


            var defaultMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == userName);
            if (!String.IsNullOrEmpty(mem))
            {
                AddDefaultUserToRole(prj, mem, defaultMember, out message);
            }

            bool status = projectRepo.SetProjectSchema(_mapper.Map<Infrastructure.ModelDomain.SchemaVersion>(request.SchemaVersion));

            if (!status)
            {
                x.IsSuccess = false;
                x.Message = "设置项目SchemaVersion错误";
            }

            return x;
        }

        /// <summary>
        /// 删除模型
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<DeleteProjectResponse> DeleteProject(Protos.DeleteProjectRequest request, ServerCallContext context)
        {
            var x = new DeleteProjectResponse();
            var sw = new Stopwatch();
            sw.Start();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.ProjectId, out var projectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.ProjectId} is error！";
            }
            //删除项目角色及授权等信息
            var ret = _authorityManager.DeleteProject(projectGuid, out var message);
            if (ret)
            {
                //删除项目信息
                if (_teamRepository.DeleteProject(projectGuid) != null)
                {
                    x.IsSuccess = true;
                }
            }
            else
            {
                x.Message = message;
            }

            return x;
        }

        /// <summary>
        /// 更新模型
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<ModifyProjectInfoResponse> ModifyProjectInfo(ModifyProjectInfoRequest request, ServerCallContext context)
        {
            var x = new ModifyProjectInfoResponse();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (request.Project == null || string.IsNullOrWhiteSpace(request.Project.Id) || !Guid.TryParse(request.Project.Id, out var projectGuid))
            {
                x.Message = "参数不能为空！";
                return x;
            }

            var teamProject = await _teamRepository.Projects.SingleOrDefaultAsync(p => p.ID == projectGuid);

            if (teamProject != null)
            {
                if (request.Project.Name != teamProject.Name && !_teamRepository.CheckProjectName(request.Project.Name))
                {
                    //已存在同名项目
                    x.Message = "已存在同名项目";
                    return x;
                }
                teamProject.Name = request.Project.Name;
                teamProject.Description = request.Project.Description;
                teamProject.Leader = request.Project.Leader;
                teamProject.Avatar = request.Project.Avatar;
                teamProject.StartTime = request.Project.StartTime.ToDateTime();
                teamProject.EndTime = request.Project.EndTime.ToDateTime();
                teamProject.Progress = request.Project.Progress;
                x.IsSuccess = _teamRepository.ModifyProjectInfo(teamProject);
            }
            return x;
        }

        public override Task<GetAllProjectAndRoleAndPermissionByLognameResponse> GetAllProjectAndRoleAndPermissionByLogname(GetAllProjectAndRoleAndPermissionByLognameRequest request, ServerCallContext context)
        {
            var x = new GetAllProjectAndRoleAndPermissionByLognameResponse();
            var memberRolePermission = new List<GrpcTeamMemberRoleAndPermission>();


            string name = request.LoginName;
            var mem = _teamRepository.Members.SingleOrDefault(m => m.LoginName == name);
            if (mem != null)
            {
                List<TeamProject> projects;
                if ((name == BuildinAdministrators.BuildInTeamAdministratorLoginName) || (name == BuildinAdministrators.BuildInSystemModelAdminLoginName) || (name == BuildinAdministrators.BuildInSystemAdminLoginName))
                    projects = _teamRepository.Projects.ToList();
                else
                    projects = _teamRepository.GetTeamProjectListByMember(mem.ID);

                foreach (TeamProject prj in projects)
                {
                    var trp = new GrpcTeamMemberRoleAndPermission();
                    trp.PrjGuid = prj.ID.ToString();
                    trp.PrjName = prj.Name;
                    var roles = new List<RoleDto>();
                    var ret = _authorityManager.GetRoleInfoByUserIdAndProjectId(mem.ID, prj.ID, out roles, out var message);
                    string pRoleName = "";
                    foreach (var role in roles)
                    {
                        pRoleName = pRoleName + role.Name + "|";
                    }
                    trp.RoleName = pRoleName.Substring(0, pRoleName.Length - 1);
                    var authInfos = new List<GrpcAuthInfo>();
                    string pPermission = "";
                    ret = _authorityManager.GetProjectUserAuth(mem.ID, prj.ID, out authInfos, out message);
                    foreach (var auth in authInfos)
                    {
                        string authname = "";
                        switch (auth.Name)
                        {
                            case "Architecture":
                                authname = "建筑";
                                break;
                            case "Structure":
                                authname = "结构";
                                break;
                            case "AssemblyDesign":
                                authname = "装配式";
                                break;
                            case "SPBBIMStruct":
                                authname = "新结构";
                                break;
                            case "Water":
                                authname = "给排水";
                                break;
                            case "HVAC":
                                authname = "暖通";
                                break;
                            case "Electric":
                                authname = "电气";
                                break;
                            case "AssemblySteel":
                                authname = "钢结构";
                                break;
                            default:
                                break;

                        }

                        if (auth.Name == "SPBBIMStruct" || auth.Name == "AMCommon" || auth.Name == "MepCommon")
                        {
                            continue;
                        }
                        if (auth.Permission > 0)
                        {
                            pPermission = pPermission + authname + ":";
                            if ((auth.Permission & 8) == 8)
                            {
                                pPermission += "增";
                            }
                            if ((auth.Permission & 4) == 4)
                            {
                                pPermission += "删";
                            }
                            if ((auth.Permission & 2) == 2)
                            {
                                pPermission += "改";
                            }
                            if ((auth.Permission & 1) == 1)
                            {
                                pPermission += "查";
                            }
                            pPermission += "|";
                        }
                    }
                    trp.Permission = pPermission.Substring(0, pPermission.Length - 1);
                    memberRolePermission.Add(trp);
                }
            }
            return Task.FromResult(x);
        }

        /// <summary>
        /// 添加成员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<AddMemberResponse> AddMember(AddMemberRequest request, ServerCallContext context)
        {
            var x = new AddMemberResponse();
            if (request.TeamMember == null || String.IsNullOrEmpty(request.TeamMember.PasswordMD5) || request.TeamMember.LoginName.ToLower() == BuildinAdministrators.BuildInTeamAdministratorLoginName.ToLower())
            {
                x.Message = "参数有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (teamMember is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                x.MemberGuid = string.Empty;
                return x;
            }

            var member = _mapper.Map<TeamMember>(request.TeamMember);
            member.CreateId = teamMember.ID;
            var tMember = _teamRepository.AddMember(member);

            if (tMember != null)
            {
                x.MemberGuid = tMember.ID.ToString();
                x.IsSuccess = true;
            }
            else
            {
                x.IsSuccess = false;
                x.Message = "已经存在同名用户！";
                x.MemberGuid = string.Empty;
            }


            return x;

        }

        /// <summary>
        /// 移除成员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override Task<DeleteMemberResponse> DeleteMember(DeleteMemberRequest request, ServerCallContext context)
        {
            var x = new DeleteMemberResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return Task.FromResult(x);
            }

            if (_teamRepository.DeleteMember(request.MemberName, out var message))
            {
                x.DelMemberSuccess = true;
                x.IsSuccess = true;
            }
            else
            {
                x.Message = message;
            }
            return Task.FromResult(x);
        }

        /// <summary>
        /// 编辑成员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override Task<ModifyMemberResponse> ModifyMember(ModifyMemberRequest request, ServerCallContext context)
        {
            var x = new ModifyMemberResponse();
            if (request.Member == null || string.IsNullOrWhiteSpace(request.Member.Id))
            {
                x.Message = $"参数有误！";
                return Task.FromResult(x);
            }


            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return Task.FromResult(x);
            }


            if (!Guid.TryParse(request.Member.Id, out var memberId))
            {
                x.Message = $"参数： {request.Member.Id} 有误！";
                return Task.FromResult(x);
            }
            var tmp = _teamRepository.QueryFrom<TeamMember>(m => m.ID == memberId).FirstOrDefault();

            if (tmp != null && tmp.LoginName == request.Member.LoginName)
            {
                tmp.DisplayName = request.Member.DisplayName;
                tmp.Color = request.Member.Color;
                tmp.Avatar = request.Member.Avatar;
                tmp.AvatarData = request.Member.AvatarData.ToByteArray();
                tmp.AvatarExtType = request.Member.AvatarExtType;
                tmp.Email = request.Member.Email;
                if (!string.IsNullOrWhiteSpace(request.Member.PasswordMD5)) tmp.PasswordMD5 = request.Member.PasswordMD5;
                _teamRepository.Update(tmp);
                x.IsSuccess = true;
                x.ModifyMemberSuccess = true;
            }

            return Task.FromResult(x);
        }

        /// <summary>
        /// 设置团队管理员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<SetTeamAdministratorResponse> SetTeamAdministrator(SetTeamAdministratorRequest request, ServerCallContext context)
        {
            var x = new SetTeamAdministratorResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }
            if (!Guid.TryParse(request.MemberId, out var memberId))
            {
                x.Message = $"参数： {request.MemberId} 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            if (currentUser.LoginName != BuildinAdministrators.BuildInTeamAdministratorLoginName)
            {
                x.Message = "只有系统管理员可以指派团队管理员";
                return x;
            }

            var ret = _authorityManager.GetRoleUsers(BuildinAdministrators.BuildInTeamAdministratorRoleGuid.ToString(), out var memberGuids, out var message);
            if (!ret)
            {
                return x;
            }

            if (memberGuids.Contains(memberId))
            {
                x.Message = "团队管理员已包含该用户";
                return x;
            }
            else
            {

                memberGuids = new List<Guid>();
                memberGuids.Add(memberId);
                ret = _authorityManager.GiveRoleUser(BuildinAdministrators.BuildInTeamAdministratorRoleGuid.ToString(), memberGuids, currentUser.ID, out message);
                if (ret)
                {
                    x.IsSuccess = ret;
                    x.SetAdminSuccess = ret;
                    return x;
                }
            }
            return x;
        }

        /// <summary>
        /// 移除团队管理员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<RemoveTeamAdministratorResponse> RemoveTeamAdministrator(RemoveTeamAdministratorRequest request, ServerCallContext context)
        {
            var x = new RemoveTeamAdministratorResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }
            if (!Guid.TryParse(request.MemberId, out var memberId))
            {
                x.Message = $"参数： {request.MemberId} 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            if (currentUser.LoginName != BuildinAdministrators.BuildInTeamAdministratorLoginName)
            {
                x.Message = "只有系统管理员可以移除团队管理员";
                return x;
            }

            var ret = _authorityManager.GetRoleUsers(BuildinAdministrators.BuildInTeamAdministratorRoleGuid.ToString(), out var memberGuids, out var message);
            if (!ret)
            {
                return x;
            }

            if (!memberGuids.Contains(memberId))
            {
                x.Message = "团队管理员中未包含该用户";
                return x;
            }
            else
            {

                memberGuids = new List<Guid>();
                memberGuids.Add(memberId);
                ret = _authorityManager.DeleteRoleUser(BuildinAdministrators.BuildInTeamAdministratorRoleGuid.ToString(), memberGuids.Select(x => x.ToString()).ToList(), out message);
                if (ret)
                {
                    x.IsSuccess = ret;
                    x.RemoveAdminSuccess = ret;
                    return x;
                }
            }
            return x;
        }

        public override async Task<ExportTeamMembersResponse> ExportTeamMembers(ExportTeamMembersRequest request, ServerCallContext context)
        {
            var x = new ExportTeamMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }
            if (request.UserNameList is null || string.IsNullOrWhiteSpace(request.TemplatePath))
            {
                x.Message = $"参数不能为空！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            var members = _teamRepository.QueryFrom<TeamMember>(mem => (request.UserNameList.ToList().Exists(m => m == mem.LoginName) && mem.LoginName != BuildinAdministrators.BuildInTeamAdministratorLoginName));
            List<TeamMember> tMems = new List<TeamMember>();
            foreach (var mem in members)
            {
                TeamMember member = new TeamMember();
                member.LoginName = mem.LoginName;
                member.Avatar = mem.Avatar;
                member.DisplayName = mem.DisplayName;
                member.PasswordMD5 = mem.PasswordMD5;
                member.Color = mem.Color;
                member.Department = mem.Department;
                mem.Email = mem.Email;
                tMems.Add(member);
            }
            var saveSuccess = BimBaseServerTemplates.SaveTemplateFile(tMems, BimBaseServerTemplates.TeamMemberRoot + request.TemplatePath + ".tplt");

            x.IsSuccess = saveSuccess;
            x.ExportMembersSuccess = saveSuccess;
            return x;
        }

        public override async Task<ImportTeamMembersResponse> ImportTeamMembers(ImportTeamMembersRequest request, ServerCallContext context)
        {
            var x = new ImportTeamMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.TemplatePath))
            {
                x.Message = $"参数不能为空！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            var members = BimBaseServerTemplates.ReadTemplateFile<List<TeamMember>>(BimBaseServerTemplates.TeamMemberRoot + request.TemplatePath + ".tplt");

            foreach (var member in members)
            {
                if (_teamRepository.QueryFrom<TeamMember>(m => m.LoginName == member.LoginName).FirstOrDefault() == null)
                    _teamRepository.AddMember(member);
            }
            x.IsSuccess = true;
            x.ImportMembersSuccess = true;
            return x;
        }

        public override async Task<CopyMemberAndRoleToProjectResponse> CopyMemberAndRoleToProject(CopyMemberAndRoleToProjectRequest request, ServerCallContext context)
        {
            var x = new CopyMemberAndRoleToProjectResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.FromProjectId) || !Guid.TryParse(request.FromProjectId, out var fromProjectId))
            {
                x.Message = $"参数FromProjectId不能为空！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ToProjectId) || !Guid.TryParse(request.ToProjectId, out var toProjectId))
            {
                x.Message = $"参数ToProjectId不能为空！";
                return x;
            }

            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            var fromTeamProject = _teamRepository.QueryFrom<TeamProject>(tp => tp.ID == fromProjectId).SingleOrDefault();
            var toTeamProject = _teamRepository.QueryFrom<TeamProject>(tp => tp.ID == toProjectId).SingleOrDefault();
            if (fromTeamProject != null && toTeamProject != null)
            {
                //获取要复制的项目的人员
                List<TeamMember> fromMembers = GetCopyOrDeleteMembers(fromProjectId);
                List<TeamMember> toMembers = GetCopyOrDeleteMembers(toProjectId);

                CopyProjectRoleConfig(fromProjectId, toProjectId, teamMember.LoginName, out var toRoles, out var newRoles, out var message);

                var members = _teamRepository.QueryFrom<TeamMember>(m => toMembers.Select(s => s.LoginName).Contains(m.LoginName));

                bool flag = true;

                foreach (var member in members)
                {
                    //删除toprojectid 中的成员
                    flag = flag && _teamRepository.RemoveMemberFromProject(member, toTeamProject);
                }

                foreach (var member in fromMembers)
                {
                    //将fromprojectid中 成员加入 toprojectid 项目中
                    var pMem = _teamRepository.AddMemberToProject(member, toTeamProject, false);
                }
                var ret = _authorityManager.GetProjectRole(fromProjectId.ToString(), out var fromRoles, out message);
                List<Guid> memberGuids;
                foreach (var fromRole in fromRoles)
                {
                    ret = _authorityManager.GetRoleUsers(fromRole.Id, out memberGuids, out message);
                    foreach (var toRole in newRoles)
                    {
                        if (fromRole.Name == toRole.Name)
                        {
                            ret = _authorityManager.GiveRoleUser(toRole.Id, memberGuids, teamMember.ID, out message);
                        }
                    }
                }

            }
            x.IsSuccess = true;
            x.CopyMembersSuccess = true;
            return x;
        }

        private List<TeamMember> GetCopyOrDeleteMembers(Guid proID)
        {
            List<ProjectMember> members = _teamRepository.GetProjectRepository(proID).Members.ToList();
            List<TeamMember> teamMembers = new List<TeamMember>();
            foreach (var mem in members)
            {
                TeamMember teamMember = _teamRepository.Members.SingleOrDefault(memT => memT.ID == mem.TeamMemberID);
                if (teamMember.LoginName != "system")
                {
                    teamMembers.Add(teamMember);
                }
            }
            return teamMembers;
        }

        private bool CopyProjectRoleConfig(Guid fromProjectGuid, Guid toProjectGuid, string username, out List<GrpcRole> roles, out List<GrpcRole> newRoles, out string message)
        {
            message = "";
            roles = new List<GrpcRole>();
            newRoles = new List<GrpcRole>();
            var teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == username);
            if (null == teamMember)
            {
                message = "用户登录失效";
                return false;
            }
            //查找要复制角色列表的项目的旧的角色列表
            var deleteOldRoles = _authorityManager.GetProjectRole(toProjectGuid.ToString(), out roles, out message);

            //删除toProjectGuid项目角色，同时把角色的权限和成员一并删除
            var deleteRet = _authorityManager.DeleteProjectRoles(roles.Select(c => c.Id).ToList(), out message);
            var ret = _authorityManager.GetProjectRole(fromProjectGuid.ToString(), out roles, out message);

            if (ret)
            {
                foreach (var r in roles)
                {
                    var role = new GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = r.Name,
                        Status = r.Status,
                        Type = r.Type
                    };
                    newRoles.Add(role);
                    //复制角色列表到 toProjectGuid 项目中
                    ret = _authorityManager.AddProjectRole(toProjectGuid.ToString(), role, teamMember.ID, out message);
                    if (!ret)
                    {
                        return false;
                    }
                    //相应角色的权限
                    List<GrpcAuthInfo> authInfos = new List<GrpcAuthInfo>();
                    ret = _authorityManager.GetRoleAuthorities(r.Id, out authInfos);
                    if (!ret)
                    {
                        return false;
                    }
                    //将获取到的权限拷贝到 toProjectGuid 项目中
                    ret = _authorityManager.GiveRoleAuth(role.Id, authInfos, teamMember.ID, out message);
                    if (!ret)
                    {
                        return false;
                    }

                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 创建文件目录列表
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<CreateFileDicListResponse> CreateFileDicList(CreateFileDicListRequest request, ServerCallContext context)
        {
            var x = new CreateFileDicListResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }

            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var files = _mapper.Map<List<FileDirectory>>(request.Files);
            var ret = _teamRepository.AddFileDirectoreyList(files);
            x.IsSuccess = ret;
            return x;
        }

        /// <summary>
        /// 创建文件目录
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<CreateFileDirectoryResponse> CreateFileDirectory(CreateFileDirectoryRequest request, ServerCallContext context)
        {
            var x = new CreateFileDirectoryResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }

            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var files = _mapper.Map<FileDirectory>(request.File);
            var ret = _teamRepository.AddFileDirectorey(files);
            if (ret != null)
            {
                x.IsSuccess = true;
                x.File = _mapper.Map<GrpcFileDirectory>(ret);
            }
            return x;
        }

        /// <summary>
        /// 移除组用户
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<RemoveMemberFromTeamGroupResponse> RemoveMemberFromTeamGroup(RemoveMemberFromTeamGroupRequest request, ServerCallContext context)
        {
            var x = new RemoveMemberFromTeamGroupResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = "参数 SessionId 不能为空！";
                return x;
            }

            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var nameList = request.NameList;
            var teamGroupId = request.TeamGroupId;
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            List<Guid> guidList = new List<Guid>();
            foreach (var n in nameList)
            {
                Guid g = Guid.Parse(n);
                guidList.Add(g);
            }
            //获取要加入角色的成员对象列表
            List<TeamMember> members = memberTmp.FindAll(mem => guidList.Exists(na => na == mem.ID));
            TeamGroup mp = _teamRepository.TeamGroups.FirstOrDefault(m => m.Id == teamGroupId);
            if (members.Any() && mp != null)
            {
                foreach (var name in members)
                {
                    bool flag = _teamRepository.RemoveMemberFromTeamGroup(name, mp);
                    if (!flag)
                    {
                        _logger.LogInformation("用户" + name.LoginName + "移除出组" + mp.GroupName + "失败");
                    }
                }
            }
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 修改团队组权限
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> ModifyTeamGroupAuth(ModifyTeamGroupAuthRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var authList = _mapper.Map<List<TeamAuth>>(request.AuthList);
            var teamGroupAuth = _mapper.Map<TeamGroupAuth>(request.TeamGroupAuth);
            var ret = _teamRepository.GiveAuthToTeamGroup(teamGroupAuth, authList);
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GrpcResult> GiveTeamGroupAuth(GiveTeamGroupAuthRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var authList = _mapper.Map<List<TeamAuth>>(request.AuthList);
            var teamGroupAuth = _mapper.Map<TeamGroupAuth>(request.TeamGroupAuth);
            var ret = _teamRepository.GiveAuthToTeamGroup(teamGroupAuth, authList);
            x.IsSuccess = ret;
            return x;
        }

        /// <summary>
        /// 为项目添加用户（组）
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddTeamGroupMemberToMainProject(AddTeamGroupMemberToMainProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var ObjectId = Guid.Empty;
            if (!Guid.TryParse(request.ObjectId, out ObjectId))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var memberIds = request.MemberIds;
            List<TeamMember> TeamMembers = _teamRepository.Members.ToList();
            List<Guid> guidList = new List<Guid>();
            foreach (var n in memberIds)
            {
                Guid g = Guid.Parse(n);
                guidList.Add(g);
            }
            //获取要加入项目的用户组对象列表
            List<TeamMember> members = TeamMembers.FindAll(mem => guidList.Exists(na => na == mem.ID));
            MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
            if (members.Any() && mp != null)
            {
                foreach (var name in members)
                {
                    bool flag = _teamRepository.AddTeamGrouMemberToMainProject(name, mp, ObjectId);
                    if (!flag)
                    {
                        _logger.LogInformation("用户" + name.LoginName + "加入项目(权限)" + mp.Name + "失败");
                    }
                }
            }
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 为项目添加组
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddTeamGroupToMainProject(AddTeamGroupToMainProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var ObjectId = Guid.Empty;
            if (!Guid.TryParse(request.ObjectId, out ObjectId))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            List<TeamGroup> TeamGroups = _teamRepository.TeamGroups.ToList();

            var teamGroupIds = request.TeamGroupIds;
            List<int> intList = new List<int>();
            foreach (var n in teamGroupIds)
            {
                int g = int.Parse(n);
                intList.Add(g);
            }
            //获取要加入项目的用户组对象列表
            List<TeamGroup> members = TeamGroups.FindAll(mem => intList.Exists(na => na == mem.Id));
            MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
            if (members.Any() && mp != null)
            {
                foreach (var name in members)
                {
                    bool flag = _teamRepository.AddTeamGroupToMainproject(name, mp, ObjectId);
                    if (!flag)
                    {
                        _logger.LogInformation("用户组" + name.GroupName + "加入项目" + mp.Name + "失败");
                    }
                }
            }
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 添加组成员
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddMemberToTeamGroup(AddMemberToTeamGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var nameList = request.NameList;
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            List<Guid> guidList = new List<Guid>();
            foreach (var n in nameList)
            {
                Guid g = Guid.Parse(n);
                guidList.Add(g);
            }
            //获取要加入角色的成员对象列表
            List<TeamMember> members = memberTmp.FindAll(mem => guidList.Exists(na => na == mem.ID));
            TeamGroup mp = _teamRepository.TeamGroups.FirstOrDefault(m => m.Id == request.TeamGroupId);
            if (members.Any() && mp != null)
            {
                foreach (var name in members)
                {
                    bool flag = _teamRepository.AddMemberToTeamGroup(name, mp);
                    if (!flag)
                    {
                        _logger.LogInformation("用户" + name.LoginName + "加入组" + mp.GroupName + "失败");
                    }
                }
            }
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 模型解锁
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        public override async Task<GrpcResult> CheckInModelLockForce(CheckInModelLockForceRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var projectGuid = Guid.Empty;
            if (!Guid.TryParse(request.ProjectGuid, out projectGuid))
            {
                x.Message = $"参数{request.ProjectGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var _modelManager = _teamRepository.GetProjectRepository(projectGuid);
            var ret = _modelManager.DeleteModelLock(currentUser.LoginName);

            x.IsSuccess = ret;
            return x;
        }

        /// <summary>
        /// 删除项目用户组
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        public override async Task<GrpcResult> DeleteGroupOrMemberFromMainProject(DeleteGroupOrMemberFromMainProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var objectGuid = Guid.Empty;
            if (!Guid.TryParse(request.ObjectGuid, out objectGuid))
            {
                x.Message = $"参数{request.ObjectGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            var memberIds = request.MemberIds.ToList();
            List<TeamMember> TeamMembers = _teamRepository.Members.ToList();

            string username = currentUser.LoginName;
            bool flag = _teamRepository.DeleteGroupOrMemberListFromMainProject(mainProjectGuid, objectGuid, memberIds);
            if (flag)
            {
                _logger.LogInformation(username + "将用户(组)(共" + memberIds.Count + "个)" + string.Join(",", memberIds.Take(3).ToList()) + "移除出项目(权限)" + mainProjectGuid);
            }


            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 编辑组
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        public override async Task<GrpcResult> UpdateTeamGroup(UpdateTeamGroupReqest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var rep = _teamRepository.TeamGroups.FirstOrDefault(t => t.Id != request.TeamGroup.Id && t.GroupName == request.TeamGroup.GroupName);
            if (rep != null)
            {
                x.Message = "名称被占用";
                return x;
            }
            else
            {
                var prj = _teamRepository.UpdateTeamGroup(_mapper.Map<TeamGroup>(request.TeamGroup));
                x.IsSuccess = prj;
            }

            return x;
        }

        /// <summary>
        /// 删除组
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        public override async Task<GrpcResult> DeleteTeamGroup(DeleteTeamGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var prj = _teamRepository.DeleteTeamGroup(request.Id);
            x.IsSuccess = prj;
            return x;
        }

        /// <summary>
        /// 创建组
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        public override async Task<AddTeamGroupWithInfoResponse> AddTeamGroupWithInfo(AddTeamGroupWithInfoRequest request, ServerCallContext context)
        {
            var x = new AddTeamGroupWithInfoResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var prj = _teamRepository.AddTeamGroup(_mapper.Map<TeamGroup>(request.TeamGroup));
            if (prj == null)
            {
                x.Message = "添加失败，名称可能被占用";
                x.IsSuccess = false;
            }
            else
            {
                x.TeamGroup = _mapper.Map<GrpcTeamGroup>(prj);
                x.IsSuccess = true;
            }

            return x;
        }

        public override async Task<GrpcResult> AddTeamGroup(AddTeamGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            TeamGroup tg = new TeamGroup();
            tg.GroupName = request.GroupName;
            var prj = _teamRepository.AddTeamGroup(tg);
            if (prj == null)
            {
                x.Message = "添加失败，名称可能被占用";
                x.IsSuccess = false;
            }
            else
            {
                x.IsSuccess = true;
            }

            return x;
        }

        /// <summary>
        /// 创建项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<CreateMainProjectForPDMSWebWithErrMsgResponse> CreateMainProjectForPDMSWebWithErrMsg(CreateMainProjectForPDMSWebWithErrMsgRequest request, ServerCallContext context)
        {
            var response = new CreateMainProjectForPDMSWebWithErrMsgResponse();

            // 1. 参数验证
            var validationResult = await ValidateCreateMainProjectRequest(request);
            if (!validationResult.IsValid)
            {
                response.Message = validationResult.ErrorMessage;
                return response;
            }

            var currentUser = validationResult.CurrentUser;
            request.Project.CreateUser = currentUser.LoginName;

            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                // 2. 创建主项目
                var mainProject = await CreateMainProject(request, currentUser);
                if (mainProject == null)
                {
                    response.Message = "创建主项目失败";
                    return response;
                }

                // 3. 添加项目成员
                await AddProjectMembers(mainProject, currentUser);

                // 4. 保存节点名称检查配置
                await SaveNodeNameCheckConfigs(request.NodeNameCheckConfig);

                // 5. 创建PDMS子项目
                var teamProject = await CreatePDMSSubProject(mainProject, currentUser);
                if (teamProject == null)
                {
                    response.Message = "创建PDMS子项目失败";
                    return response;
                }

                // 6. 设置响应结果
                response.IsSuccess = true;
                response.Teamproject = _mapper.Map<GrpcTeamProject>(teamProject);
                response.Mainproject = _mapper.Map<GrpcMainProject>(mainProject);

                scope.Complete();
            }

            return response;
        }

        /// <summary>
        /// 验证创建主项目请求参数
        /// </summary>
        private async Task<(bool IsValid, string ErrorMessage, TeamMember CurrentUser)> ValidateCreateMainProjectRequest(CreateMainProjectForPDMSWebWithErrMsgRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                return (false, "参数 SessionId 有误！", null);
            }

            if (request.Project is null || string.IsNullOrEmpty(request.Project.Name))
            {
                return (false, "项目参数不能为空", null);
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                return (false, "用户登录已失效，请保存退出并重新登录服务器", null);
            }

            return (true, null, currentUser);
        }

        /// <summary>
        /// 创建主项目
        /// </summary>
        private async Task<MainProject> CreateMainProject(CreateMainProjectForPDMSWebWithErrMsgRequest request, TeamMember currentUser)
        {
            // 生成项目ID
            request.Project.Id = Guid.NewGuid().ToString();

            // 检查项目名称是否重复
            var existingProject = _teamRepository.MainProjects.FirstOrDefault(m => m.Name.ToLower() == request.Project.Name.ToLower());
            if (existingProject != null)
            {
                throw new InvalidOperationException("已存在同名项目");
            }

            // 准备主项目数据
            var mainProject = PrepareMainProjectData(request.Project);
            
            // 保存到数据库
            return _teamRepository.AddMainProject(mainProject);
        }

        /// <summary>
        /// 准备主项目数据
        /// </summary>
        private MainProject PrepareMainProjectData(GrpcMainProject grpcMainProject)
        {
            var now = DateTime.Now.ToUniversalTime();
            grpcMainProject.CreationTime = Timestamp.FromDateTime(now);
            grpcMainProject.LastUpdateTime = Timestamp.FromDateTime(now);

            var mainProject = _mapper.Map<MainProject>(grpcMainProject);
            mainProject.ClientVersion = GrpcContextAccessor.GetClientVersion();
            mainProject.clientId = GrpcContextAccessor.GetClientId();
            mainProject.status = "success";

            return mainProject;
        }

        /// <summary>
        /// 添加项目成员
        /// </summary>
        private async Task AddProjectMembers(MainProject mainProject, TeamMember currentUser)
        {
            // 添加当前用户
            _teamRepository.AddMemberToMainProject(currentUser, mainProject);

            // 添加团队管理员
            var teamAdmin = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
            if (teamAdmin != null)
            {
                _teamRepository.AddMemberToMainProject(teamAdmin, mainProject);
            }

            // 添加系统管理员
            var systemAdmin = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
            if (systemAdmin != null)
            {
                _teamRepository.AddMemberToMainProject(systemAdmin, mainProject);
            }
        }

        /// <summary>
        /// 保存节点名称检查配置
        /// </summary>
        private async Task SaveNodeNameCheckConfigs(Google.Protobuf.Collections.RepeatedField<GrpcNodeNameCheckConfig> nodeNameCheckConfigs)
        {
            if (nodeNameCheckConfigs == null || !nodeNameCheckConfigs.Any())
            {
                return;
            }

            try
            {
                var configs = _mapper.Map<List<NodeNameCheckConfig>>(nodeNameCheckConfigs);
                var addResult = _teamRepository.AddNodeNameCheckConfigs(configs);
                
                if (addResult)
                {
                    _logger.LogInformation($"成功保存 {configs.Count} 条节点名称检查配置");
                }
                else
                {
                    _logger.LogWarning("保存节点名称检查配置失败，但项目创建继续执行");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存节点名称检查配置时发生异常，但项目创建继续执行");
            }
        }

        /// <summary>
        /// 创建PDMS子项目
        /// </summary>
        private async Task<TeamProject> CreatePDMSSubProject(MainProject mainProject, TeamMember currentUser)
        {
            // 创建PDMS项目
            var pdmsProject = CreatePDMSProject(mainProject, currentUser);
            var teamProject = _teamRepository.AddProject(pdmsProject);
            
            if (teamProject == null)
            {
                return null;
            }

            // 添加到主项目仓库
            var mainProjectRepo = _teamRepository.GetMainProjectRepository(mainProject.ID);
            var mpTeamProject = CreateMPTeamProject(teamProject);
            mainProjectRepo.AddMPTeamProject(mpTeamProject);

            // 添加项目成员
            await AddTeamProjectMembers(teamProject, mainProject);

            return teamProject;
        }

        /// <summary>
        /// 创建PDMS项目
        /// </summary>
        private TeamProject CreatePDMSProject(MainProject mainProject, TeamMember currentUser)
        {
            return new TeamProject
            {
                Name = mainProject.Name,
                CreateUser = mainProject.CreateUser,
                CreationTime = DateTime.Now,
                Description = "pdms自动创建与项目名称相同模型",
                MainProjectID = mainProject.ID.ToString(),
                FileDirectoryID = Guid.Empty.ToString(),
                Leader = currentUser.LoginName,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(2),
                ProjectType = 0,
                Progress = "完成"
            };
        }

        /// <summary>
        /// 创建MP团队项目
        /// </summary>
        private MPTeamProject CreateMPTeamProject(TeamProject teamProject)
        {
            return new MPTeamProject
            {
                ID = teamProject.ID,
                Avatar = teamProject.Avatar,
                CreateUser = teamProject.CreateUser,
                CreationTime = teamProject.CreationTime,
                Description = teamProject.Description,
                EnableAuthority = teamProject.EnableAuthority,
                EndTime = teamProject.EndTime,
                ExtendProperty = teamProject.ExtendProperty,
                FileDirectoryID = teamProject.FileDirectoryID,
                FilePath = teamProject.FilePath,
                Leader = teamProject.Leader,
                Name = teamProject.Name,
                ParentProjectID = teamProject.ParentProjectID,
                Progress = teamProject.Progress,
                ProjectType = teamProject.ProjectType,
                StartTime = teamProject.StartTime,
                InitState = 0
            };
        }

        /// <summary>
        /// 添加团队项目成员
        /// </summary>
        private async Task AddTeamProjectMembers(TeamProject teamProject, MainProject mainProject)
        {
            // 添加项目创建者
            var creator = _teamRepository.Members.SingleOrDefault(m => m.LoginName == mainProject.CreateUser);
            if (creator != null)
            {
                _teamRepository.AddMemberToProject(creator, teamProject, true);
            }

            // 添加团队管理员
            var teamAdmin = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
            if (teamAdmin != null)
            {
                _teamRepository.AddMemberToProject(teamAdmin, teamProject);
            }

            // 添加系统管理员
            var systemAdmin = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
            if (systemAdmin != null)
            {
                _teamRepository.AddMemberToProject(systemAdmin, teamProject);
            }
        }

        /// <summary>
        /// 检查项目名称
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> CheckMainProjectName(CheckMainProjectNameRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var serverProject = _teamRepository.MainProjects.FirstOrDefault(p => p.Name == request.ProjectName);


            if (serverProject != null)
            {
                x.Message = "名称重复";
                x.IsSuccess = false;
            }
            else
            {
                x.IsSuccess = true;
            }
            return x;
        }

        /// <summary>
        /// 检查文件夹名称
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> CheckFileDirectory(CheckFileDirectoryRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var parentfileid = request.Parentfileid;
            var directoryName = request.DirectoryName;
            var serverProject = _teamRepository.FileDirectories.FirstOrDefault(p => p.ParentID.ToString().ToLower() == parentfileid.ToLower() && p.Name.ToLower() == directoryName.ToLower());


            if (serverProject != null)
            {
                x.Message = "名称重复";
                x.IsSuccess = false;
            }
            else
            {
                x.IsSuccess = true;
            }
            return x;
        }

        /// <summary>
        /// 删除文件目录
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> DeleteFileDir(DeleteFileDirRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var filedirGuid = Guid.Empty;
            if (!Guid.TryParse(request.FiledirGuid, out filedirGuid))
            {
                x.Message = $"参数{request.FiledirGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                if (_teamRepository.DeleteFileDirectory(filedirGuid) != null)
                {
                    x.IsSuccess = true;
                }
                scope.Complete();
            }
            return x;
        }

        /// <summary>
        /// 删除项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> DeleteMainProject(DeleteMainProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            string UploadModelPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var mainproject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprojectGuid);
            var mainProjectName = mainproject.Name;
            var teamprojectList = _teamRepository.Projects.ToList().Where(s => s.MainProjectID.ToString().ToLower() == mainprojectGuid.ToString().ToLower()).ToList();
            //删除模型文件
            foreach (var item in teamprojectList)
            {
                var UploadFileCommandsPath = UploadModelPath + separator + "UploadFileCommands" + separator + item.ID;
                UploadFileCommandsPath = UploadFileCommandsPath.Replace("\\", separator.ToString());
                if (Directory.Exists(UploadFileCommandsPath))
                {
                    Directory.Delete(UploadFileCommandsPath, true);
                }

                var UploadCommandsToPublicPath = UploadModelPath + separator + "UploadCommandsToPublic" + separator + item.ID;
                UploadCommandsToPublicPath = UploadCommandsToPublicPath.Replace("\\", separator.ToString());
                if (Directory.Exists(UploadCommandsToPublicPath))
                {
                    Directory.Delete(UploadCommandsToPublicPath, true);
                }
                var resourcefilePath = UploadModelPath + separator + item.Name;
                resourcefilePath = resourcefilePath.Replace("\\", separator.ToString());
                if (Directory.Exists(resourcefilePath))
                {
                    Directory.Delete(resourcefilePath, true);
                }
            }

            //删除云连接文件
            var _uploadPath = UploadModelPath + separator + "ProjectDirectory" + separator + mainProjectName;
            _uploadPath = _uploadPath.Replace("\\", separator.ToString());
            if (Directory.Exists(_uploadPath))
            {
                Directory.Delete(_uploadPath, true);
            }
            var volumes = _teamRepository.Volumes.Where(v => v.MainProjectId == mainprojectGuid).ToList();
            foreach (var item in volumes)
            {
                //删除卷册图档上传目录路径
                var volumePath = UploadModelPath + separator + "UploadFileCommands" + separator + item.VolumeId;
                volumePath = volumePath.Replace("\\", separator.ToString());
                if (Directory.Exists(volumePath))
                {
                    Directory.Delete(volumePath, true);
                }
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            if (mainprojectManager != null)
            {
                var libList = mainprojectManager.MPLibraryInfos.ToList();
                foreach (var item in libList)
                {
                    var libPath = UploadModelPath + separator + "LibraryFile" + separator + item.LibId;
                    if (Directory.Exists(libPath))
                    {
                        Directory.Delete(libPath, true);
                    }
                }
                var libids = libList.Select(s => s.LibId).ToList();
                //删除lib信息
                //var catalogmanager = new CatalogManager();
                _libRepository.RemoveCatalogTreeNodesByLibIds(libids);
                _libRepository.RemoveCLLibraryDatasByLibIds(libids);
                _libRepository.RemoveCLMainProjectLibsInfo(mainprojectGuid);
            }
            string linkfilepath = UploadModelPath + separator + "LinkFile" + separator + mainprojectGuid;
            linkfilepath = linkfilepath.Replace("\\", separator.ToString());
            if (Directory.Exists(linkfilepath))
            {
                Directory.Delete(linkfilepath, true);
            }
            _teamRepository.DeleteMainProjectFiles(mainprojectGuid);
            //删除主项目信息
            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                if (_teamRepository.DeleteMainProject(mainprojectGuid))
                {
                    x.IsSuccess = true;
                }
                scope.Complete();
            }

            return x;
        }

        /// <summary>
        /// 移动文件夹目录
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> UpdateFileDirectoryOrderNo(UpdateFileDirectoryOrderNoRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var dicGuid = Guid.Empty;
            if (!Guid.TryParse(request.DicGuid, out dicGuid))
            {
                x.Message = $"参数{request.DicGuid}格式有误！";
                return x;
            }
            var upOrDown = request.UpOrDown;
            var ret = _teamRepository.UpdateFileDirectoryOrderNo(dicGuid, upOrDown);
            x.IsSuccess = ret;
            return x;
        }

        /// <summary>
        /// 编辑项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>   
        public override async Task<GrpcResult> ModifyMainProjectInfo(ModifyMainProjectInfoRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (request.Mainproject == null)
            {
                x.Message = $"参数 Mainproject 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            var mainproject = request.Mainproject;
            var mainprojectid = Guid.Empty;
            if (!Guid.TryParse(mainproject.Id, out mainprojectid))
            {
                x.Message = $"参数mainproject.Id格式有误！";
                return x;
            }
            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {
                bool ret = true;
                MainProject oldProject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprojectid);
                if (oldProject != null)
                {
                    TeamProject oldteamProject = _teamRepository.Projects.FirstOrDefault(p => p.Name == oldProject.Name);
                    if (oldteamProject != null)
                    {
                        if (mainproject.Name != oldteamProject.Name && !_teamRepository.CheckProjectName(mainproject.Name, oldteamProject.MainProjectID))
                        {
                            //已存在同名项目
                            x.Message = $"该项目下已有相同模型名称";
                            return x;
                        }
                        oldteamProject.Name = mainproject.Name;
                        ret = _teamRepository.ModifyProjectInfo(oldteamProject);
                    }

                    if (ret)
                    {
                        if (mainproject.Name != oldProject.Name && !_teamRepository.CheckMainProjectName(mainproject.Name))
                        {
                            //已存在同名项目

                            x.Message = $"项目名称已被占用";
                            return x;
                        }
                        oldProject.Name = mainproject.Name;
                        oldProject.Description = mainproject.Description;
                        oldProject.ExtendStr = mainproject.ExtendStr;
                        _teamRepository.UpdateMainprojectInfo(oldProject);
                        x.IsSuccess = true;
                    }
                }
                scope.Complete();
            }
            return x;
        }

        public override async Task<GrpcResult> AddMemberToMainProject(AddMemberToMainProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainGuid, out mainGuid))
            {
                x.Message = $"参数{request.MainGuid}格式有误！";
                return x;
            }
            var nameList = request.NameList;
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            List<TeamMember> members = memberTmp.Where(s => nameList.Contains(s.LoginName)).ToList();
            var memberIds = members.Select(s => s.ID).Select(s => s.ToString().ToLower()).ToList();

            MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainGuid);
            if (mp == null)
            {
                x.Message = "项目不存在或已被删除";
                x.IsSuccess = false;
                _logger.LogInformation("项目不存在或已被删除,id:" + mainGuid);
                return x;
            }
            var exsitMainProjectTeamGroup = _teamRepository.MainProjectTeamGroups.Where(t => t.MainProjectId == mp.ID && memberIds.Contains(t.GroupOrTeamMemberId)).Select(s => s.GroupOrTeamMemberId).ToList();
            //var select = m_db.Select<MainProjectTeamGroup>(mp => mp.GroupOrTeamMemberId == mbr.ID.ToString() && mp.MainProjectId == mainProject.ID && (mp.ObjectId == mainProject.ID || mp.ObjectId == Guid.Empty));
            if (!exsitMainProjectTeamGroup.Any())
            {
                bool flag = _teamRepository.AddMemberListToMainProject(members, mp);
                x.IsSuccess = flag;
            }
            else
            {
                var exsitGuid = exsitMainProjectTeamGroup.Select(s => Guid.Parse(s)).ToList();
                var toAddMembers = members.Where(m => !exsitGuid.Contains(m.ID)).ToList();
                bool flag = _teamRepository.AddMemberListToMainProject(toAddMembers, mp);
                x.IsSuccess = flag;
            }
            return x;
        }

        /// <summary>
        /// 编辑文件夹
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns> 
        public override async Task<GrpcResult> ModifyFileDirectoryInfo(ModifyFileDirectoryInfoRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var filedir = request.Filedir;
            var dicGuid = Guid.Empty;
            if (!Guid.TryParse(request.Filedir.Id, out dicGuid))
            {
                x.Message = $"参数Filedir.Id格式有误！";
                return x;
            }
            FileDirectory olddir = _teamRepository.FileDirectories.FirstOrDefault(f => f.ID == dicGuid);
            if (olddir != null)
            {
                if (filedir.Name != olddir.Name && !_teamRepository.CheckFileDirectoryName(_mapper.Map<FileDirectory>(filedir)))
                {
                    //已存在同名项目
                    x.Message = $"文件夹名重复";
                    return x;
                }

                olddir.Name = filedir.Name;
                olddir.Type = filedir.Type;

                var ret = _teamRepository.UpdateFileDirectoryInfo(olddir);
                x.IsSuccess = ret;
            }

            return x;
        }

        /// <summary>
        /// 执行Cmd命令
        /// </summary>
        /// <param name="workingDirectory">要启动的进程的目录</param>
        /// <param name="command">要执行的命令</param>
        public static void StartCmd(String workingDirectory, String command)
        {
            using (Process p = new Process())
            {
                p.StartInfo.FileName = "cmd.exe";
                p.StartInfo.WorkingDirectory = workingDirectory;
                p.StartInfo.UseShellExecute = false;
                p.StartInfo.RedirectStandardInput = true;
                p.StartInfo.RedirectStandardOutput = true;
                p.StartInfo.RedirectStandardError = true;
                p.StartInfo.CreateNoWindow = true;
                //p.EnableRaisingEvents = true;
                p.Start();
                p.StandardInput.WriteLine(command);
                p.StandardInput.WriteLine("exit");
                //p.StandardInput.Flush();

                p.BeginOutputReadLine();
                string error = p.StandardError.ReadToEnd();
                p.WaitForExit();
                if (!string.IsNullOrEmpty(error) && (error.ToLower().Contains("error")))
                    throw new System.Data.DataException(error);
            }

        }
        public bool ExportMainproject(Guid mainProjectGuid, out string errMsg, out string zipPath)
        {
            errMsg = "";
            zipPath = "";
            string backupZipDirPath = "";
            char separator = Path.DirectorySeparatorChar;

            try
            {
                // 1. 初始化路径和目录 
                var (backupPath, uploadModelPath) = InitializePaths(mainProjectGuid, out backupZipDirPath, out zipPath);

                // 2. 获取项目相关数据 
                var projectData = RetrieveProjectData(mainProjectGuid);

                // 3. 备份文件目录 
                BackupProjectDirectories(uploadModelPath, backupPath, projectData.MainProject);

                // 4. 备份数据库 
                BackupDatabases(backupPath, projectData.MainProject.ID, projectData.TeamProjectIds);

                // 5. 序列化项目数据到XML 
                SerializeProjectData(backupPath, projectData);

                // 6. 备份模型库文件 
                BackupModelLibraryFiles(backupPath, projectData.MainProjectManager);

                // 7. 备份团队项目文件 
                BackupTeamProjectFiles(uploadModelPath, backupPath, projectData.TeamProjectIds);

                // 8. 创建压缩包并清理临时文件 
                CreateZipAndCleanup(backupPath, zipPath);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportMainproject failed: {ex.Message}\n{ex.StackTrace}");
                errMsg = ex.Message;
                return false;
            }
        }

        #region Helper Methods 

        private (string backupPath, string uploadModelPath) InitializePaths(Guid mainProjectGuid, out string backupZipDirPath, out string zipPath)
        {
            string uploadModelPath = GetUploadModelPath();
            string assemblyPath = GetAssemblyDirectory();
            string parentPath = new DirectoryInfo(assemblyPath).Parent.FullName;
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                parentPath = assemblyPath;
            }

            string backupGuid = Guid.NewGuid().ToString();
            string backupPath = Path.Combine(parentPath, "DBBackup" + backupGuid);
            backupZipDirPath = Path.Combine(parentPath, "DBBackupOut" + backupGuid);

            var mainProject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
            zipPath = Path.Combine(backupZipDirPath, $"{mainProject?.Name}.zip");

            Directory.CreateDirectory(backupPath);
            Directory.CreateDirectory(backupZipDirPath);

            return (backupPath, uploadModelPath);
        }

        private string GetUploadModelPath()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                return _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                return _urls.LoadDataSavePath;
            }
            throw new PlatformNotSupportedException("Unsupported operating system");
        }

        private ProjectData RetrieveProjectData(Guid mainProjectGuid)
        {
            var data = new ProjectData
            {
                MainProject = _teamRepository.MainProjects.Select(mainproject => new MainProjects
                {
                    ID = mainproject.ID,
                    Name = mainproject.Name,
                    ShortName = mainproject.ShortName,
                    Description = mainproject.Description,
                    CreationTime = mainproject.CreationTime,
                    CreateUser = mainproject.CreateUser,
                    RoleGroupId = mainproject.RoleGroupId,
                    RoleString = mainproject.RoleString,
                    LastUpdateTime = mainproject.LastUpdateTime,
                    ExtendStr = mainproject.ExtendStr,
                    MainProjectType = mainproject.MainProjectType,
                    IsDelete = mainproject.IsDelete,
                    EngineerType = mainproject.EngineerType,
                    DesignStage = mainproject.DesignStage,
                    clientId = mainproject.clientId,
                    ClientVersion = mainproject.ClientVersion
                }).FirstOrDefault(m => m.ID == mainProjectGuid),
                TeamProjects = _teamRepository.Projects
                    .Where(t => !string.IsNullOrEmpty(t.MainProjectID) &&
                               (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower()))
                    .ToList(),
                FileDirectories = _teamRepository.FileDirectories
                    .Where(f => f.MainProjectId == mainProjectGuid)
                    .ToList(),
                Volumes = _teamRepository.Volumes
                    .Where(v => v.MainProjectId == mainProjectGuid)
                    .ToList(),
                clientModuleVersions = _teamRepository.ClientModuleVersions
                    .Where(t => !string.IsNullOrEmpty(t.ProjectId) &&
                                (t.ProjectId.ToLower() == mainProjectGuid.ToString().ToLower()))
                    .ToList()

            };

            data.TeamProjectIds = data.TeamProjects.Select(t => t.ID).ToList();
            data.MainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);

            // 获取用户组相关数据 
            RetrieveUserGroupData(mainProjectGuid, data);

            return data;
        }

        private void RetrieveUserGroupData(Guid mainProjectGuid, ProjectData data)
        {
            data.MainProjectUserGroupMembers = _teamRepository.mainProjectUserGroupMembers
                .Where(v => v.MainProjectId == mainProjectGuid)
                .ToList();

            var memberIds = data.MainProjectUserGroupMembers.Select(s => s.TeamMemberId).ToList();

            data.MainProjectUserGroupLibAuths = _teamRepository.mainProjectUserGroupLibAuths
                .Where(v => v.MainProjectID == mainProjectGuid)
                .ToList();

            data.MainProjectUserGroupAuths = _teamRepository.mainProjectUserGroupAuths
                .Where(v => v.MainProjectID == mainProjectGuid)
                .ToList();

            data.MainProjectTeamGroups = _teamRepository.MainProjectTeamGroups
                .Where(v => v.MainProjectId == mainProjectGuid)
                .ToList();

            data.TeamMembers = _teamRepository.Members
                .Where(m => memberIds.Contains(m.ID))
                .ToList();

            var teamUserGroups = _teamRepository.TeamUserGroups
                .Where(m => memberIds.Contains(m.TeamMemberId))
                .ToList();

            var groupIds = teamUserGroups.Select(g => g.GroupId).ToList();

            data.TeamGroups = _teamRepository.TeamGroups
                .Where(g => groupIds.Contains(g.Id))
                .ToList();
        }

        private void BackupProjectDirectories(string uploadModelPath, string backupPath, MainProjects mainProject)
        {
            if (mainProject == null) return;

            string[] directoriesToBackup = {
        Path.Combine(uploadModelPath, "ProjectDirectory", mainProject.Name),
        Path.Combine(uploadModelPath, "LinkFile", mainProject.ID.ToString()),
        Path.Combine(uploadModelPath, "DrawingFile", mainProject.ID.ToString())
    };

            string[] backupDestinations = {
        Path.Combine(backupPath, "ProjectDirectory", mainProject.Name),
        Path.Combine(backupPath, "LinkFile", mainProject.ID.ToString()),
        Path.Combine(backupPath, "DrawingFile", mainProject.ID.ToString())
    };

            for (int i = 0; i < directoriesToBackup.Length; i++)
            {
                if (Directory.Exists(directoriesToBackup[i]))
                {
                    UtilityHelper.CopyEntireDir(directoriesToBackup[i], backupDestinations[i]);
                }
            }
        }

        private void BackupDatabases(string backupPath, Guid mainProjectId, List<Guid> teamProjectIds)
        {
            var conf = UtilityHelper.GetMysqlConnectionConfig();

            // 备份主项目数据库 
            BackupDatabase(backupPath, $"pkpm-pbimserver-mpdb-{mainProjectId.ToString().ToLower()}", conf);

            // 备份每个团队项目的模型数据库 
            foreach (var tpId in teamProjectIds)
            {
                BackupDatabase(backupPath, $"pkpm-pbimserver-modeldb-{tpId}", conf);
            }
        }

        private void BackupDatabase(string backupPath, string dbName, MysqlConnectionConfig conf)
        {
            _logger.LogInformation($"开始备份数据库: {dbName}");

            string command;
            string scriptPath = Path.Combine(GetAssemblyDirectory(), $"{dbName}-backup-{Guid.NewGuid()}");

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                command = $"mysqldump -h {conf.IP} -P {conf.Port} -u{conf.User} -p{conf.Password} {dbName} > \"{Path.Combine(backupPath, $"{dbName}.sql")}\"";
                scriptPath += ".sh";

                ExcuteBash(command, scriptPath);
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                command = $"\"{UtilityHelper.getmysqlpath()}\\mysqldump.exe\"  -h {conf.IP} -P {conf.Port} -u{conf.User} -p{conf.Password} {dbName} > \"{Path.Combine(backupPath, $"{dbName}.sql")}\"";
                scriptPath += ".bat";


                ExcuteBat($"chcp 65001\r\n{command}", scriptPath);
            }

            _logger.LogInformation($"完成备份数据库: {dbName}");
        }

        private void SerializeProjectData(string backupPath, ProjectData data)
        {
            // 序列化各种项目数据到XML文件 
            BimBaseServerConfigurations.WriteCustomConfiguration(data.MainProjectUserGroupMembers, Path.Combine(backupPath, "mainProjectUserGroupMember.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.MainProjectUserGroupLibAuths, Path.Combine(backupPath, "mainProjectUserGroupLibAuth.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.MainProjectUserGroupAuths, Path.Combine(backupPath, "mainProjectUserGroupAuth.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.MainProjectTeamGroups, Path.Combine(backupPath, "mainProjectTeamGroup.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.TeamMembers, Path.Combine(backupPath, "teamMember.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.TeamUserGroups, Path.Combine(backupPath, "teamUserGroup.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.TeamGroups, Path.Combine(backupPath, "teamGroup.xml"));
            BimBaseServerConfigurations.WriteCustomConfiguration(data.clientModuleVersions, Path.Combine(backupPath, "clientModuleVersion.xml"));
            // 序列化库信息 
            if (data.MainProjectManager != null)
            {
                BimBaseServerConfigurations.WriteCustomConfiguration(data.MainProjectManager.MPLibraryInfos.ToList(), Path.Combine(backupPath, "MPLibInfo.xml"));
            }

            // 序列化主项目信息 
            var mainProjectInfo = new MainProjects
            {
                ID = data.MainProject.ID,
                Name = data.MainProject.Name,
                ShortName = data.MainProject.ShortName,
                Description = data.MainProject.Description,
                CreationTime = data.MainProject.CreationTime,
                CreateUser = data.MainProject.CreateUser,
                RoleGroupId = data.MainProject.RoleGroupId,
                RoleString = data.MainProject.RoleString,
                LastUpdateTime = data.MainProject.LastUpdateTime,
                ExtendStr = data.MainProject.ExtendStr,
                MainProjectType = data.MainProject.MainProjectType,
                IsDelete = data.MainProject.IsDelete,
                EngineerType = data.MainProject.EngineerType,
                DesignStage = data.MainProject.DesignStage,
                clientId = data.MainProject.clientId,
                ClientVersion = data.MainProject.ClientVersion
            };
            BimBaseServerConfigurations.WriteCustomConfiguration(mainProjectInfo, Path.Combine(backupPath, "MainProjectInfo.xml"));

            // 序列化团队项目列表 
            BimBaseServerConfigurations.WriteCustomConfiguration(data.TeamProjects, Path.Combine(backupPath, "TeamProjectListInfo.xml"));

            // 序列化文件目录信息 
            BimBaseServerConfigurations.WriteCustomConfiguration(data.FileDirectories, Path.Combine(backupPath, "FiledirectoryInfo.xml"));

            // 序列化卷信息 
            BimBaseServerConfigurations.WriteCustomConfiguration(data.Volumes, Path.Combine(backupPath, "VolumeInfo.xml"));

            // 序列化卷版本信息 
            var volumeVersions = RetrieveVolumeVersions(data.Volumes, backupPath);
            BimBaseServerConfigurations.WriteCustomConfiguration(volumeVersions, Path.Combine(backupPath, "VolumeVersionInfo.xml"));

            // 序列化库项目信息 
            var clMainprojectLibs = _libRepository.CLMainprojectLibs
                .Where(c => c.MainProjectGuid == data.MainProject.ID)
                .ToList();
            BimBaseServerConfigurations.WriteCustomConfiguration(clMainprojectLibs, Path.Combine(backupPath, "CLMainprojectLibInfo.xml"));

            // 序列化仓库信息 
            var repositoryInfo = _teamRepository.GetRepository();
            BimBaseServerConfigurations.WriteCustomConfiguration(repositoryInfo, Path.Combine(backupPath, "RepositoryInformationInfo.xml"));
        }

        private List<VolumeVersion> RetrieveVolumeVersions(List<Volume> volumes, string backupPath)
        {
            var volumeVersions = new List<VolumeVersion>();
            char separator = Path.DirectorySeparatorChar;

            foreach (var volume in volumes)
            {
                var volVer = _teamRepository.VolumeVersions
                    .Where(a => a.VolumeId == volume.VolumeId)
                    .OrderByDescending(a => a.VerNo)
                    .FirstOrDefault();

                if (volVer != null)
                {
                    volumeVersions.Add(volVer);

                    var dir = volVer.SavePath.Substring(
                        volVer.SavePath.IndexOf("UploadFileCommands"),
                        volVer.SavePath.LastIndexOf(separator) - volVer.SavePath.IndexOf("UploadFileCommands") + 1);

                    var filename = volVer.SavePath.Substring(
                        volVer.SavePath.LastIndexOf(separator) + 1,
                        volVer.SavePath.Length - volVer.SavePath.LastIndexOf(separator) - 1);

                    var newVolVersionPath = Path.Combine(backupPath, dir);

                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(newVolVersionPath);
                    }

                    File.Copy(volVer.SavePath, Path.Combine(newVolVersionPath, filename), true);
                }
            }

            return volumeVersions;
        }

        private void BackupModelLibraryFiles(string backupPath, IMainProjectRepository mainProjectManager)
        {
            if (mainProjectManager == null) return;

            string newPath = Path.Combine(backupPath, "MPLibraryFile");
            var mainLibDatas = mainProjectManager.MPLibraryDatas.Where(s => s.ServerFilePath != null).ToList();
            char separator = Path.DirectorySeparatorChar;

            foreach (var item in mainLibDatas)
            {
                var tempFilePath = item.ServerFilePath?
                    .Replace('/', separator)
                    .Replace('\\', separator);

                if (string.IsNullOrEmpty(tempFilePath) || !File.Exists(tempFilePath)) continue;

                var tempFilePathStr = tempFilePath.Substring(0, tempFilePath.IndexOf(separator + "MPLibraryFile"));
                string tmpCurPrjPath = tempFilePathStr + separator + "MPLibraryFile";

                var newCopyFilePath = tempFilePath.Replace(tmpCurPrjPath, newPath);
                var oldCopyDir = tempFilePath.Substring(0, tempFilePath.LastIndexOf(separator));
                var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));

                if (!Directory.Exists(newCopyDir))
                {
                    Directory.CreateDirectory(newCopyDir);
                }

                File.Copy(tempFilePath, tempFilePath.Replace(oldCopyDir, newCopyDir), true);
            }
        }

        private void BackupTeamProjectFiles(string uploadModelPath, string backupPath, List<Guid> teamProjectIds)
        {
            char separator = Path.DirectorySeparatorChar;

            foreach (var tpId in teamProjectIds)
            {
                string currentProjectPath = Path.Combine(uploadModelPath, "UploadFileCommands", tpId.ToString());
                string newPath = Path.Combine(backupPath, "UploadFileCommands", tpId.ToString());

                var tempModelManager = _teamRepository.GetProjectRepository(tpId);

                // 备份模型文件 
                BackupModelFiles(tempModelManager, newPath, separator,tpId);

                // 处理里程碑文件 
                ProcessMilestoneFiles(tempModelManager, backupPath, separator);
            }
        }

        private void BackupModelFiles(IProjectRepository modelManager, string newPath, char separator,Guid tpid)
        {
            var modelFiles = modelManager.ModelFiles.ToList();
            if (!modelFiles.Any()) return;

            var fileNames = modelFiles.Select(m => m.FileName).Distinct().ToList();

            foreach (var fileName in fileNames)
            {
                var maxVersion = modelFiles.Where(n => n.FileName == fileName).Max(f => f.VersionNo);
                var currentFile = modelManager.ModelFiles.FirstOrDefault(f => f.VersionNo == maxVersion && f.FileName == fileName);

                if (currentFile == null) continue;

                var tempFilePath = currentFile.FilePath?
                    .Replace('/', separator)
                    .Replace('\\', separator);

                if (string.IsNullOrEmpty(tempFilePath) || !File.Exists(tempFilePath)) continue;

                var tempFilePathStr = tempFilePath.Substring(0, tempFilePath.IndexOf(separator + "UploadFileCommands"));
                string tmpCurPrjPath = tempFilePathStr + separator + "UploadFileCommands" + separator + tpid;

                var newCopyFilePath = tempFilePath.Replace(tmpCurPrjPath, newPath);
                var oldCopyDir = tempFilePath.Substring(0, tempFilePath.LastIndexOf(separator));
                var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));

                if (!Directory.Exists(newCopyDir))
                {
                    Directory.CreateDirectory(newCopyDir);
                }

                File.Copy(tempFilePath, tempFilePath.Replace(oldCopyDir, newCopyDir), true);
            }
        }

        private void ProcessMilestoneFiles(IProjectRepository modelManager, string backupPath, char separator)
        {
            var milestoneFiles = modelManager.MilestoneFiles.ToList();
            if (!milestoneFiles.Any() || milestoneFiles.Count <= 1) return;

            var maxVersion = milestoneFiles.Max(s => s.VersionNo);
            var oldStones = milestoneFiles.Where(s => s.VersionNo < maxVersion).ToList();

            foreach (var stoneFile in oldStones)
            {
                var tempFilePath = stoneFile.SavePath;
                var tempFilePathStr = tempFilePath.Substring(
                    tempFilePath.IndexOf(separator + "ProjectDirectory") + 1,
                    tempFilePath.LastIndexOf(separator) - tempFilePath.IndexOf(separator + "ProjectDirectory") - 1);

                string oldStonePath = Path.Combine(backupPath, tempFilePathStr);

                if (Directory.Exists(oldStonePath))
                {
                    Directory.Delete(oldStonePath, true);
                }
            }
        }

        private void CreateZipAndCleanup(string backupPath, string zipPath)
        {
            // 创建压缩包 
            var zipHelper = new ZipHelper();
            zipHelper.ZipFileFromDirectory(backupPath, zipPath, 4);

            // 清理临时文件 
            try
            {
                if (Directory.Exists(backupPath))
                {
                    Directory.Delete(backupPath, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"删除临时文件夹 {backupPath} 失败: {ex.Message}");
            }
        }

        


        #endregion

        public bool ExportMainprojectold(Guid mainProjectGuid, out string errMsg, out string zipPath)
        {
            errMsg = "";
            string backuppath = "";
            zipPath = "";
            string backupZipDirPath = "";
            char separator = Path.DirectorySeparatorChar;
            try
            {
                string UploadModelPath = "";
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    UploadModelPath = _urls.UploadRootPath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    UploadModelPath = _urls.LoadDataSavePath;
                }

                //获取mainproject下的所有teamproject
                //var tpList = teamManager.Projects.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower())).ToList();
                var mainproject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
                var tpList = _teamRepository.Projects.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower())).ToList();
                var filedirList = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();//(t => (!string.IsNullOrEmpty(t.MainProjectId)) && (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower())).ToList();
                var volumeList = _teamRepository.Volumes.Where(v => v.MainProjectId == mainProjectGuid).ToList();
                //mainprojectusergroupmembers
                var mainProjectUserGroupMembersList = _teamRepository.mainProjectUserGroupMembers.Where(v => v.MainProjectId == mainProjectGuid).ToList();
                var mpmemberidList = mainProjectUserGroupMembersList.Select(s => s.TeamMemberId).ToList();
                //mainprojectusergrouplibauths
                var mainProjectUserGroupLibAuthsList = _teamRepository.mainProjectUserGroupLibAuths.Where(v => v.MainProjectID == mainProjectGuid).ToList();
                //mainprojectusergroupauths
                var mainProjectUserGroupAuthsList = _teamRepository.mainProjectUserGroupAuths.Where(v => v.MainProjectID == mainProjectGuid).ToList();
                //mainprojectteamgroups
                var mainProjectTeamGroupsList = _teamRepository.MainProjectTeamGroups.Where(v => v.MainProjectId == mainProjectGuid).ToList();
                //teammembers
                var teamMembersList = _teamRepository.Members.Where(m => mpmemberidList.Contains(m.ID)).ToList();
                var tpIDList = tpList.Select(t => t.ID).ToList();

                //teamusergroups
                var teamusergroups = _teamRepository.TeamUserGroups.Where(m => mpmemberidList.Contains(m.TeamMemberId)).ToList();
                var groupids = teamusergroups.Select(g => g.GroupId).ToList();
                //teamgroups
                var teamgroups = _teamRepository.TeamGroups.Where(g => groupids.Contains(g.Id)).ToList();


                string modelDBName = "";



                //备份数据库
                var conf = UtilityHelper.GetMysqlConnectionConfig();
                var databaseIp = conf.IP;
                var databasePort = conf.Port;
                var dbusername = conf.User;
                var dbpassword = conf.Password;
                string batPath = GetAssemblyDirectory();
                DirectoryInfo path_exe = new DirectoryInfo(batPath);
                String path = path_exe.Parent.FullName;
                var backzippathGuid = Guid.NewGuid().ToString();
                backuppath = Path.Combine(path, "DBBackup" + backzippathGuid);
                //string unzipPath = Path.Combine(path, "unzip");
                backupZipDirPath = Path.Combine(path, "DBBackupOut" + backzippathGuid);
                zipPath = backupZipDirPath + separator + mainproject.Name + ".zip";
                if (!Directory.Exists(backuppath))
                {
                    Directory.CreateDirectory(backuppath);
                }
                if (!Directory.Exists(backupZipDirPath))
                {
                    Directory.CreateDirectory(backupZipDirPath);
                }

                //里程碑及云链接文件夹复制
                string mainprojectDirPath = UploadModelPath + separator + "ProjectDirectory" + separator + mainproject.Name;
                string webnumberDirPath = UploadModelPath + separator + "ProjectDirectory" + separator + mainproject.Name;
                string mplinkfilePath = UploadModelPath + separator + "LinkFile" + separator + mainproject.ID;
                string mpdrawingfilepath = UploadModelPath + separator + "DrawingFile" + separator + mainproject.ID;
                //复制文件夹
                string newmpPath = Path.Combine(backuppath, "ProjectDirectory", mainproject.Name);
                string newmplinkfilepath = Path.Combine(backuppath, "LinkFile", mainproject.ID.ToString());
                string newdrawingfilepath = Path.Combine(backuppath, "DrawingFile", mainproject.ID.ToString());
                if (Directory.Exists(mainprojectDirPath))
                {
                    UtilityHelper.CopyEntireDir(mainprojectDirPath, newmpPath);
                }
                if (Directory.Exists(webnumberDirPath))
                {
                    UtilityHelper.CopyEntireDir(webnumberDirPath, newmpPath);
                }
                if (Directory.Exists(mplinkfilePath))
                {
                    UtilityHelper.CopyEntireDir(mplinkfilePath, newmplinkfilepath);
                }
                if (Directory.Exists(mpdrawingfilepath))
                {
                    UtilityHelper.CopyEntireDir(mpdrawingfilepath, newdrawingfilepath);
                }
                string codetype = "chcp 65001";
                _logger.LogInformation("===================================开始备份项目数据库=========================");
                var mainprojectid = mainProjectGuid.ToString().ToLower();
                string mainprojectdbname = "pkpm-pbimserver-mpdb-" + mainprojectid;

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    //var sqlpath = unzipPath + separator + oldmainprojectDBName + ".sql";
                    var command = "mysqldump -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + mainprojectdbname + ">" + "\"" + backuppath + separator
                            + mainprojectdbname + ".sql" + "\"";


                    _logger.LogInformation("command命令行============》" + command);
                    string scriptPath = batPath + "mpbacktemp" + backzippathGuid + ".sh";  // 替换为实际路径
                    ExcuteBash(command, scriptPath);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    string mainprojectcmdstr = "\"" + UtilityHelper.getmysqlpath() + "\\mysqldump.exe\" -h " + databaseIp + " -P "
                            + databasePort + " -u" + dbusername + " -p" + dbpassword + "  " + mainprojectdbname + ">" + "\"" + backuppath + "\\"
                            + mainprojectdbname + ".sql" + "\"";
                    _logger.LogInformation("mainprojectcmdstr:" + mainprojectcmdstr);
                    string command = codetype + "\r\n" + mainprojectcmdstr;
                    string batpath = batPath + "mpbacktemp" + backzippathGuid + ".bat";
                    ExcuteBat(command, batpath);
                }

                var mainprojectmanager = _teamRepository.GetMainProjectRepository(mainProjectGuid);

                //mainProjectUserGroupMembersList
                string mainProjectusergroupmemberXML = backuppath + separator + "mainProjectUserGroupMember.xml";
                //mainProjectUserGroupMembersList = UtilityHelper.CopyToObject(mainProjectUserGroupMembersList);
                BimBaseServerConfigurations.WriteCustomConfiguration(mainProjectUserGroupMembersList, mainProjectusergroupmemberXML);
                //mainProjectUserGroupLibAuthsList
                string mainProjectusergrouplibauthXML = backuppath + separator + "mainProjectUserGroupLibAuth.xml";
                //mainProjectUserGroupLibAuthsList = UtilityHelper.CopyToObject(mainProjectUserGroupLibAuthsList);
                BimBaseServerConfigurations.WriteCustomConfiguration(mainProjectUserGroupLibAuthsList, mainProjectusergrouplibauthXML);
                //mainProjectUserGroupAuthsList
                string mainProjectusergroupauthXML = backuppath + separator + "mainProjectUserGroupAuth.xml";
                //mainProjectUserGroupAuthsList = UtilityHelper.CopyToObject(mainProjectUserGroupAuthsList);
                BimBaseServerConfigurations.WriteCustomConfiguration(mainProjectUserGroupAuthsList, mainProjectusergroupauthXML);
                //mainProjectTeamGroupsList
                string mainProjectteamgroupXML = backuppath + separator + "mainProjectTeamGroup.xml";
                //mainProjectTeamGroupsList = UtilityHelper.CopyToObject(mainProjectTeamGroupsList);
                BimBaseServerConfigurations.WriteCustomConfiguration(mainProjectTeamGroupsList, mainProjectteamgroupXML);
                //teamMembersList
                string teamMemberXML = backuppath + separator + "teamMember.xml";
                //teamMembersList = UtilityHelper.CopyToObject(teamMembersList);
                BimBaseServerConfigurations.WriteCustomConfiguration(teamMembersList, teamMemberXML);
                //teamusergroups
                string teamUserGroupXML = backuppath + separator + "teamUserGroup.xml";
                //teamusergroups = UtilityHelper.CopyToObject(teamusergroups);
                BimBaseServerConfigurations.WriteCustomConfiguration(teamusergroups, teamUserGroupXML);
                //teamgroups
                string teamGroupXML = backuppath + separator + "teamGroup.xml";
                //teamgroups = UtilityHelper.CopyToObject(teamgroups);
                BimBaseServerConfigurations.WriteCustomConfiguration(teamgroups, teamGroupXML);

                //记录LibInfo
                var libinfolist = mainprojectmanager.MPLibraryInfos.ToList();
                string mplibinfoXML = backuppath + separator + "MPLibInfo.xml";
                //libinfolist = UtilityHelper.CopyToObject(libinfolist);
                BimBaseServerConfigurations.WriteCustomConfiguration(libinfolist, mplibinfoXML);

                if (mainprojectmanager != null)
                {
                    string newpath = Path.Combine(backuppath, "MPLibraryFile");
                    _logger.LogInformation("newpath==>" + newpath);
                    var mainlibdatas = mainprojectmanager.MPLibraryDatas.Where(s => s.ServerFilePath != null).ToList();
                    _logger.LogInformation("mainlibdatas==>" + mainlibdatas.Count);
                    foreach (var item in mainlibdatas)
                    {
                        var tempfilepath = item.ServerFilePath;
                        _logger.LogInformation("tempfilepath==>" + tempfilepath);
                        tempfilepath = tempfilepath.Replace('/', separator);
                        tempfilepath = tempfilepath.Replace('\\', separator);
                        _logger.LogInformation("tempfilepath Replace()后==>" + tempfilepath);
                        var tempfilepathStr = tempfilepath.Substring(0, tempfilepath.IndexOf(separator + "MPLibraryFile"));
                        string tmpcurPrjPath = tempfilepathStr + separator + "MPLibraryFile";
                        _logger.LogInformation("tempfilepath==>" + tempfilepath);
                        if (File.Exists(tempfilepath))
                        {
                            _logger.LogInformation("tempfilepath==>" + tempfilepath);
                            var newCopyFilePath = tempfilepath.Replace(tmpcurPrjPath, newpath);
                            _logger.LogInformation("tempfilepath Replace==>" + tempfilepath);
                            var oldCopyDir = tempfilepath.Substring(0, tempfilepath.LastIndexOf(separator));
                            _logger.LogInformation("oldCopyDir==>" + oldCopyDir);
                            var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));
                            _logger.LogInformation("newCopyDir==>" + newCopyDir);
                            if (!Directory.Exists(newCopyDir))
                            {
                                Directory.CreateDirectory(newCopyDir);
                            }
                            File.Copy(tempfilepath, tempfilepath.Replace(oldCopyDir, newCopyDir), true);
                        }
                    }
                }
                _logger.LogInformation("===================================结束备份项目数据库=========================");

                _logger.LogInformation("===================================开始备份模型数据库=========================");
                //备份模型数据库
                foreach (var tp in tpIDList)
                {
                    modelDBName = "pkpm-pbimserver-modeldb-" + tp;
                    //_logger.LogInformation(cmdStr);


                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        //var sqlpath = unzipPath + separator + oldmainprojectDBName + ".sql";
                        var command = "mysqldump -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + ">" + "\"" + backuppath + separator
                                + modelDBName + ".sql" + "\"";


                        _logger.LogInformation("command命令行============》" + command);
                        string scriptPath = batPath + "tpbacktemp" + backzippathGuid + ".sh";  // 替换为实际路径
                        ExcuteBash(command, scriptPath);
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        string mainprojectcmdstr = "\"" + UtilityHelper.getmysqlpath() + "\\mysqldump.exe\" -h " + databaseIp + " -P "
                                + databasePort + " -u" + dbusername + " -p" + dbpassword + "  " + modelDBName + ">" + "\"" + backuppath + "\\"
                                + modelDBName + ".sql" + "\"";
                        _logger.LogInformation("mainprojectcmdstr:" + mainprojectcmdstr);
                        string command = codetype + "\r\n" + mainprojectcmdstr;
                        string batpath = batPath + "tpbacktemp" + backzippathGuid + ".bat";
                        ExcuteBat(command, batpath);
                    }

                    string currentProjectPath = UploadModelPath + separator + "UploadFileCommands" + separator + tp;
                    //复制文件夹
                    string newpath = Path.Combine(backuppath, "UploadFileCommands", tp.ToString());

                    //modelfiles初始版本中的路径
                    var tempModelManager = _teamRepository.GetProjectRepository(tp);
                    var modelfiles = tempModelManager.ModelFiles.ToList();
                    if (modelfiles.Any())
                    {
                        var filenames = modelfiles.Select(m => m.FileName).Distinct().ToList();
                        foreach (var fn in filenames)
                        {
                            var tempfile = modelfiles.Where(n => n.FileName == fn).Max(f => f.VersionNo);
                            var curFile = tempModelManager.ModelFiles.FirstOrDefault(f => f.VersionNo == tempfile && f.FileName == fn);
                            var tempfilepath = curFile.FilePath;
                            tempfilepath = tempfilepath.Replace('/', separator);
                            tempfilepath = tempfilepath.Replace('\\', separator);
                            var tempfilepathStr = tempfilepath.Substring(0, tempfilepath.IndexOf(separator + "UploadFileCommands"));
                            string tmpcurPrjPath = tempfilepathStr + separator + "UploadFileCommands" + separator + tp;

                            if (File.Exists(tempfilepath))
                            {
                                var newCopyFilePath = tempfilepath.Replace(tmpcurPrjPath, newpath);
                                var oldCopyDir = tempfilepath.Substring(0, tempfilepath.LastIndexOf(separator));
                                var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));
                                if (!Directory.Exists(newCopyDir))
                                {
                                    Directory.CreateDirectory(newCopyDir);
                                }
                                File.Copy(tempfilepath, tempfilepath.Replace(oldCopyDir, newCopyDir), true);
                            }
                        }

                    }

                    //获取非最新版本里程碑文件版本，排除
                    var stonemilesfiles = tempModelManager.MilestoneFiles.ToList();
                    if (stonemilesfiles.Any())
                    {
                        if (stonemilesfiles.Count > 1)
                        {
                            var maxVer = stonemilesfiles.Max(s => s.VersionNo);
                            var oldStones = stonemilesfiles.Where(s => s.VersionNo < maxVer).ToList();
                            foreach (var sf in oldStones)
                            {
                                var tempfilepath = sf.SavePath;
                                var tempfilepathStr = tempfilepath.Substring(tempfilepath.IndexOf(separator + "ProjectDirectory") + 1, tempfilepath.LastIndexOf(separator) - tempfilepath.IndexOf(separator + "ProjectDirectory") - 1);
                                string oldstonepath = Path.Combine(backuppath, tempfilepathStr);
                                //_logger.LogInformation(oldstonepath);
                                if (Directory.Exists(oldstonepath))
                                {
                                    Directory.Delete(oldstonepath, true);
                                }
                            }
                        }
                    }

                }
                _logger.LogInformation("===================================结束备份模型数据库=========================");
                //记录mainproject
                string mpXML = backuppath + separator + "MainProjectInfo.xml";
                MainProjects project = new MainProjects();
                project.ID = mainproject.ID;
                project.Name = mainproject.Name;
                project.ShortName = mainproject.ShortName;
                project.Description = mainproject.Description;
                project.CreationTime = mainproject.CreationTime;
                project.CreateUser = mainproject.CreateUser;
                project.RoleGroupId = mainproject.RoleGroupId;
                project.RoleString = mainproject.RoleString;
                project.LastUpdateTime = mainproject.LastUpdateTime;
                project.ExtendStr = mainproject.ExtendStr;
                project.MainProjectType = mainproject.MainProjectType;
                project.IsDelete = mainproject.IsDelete;
                project.EngineerType = mainproject.EngineerType;
                project.DesignStage = mainproject.DesignStage;
                BimBaseServerConfigurations.WriteCustomConfiguration(project, mpXML);
                //记录mainproject下的teamprojectlist
                string tpXML = backuppath + separator + "TeamProjectListInfo.xml";
                //tpList = UtilityHelper.CopyToObject(tpList);
                BimBaseServerConfigurations.WriteCustomConfiguration(tpList, tpXML);
                //记录MainProject下文件夹信息
                string dirXML = backuppath + separator + "FiledirectoryInfo.xml";
                //filedirList = UtilityHelper.CopyToObject(filedirList);
                BimBaseServerConfigurations.WriteCustomConfiguration(filedirList, dirXML);
                //记录volums和volumeVersion信息
                //volumeList = UtilityHelper.CopyToObject(volumeList);
                string volXML = backuppath + separator + "VolumeInfo.xml";
                BimBaseServerConfigurations.WriteCustomConfiguration(volumeList, volXML);
                List<VolumeVersion> volVerList = new List<VolumeVersion>();
                foreach (var v in volumeList)
                {
                    var volVer = _teamRepository.VolumeVersions.Where(a => a.VolumeId == v.VolumeId).OrderByDescending(a => a.VerNo).FirstOrDefault();
                    if (volVer != null)
                    {
                        volVerList.Add(volVer);
                        var dir = volVer.SavePath.Substring(volVer.SavePath.IndexOf("UploadFileCommands"), volVer.SavePath.LastIndexOf(separator) - volVer.SavePath.IndexOf("UploadFileCommands") + 1);
                        var filename = volVer.SavePath.Substring(volVer.SavePath.LastIndexOf(separator) + 1, volVer.SavePath.Length - volVer.SavePath.LastIndexOf(separator) - 1);
                        var newVolVersionPath = Path.Combine(backuppath, dir);//volVer.SavePath.Replace(BimBaseServerConfigurations.SystemConfig.UploadModelPath, backuppath);
                        if (!Directory.Exists(dir))
                        {
                            Directory.CreateDirectory(newVolVersionPath);
                        }
                        File.Copy(volVer.SavePath, newVolVersionPath + filename, true);
                    }

                }
                //volVerList = UtilityHelper.CopyToObject(volVerList);
                string volVerXML = backuppath + separator + "VolumeVersionInfo.xml";
                BimBaseServerConfigurations.WriteCustomConfiguration(volVerList, volVerXML);
                //记录Library库中mainprojectid下的clmainprojectlibs数据
                List<CLMainprojectLib> cLMainprojectLibs = new List<CLMainprojectLib>();

                cLMainprojectLibs = _libRepository.CLMainprojectLibs.Where(c => c.MainProjectGuid == mainProjectGuid).ToList();
                //cLMainprojectLibs = UtilityHelper.CopyToObject(cLMainprojectLibs);
                string cllibXML = backuppath + separator + "CLMainprojectLibInfo.xml";
                BimBaseServerConfigurations.WriteCustomConfiguration(cLMainprojectLibs, cllibXML);

                //记录高位id
                BimBase.Api.Infrastructure.Domain.RepositoryInformation rep = _teamRepository.GetRepository();
                string repXML = backuppath + separator + "RepositoryInformationInfo.xml";
                BimBaseServerConfigurations.WriteCustomConfiguration(rep, repXML);
                //zip打包备份目录
                //压缩zip
                ZipHelper zip = new ZipHelper();
                zip.ZipFileFromDirectory(backuppath, zipPath, 4);

                try
                {
                    if (Directory.Exists(backuppath))
                    {
                        Directory.Delete(backuppath, true);
                    }
                }
                catch
                {
                    _logger.LogInformation("ExportMainproject==>删除临时文件夹" + backuppath + "出错，忽略！！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("ExportMainproject==============>" + ex.Message);
                Console.WriteLine("ExportMainproject==============>" + ex.StackTrace);
                _logger.LogInformation("ExportMainproject err==>" + ex.StackTrace);
                errMsg = ex.Message;
                return false;
            }
            return true;
        }

        public override async Task<GrpcResult> BackUpMainProjectForPDMS(BackUpMainProjectForPDMSRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数MainProjectGuid格式有误！";
                return x;
            }
            string zipPath = "";
            string errMsg = "";
            bool ret = ExportMainproject(mainProjectGuid, out errMsg, out zipPath);
            x.IsSuccess = ret;
            x.Message = errMsg;
            return x;
        }

        public override async Task<ExportMainprojectForClientResponse> ExportMainprojectForClient(ExportMainprojectForClientRequest request, ServerCallContext context)
        {
            var x = new ExportMainprojectForClientResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数MainProjectGuid格式有误！";
                return x;
            }
            string zipPath = "";
            string errMsg = "";

            

            bool ret = ExportMainproject(mainProjectGuid, out errMsg, out zipPath);

            if (ret)
            {
                _logger.LogInformation("ExportMainprojectForClient zipPath==>" + zipPath);
                if (!File.Exists(zipPath))
                {
                    errMsg = "服务端不存在该文件!";
                    _logger.LogInformation("ExportMainprojectForClient 路径不存在");
                    x.Message = errMsg;
                    x.RequestId = -1;
                    return x;
                }
                //判断是否需要分段,保证每段大小不大于500M
                int limitLength = 1024 * 1024 * 500;
                long dataLength = 0;//每次实际读取长度
                long currentFilesize = 0;
                var filename = Path.GetFileName(zipPath);
                var stream = File.OpenRead(zipPath);
                var requestId = request.RequestId;

                _logger.LogInformation("文件总长度" + stream.Length);
                string requestIdKey = "ExportMainprojectForClient:" + requestId;
                if (stream.Length <= limitLength)
                {
                    var outFileData = new byte[stream.Length];
                    stream.Position = 0;
                    stream.Read(outFileData, 0, outFileData.Length);
                    stream.Close();
                    stream.Dispose();
                    x.FilePosition = outFileData.Length;
                    x.OutFileData = ByteString.CopyFrom(outFileData);
                    outFileData = null;
                    x.Filename = filename;
                    x.IsSuccess = true;
                    requestId = 0;
                    _logger.LogInformation("读取完毕-未分段" + "fileInfo.Length" + x.OutFileData.Length);
                }
                else
                {
                    if (request.FilePosition > 0)
                    {
                        currentFilesize = request.FilePosition;
                    }
                    using (stream)
                    {
                        if ((stream.Length - currentFilesize) > limitLength)
                        {
                            dataLength = limitLength;
                        }
                        else
                        {
                            dataLength = stream.Length - currentFilesize;
                            requestId = 0;
                        }
                        _logger.LogInformation("datalength:" + dataLength);
                        var outFileData = new byte[dataLength];
                        stream.Position = currentFilesize;
                        stream.Read(outFileData, 0, (int)dataLength);
                        x.FilePosition = currentFilesize + dataLength;
                        x.OutFileData = ByteString.CopyFrom(outFileData);
                        outFileData = null;
                        x.Filename = filename;
                        x.IsSuccess = true;
                        _logger.LogInformation("ExportMainprojectForClient 本次读取完毕,一共已读取文件大小:" + x.FilePosition);
                        stream.Close();
                        stream.Dispose();
                    }
                    x.RequestId = requestId;
                }


                if (requestId == 0)
                {
                    try
                    {
                        //删除临时文件
                        if (File.Exists(zipPath))
                        {
                            FileInfo temp = new FileInfo(zipPath);
                            var dirctory = temp.FullName;
                            if (Directory.Exists(dirctory))
                            {
                                Directory.Delete(dirctory, true);
                            }
                        }
                    }
                    catch
                    {

                    }

                }
            }
            else
            {
                x.IsSuccess = false;
                x.Message = errMsg;
            }

            return x;
        }

        private void ExcuteBat(string command, string batpath)
        {
            using (System.Diagnostics.Process p = new System.Diagnostics.Process())
            {
                string tempfile = batpath;//batPath + "mptemp.bat";
                using (var fileStream = new FileStream(tempfile, FileMode.Create, FileAccess.Write))
                {
                    var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                    using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                    {
                        //sw.Write(codetype + "\r\n" + cmdStr);
                        sw.Write(command);
                    }
                }

                p.StartInfo.FileName = tempfile;
                //p.StartInfo.Arguments = cmdStr;
                p.StartInfo.UseShellExecute = false;    //是否使用操作系统shell启动
                p.StartInfo.CreateNoWindow = true;//不显示程序窗口
                p.StartInfo.RedirectStandardError = true;//重定向标准错误输出
                p.StartInfo.RedirectStandardInput = true;

                p.Start();//启动程序

                string StandardError = p.StandardError.ReadToEnd();
                p.WaitForExit();//等待程序执行完退出进程
                p.Close();
                if (!string.IsNullOrEmpty(StandardError))
                {
                    Console.WriteLine(tempfile + " " + command + " " + StandardError);
                    //throw new Exception(tempfile + " " + cmdStr + " " + StandardError);
                }
                try
                {
                    //File.Delete(tempfile);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + tempfile + "无法释放，请手动删除");
                }
            }
        }

        private void ExcuteBash(string command, string bashFilePath)
        {
            // 定义脚本内容
            string scriptContent = @"#!/bin/bash
                                             " + command + "";

            // 指定脚本文件路径
            string scriptPath = bashFilePath;//unzipPath + separator + "mptemp.sh";  // 替换为实际路径

            try
            {
                // 创建并写入脚本内容
                File.WriteAllText(scriptPath, scriptContent);
                Console.WriteLine($"Script created at: {scriptPath}");

                // 调用 chmod +x 赋予可执行权限
                var chmodProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "chmod",
                        Arguments = $"+x {scriptPath}",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                chmodProcess.Start();
                chmodProcess.WaitForExit();

                if (chmodProcess.ExitCode == 0)
                {
                    Console.WriteLine("Script is now executable.");
                }
                else
                {
                    Console.WriteLine("Failed to set executable permission.");
                    Console.WriteLine($"Error: {chmodProcess.StandardError.ReadToEnd()}");
                }

                // 可选择直接执行脚本
                var executeProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "bash",
                        Arguments = scriptPath,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                executeProcess.Start();
                string output = executeProcess.StandardOutput.ReadToEnd();
                string error = executeProcess.StandardError.ReadToEnd();
                executeProcess.WaitForExit();

                Console.WriteLine("Script executed. Output:");
                Console.WriteLine(output);

                if (!string.IsNullOrEmpty(error))
                {
                    Console.WriteLine("Error:");
                    Console.WriteLine(error);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred: {ex.Message}");
            }
        }

        private bool CheckClientVerisonAndClientid(ITeamRepository teamRepository,MainProjects mainProjects,string requestClientId,string requestClientVersion,out string message)
        {
            message = "";
            try
            {
                if (string.IsNullOrEmpty(mainProjects.clientId)||string.IsNullOrEmpty(mainProjects.ClientVersion))
                {
                    var serverClientVersionInfo = teamRepository.ClientVersionCompatibilities.FirstOrDefault(s => s.ClientId == requestClientId&&s.ClientVersion == requestClientVersion);
                    //若clientId或者ClientVersion为空，表示导出的项目是旧版本项目，直接返回失败
                    if (serverClientVersionInfo!=null)
                    {
                        message = $"版本校验未通过，期望导出版本不低于{serverClientVersionInfo.MinCompatibleVersion}";
                    }
                    else
                    {
                        message = $"版本校验未通过";
                    }
                    
                    
                    _logger.LogInformation($"clientId或者ClientVersion为空");
                    return false;
                }
                else
                {
                    if (!string.IsNullOrEmpty(mainProjects.clientId))
                    {
                        //比较clientid
                        if (!mainProjects.clientId.Equals(requestClientId))
                        {
                            message = "需使用相同客户端产品程序导入；";
                            _logger.LogInformation($"{message}{requestClientId}");
                            return false;
                        }
                    }

                    _logger.LogInformation($"clientid={mainProjects.clientId},clientversion={mainProjects.ClientVersion}");
                    //获取服务器中clientid对应的最小兼容版本号：clientversioncompatibility
                    var serverClientVersionInfo = teamRepository.ClientVersionCompatibilities.FirstOrDefault(s=>s.ClientId == mainProjects.clientId&&s.ClientVersion == mainProjects.ClientVersion);
                    if (serverClientVersionInfo!=null)
                    {
                        var CurrentMinCompatibleVersion = serverClientVersionInfo.MinCompatibleVersion;
                        var CurrentClientVersion = serverClientVersionInfo.ClientVersion;
                        _logger.LogInformation($"查询到clientid={mainProjects.clientId}的版本兼容记录：clientversion:{CurrentClientVersion}" +
                            $"CurrentMinCompatibleVersion:{CurrentMinCompatibleVersion}");
                        if (string.Compare(mainProjects.ClientVersion, CurrentMinCompatibleVersion) >= 0&&
                            string.Compare(CurrentClientVersion, mainProjects.ClientVersion)>=0)
                        {
                            return true;
                        }
                        else
                        {
                            message = $"版本校验不通过，期望导出版本不低于{CurrentClientVersion}";
                            _logger.LogInformation($"查询到clientid={mainProjects.clientId}的版本兼容记录：clientversion:{CurrentClientVersion}" +
                            $"CurrentMinCompatibleVersion:{CurrentMinCompatibleVersion};项目版本：{mainProjects.ClientVersion}");
                            return false;
                        }
                    }
                    else
                    {
                        message = $"未查询到clientid={mainProjects.clientId}的版本兼容记录";
                        _logger.LogInformation(message);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }
            return false;
        }


        

        public override async Task<GrpcResult> ImportMainProjectForClient(ImportMainProjectForClientRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            Dictionary<Guid, Guid> dicAllChangeGuid = new Dictionary<Guid, Guid>();
            string username = currentUser.LoginName;
            string zippath = "";
            string locker = "";
            string batPath = GetAssemblyDirectory();
            _logger.LogInformation("ImportMainProjectForClient batPath:" + batPath);
            string UploadModelPath = "";
            var mysqlpath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
                mysqlpath = UtilityHelper.getmysqlpath();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            _logger.LogInformation("ImportMainProjectForClient mysqlpath:" + mysqlpath);
            _logger.LogInformation("ImportMainProjectForClient UploadModelPath:" + UploadModelPath);
            if (!Directory.Exists(UploadModelPath))
            {
                Directory.CreateDirectory(UploadModelPath);
            }
            var conf = UtilityHelper.GetMysqlConnectionConfig();
            var databaseIp = conf.IP;
            var databasePort = conf.Port;
            var dbusername = conf.User;
            var dbpassword = conf.Password;
            _logger.LogInformation("conf:databaseIp:" + databaseIp + "|databasePort:" + databasePort + "|dbusername:" + dbusername + "|dbpassword:" + dbpassword);
            DirectoryInfo path_exe = new DirectoryInfo(batPath);
            String path = path_exe.Parent.FullName;
            _logger.LogInformation("ImportMainProjectForClient path:" + path);
            var uploadRequestId = request.UploadRequestId;
            var unzippathGuid = Guid.NewGuid().ToString();
            if (uploadRequestId != 0)
            {
                unzippathGuid = uploadRequestId.ToString();
            }
            string unzipPath = Path.Combine(path, "unzip" + unzippathGuid);


            if (Directory.Exists(unzipPath))
            {
                Directory.Delete(unzipPath, true);
            }
            if (!Directory.Exists(unzipPath))
            {
                Directory.CreateDirectory(unzipPath);
            }
            string savePath = Path.Combine(path, "zip" + unzippathGuid);

            var requestFilename = request.Oldfilename;
            var shortName = request.ShortName;
            string utf8FileName = Encoding.UTF8.GetString(Encoding.Default.GetBytes(requestFilename));
            var oldfilename = utf8FileName;
            var mpDescription = request.Description;
            _logger.LogInformation("ImportMainProjectForClient utf8FileName:" + oldfilename);
            var fileData = request.FileData.ToByteArray();
            char separator = Path.DirectorySeparatorChar;
            if (uploadRequestId != 0)
            {
                lock (locker)
                {
                    _logger.LogInformation("上传文件大于500M");
                    Stopwatch oncefile = new Stopwatch();
                    oncefile.Start();
                    _logger.LogInformation("zip路径:" + savePath);
                    if (!Directory.Exists(savePath))
                    {
                        Directory.CreateDirectory(savePath);
                    }
                    savePath = savePath + separator + oldfilename;
                    oncefile.Stop();
                    _logger.LogInformation("上传文件大于500M，配置文件路径，获取文件版本耗时：" + oncefile.ElapsedMilliseconds);
                    _logger.LogInformation("上传文件大于500M，开始文件流写入");
                    oncefile.Restart();

                    var fs = new FileStream(savePath, FileMode.OpenOrCreate);

                    _logger.LogInformation("fileData.Length:" + fileData.Length);
                    fs.Position = fs.Length;
                    _logger.LogInformation("fs.Position:" + fs.Position);

                    fs.Write(fileData, 0, fileData.Length);
                    _logger.LogInformation("已上传文件大小:" + fs.Length);
                    fs.Flush();
                    fs.Close();
                    fs.Dispose();

                    oncefile.Stop();
                    _logger.LogInformation("上传文件大于500M，结束文件流写入，文件流操作耗时：" + oncefile.ElapsedMilliseconds);
                }
            }
            else
            {
                lock (locker)
                {
                    _logger.LogInformation("上传文件小于500M");
                    Stopwatch oncefile = new Stopwatch();
                    oncefile.Start();
                    _logger.LogInformation("小于500M,zip路径:" + savePath);

                    if (!Directory.Exists(savePath))
                    {
                        Directory.CreateDirectory(savePath);
                    }

                    savePath = savePath + separator + oldfilename;
                    _logger.LogInformation("小于500M,oldfilename文件路径:" + savePath);
                    oncefile.Stop();
                    _logger.LogInformation("上传文件小于500M，配置文件路径，获取文件版本耗时：" + oncefile.ElapsedMilliseconds);
                    _logger.LogInformation("上传文件小于500M，开始文件流写入");
                    oncefile.Restart();
                    using (var fs1 = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
                    {

                        _logger.LogInformation("传输文件");
                        byte[] byteData = fileData;
                        fs1.Write(byteData, 0, byteData.Length);
                        fs1.Flush();
                        fs1.Close();
                        fs1.Dispose();
                    }
                    oncefile.Stop();
                    _logger.LogInformation("上传文件小于500M，结束文件流写入，文件流操作耗时：" + oncefile.ElapsedMilliseconds);

                }
            }

            if (!request.IsLastUpload)
            {
                x.IsSuccess = true;
                return x;
            }


            //string zippath = @"D:\work_new\PBIMServer\PBIMWebServer-dev-role\ProjectManager\\upload\\mainprojectbackup\7cbe4e04-3a7e-4153-ac2b-9c461ed41c96协同培训演示.zip";

            zippath = savePath;
            ZipHelper zipHelper = new ZipHelper();
            List<string> outunzipstr = new List<string>();
            var filelist = zipHelper.UnZip(zippath, unzipPath);
            foreach (var item in filelist)
            {
                _logger.LogInformation(item);
            }

            if (!File.Exists(unzipPath + separator + "MainProjectInfo.xml"))
            {
                _logger.LogInformation("解压后未找到所需文件");
                x.Message = "导入失败：导入程序非项目文件，请重新导入";
                x.IsSuccess = false;
                return x;
            }
            MainProjects reMainProject = BimBaseServerConfigurations.ReadCustomConfiguration<MainProjects>(unzipPath + separator + "MainProjectInfo.xml");
            _logger.LogInformation("ImportMainProjectForClient reMainProject：" + reMainProject.ID);
            var requstClientId = GrpcContextAccessor.GetClientId();
            var requstClientVersion = GrpcContextAccessor.GetClientVersion();
            var clientVersion = reMainProject.ClientVersion;//
            var clientId = reMainProject.clientId;//
            string strCheckClientComVersion = "";
            var isOldMainprojectUpgrade = false;  //标记本次导入用于330升级导入
            if (!CheckClientVerisonAndClientid(_teamRepository, reMainProject, requstClientId,requstClientVersion, out strCheckClientComVersion))
            {

                //x.IsSuccess = false;
                //x.Message = "项目版本与程序不匹配";
                //_logger.LogInformation($"{strCheckClientComVersion}");
                //return x;
                //兼容升级流程,返回false判断未是330升级
                isOldMainprojectUpgrade = true;
                
            }
            else
            {
                //如果未非升级流程导入，需要验证版本记录文件
                if (!File.Exists(unzipPath + separator + "clientModuleVersion.xml"))
                {
                    x.IsSuccess = false;
                    x.Message = "未找到版本记录文件";
                    _logger.LogInformation(x.Message);
                    return x;
                }
            }

            List<ClientModuleVersion> clientModuleVersion = null;
            if (!isOldMainprojectUpgrade)
            {
                clientModuleVersion = BimBaseServerConfigurations.ReadCustomConfiguration<List<ClientModuleVersion>>(unzipPath + separator + "clientModuleVersion.xml");
            }

            //判断当前服务器是否存在该项目
            bool isExsistMP = false;
            var MP = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == reMainProject.ID);
            if (MP != null)
            {
                isExsistMP = true;
            }
            var newMainprojectName = request.NewMainprojectName;
            
            var status = "creating";
            MainProject main = new MainProject
            {
                ID = Guid.NewGuid(),
                Name = newMainprojectName,
                Description = string.IsNullOrEmpty(mpDescription) ? reMainProject.Description : mpDescription,
                CreationTime = DateTime.Now,
                CreateUser = username,//reMainProject.CreateUser,
                RoleGroupId = reMainProject.RoleGroupId,
                RoleString = reMainProject.RoleString,
                LastUpdateTime = DateTime.Now,
                ExtendStr = reMainProject.ExtendStr,
                ShortName = shortName,
                MainProjectType = reMainProject.MainProjectType,
                IsDelete = reMainProject.IsDelete,
                EngineerType = string.IsNullOrEmpty(reMainProject.EngineerType) ? "无" : reMainProject.EngineerType,
                DesignStage = string.IsNullOrEmpty(reMainProject.DesignStage) ? "无" : reMainProject.DesignStage,
                status = status
            };
            if (isOldMainprojectUpgrade)
            {
                //若为330项目导入，设置版本为330版本
                main.clientId = clientId;
                main.ClientVersion = "BIMBase-2025R01.00";
            }
            else
            {
                main.clientId = string.IsNullOrEmpty(reMainProject.clientId) ? clientId : reMainProject.clientId;
                main.ClientVersion = string.IsNullOrEmpty(reMainProject.ClientVersion) ? clientVersion : reMainProject.ClientVersion;
            }
            var mainprj = _teamRepository.AddMainProject(main);
            if (mainprj != null)
            {
                if (!dicAllChangeGuid.ContainsKey(reMainProject.ID))
                {
                    dicAllChangeGuid.Add(reMainProject.ID, mainprj.ID);
                }
                if (clientModuleVersion != null)
                {
                    List<ClientModuleVersion> newclientmoduleversion = clientModuleVersion.Select(s => new ClientModuleVersion
                    {
                        ClientId = s.ClientId,
                        ModuleId = s.ModuleId,
                        Version = s.Version,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        ProjectId = mainprj.ID.ToString(),
                        ClientIp = s.ClientIp,
                        UserId = s.UserId,
                        UserName = s.UserName
                    }).ToList();

                    _teamRepository.AddClientModuleVersions(newclientmoduleversion);
                }


                var creatempdb = _teamRepository.CreateMPdataBase(mainprj.ID);
                if (!creatempdb)
                {
                    _logger.LogInformation("创建PKPM-PBIMServer-MPDB-" + mainprj.ID + "数据库失败");
                    x.IsSuccess = false;
                    return x;
                }

            }
            Guid newMPGuid = Guid.Empty;
            dicAllChangeGuid.TryGetValue(reMainProject.ID, out newMPGuid);

            //生成项目数据库
            var oldmainprojectDBName = "pkpm-pbimserver-mpdb-" + reMainProject.ID;
            var mainProjectDBName = "pkpm-pbimserver-mpdb-" + mainprj.ID;


            //mainprojectmanager.RestoreMPDb(sqlpath);


            _logger.LogInformation("ImportMainProjectForClient 生成项目数据库 Start" + mainprj.ID);
            using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
            {


                if (mainprj != null)
                {
                    #region
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        var sqlpath = unzipPath + separator + oldmainprojectDBName + ".sql";
                        var command = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + mainProjectDBName + " < " + sqlpath + "";
                        var argment = "--host=" + databaseIp + " --user=" + dbusername + " --password=" + dbpassword + " " + mainProjectDBName + " < " + sqlpath + "";
                        //string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
                        //        + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + mainProjectDBName + "<" + "\"" + unzipPath + "\\"
                        //        + oldmainprojectDBName + ".sql" + "\"";


                        _logger.LogInformation("command命令行============》" + command);
                        string scriptPath = unzipPath + separator + "mptemp" + unzippathGuid + ".sh";  // 替换为实际路径
                        ExcuteBash(command, scriptPath);
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        string codetype = "chcp 65001";
                        string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
                                + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + mainProjectDBName + "<" + "\"" + unzipPath + "\\"
                                + oldmainprojectDBName + ".sql" + "\"";
                        string command = codetype + "\r\n" + cmdStr;
                        string batpath = batPath + "mptemp" + unzippathGuid + ".bat";
                        ExcuteBat(command, batpath);
                    }
                    _logger.LogInformation("ImportMainProjectForClient 生成项目数据库 End" + mainprj.ID);



                    #endregion

                    List<TeamMember> memberTmp = _teamRepository.Members.ToList();
                    TeamMember member = memberTmp.FirstOrDefault(mem => mem.LoginName == username);
                    MainProject mp = _teamRepository.MainProjectsIgnoreClientVersion.FirstOrDefault(m => m.ID == mainprj.ID);//_teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprj.ID);
                    _teamRepository.AddMemberToMainProject(member, mp);

                    member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                    _teamRepository.AddMemberToMainProject(member, mp);

                    member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                    _teamRepository.AddMemberToMainProject(member, mp);

                }
                scope.Complete();
            }

            //记录filedirectoryguid
            List<FileDirectory> listFiledir = BimBaseServerConfigurations.ReadCustomConfiguration<List<FileDirectory>>(unzipPath + separator + "FiledirectoryInfo.xml");

            List<FileDirectory> newFileDirList = new List<FileDirectory>();
            foreach (var fd in listFiledir)
            {
                if (!dicAllChangeGuid.ContainsKey(fd.ID))
                {
                    dicAllChangeGuid.Add(fd.ID, Guid.NewGuid());
                }

            }

            foreach (var fd in listFiledir)
            {
                Guid temp = Guid.Empty; //文件夹ID更新
                Guid tmpP = Guid.Empty; //文件夹parentid更新
                dicAllChangeGuid.TryGetValue(fd.ID, out temp);
                if (fd.ParentID != Guid.Empty)
                {
                    dicAllChangeGuid.TryGetValue(fd.ParentID, out tmpP);
                }
                FileDirectory f = new FileDirectory
                {
                    ID = temp,
                    Name = fd.Name,
                    MainProjectId = main.ID,
                    ParentID = tmpP,
                    CreateUser = fd.CreateUser,
                    RoleGroupId = fd.RoleGroupId,
                    RoleString = fd.RoleString,
                    OrderNo = fd.OrderNo,
                    Type = fd.Type
                };
                newFileDirList.Add(f);

            }
            _teamRepository.AddFileDirectoreyList(newFileDirList);

            _logger.LogInformation("ImportMainProjectForClient AddFileDirectoreyList" + mainprj.ID);
            //还原volume和volumeversion信息
            List<Volume> oldVolList = BimBaseServerConfigurations.ReadCustomConfiguration<List<Volume>>(unzipPath + separator + "VolumeInfo.xml");

            List<Volume> newVolList = new List<Volume>();
            foreach (var v in oldVolList)
            {
                Guid newVolumeid = Guid.NewGuid();
                Guid volfiledirid = Guid.Empty;
                if (v.FileDirectoryId != Guid.Empty)
                {
                    dicAllChangeGuid.TryGetValue(v.FileDirectoryId, out volfiledirid);
                }
                Volume vl = new Volume
                {
                    VolumeId = newVolumeid,
                    VolumeName = v.VolumeName,
                    CreateTime = v.CreateTime,
                    CreateUser = username,
                    ExtendStr = v.ExtendStr,
                    FileDirectoryId = volfiledirid,
                    MainProjectId = main.ID

                };
                newVolList.Add(vl);
                if (!dicAllChangeGuid.ContainsKey(v.VolumeId))
                {
                    dicAllChangeGuid.Add(v.VolumeId, newVolumeid);
                }


                var newVolPath = UploadModelPath + separator + "UploadFileCommands" + separator + vl.VolumeId;
                string currentVolumePath = unzipPath + separator + "UploadFileCommands" + separator + v.VolumeId;

                if (Directory.Exists(currentVolumePath))
                {
                    //复制文件夹
                    UtilityHelper.CopyEntireDir(currentVolumePath, newVolPath);
                }

            }
            _teamRepository.AddVolumeList(newVolList);


            _logger.LogInformation("ImportMainProjectForClient AddVolumeList" + mainprj.ID);
            List<VolumeVersion> oldVolVerList = BimBaseServerConfigurations.ReadCustomConfiguration<List<VolumeVersion>>(unzipPath + separator + "VolumeVersionInfo.xml");
            List<VolumeVersion> newVolVerList = new List<VolumeVersion>();

            var mainprojectmanager = _teamRepository.GetMainProjectRepository(newMPGuid);

            _logger.LogInformation("ImportMainProjectForClient UpdateMPVolumeVersionFilePathInfo foreach " + mainprj.ID + " " + oldVolVerList.Count);
            foreach (var vv in oldVolVerList)
            {
                Guid volid = Guid.Empty;
                dicAllChangeGuid.TryGetValue(vv.VolumeId, out volid);
                string newSavePath = "";
                string str = vv.SavePath;
                string subString = vv.VolumeId.ToString();
                int index = str.IndexOf(subString);
                if (index != -1)
                {
                    string result = str.Substring(index + subString.Length);
                    var debugpath = UploadModelPath;
                    //var debugpath = "E:\\debugServerPath";
                    newSavePath = debugpath + separator + "UploadFileCommands" + separator + volid + result;
                }
                VolumeVersion newVV = new VolumeVersion
                {
                    VolumeId = volid,
                    SavePath = newSavePath,
                    Description = vv.Description,
                    ExtendStr = vv.ExtendStr,
                    FileName = vv.FileName,
                    FileSize = vv.FileSize,
                    FileType = vv.FileType,
                    UploadTime = vv.UploadTime,
                    UploadUser = username,
                    VerNo = vv.VerNo
                };
                newVolVerList.Add(newVV);
                //更新mpdb中mpvolumeversions表中的路径
                MPVolumeVersion mPVolumeVersion = new MPVolumeVersion
                {
                    VolumeId = volid,
                    SavePath = newSavePath,
                    Description = vv.Description,
                    ExtendStr = vv.ExtendStr,
                    FileName = vv.FileName,
                    FileSize = vv.FileSize,
                    FileType = vv.FileType,
                    UploadTime = vv.UploadTime,
                    UploadUser = username,
                    VerNo = vv.VerNo
                };
                mainprojectmanager.UpdateMPVolumeVersionFilePathInfo(mPVolumeVersion);
            }
            _logger.LogInformation("ImportMainProjectForClient AddVolumeVersionList start" + mainprj.ID);
            _teamRepository.AddVolumeVersionList(newVolVerList);

            _logger.LogInformation("ImportMainProjectForClient AddVolumeVersionList end" + mainprj.ID);

            if (File.Exists(unzipPath + separator + "MPLibInfo.xml"))
            {
                //处理pkpm-pbimserver-mpdb-中与lib相关的libGuid
                List<MPLibraryInfo> mPLibraryInfos = BimBaseServerConfigurations.ReadCustomConfiguration<List<MPLibraryInfo>>(unzipPath + separator + "MPLibInfo.xml");

                foreach (var lib in mPLibraryInfos)
                {
                    var newlibId = Guid.NewGuid();
                    MPLibraryInfo newLib = new MPLibraryInfo
                    {
                        LibId = newlibId,
                        CreateTime = lib.CreateTime,
                        CreateUser = lib.CreateUser,
                        ExtendStr = lib.ExtendStr,
                        LibDescription = lib.LibDescription,
                        LibName = lib.LibName,
                        LibType = lib.LibType
                    };
                    //lib.LibId = newlibId;
                    if (!dicAllChangeGuid.ContainsKey(lib.LibId))
                    {
                        dicAllChangeGuid.Add(lib.LibId, newlibId);
                    }
                    mainprojectmanager.RemoveMPLibraryInfo(lib.LibId);
                    mainprojectmanager.AddMPLibraryInfo(newLib);
                    //mainprojectmanager.UpdateMPLibraryInfoGuid(lib.LibId, newlibId);
                    string currentlibPath = unzipPath + separator + "MPLibraryFile" + separator + lib.LibId;
                    var newlibPath = UploadModelPath + separator + "MPLibraryFile" + separator + newlibId;
                    if (Directory.Exists(currentlibPath))
                    {
                        //复制文件夹
                        UtilityHelper.CopyEntireDir(currentlibPath, newlibPath);
                    }
                    var newmpfilelibraryfilepath = UploadModelPath + separator + "MPLibraryFile" + separator + newlibId;
                    var newmpfilelibraryfilepathStr = newmpfilelibraryfilepath.Replace(@"\", @"\\");//separator.ToString());
                    mainprojectmanager.UpdateMPLibFilePath(newMPGuid.ToString(), newmpfilelibraryfilepathStr, lib.LibId.ToString(), newlibId.ToString());

                    mainprojectmanager.UpdateMPTreeNodeVersionInfo(newMPGuid.ToString(), lib.LibId.ToString(), newlibId.ToString());

                    mainprojectmanager.UpdateMPCataLogTreeNodeLibGuid(newMPGuid.ToString(), lib.LibId.ToString(), newlibId.ToString());
                }
            }


            //还原CLMainprojectLibs
            _logger.LogInformation("ImportMainProjectForClient ReadCustomConfiguration start" + mainprj.ID);
            List<CLMainprojectLib> cLMainprojectLibs = BimBaseServerConfigurations.ReadCustomConfiguration<List<CLMainprojectLib>>(unzipPath + separator + "CLMainprojectLibInfo.xml");
            List<CLMainprojectLib> newCLMpLibs = new List<CLMainprojectLib>();
            _logger.LogInformation("ImportMainProjectForClient ReadCustomConfiguration end" + mainprj.ID);
            foreach (var mplibs in cLMainprojectLibs)
            {
                Guid newmplibguid = Guid.Empty;
                dicAllChangeGuid.TryGetValue(mplibs.LibGuid, out newmplibguid);
                var newCLMpLib = new CLMainprojectLib
                {
                    MainProjectGuid = newMPGuid,
                    LibGuid = newmplibguid,
                    TreeId = mplibs.TreeId,
                    TreeType = mplibs.TreeType
                };
                newCLMpLibs.Add(newCLMpLib);
            }
            _logger.LogInformation("ImportMainProjectForClient AddCLMainprojectLibList start" + mainprj.ID);
            _libRepository.AddCLMainprojectLibList(newCLMpLibs);


            _logger.LogInformation("ImportMainProjectForClient AddCLMainprojectLibList end" + mainprj.ID);

            //处理teamproject
            List<TeamProject> listTP =
                BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamProject>>(unzipPath + separator + "TeamProjectListInfo.xml");

            foreach (var tp in listTP)
            {
                Guid fdirid = Guid.Empty;
                if (!string.IsNullOrEmpty(tp.FileDirectoryID) && tp.FileDirectoryID != Guid.Empty.ToString())
                {
                    dicAllChangeGuid.TryGetValue(Guid.Parse(tp.FileDirectoryID), out fdirid);
                }
                TeamProject newTp = new TeamProject
                {
                    ID = Guid.NewGuid(),
                    Name = tp.Name,
                    Avatar = tp.Avatar,
                    //CreateUser = username,
                    CreationTime = tp.CreationTime,
                    Description = tp.Description,
                    EnableAuthority = tp.EnableAuthority,
                    EndTime = tp.EndTime,
                    FileDirectoryID = fdirid.ToString(),
                    FilePath = tp.FilePath,
                    Leader = username,
                    MainProjectID = newMPGuid.ToString(),
                    ParentProjectID = newMPGuid.ToString(),
                    Progress = tp.Progress,
                    ProjectType = tp.ProjectType,
                    StartTime = tp.StartTime
                };
                if (tp.Name == reMainProject.Name)
                {
                    newTp.Name = newMainprojectName;
                }
                newTp.MainProjectID = newMPGuid.ToString();
                var prj = _teamRepository.AddProject(newTp);
                if (prj != null)
                {
                    if (!dicAllChangeGuid.ContainsKey(tp.ID))
                    {
                        dicAllChangeGuid.Add(tp.ID, prj.ID);
                    }
                    var tempmpteam = mainprojectmanager.MPTeamProjects.FirstOrDefault(s => s.ID == tp.ID);
                    MPTeamProject mPTeam = new MPTeamProject
                    {
                        ID = prj.ID,
                        Avatar = prj.Avatar,
                        CreateUser = prj.CreateUser,
                        CreationTime = prj.CreationTime,
                        Description = prj.Description,
                        EnableAuthority = prj.EnableAuthority,
                        EndTime = prj.EndTime,
                        ExtendProperty = prj.ExtendProperty,
                        FileDirectoryID = prj.FileDirectoryID,
                        FilePath = prj.FilePath,
                        Leader = prj.Leader,
                        Name = prj.Name,
                        ParentProjectID = prj.ParentProjectID,
                        Progress = prj.Progress,
                        ProjectType = prj.ProjectType,
                        StartTime = prj.StartTime,
                        InitState = tempmpteam.InitState
                    };
                    mainprojectmanager.RemoveMPTeamProject(tp.ID);
                    mainprojectmanager.AddMPTeamProject(mPTeam);

                    string currentmplinkfilePath = unzipPath + separator + "LinkFile" + separator + reMainProject.ID.ToString() + separator + tp.ID.ToString();
                    var newmplinkfilePath = UploadModelPath + separator + "LinkFile" + separator + newMPGuid.ToString() + separator + prj.ID.ToString();
                    if (Directory.Exists(currentmplinkfilePath))
                    {
                        //复制文件夹
                        UtilityHelper.CopyEntireDir(currentmplinkfilePath, newmplinkfilePath);
                    }
                    var newmplinkfilePathStr = newmplinkfilePath.Replace(@"\", @"\\");
                    mainprojectmanager.UpdateMPLinkFilePathWithNewProjectId(newMPGuid.ToString(), newmplinkfilePathStr, tp.ID.ToString(), prj.ID.ToString());

                    string currentdrawfilepath = unzipPath + separator + "DrawingFile" + separator + reMainProject.ID.ToString();
                    var newmpdrawfilepath = UploadModelPath + separator + "DrawingFile" + separator + newMPGuid.ToString();
                    if (Directory.Exists(currentdrawfilepath))
                    {
                        UtilityHelper.CopyEntireDir(currentdrawfilepath, newmpdrawfilepath);
                    }
                    var newmpdrawfilepathStr = newmpdrawfilepath.Replace(@"\", @"\\");
                    mainprojectmanager.UpdateMPDrawingPathWithNewProjectId(newMPGuid.ToString(), newmpdrawfilepathStr, tp.ID.ToString(), prj.ID.ToString(), reMainProject.ID.ToString());

                    mainprojectmanager.UpdateMPProjectTreeNodeSubprojectId(newMPGuid.ToString(), tp.ID.ToString(), prj.ID.ToString());

                    mainprojectmanager.UpdateMPReleaseConfigInfo(newMPGuid.ToString(), tp.ID.ToString(), prj.ID.ToString());

                    mainprojectmanager.UpdateMPUserGroupAuthInfo(newMPGuid.ToString(), tp.ID.ToString(), prj.ID.ToString());


                    var createmodeldb = _teamRepository.CreateModeldataBase(prj.ID);
                    if (!createmodeldb)
                    {
                        _logger.LogInformation("创建PKPM-PBIMServer-ModelDB-" + prj.ID + "数据库失败");
                        x.IsSuccess = false;
                        return x;
                    }
                    var modelDBName = "pkpm-pbimserver-modeldb-" + prj.ID;
                    var oldmdb = "pkpm-pbimserver-modeldb-" + tp.ID;
                    //string codetype = "chcp 65001";
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        string modelsqlpath = unzipPath + separator + oldmdb + ".sql";
                        var modelcommand = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + " < " + modelsqlpath + "";
                        _logger.LogInformation("modelcommand:====>" + modelcommand);

                        string modelscriptPath = unzipPath + separator + "modeltemp" + unzippathGuid + ".sh";

                        ExcuteBash(modelcommand, modelscriptPath);
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        string codetype = "chcp 65001";
                        string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
                                + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + modelDBName + "<" + "\"" + unzipPath + "\\"
                                + oldmdb + ".sql" + "\"";
                        string command = codetype + "\r\n" + cmdStr;
                        string batpath = batPath + "modeltemp" + unzippathGuid + ".bat";
                        ExcuteBat(command, batpath);
                    }



                    _teamRepository.ClearLockedComponents(prj.ID);
                    _teamRepository.ClearLockModelfiles(prj.ID);
                    var newPath = UploadModelPath + separator + "UploadFileCommands" + separator + prj.ID;
                    var newPathStr = newPath.Replace(@"\", @"\\");
                    _teamRepository.UpdateModelFileInfo(tp.ID, prj.ID, newPathStr);

                    string currentProjectPath = unzipPath + separator + "UploadFileCommands" + separator + tp.ID;
                    if (Directory.Exists(currentProjectPath))
                    {
                        //复制文件夹
                        UtilityHelper.CopyEntireDir(currentProjectPath, newPath);
                    }
                    var newWebPath = UploadModelPath + separator + "ProjectDirectory" + separator + main.Name;

                    var newWebPathStr = newWebPath.Replace(@"\", @"\\");

                    _teamRepository.UpdateMileStoneFileInfo(tp.ID, reMainProject.Name, prj.ID, newWebPathStr);

                    string currentMileStonePath = unzipPath + separator + "ProjectDirectory" + separator + reMainProject.Name;
                    Console.WriteLine("currentMileStonePath===============>" + currentMileStonePath);
                    if (Directory.Exists(currentMileStonePath))
                    {
                        Console.WriteLine("ProjectDirectory===============>" + currentMileStonePath);
                        UtilityHelper.CopyEntireDir(currentMileStonePath, newWebPath);
                    }
                }
            }
            main.status = "success";
            _teamRepository.UpdateMainprojectInfo(main);

            //mainProjectUserGroupMembersList @"\mainProjectUserGroupMember.xml";
            var mainProjectUserGroupMembersList = new List<MainProjectUserGroupMember>();
            if (File.Exists(unzipPath + separator + "mainProjectUserGroupMember.xml"))
            {
                mainProjectUserGroupMembersList = BimBaseServerConfigurations.ReadCustomConfiguration<List<MainProjectUserGroupMember>>(unzipPath + separator + "mainProjectUserGroupMember.xml");
            }
            //mainProjectUserGroupLibAuthsList @"\mainProjectUserGroupLibAuth.xml";
            var mainProjectUserGroupLibAuthsList = new List<MainProjectUserGroupLibAuth>();
            if (File.Exists(unzipPath + separator + "mainProjectUserGroupLibAuth.xml"))
            {
                mainProjectUserGroupLibAuthsList = BimBaseServerConfigurations.ReadCustomConfiguration<List<MainProjectUserGroupLibAuth>>(unzipPath + separator + "mainProjectUserGroupLibAuth.xml");
            }
            //mainProjectUserGroupAuthsList  @"\mainProjectUserGroupAuth.xml";
            var mainProjectUserGroupAuthsList = new List<MainProjectUserGroupAuth>();
            if (File.Exists(unzipPath + separator + "mainProjectUserGroupAuth.xml"))
            {
                mainProjectUserGroupAuthsList = BimBaseServerConfigurations.ReadCustomConfiguration<List<MainProjectUserGroupAuth>>(unzipPath + separator + "mainProjectUserGroupAuth.xml");
            }
            //mainProjectTeamGroupsList @"\mainProjectTeamGroup.xml";
            var mainProjectTeamGroupsList = new List<MainProjectTeamGroup>();
            if (File.Exists(unzipPath + separator + "mainProjectTeamGroup.xml"))
            {
                mainProjectTeamGroupsList = BimBaseServerConfigurations.ReadCustomConfiguration<List<MainProjectTeamGroup>>(unzipPath + separator + "mainProjectTeamGroup.xml");
            }
            //teamMembersList @"\teamMember.xml";
            var teamMembersList = new List<TeamMember>();
            if (File.Exists(unzipPath + separator + "teamMember.xml"))
            {
                teamMembersList = BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamMember>>(unzipPath + separator + "teamMember.xml");
            }

            //teamusergroups @"\teamUserGroup.xml";
            var teamUserGroupList = new List<TeamUserGroup>();
            if (File.Exists(unzipPath + separator + "teamUserGroup.xml"))
            {
                teamUserGroupList = BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamUserGroup>>(unzipPath + separator + "teamUserGroup.xml");
            }
            //teamgroups @"\teamGroup.xml";
            var teamGroupList = new List<TeamGroup>();
            if (File.Exists(unzipPath + separator + "teamGroup.xml"))
            {
                teamGroupList = BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamGroup>>(unzipPath + separator + "teamGroup.xml");
            }
            if (isExsistMP)
            {
                foreach (var item in teamMembersList)
                {
                    var cur = _teamRepository.Members.FirstOrDefault(s => s.LoginName == item.LoginName);
                    var oldid = item.ID;
                    var newid = "";
                    if (cur != null)
                    {
                        if (!dicAllChangeGuid.ContainsKey(item.ID))
                        {
                            dicAllChangeGuid.Add(item.ID, cur.ID);
                        }
                        newid = cur.ID.ToString();

                    }
                    if (!string.IsNullOrEmpty(newid))
                    {
                        mainprojectmanager.UpdateMPUserGroupMemberInfo(newMPGuid.ToString(), oldid.ToString(), newid);
                    }

                }
                List<MainProjectUserGroupMember> newMPusergroupmembers = new List<MainProjectUserGroupMember>();
                //同服务器导入：将权限相关数据替换Guid后，直接导入
                foreach (var item in mainProjectUserGroupMembersList)
                {
                    MainProjectUserGroupMember newmpusergroupmember = new MainProjectUserGroupMember
                    {
                        MainProjectId = newMPGuid,
                        TeamMemberId = item.TeamMemberId,
                        UserGroupId = item.UserGroupId,
                        UserGroupName = item.UserGroupName
                    };
                    newMPusergroupmembers.Add(newmpusergroupmember);
                }
                var mpadmin = mainProjectUserGroupMembersList.FirstOrDefault(n => n.UserGroupName == "项目管理员");
                if (mpadmin != null)
                {
                    var ishas = newMPusergroupmembers.FirstOrDefault(s => s.TeamMemberId == currentUser.ID && s.UserGroupId == mpadmin.UserGroupId);
                    if (ishas == null)
                    {
                        MPUserGroup mPUserGroup = mainprojectmanager.MPUserGroups.FirstOrDefault(s => s.ID == mpadmin.UserGroupId);
                        mainprojectmanager.AddTeamMemberToMPUsergroup(currentUser, mPUserGroup);
                        MainProjectUserGroupMember tem = new MainProjectUserGroupMember
                        {
                            MainProjectId = newMPGuid,
                            TeamMemberId = currentUser.ID,
                            UserGroupId = mpadmin.UserGroupId,
                            UserGroupName = mpadmin.UserGroupName
                        };
                        //当前用户未在项目中
                        newMPusergroupmembers.Add(tem);
                    }
                }
                _teamRepository.AddMainProjectUserGroupMemberList(newMPusergroupmembers);
                List<MainProjectUserGroupLibAuth> newMainProjectUserGroupLibAuths = new List<MainProjectUserGroupLibAuth>();
                foreach (var item in mainProjectUserGroupLibAuthsList)
                {
                    MainProjectUserGroupLibAuth newlibauth = new MainProjectUserGroupLibAuth
                    {
                        AuthInfo = item.AuthInfo,
                        MPUserGroupId = item.MPUserGroupId,
                        ExtendStr = item.ExtendStr,
                        LibType = item.LibType,
                        MainProjectID = newMPGuid,
                        Permission = item.Permission
                    };
                    newMainProjectUserGroupLibAuths.Add(newlibauth);
                }
                _teamRepository.AddMainProjectUserGroupLibAuthList(newMainProjectUserGroupLibAuths);
                List<MainProjectUserGroupAuth> newMainProjectUserGroupAuths = new List<MainProjectUserGroupAuth>();
                foreach (var item in mainProjectUserGroupAuthsList)
                {

                    var newobjguid = Guid.Empty;
                    dicAllChangeGuid.TryGetValue(item.ObjectId, out newobjguid);
                    MainProjectUserGroupAuth newitem = new MainProjectUserGroupAuth
                    {
                        MainProjectID = newMPGuid,
                        AuthInfo = item.AuthInfo,
                        ObjectId = newobjguid,
                        GroupOrMemberId = item.GroupOrMemberId,
                        InstanceId = item.InstanceId,
                        IsGroupOrTeamMember = item.IsGroupOrTeamMember,
                        ObjectType = item.ObjectType,
                        TreeId = item.TreeId
                    };
                    newMainProjectUserGroupAuths.Add(newitem);
                }
                _teamRepository.AddMainProjectUserGroupAuthList(newMainProjectUserGroupAuths);
                List<MainProjectTeamGroup> newMainProjectTeamGroups = new List<MainProjectTeamGroup>();
                foreach (var item in mainProjectTeamGroupsList)
                {
                    var newobjguid = Guid.Empty;
                    dicAllChangeGuid.TryGetValue(item.ObjectId, out newobjguid);
                    MainProjectTeamGroup newitem = new MainProjectTeamGroup
                    {
                        MainProjectId = newMPGuid,
                        GroupOrTeamMemberId = item.GroupOrTeamMemberId,
                        MemberType = item.MemberType,
                        ObjectId = newobjguid
                    };

                    newMainProjectTeamGroups.Add(newitem);
                }
                _teamRepository.AddMainProjectTeamGroupList(newMainProjectTeamGroups);

                _logger.LogInformation("ImportMainProjectForClient AddTeamUserGroupList" + mainprj.ID);

            }
            else
            {
                //不是同服务器导入，需先将用户信息导入
                foreach (var item in teamMembersList)
                {
                    var cur = _teamRepository.Members.FirstOrDefault(s => s.LoginName == item.LoginName);
                    var oldid = item.ID;
                    var newid = "";
                    if (cur == null)
                    {
                        item.ID = Guid.NewGuid();
                        //新建用户，将用户id存入dicAllChangeGuid
                        var newmemb = _teamRepository.AddMember(item);
                        if (newmemb != null)
                        {
                            if (!dicAllChangeGuid.ContainsKey(oldid))
                            {
                                dicAllChangeGuid.Add(oldid, newmemb.ID);
                            }
                            newid = newmemb.ID.ToString();
                        }

                    }
                    else
                    {
                        if (!dicAllChangeGuid.ContainsKey(item.ID))
                        {
                            dicAllChangeGuid.Add(item.ID, cur.ID);
                        }
                        newid = cur.ID.ToString();
                    }
                    if (!string.IsNullOrEmpty(newid))
                    {
                        mainprojectmanager.UpdateMPUserGroupMemberInfo(newMPGuid.ToString(), oldid.ToString(), newid);
                    }

                }

                List<MainProjectUserGroupMember> newMPusergroupmembers = new List<MainProjectUserGroupMember>();
                foreach (var item in mainProjectUserGroupMembersList)
                {
                    var newtmguid = Guid.Empty;
                    dicAllChangeGuid.TryGetValue(item.TeamMemberId, out newtmguid);
                    MainProjectUserGroupMember newmpusergroupmember = new MainProjectUserGroupMember
                    {
                        MainProjectId = newMPGuid,
                        TeamMemberId = newtmguid,
                        UserGroupId = item.UserGroupId,
                        UserGroupName = item.UserGroupName
                    };
                    newMPusergroupmembers.Add(newmpusergroupmember);
                }
                var mpadmin = mainProjectUserGroupMembersList.FirstOrDefault(n => n.UserGroupName == "项目管理员");
                if (mpadmin != null)
                {
                    var ishas = newMPusergroupmembers.FirstOrDefault(s => s.TeamMemberId == currentUser.ID && s.UserGroupId == mpadmin.UserGroupId);
                    if (ishas == null)
                    {
                        MPUserGroup mPUserGroup = mainprojectmanager.MPUserGroups.FirstOrDefault(s => s.ID == mpadmin.UserGroupId);
                        mainprojectmanager.AddTeamMemberToMPUsergroup(currentUser, mPUserGroup);
                        MainProjectUserGroupMember tem = new MainProjectUserGroupMember
                        {
                            MainProjectId = newMPGuid,
                            TeamMemberId = currentUser.ID,
                            UserGroupId = mpadmin.UserGroupId,
                            UserGroupName = mpadmin.UserGroupName
                        };
                        //当前用户未在项目中
                        newMPusergroupmembers.Add(tem);
                    }
                }
                _teamRepository.AddMainProjectUserGroupMemberList(newMPusergroupmembers);
                List<MainProjectUserGroupLibAuth> newMainProjectUserGroupLibAuths = new List<MainProjectUserGroupLibAuth>();
                foreach (var item in mainProjectUserGroupLibAuthsList)
                {
                    MainProjectUserGroupLibAuth newlibauth = new MainProjectUserGroupLibAuth
                    {
                        AuthInfo = item.AuthInfo,
                        MPUserGroupId = item.MPUserGroupId,
                        ExtendStr = item.ExtendStr,
                        LibType = item.LibType,
                        MainProjectID = newMPGuid,
                        Permission = item.Permission
                    };
                    newMainProjectUserGroupLibAuths.Add(newlibauth);
                }
                _teamRepository.AddMainProjectUserGroupLibAuthList(newMainProjectUserGroupLibAuths);
                List<MainProjectUserGroupAuth> newMainProjectUserGroupAuths = new List<MainProjectUserGroupAuth>();
                foreach (var item in mainProjectUserGroupAuthsList)
                {
                    var newobjguid = Guid.Empty;
                    dicAllChangeGuid.TryGetValue(item.ObjectId, out newobjguid);
                    if (item.IsGroupOrTeamMember == 1)
                    {
                        var newmemid = Guid.Empty;
                        dicAllChangeGuid.TryGetValue(Guid.Parse(item.GroupOrMemberId), out newmemid);
                        item.GroupOrMemberId = newmemid.ToString();
                    }
                    MainProjectUserGroupAuth newitem = new MainProjectUserGroupAuth
                    {
                        MainProjectID = newMPGuid,
                        AuthInfo = item.AuthInfo,
                        ObjectId = newobjguid,
                        GroupOrMemberId = item.GroupOrMemberId,
                        InstanceId = item.InstanceId,
                        IsGroupOrTeamMember = item.IsGroupOrTeamMember,
                        ObjectType = item.ObjectType,
                        TreeId = item.TreeId
                    };
                    newMainProjectUserGroupAuths.Add(newitem);
                }
                _teamRepository.AddMainProjectUserGroupAuthList(newMainProjectUserGroupAuths);
                List<MainProjectTeamGroup> newMainProjectTeamGroups = new List<MainProjectTeamGroup>();
                foreach (var item in mainProjectTeamGroupsList)
                {
                    item.MainProjectId = newMPGuid;
                    var newobjguid = Guid.Empty;
                    dicAllChangeGuid.TryGetValue(item.ObjectId, out newobjguid);
                    if (item.MemberType == 1)
                    {
                        var newmemid = Guid.Empty;
                        dicAllChangeGuid.TryGetValue(Guid.Parse(item.GroupOrTeamMemberId), out newmemid);
                        item.GroupOrTeamMemberId = newmemid.ToString();
                    }
                    MainProjectTeamGroup newitem = new MainProjectTeamGroup
                    {
                        MainProjectId = newMPGuid,
                        GroupOrTeamMemberId = item.GroupOrTeamMemberId,
                        MemberType = item.MemberType,
                        ObjectId = newobjguid
                    };

                    newMainProjectTeamGroups.Add(newitem);
                }
                _teamRepository.AddMainProjectTeamGroupList(newMainProjectTeamGroups);
                Dictionary<int, int> dicTeamGroupId = new Dictionary<int, int>();
                //导入部门
                foreach (var item in teamGroupList)
                {
                    var tg = _teamRepository.TeamGroups.FirstOrDefault(g => g.GroupName == item.GroupName);
                    var oldid = item.Id;
                    if (tg == null)
                    {
                        TeamGroup teamGroup = new TeamGroup();
                        teamGroup.GroupName = item.GroupName;
                        teamGroup.Description = item.Description;
                        var ret = _teamRepository.AddTeamGroup(teamGroup);
                        if (ret != null)
                        {
                            if (!dicTeamGroupId.ContainsKey(oldid))
                            {
                                dicTeamGroupId.Add(oldid, ret.Id);
                            }
                        }
                    }
                    else
                    {
                        if (!dicTeamGroupId.ContainsKey(oldid))
                        {
                            dicTeamGroupId.Add(oldid, tg.Id);
                        }
                    }
                }
                List<TeamUserGroup> newTeamUserGroupList = new List<TeamUserGroup>();
                var has = _teamRepository.TeamUserGroups.ToList();

                foreach (var item in teamUserGroupList)
                {
                    var ret1 = dicTeamGroupId.TryGetValue(item.GroupId, out var newgroupid);
                    var ret2 = dicAllChangeGuid.TryGetValue(item.TeamMemberId, out var newmemid);

                    if (ret1 && ret2)
                    {
                        var old = has.FirstOrDefault(s => s.TeamMemberId == newmemid && s.GroupId == newgroupid);
                        if (old == null)
                        {
                            TeamUserGroup newitem = new TeamUserGroup
                            {
                                GroupId = newgroupid,
                                TeamMemberId = newmemid
                            };
                            newTeamUserGroupList.Add(newitem);
                        }
                    }
                }
                _teamRepository.AddTeamUserGroupList(newTeamUserGroupList);
                _logger.LogInformation("ImportMainProjectForClient AddTeamUserGroupList" + mainprj.ID);
            }

            try
            {
                if (Directory.Exists(unzipPath))
                {
                    Directory.Delete(unzipPath, true);
                }
                if (Directory.Exists(zippath))
                {
                    Directory.Delete(zippath, true);
                }
            }
            catch
            {

            }
            x.IsSuccess = true;
            x.Message = "";


            _logger.LogInformation("ImportMainProjectForClient IsSuccess" + mainprj.ID);

            return x;
        }

        public override async Task<AddCLRepositoryResponse> AddCLRepository(SessionRequest request, ServerCallContext context)
        {
            var x = new AddCLRepositoryResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            long ret = _teamRepository.CreateRepositoryId();
            x.IsSuccess = true;
            x.RepId = ret;
            return x;
        }

        public async Task<List<GrpcLogFile>> GetAllSubFiles(string directoryPath, List<GrpcLogFile> files)
        {
            DirectoryInfo currentDirectoryInfo = new DirectoryInfo(directoryPath);

            // 当前一级文件夹内的子文件们
            FileInfo[] currentFileInfos = currentDirectoryInfo.GetFiles();
            foreach (FileInfo f in currentFileInfos)
            {
                GrpcLogFile lf = new GrpcLogFile
                {
                    LogName = f.Name,
                    LogPath = f.FullName
                };
                files.Add(lf);

            }
            // 当前一级文件夹内的子文件夹们
            DirectoryInfo[] subDirectoryInfos = currentDirectoryInfo.GetDirectories();
            //递归
            foreach (DirectoryInfo d in subDirectoryInfos)
            {
                await GetAllSubFiles(d.FullName, files);
            }
            return files;
        }

        /// <summary>
        /// 下载日志文件
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<DownLoadLogFileResponse> DownLoadLogFile(DownLoadLogFileRequest request, ServerCallContext context)
        {
            string UploadModelPath = "";

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }

            var x = new DownLoadLogFileResponse();
            if (!File.Exists(request.Log.LogPath))
            {
                x.IsSuccess = false;
                return x;
            }

            //IISManager iis = new IISManager("WebServer");
            //var virlogpath = GetWebSiteNum("WebServer") + @"\Logs";

            //string path = System.AppDomain.CurrentDomain.BaseDirectory + @"\logs\";
            string virlogpath = UploadModelPath + separator + "Logs";



            // 检查目录是否存在，不存在则创建
            if (!Directory.Exists(virlogpath))
            {
                Directory.CreateDirectory(virlogpath);
            }

            // 获取文件信息
            FileInfo file = new FileInfo(request.Log.LogPath);

            // 检查文件是否存在
            if (!file.Exists)
            {
                x.Message = $"文件 {file.FullName} 不存在。";
                x.IsSuccess = false;
                return x;
            }

            // 读取文件内容
            using (FileStream fs = file.OpenRead())
            {
                // 检查文件长度是否超出数组最大长度
                if (fs.Length > int.MaxValue)
                {
                    x.Message = "文件太大，无法读取。";
                    x.IsSuccess = false;
                    return x;
                }
                var fileByte = new byte[fs.Length];
                int bytesRead = fs.Read(fileByte, 0, (int)fs.Length);

                // 检查是否读取到预期的字节数
                if (bytesRead != fs.Length)
                {
                    x.Message = "读取文件时出现问题。";
                    x.IsSuccess = false;
                    return x;
                }
                GrpcLogFile grpcLogFile = new GrpcLogFile
                {
                    LogName = request.Log.LogName,
                    LogPath = request.Log.LogPath,
                    LogData = _mapper.Map<ByteString>(fileByte)
                };
                x.Log = grpcLogFile;
            }



            //if (!Directory.Exists(virlogpath))
            //{
            //    Directory.CreateDirectory(virlogpath);
            //}
            //FileInfo file = new FileInfo(request.Log.LogPath);
            //request.Log.LogData = new byte[file.Length];

            //FileStream fs = file.OpenRead();
            //fs.Read(request.Log.LogData, 0, Convert.ToInt32(fs.Length));
            //fs.Close();


            file.CopyTo(Path.Combine(virlogpath, request.Log.LogName), true);


            // iis.CreateVDir("serverlog", virlogpath, false, true, true, true, true, true, iis.WebSiteNum, iis.ServerName);


            x.IsSuccess = true;
            return x;
        }

        //public LogFile DownLoadLogFile(GrpcLogFile log, out string errMsg)
        //{
        //    PbimLog pbimLog = new PbimLog("Info");
        //    errMsg = "";
        //    try
        //    {

        //        if (!File.Exists(log.LogPath))
        //        {
        //            errMsg = "DirectoryNotFound";
        //            return null;
        //        }

        //        IISManager iis = new IISManager("WebServer");
        //        var virlogpath = GetWebSiteNum("WebServer") + @"\Logs";
        //        if (!Directory.Exists(virlogpath))
        //        {
        //            Directory.CreateDirectory(virlogpath);
        //        }
        //        FileInfo file = new FileInfo(log.LogPath);
        //        log.LogData = new byte[file.Length];

        //        FileStream fs = file.OpenRead();
        //        fs.Read(log.LogData, 0, Convert.ToInt32(fs.Length));
        //        fs.Close();


        //        file.CopyTo(Path.Combine(virlogpath, log.LogName), true);
        //        iis.CreateVDir("serverlog", virlogpath, false, true, true, true, true, true, iis.WebSiteNum, iis.ServerName);
        //        return log;
        //    }
        //    catch (Exception ex)
        //    {

        //        ExceptionHandler.HandleException("", ex);
        //    }
        //    return null;
        //}

        /// <summary>
        /// 日志列表
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetFileInfosResponse> GetFileInfos(Empty request, ServerCallContext context)
        {
            string UploadModelPath = "";

            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var x = new GetFileInfosResponse();
            List<GrpcLogFile> files = new List<GrpcLogFile>();
            //string path = System.AppDomain.CurrentDomain.BaseDirectory + @"\logs\";
            string path = UploadModelPath + separator + "logs" + separator;


            files = await GetAllSubFiles(path, files);

            x.IsSuccess = true;
            x.Files.AddRange(files);
            return x;
        }

        /// <summary>
        /// 导出项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<BackUpDBByMainProjectIdResponse> BackUpDBByMainProjectId(BackUpDBByMainProjectIdRequest request, ServerCallContext context)
        {
            var x = new BackUpDBByMainProjectIdResponse();
            // 检查SessionId参数是否有效
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            // 获取当前用户信息，如果获取失败表示用户登录已失效
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser == null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            Guid mainProjectGuid;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数MainProjectGuid格式有误！";
                return x;
            }
            string backupId = Guid.NewGuid().ToString();
            string assemblyDirectory = GetAssemblyDirectory();

            DirectoryInfo path_exe = new DirectoryInfo(assemblyDirectory);
            string baseBackupPath = path_exe.Parent.FullName;

            string backupRootPath = Path.Combine(baseBackupPath, "DBBackup" + backupId);
            string unzipPath = Path.Combine(baseBackupPath, "unzip" + backupId);
            string backupZipDirPath = Path.Combine(baseBackupPath, "DBBackupOut" + backupId);
            // string zipPath = backupZipDirPath + separator + mainProject.Name + ".zip";

            try
            {
                _logger.LogInformation($"开始备份项目: {mainProjectGuid}");

                string uploadRootPath = "";
                char separator = Path.DirectorySeparatorChar;

                // 根据操作系统获取上传模型路径
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    //  uploadRootPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                    uploadRootPath =_urls.UploadRootPath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    uploadRootPath = _urls.LoadDataSavePath;
                }
                if (!Directory.Exists(uploadRootPath))
                {
                    Directory.CreateDirectory(uploadRootPath);
                }
                // 获取项目
                var mainProject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
                if (mainProject == null)
                {
                    x.Message = $"项目不存在: {mainProjectGuid}";
                    return x;
                }
                // 获取模型
                var teamProjects = _teamRepository.Projects.Where(t => !string.IsNullOrEmpty(t.MainProjectID) && t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower()).ToList();
                // 获取文件夹
                var fileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                // 获取卷册图档
                var volumes = _teamRepository.Volumes.Where(v => v.MainProjectId == mainProjectGuid).ToList();
                // 获取模型id
                var teamProjectIds = teamProjects.Select(t => t.ID).ToList();

                //备份文件路径

                string zipPath = Path.Combine(backupZipDirPath, $"{mainProject.Name}.zip");

                // 创建备份路径和备份压缩包路径的目录（如果不存在）
                if (!Directory.Exists(backupRootPath))
                {
                    Directory.CreateDirectory(backupRootPath);
                }
                if (!Directory.Exists(backupZipDirPath))
                {
                    Directory.CreateDirectory(backupZipDirPath);
                }

                // 备份项目目录
                string mainProjectDirPath = Path.Combine(uploadRootPath, "ProjectDirectory", mainProject.Name);
                //string mainProjectDirPath = uploadRootPath + separator + "ProjectDirectory" + separator + mainProject.Name;
                //string webnumberDirPath = CommonTool.GetWebSiteNum("WebServer") + @"\ProjectDirectory" + @"\" + mainproject.Name;
                string backupProjectPath = Path.Combine(backupRootPath, "ProjectDirectory", mainProject.Name);
                if (Directory.Exists(mainProjectDirPath))
                {
                    UtilityHelper.CopyEntireDir(mainProjectDirPath, backupProjectPath);
                }


                _logger.LogInformation("===================================开始备份模型数据库=========================");
                //备份模型数据库
                var dbConfig = UtilityHelper.GetMysqlConnectionConfig();
                //var databaseIp = conf.IP;
                //var databasePort = conf.Port;
                //var dbusername = conf.User;
                //var dbpassword = conf.Password;

                foreach (var modelId in teamProjectIds)
                {

                    string modelDbName = "pkpm-pbimserver-modeldb-" + modelId;
                    string sqlBackupPath = Path.Combine(backupRootPath, $"{modelDbName}.sql");
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        ////var sqlpath = unzipPath + separator + oldmainprojectDBName + ".sql";
                        //var command = "mysqldump -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + ">" + "\"" + backupPath + separator
                        //        + modelDBName + ".sql" + "\"";


                        //_logger.LogInformation("command命令行============》" + command);
                        //string scriptPath = assemblyDirectory + "tpbacktemp" + backupId + ".sh";  // 替换为实际路径
                        //ExcuteBash(command, scriptPath);
                        string command = $"mysqldump -h {dbConfig.IP} -u {dbConfig.User} -p{dbConfig.Password} {modelDbName} > \"{sqlBackupPath}\"";
                        string scriptPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.sh");
                        ExcuteBash(command, scriptPath);
                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        //string codetype = "chcp 65001";
                        //string mainprojectcmdstr = "\"" + UtilityHelper.getmysqlpath() + "\\mysqldump.exe\" -h " + databaseIp + " -P "
                        //        + databasePort + " -u" + dbusername + " -p" + dbpassword + "  " + modelDbName + ">" + "\"" + backupPath + "\\"
                        //        + modelDbName + ".sql" + "\"";
                        //_logger.LogInformation("mainprojectcmdstr:" + mainprojectcmdstr);
                        //string command = codetype + "\r\n" + mainprojectcmdstr;
                        //string batpath = assemblyDirectory + "tpbacktemp" + backupId + ".bat";
                        //ExcuteBat(command, batpath);
                        string mysqlPath = UtilityHelper.getmysqlpath();
                        string command = $"chcp 65001\r\n\"{mysqlPath}\\mysqldump.exe\" -h {dbConfig.IP} -P {dbConfig.Port} -u{dbConfig.User} -p{dbConfig.Password} {modelDbName} > \"{sqlBackupPath}\"";
                        string batPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.bat");
                        ExcuteBat(command, batPath);
                    }

                    //string currentProjectPath = uploadRootPath + separator + @"UploadFileCommands" + separator + modelId;
                    ////复制文件夹
                    //string newPath = Path.Combine(backupPath, "UploadFileCommands", modelId.ToString());
                    // 备份模型文件（仅最新版本）
                    string sourceProjectPath = Path.Combine(uploadRootPath, "UploadFileCommands", modelId.ToString());
                    string targetProjectPath = Path.Combine(backupRootPath, "UploadFileCommands", modelId.ToString());

                    //modelfiles初始版本中的路径
                    var tempModelManager = _teamRepository.GetProjectRepository(modelId);
                    var modelFiles = tempModelManager.ModelFiles.ToList();

                    // 备份最新版本的模型文件
                    if (modelFiles.Any())
                    {
                        var distinctFileNames = modelFiles.Select(m => m.FileName).Distinct().ToList();
                        foreach (var fileName in distinctFileNames)
                        {
                            var latestVersion = modelFiles.Where(n => n.FileName == fileName).Max(f => f.VersionNo);
                            var fileToBackup = tempModelManager.ModelFiles.FirstOrDefault(f => f.VersionNo == latestVersion && f.FileName == fileName);

                            //var tempFilePath = fileToBackup.FilePath;
                            //var tempFilePathStr = tempFilePath.Substring(0, tempFilePath.IndexOf(separator + "UploadFileCommands"));
                            //string tempCurPrjPath = tempFilePathStr + separator + "UploadFileCommands" + separator + modelId;

                            //if (File.Exists(tempFilePath))
                            //{
                            //    var newCopyFilePath = tempFilePath.Replace(tempCurPrjPath, newPath);
                            //    var oldCopyDir = tempFilePath.Substring(0, tempFilePath.LastIndexOf(separator));
                            //    var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));
                            //    if (!Directory.Exists(newCopyDir))
                            //    {
                            //        Directory.CreateDirectory(newCopyDir);
                            //    }
                            //    File.Copy(tempFilePath, tempFilePath.Replace(oldCopyDir, newCopyDir), true);
                            //}
                            if (fileToBackup != null && File.Exists(fileToBackup.FilePath))
                            {
                                try
                                {
                                    // 构建新的文件路径
                                    string sourceFilePath = fileToBackup.FilePath;
                                    string targetFilePath = sourceFilePath.Replace(sourceProjectPath, targetProjectPath);

                                    // 创建目标目录
                                    string targetDir = Path.GetDirectoryName(targetFilePath);
                                    Directory.CreateDirectory(targetDir);

                                    // 复制文件
                                    File.Copy(sourceFilePath, targetFilePath, true);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"备份文件 {fileToBackup.FileName} 失败");
                                }
                            }

                        }

                        //获取非最新版本里程碑文件版本，排除
                        // 清理旧版本里程碑文件（仅保留最新版本）
                        var milestoneFiles = tempModelManager.MilestoneFiles.ToList();
                        if (milestoneFiles.Any())
                        {
                            if (milestoneFiles.Count > 1)
                            {
                                var maxVersion = milestoneFiles.Max(s => s.VersionNo);
                                var oldVersions = milestoneFiles.Where(s => s.VersionNo < maxVersion).ToList();
                                //删除整个目录（包括所有文件和子目录）
                                //foreach (var sf in oldVersions)
                                //{
                                //    var tempFilePath = sf.SavePath;
                                //    var tempFilePathStr = tempFilePath.Substring(tempFilePath.IndexOf(separator + "ProjectDirectory") + 1, tempFilePath.LastIndexOf(separator) - tempFilePath.IndexOf(separator + "ProjectDirectory") - 1);
                                //    string oldStonePath = Path.Combine(backupPath, tempFilePathStr);
                                //    _logger.LogInformation(oldStonePath);

                                //    if (Directory.Exists(oldStonePath))
                                //    {
                                //        Directory.Delete(oldStonePath, true);
                                //    }
                                //}
                                // 删除旧版本文件的备份目录
                                foreach (var milestoneFile in oldVersions)
                                {
                                    try
                                    {
                                        // 获取文件保存路径
                                        string savePath = milestoneFile.SavePath;

                                        // 提取ProjectDirectory之后的路径部分
                                        string projectDirSegment = separator + "ProjectDirectory";
                                        int startIndex = savePath.IndexOf(projectDirSegment);

                                        if (startIndex >= 0)
                                        {
                                            // 提取相对路径
                                            startIndex += projectDirSegment.Length;
                                            int endIndex = savePath.LastIndexOf(separator);

                                            if (endIndex > startIndex)
                                            {
                                                string relativePath = savePath.Substring(startIndex, endIndex - startIndex);

                                                // 构建备份路径
                                                string backupDirPath = Path.Combine(backupRootPath, relativePath);

                                                // 记录日志
                                                _logger.LogInformation($"尝试删除旧版本目录: {backupDirPath}");

                                                // 如果目录存在则删除
                                                if (Directory.Exists(backupDirPath))
                                                {
                                                    Directory.Delete(backupDirPath, true);
                                                    _logger.LogInformation($"成功删除旧版本目录: {backupDirPath}");
                                                }
                                                else
                                                {
                                                    _logger.LogWarning($"目录不存在，跳过删除: {backupDirPath}");
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, $"删除旧版本目录时发生错误，文件: {milestoneFile.SavePath}");
                                        // 可以选择继续处理其他文件，或者根据需要抛出异常
                                    }
                                }
                            }
                        }




                    }
                    _logger.LogInformation("===================================结束备份模型数据库=========================");
                    //记录mainproject
                    string mpXML = backupRootPath + separator + "MainProjectInfo.xml";
                    BimBaseServerConfigurations.WriteCustomConfiguration(mainProject, Path.Combine(backupRootPath, "MainProjectInfo.xml"));
                    //记录mainproject下的teamprojectlist
                    string tpXML = backupRootPath + separator + "TeamProjectListInfo.xml";
                    BimBaseServerConfigurations.WriteCustomConfiguration(teamProjects, Path.Combine(backupRootPath, "TeamProjectListInfo.xml"));
                    //记录MainProject下文件夹信息
                    string dirXML = backupRootPath + separator + "FiledirectoryInfo.xml";
                    BimBaseServerConfigurations.WriteCustomConfiguration(fileDirectories, Path.Combine(backupRootPath, "FiledirectoryInfo.xml"));
                    string volXML = backupRootPath + separator + "VolumeInfo.xml";
                    BimBaseServerConfigurations.WriteCustomConfiguration(volumes, Path.Combine(backupRootPath, "VolumeInfo.xml"));
                    List<VolumeVersion> volumeVersions = new List<VolumeVersion>();
                    foreach (var volume in volumes)
                    {
                        var latestVersion = _teamRepository.VolumeVersions.Where(a => a.VolumeId == volume.VolumeId).OrderByDescending(a => a.VerNo).FirstOrDefault();
                        if (latestVersion != null)
                        {
                            volumeVersions.Add(latestVersion);


                            //var dir = latestVersion.SavePath.Substring(latestVersion.SavePath.IndexOf("UploadFileCommands"), latestVersion.SavePath.LastIndexOf(separator) - latestVersion.SavePath.IndexOf("UploadFileCommands") + 1);
                            //var filename = latestVersion.SavePath.Substring(latestVersion.SavePath.LastIndexOf(separator) + 1, latestVersion.SavePath.Length - latestVersion.SavePath.LastIndexOf(separator) - 1);
                            //_logger.LogInformation("dir==>" + dir + "======filename==>" + filename);
                            //var newVolVersionPath = Path.Combine(backupPath, dir);//volVer.SavePath.Replace(PBIMServerConfigurations.SystemConfig.UploadModelPath, backuppath);
                            //if (!Directory.Exists(dir))
                            //{
                            //    Directory.CreateDirectory(newVolVersionPath);
                            //}
                            //File.Copy(latestVersion.SavePath, newVolVersionPath + filename, true);

                            try
                            {
                                if (File.Exists(latestVersion.SavePath))
                                {
                                    // 提取文件路径信息
                                    string fileDirectory = Path.GetDirectoryName(latestVersion.SavePath);
                                    string fileName = Path.GetFileName(latestVersion.SavePath);

                                    // 构建新路径
                                    string relativePath = fileDirectory.Substring(fileDirectory.IndexOf("UploadFileCommands"));
                                    string targetDir = Path.Combine(backupRootPath, relativePath);
                                    string targetPath = Path.Combine(targetDir, fileName);

                                    // 创建目录并复制文件
                                    Directory.CreateDirectory(targetDir);
                                    File.Copy(latestVersion.SavePath, targetPath, true);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, $"备份卷册文件失败");
                            }

                        }

                    }
                    //string volVerXML = backupPath + separator + "VolumeVersionInfo.xml";
                    BimBaseServerConfigurations.WriteCustomConfiguration(volumeVersions, Path.Combine(backupRootPath, "VolumeVersionInfo.xml"));

                    x.Backuppath = backupRootPath;
                    x.BackupZipDirPath = backupZipDirPath;
                    x.ZipPath = zipPath;
                    x.IsSuccess = true;
                }
            }

            catch (Exception ex)
            {
                SafeDeleteDirectory(backupRootPath, true);
                SafeDeleteDirectory(backupZipDirPath, true);
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }

        /// <summary>
        /// 对目录进行备份并压缩成ZIP文件
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> ZipBackUpDirctory(ZipBackUpDirctoryRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            //压缩zip
            ZipHelper zip = new ZipHelper();
            zip.ZipFileFromDirectory(request.BackupPath, request.ZipPath, 4);
            //zip.ZipFileFromDirectoryWithEncryption(backuppath, zipPath, 4);

            //IISManager iis = new IISManager("WebServer");
            var virlogpath = request.BackupZipDirPath;

            //iis.CreateVDir("dbbackupzipfile", virlogpath, false, true, true, true, true, true, iis.WebSiteNum, iis.ServerName);
            var downpath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                downpath = _urls.LoadDataSavePath;
                if (Directory.Exists(virlogpath))
                {
                    CopyDirectoryWithRoot(virlogpath, downpath);
                }
            }
            SafeDeleteDirectory(request.BackupPath, true);
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 复制源目录及其所有内容到目标父目录下，会在目标父目录下创建与源目录同名的新目录
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="sourceDir"></param>
        /// <param name="parentDestinationDir"></param>
        /// <param name="recursive"></param>
        /// <exception cref="DirectoryNotFoundException"></exception>
        public void CopyDirectoryWithRoot(string sourceDir, string parentDestinationDir, bool recursive = true)
        {
            // 获取源文件夹信息 
            var dir = new DirectoryInfo(sourceDir);
            if (!dir.Exists)
                throw new DirectoryNotFoundException($"源文件夹不存在: {dir.FullName}");

            // 在目标父路径下创建同名文件夹 
            string destinationDir = Path.Combine(parentDestinationDir, dir.Name);
            Directory.CreateDirectory(destinationDir);

            // 复制所有文件 
            foreach (FileInfo file in dir.GetFiles())
            {
                string targetFilePath = Path.Combine(destinationDir, file.Name);
                file.CopyTo(targetFilePath, true); // 覆盖已存在的文件 
            }

            // 递归复制子文件夹 
            if (recursive)
            {
                foreach (DirectoryInfo subDir in dir.GetDirectories())
                {
                    CopyDirectoryWithRoot(subDir.FullName, destinationDir, recursive);
                }
            }
        }

        /// <summary>
        /// 导入项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        //public override async Task<RestoreDBByMainProjectIdResponse> RestoreDBByMainProjectId(RestoreDBByMainProjectIdRequest request, ServerCallContext context)
        //{


        //    var x = new RestoreDBByMainProjectIdResponse();
        //    if (string.IsNullOrWhiteSpace(request.SessionId))
        //    {
        //        x.Message = $"参数 SessionId 有误！";
        //        return x;
        //    }
        //    var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
        //    if (currentUser is null)
        //    {
        //        x.Message = "用户登录已失效，请保存退出并重新登录服务器";
        //        return x;
        //    }

        //    string oldFileName = request.OldFileName;
        //    //  var mainProjectGuid = Guid.Empty;
        //    string username = currentUser.LoginName;
        //    string UploadModelPath = "";
        //    var unzippathGuid = Guid.NewGuid().ToString();
        //    char separator = Path.DirectorySeparatorChar;
        //    var mysqlpath = "";
        //    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        //    {
        //        UploadModelPath =_urls.UploadRootPath;
        //        mysqlpath = UtilityHelper.getmysqlpath();
        //    }
        //    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //    {
        //        UploadModelPath = Environment.GetEnvironmentVariable("MY_DOCUMENTS");
        //    }

        //    try
        //    {
        //        string batPath = GetAssemblyDirectory();

        //        DirectoryInfo path_exe = new DirectoryInfo(batPath);
        //        String path = path_exe.Parent.FullName;
        //        string unzipPath = Path.Combine(path, "unzip" + unzippathGuid);
        //        if (Directory.Exists(unzipPath))
        //        {
        //            Directory.Delete(unzipPath, true);
        //        }
        //        if (!Directory.Exists(unzipPath))
        //        {
        //            Directory.CreateDirectory(unzipPath);
        //        }
        //        string newCustomMainprojectName = "";
        //        if (!string.IsNullOrEmpty(oldFileName))
        //        {
        //            newCustomMainprojectName = oldFileName.Substring(0, oldFileName.LastIndexOf("."));
        //        }
        //        //string zippath = @"D:\work_new\PBIMServer\PBIMWebServer-dev-role\ProjectManager\\upload\\mainprojectbackup\7cbe4e04-3a7e-4153-ac2b-9c461ed41c96协同培训演示.zip";
        //        ZipHelper zipHelper = new ZipHelper();
        //        //zipHelper.UnZip(zippath, unzipPath);
        //        zipHelper.UnZip(request.ZipPath, unzipPath);
        //        x.UnZipPath = unzipPath;


        //        if (!File.Exists(unzipPath + separator + "MainProjectInfo.xml"))
        //        {
        //            _logger.LogInformation("解压后未找到所需文件");
        //            x.IsSuccess = false;
        //            return x;
        //        }
        //        Dictionary<string, string> dicVolGuid = new Dictionary<string, string>();
        //        MainProject reMainProject = BimBaseServerConfigurations.ReadCustomConfiguration<MainProject>(unzipPath + separator + "MainProjectInfo.xml");
        //        Dictionary<Guid, Guid> dicMainGuid = new Dictionary<Guid, Guid>();
        //        MainProject main = new MainProject
        //        {
        //            ID = Guid.NewGuid(),
        //            Name = string.IsNullOrEmpty(newCustomMainprojectName) ? reMainProject.Name + "_导入_" + DateTime.Now.ToString("yyyyMMddHHmmss") : newCustomMainprojectName + "_导入_" + DateTime.Now.ToString("yyyyMMddHHmmss"),
        //            Description = reMainProject.Description,
        //            CreationTime = DateTime.Now,
        //            CreateUser = username,//reMainProject.CreateUser,
        //            RoleGroupId = reMainProject.RoleGroupId,
        //            RoleString = reMainProject.RoleString,
        //            LastUpdateTime = DateTime.Now,
        //            ExtendStr = reMainProject.ExtendStr,
        //            MainProjectType = reMainProject.MainProjectType,
        //            IsDelete = reMainProject.IsDelete
        //        };

        //        using (var scope = new TransactionScope(
        //                TransactionScopeOption.Required,
        //                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
        //                TransactionScopeAsyncFlowOption.Enabled))
        //        {
        //            var prj = _teamRepository.AddMainProject(main);
        //            if (prj != null)
        //            {

        //                List<TeamMember> memberTmp = _teamRepository.Members.ToList();
        //                TeamMember member = memberTmp.FirstOrDefault(mem => mem.LoginName == username);
        //                MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == prj.ID);
        //                _teamRepository.AddMemberToMainProject(member, mp);

        //                member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
        //                _teamRepository.AddMemberToMainProject(member, mp);

        //                member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
        //                _teamRepository.AddMemberToMainProject(member, mp);


        //                scope.Complete();

        //                x.MainProjectName = mp.Name;
        //                x.MainprojectGuid = mp.ID.ToString();


        //            }
        //        }


        //        dicMainGuid.Add(reMainProject.ID, main.ID);

        //        x.OldMainProjectGuid = reMainProject.ID.ToString();
        //        //记录filedirectoryguid
        //        Dictionary<Guid, Guid> dicFiledirGuid = new Dictionary<Guid, Guid>();
        //        List<FileDirectory> listFiledir = BimBaseServerConfigurations.ReadCustomConfiguration<List<FileDirectory>>(unzipPath + separator + "FiledirectoryInfo.xml");
        //        List<FileDirectory> newFileDirList = new List<FileDirectory>();
        //        foreach (var fd in listFiledir)
        //        {
        //            if (!dicFiledirGuid.ContainsKey(fd.ID))
        //            {
        //                dicFiledirGuid.Add(fd.ID, Guid.NewGuid());
        //            }

        //        }

        //        foreach (var fd in listFiledir)
        //        {
        //            Guid temp = Guid.Empty; //文件夹ID更新
        //            Guid tmpP = Guid.Empty; //文件夹parentid更新
        //            dicFiledirGuid.TryGetValue(fd.ID, out temp);
        //            if (fd.ParentID != Guid.Empty)
        //            {
        //                dicFiledirGuid.TryGetValue(fd.ParentID, out tmpP);
        //            }
        //            FileDirectory f = new FileDirectory
        //            {
        //                ID = temp,
        //                Name = fd.Name,
        //                MainProjectId = main.ID,
        //                ParentID = tmpP,
        //                CreateUser = fd.CreateUser,
        //                RoleGroupId = fd.RoleGroupId,
        //                RoleString = fd.RoleString,
        //                OrderNo = fd.OrderNo,
        //                Type = fd.Type
        //            };
        //            newFileDirList.Add(f);

        //            //if (!dicVolGuid.ContainsKey(fd.ID))
        //            //{
        //            //    dicVolGuid.Add(fd.ID, temp);
        //            //}
        //        }
        //        _teamRepository.AddFileDirectoreyList(newFileDirList);
        //        //还原volume和volumeversion信息
        //        List<Volume> oldVolList = BimBaseServerConfigurations.ReadCustomConfiguration<List<Volume>>(unzipPath + separator + "VolumeInfo.xml");

        //        List<Volume> newVolList = new List<Volume>();
        //        foreach (var v in oldVolList)
        //        {
        //            Guid newVolumeid = Guid.NewGuid();
        //            Guid volfiledirid = Guid.Empty;
        //            if (v.FileDirectoryId != Guid.Empty)
        //            {
        //                dicFiledirGuid.TryGetValue(v.FileDirectoryId, out volfiledirid);
        //            }
        //            Volume vl = new Volume
        //            {
        //                VolumeId = newVolumeid,
        //                VolumeName = v.VolumeName,
        //                CreateTime = v.CreateTime,
        //                CreateUser = username,
        //                ExtendStr = v.ExtendStr,
        //                FileDirectoryId = volfiledirid,
        //                MainProjectId = main.ID

        //            };
        //            newVolList.Add(vl);
        //            if (!dicVolGuid.ContainsKey(v.VolumeId.ToString()))
        //            {
        //                dicVolGuid.Add(v.VolumeId.ToString(), newVolumeid.ToString());
        //            }



        //            var newVolPath = UploadModelPath + separator + "UploadFileCommands" + separator + vl.VolumeId;
        //            string currentVolumePath = unzipPath + separator + "UploadFileCommands" + separator + v.VolumeId;

        //            if (Directory.Exists(currentVolumePath))
        //            {
        //                //复制文件夹
        //                UtilityHelper.CopyEntireDir(currentVolumePath, newVolPath);
        //            }

        //        }
        //        _teamRepository.AddVolumeList(newVolList);


        //        List<VolumeVersion> oldVolVerList = BimBaseServerConfigurations.ReadCustomConfiguration<List<VolumeVersion>>(unzipPath + separator + "VolumeVersionInfo.xml");
        //        List<VolumeVersion> newVolVerList = new List<VolumeVersion>();
        //        foreach (var vv in oldVolVerList)
        //        {
        //            string volid = Guid.Empty.ToString();
        //            dicVolGuid.TryGetValue(vv.VolumeId.ToString(), out volid);
        //            string newSavePath = "";
        //            string str = vv.SavePath;
        //            string subString = vv.VolumeId.ToString();
        //            int index = str.IndexOf(subString);
        //            if (index != -1)
        //            {
        //                string result = str.Substring(index + subString.Length);
        //                var debugpath = UploadModelPath;
        //                //var debugpath = "E:\\debugServerPath";
        //                newSavePath = debugpath + separator + "UploadFileCommands" + separator + volid + result;
        //            }
        //            VolumeVersion newVV = new VolumeVersion
        //            {
        //                VolumeId = Guid.Parse(volid),
        //                SavePath = newSavePath,
        //                Description = vv.Description,
        //                ExtendStr = vv.ExtendStr,
        //                FileName = vv.FileName,
        //                FileSize = vv.FileSize,
        //                FileType = vv.FileType,
        //                UploadTime = vv.UploadTime,
        //                UploadUser = username,
        //                VerNo = vv.VerNo
        //            };
        //            newVolVerList.Add(newVV);
        //        }
        //        _teamRepository.AddVolumeVersionList(newVolVerList);
        //        //处理teamproject
        //        Dictionary<string, string> dicTeamProjectGuid = new Dictionary<string, string>();
        //        List<TeamProject> listTP = BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamProject>>(unzipPath + separator + "TeamProjectListInfo.xml");


        //        foreach (var tp in listTP)
        //        {
        //            Guid fdirid = Guid.Empty;
        //            if (!string.IsNullOrEmpty(tp.FileDirectoryID) && tp.FileDirectoryID != Guid.Empty.ToString())
        //            {
        //                dicFiledirGuid.TryGetValue(Guid.Parse(tp.FileDirectoryID), out fdirid);
        //            }
        //            TeamProject newTp = new TeamProject
        //            {
        //                Name = tp.Name,
        //                Avatar = tp.Avatar,
        //                CreateUser = username,
        //                CreationTime = tp.CreationTime,
        //                Description = tp.Description,
        //                EnableAuthority = tp.EnableAuthority,
        //                EndTime = tp.EndTime,
        //                FileDirectoryID = fdirid.ToString(),
        //                FilePath = tp.FilePath,
        //                Leader = username,
        //                MainProjectID = main.ID.ToString(),
        //                ParentProjectID = tp.ParentProjectID,
        //                Progress = tp.Progress,
        //                ProjectType = tp.ProjectType,
        //                RoleGroupId = tp.RoleGroupId,
        //                RoleString = tp.RoleString,
        //                StartTime = tp.StartTime
        //            };
        //            var prj = _teamRepository.AddProject(newTp);



        //            if (prj != null)
        //            {
        //                if (!dicTeamProjectGuid.ContainsKey(tp.ID.ToString()))
        //                {
        //                    dicTeamProjectGuid.Add(tp.ID.ToString(), prj.ID.ToString());
        //                }


        //                var conf = UtilityHelper.GetMysqlConnectionConfig();
        //                var databaseIp = conf.IP;
        //                var databasePort = conf.Port;
        //                var dbusername = conf.User;
        //                var dbpassword = conf.Password;

        //                var createmodeldb = _teamRepository.CreateModeldataBase(prj.ID);
        //                if (!createmodeldb)
        //                {
        //                    _logger.LogInformation("创建PKPM-PBIMServer-ModelDB-" + prj.ID + "数据库失败");
        //                    x.IsSuccess = false;
        //                    return x;
        //                }
        //                var modelDBName = "pkpm-pbimserver-modeldb-" + prj.ID;
        //                var oldmdb = "pkpm-pbimserver-modeldb-" + tp.ID;
        //                //string codetype = "chcp 65001";
        //                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //                {
        //                    string modelsqlpath = unzipPath + separator + oldmdb + ".sql";
        //                    var modelcommand = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + " < " + modelsqlpath + "";
        //                    _logger.LogInformation("modelcommand:====>" + modelcommand);

        //                    string modelscriptPath = unzipPath + separator + "modeltemp" + unzippathGuid + ".sh";

        //                    ExcuteBash(modelcommand, modelscriptPath);
        //                }
        //                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        //                {
        //                    string codetype = "chcp 65001";
        //                    string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
        //                            + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + modelDBName + "<" + "\"" + unzipPath + "\\"
        //                            + oldmdb + ".sql" + "\"";
        //                    string command = codetype + "\r\n" + cmdStr;
        //                    string batpath = batPath + "modeltemp" + unzippathGuid + ".bat";
        //                    ExcuteBat(command, batpath);
        //                }




        //                _teamRepository.ClearLockedComponents(prj.ID);
        //                _teamRepository.ClearLockModelfiles(prj.ID);
        //                var newPath = UploadModelPath + separator + "UploadFileCommands" + separator + prj.ID;
        //                var newPathStr = newPath.Replace(@"\", @"\\");
        //                _teamRepository.UpdateModelFileInfo(tp.ID, prj.ID, newPathStr);

        //                string currentProjectPath = unzipPath + separator + "UploadFileCommands" + separator + tp.ID;
        //                if (Directory.Exists(currentProjectPath))
        //                {
        //                    //复制文件夹
        //                    UtilityHelper.CopyEntireDir(currentProjectPath, newPath);
        //                }
        //                var newWebPath = UploadModelPath + separator + "ProjectDirectory" + separator + main.Name;

        //                var newWebPathStr = newWebPath.Replace(@"\", @"\\");

        //                _teamRepository.UpdateMileStoneFileInfo(tp.ID, reMainProject.Name, prj.ID, newWebPathStr);

        //                string currentMileStonePath = unzipPath + separator + "ProjectDirectory" + separator + reMainProject.Name;
        //                Console.WriteLine("currentMileStonePath===============>" + currentMileStonePath);
        //                if (Directory.Exists(currentMileStonePath))
        //                {
        //                    Console.WriteLine("ProjectDirectory===============>" + currentMileStonePath);
        //                    UtilityHelper.CopyEntireDir(currentMileStonePath, newWebPath);
        //                }

        //            }

        //        }


        //        x.DicProjectGuid.Add(dicTeamProjectGuid);
        //        x.DicVolumeGuid.Add(dicVolGuid);
        //        x.IsSuccess = true;
        //    }
        //    catch (Exception ex)
        //    {
        //        x.Message = ex.Message;
        //        x.IsSuccess = false;
        //        return x;
        //    }
        //    return x;
        //}

        /// <summary>
        /// 移动模型到新的目录(变电)
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> RemoveProjectToNewDir(RemoveProjectToNewDirRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            var newDirId = Guid.Empty;
            if (!Guid.TryParse(request.NewDirId, out newDirId))
            {
                x.Message = $"参数{request.NewDirId}格式有误！";
                return x;
            }
            var prjGuidList = _mapper.Map<List<Guid>>(request.PrjGuidList);
            try
            {
                var ret = _teamRepository.UpdataProjectFileDirId(mainprojectGuid, prjGuidList, newDirId);
                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }

        /// <summary>
        /// 移动卷册图档到新的目录(变电)
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> RemoveVolumeToNewDir(RemoveVolumeToNewDirRequest request, ServerCallContext context)
        {

            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            var newDirId = Guid.Empty;
            if (!Guid.TryParse(request.NewDirId, out newDirId))
            {
                x.Message = $"参数{request.NewDirId}格式有误！";
                return x;
            }
            var volGuidList = _mapper.Map<List<Guid>>(request.VolGuidList);
            try
            {
                var ret = _teamRepository.UpdateVolumeFileDirId(mainprojectGuid, volGuidList, newDirId);
                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }

        /// <summary>
        /// 将主项目加入回收站，不删除数据库数据
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> ReplyMainProjectFromRecycle(ReplyMainProjectFromRecycleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            //var sw = new Stopwatch();
            //sw.Start();
            try
            {
                var mainproject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprojectGuid);
                mainproject.IsDelete = 0;
                var ret = _teamRepository.UpdateMainprojectDeleteState(mainproject);

                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }

        /// <summary>
        /// 将主项目加入回收站，不删除数据库数据
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> DeleteMainProjectToRecycle(DeleteMainProjectToRecycleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            //var sw = new Stopwatch();
            //sw.Start();
            try
            {
                var mainproject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprojectGuid);
                mainproject.IsDelete = 1;
                var ret = _teamRepository.UpdateMainprojectDeleteState(mainproject);

                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }

        #region  复制项目优化前
        ///// <summary>
        ///// 复制项目（优化前）
        ///// </summary>
        ///// <remarks>WEB</remarks>
        ///// <param name="request"></param>
        ///// <param name="context"></param>
        ///// <returns></returns>
        //public override async Task<CopyMainprojectResponse> CopyMainproject(CopyMainprojectRequest request, ServerCallContext context)
        //{
        //    var x = new CopyMainprojectResponse();
        //    if (string.IsNullOrWhiteSpace(request.SessionId))
        //    {
        //        x.Message = $"参数 SessionId 有误！";
        //        return x;
        //    }
        //    var mainprojectGuid = Guid.Empty;
        //    if (!Guid.TryParse(request.MainProjectGuid, out mainprojectGuid))
        //    {
        //        x.Message = $"参数{request.MainProjectGuid}格式有误！";
        //        return x;
        //    }
        //    var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
        //    if (currentUser is null)
        //    {
        //        x.Message = "用户登录已失效，请保存退出并重新登录服务器";
        //        return x;
        //    }
        //    string UploadModelPath = "";
        //    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        //    {
        //        UploadModelPath = _urls.UploadRootPath;
        //    }
        //    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //    {
        //        UploadModelPath = Environment.GetEnvironmentVariable("MY_DOCUMENTS");
        //    }
        //    string username = currentUser.LoginName;
        //    try
        //    {
        //        //获取mainproject下的所有teamproject
        //        //var tpList = teamManager.Projects.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower())).ToList();
        //        var mainproject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainprojectGuid);
        //        var tpList = _teamRepository.Projects.Where(t => request.GuidList.Contains(t.ID.ToString()) && (!string.IsNullOrEmpty(t.MainProjectID)) && (t.MainProjectID.ToLower() == mainprojectGuid.ToString().ToLower())).ToList();
        //        var filedirList = _teamRepository.FileDirectories.Where(f => request.GuidList.Contains(f.ID.ToString()) && f.MainProjectId == mainprojectGuid).ToList();//(t => (!string.IsNullOrEmpty(t.MainProjectId)) && (t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower())).ToList();
        //        var volumeList = _teamRepository.Volumes.Where(v => request.GuidList.Contains(v.VolumeId.ToString()) && v.MainProjectId == mainprojectGuid).ToList();
        //        var tpIDList = tpList.Select(t => t.ID).ToList();
        //        string modelDBName = "";

        //        //备份数据库
        //        var conf = UtilityHelper.GetMysqlConnectionConfig();
        //        var databaseIp = conf.IP;
        //        var databasePort = conf.Port;
        //        var dbusername = conf.User;
        //        var dbpassword = conf.Password;
        //        string batPath = GetAssemblyDirectory();
        //        DirectoryInfo path_exe = new DirectoryInfo(batPath);

        //        String path = path_exe.Parent.FullName;
        //        string backuppath = Path.Combine(path, "CopyMainProject");
        //        if (!Directory.Exists(backuppath))
        //        {
        //            Directory.CreateDirectory(backuppath);
        //        }

        //        //里程碑及云链接文件夹复制
        //        string mainprojectDirPath = UploadModelPath + separator + "ProjectDirectory" + separator + mainproject.Name;
        //        string webnumberDirPath = UploadModelPath + separator + "ProjectDirectory" + separator + mainproject.Name;
        //        //复制文件夹
        //        string newmpPath = Path.Combine(backuppath, "ProjectDirectory", mainproject.Name);
        //        if (Directory.Exists(mainprojectDirPath))
        //        {
        //            UtilityHelper.CopyEntireDir(mainprojectDirPath, newmpPath);
        //        }
        //        if (Directory.Exists(webnumberDirPath))
        //        {
        //            UtilityHelper.CopyEntireDir(webnumberDirPath, newmpPath);
        //        }

        //        _logger.LogInformation("===================================开始备份模型数据库=========================");
        //        var backzippathGuid = Guid.NewGuid();
        //        string codetype = "chcp 65001";
        //        //备份模型数据库
        //        foreach (var tp in tpIDList)
        //        {
        //            modelDBName = "pkpm-pbimserver-modeldb-" + tp;
        //            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //            {
        //                //var sqlpath = unzipPath + separator + oldmainprojectDBName + ".sql";
        //                var command = "mysqldump -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + ">" + "\"" + backuppath + separator
        //                        + modelDBName + ".sql" + "\"";


        //                _logger.LogInformation("command命令行============》" + command);
        //                string scriptPath = batPath + "tpbacktemp" + backzippathGuid + ".sh";  // 替换为实际路径
        //                ExcuteBash(command, scriptPath);
        //            }
        //            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        //            {
        //                string mainprojectcmdstr = "\"" + UtilityHelper.getmysqlpath() + "\\mysqldump.exe\" -h " + databaseIp + " -P "
        //                        + databasePort + " -u" + dbusername + " -p" + dbpassword + "  " + modelDBName + ">" + "\"" + backuppath + "\\"
        //                        + modelDBName + ".sql" + "\"";
        //                _logger.LogInformation("mainprojectcmdstr:" + mainprojectcmdstr);
        //                string command = codetype + "\r\n" + mainprojectcmdstr;
        //                string batpath = batPath + "tpbacktemp" + backzippathGuid + ".bat";
        //                ExcuteBat(command, batpath);
        //            }

        //            string currentProjectPath = UploadModelPath + separator + "UploadFileCommands" + separator + tp;
        //            //复制文件夹
        //            string newpath = Path.Combine(backuppath, "UploadFileCommands", tp.ToString());

        //            //modelfiles初始版本中的路径
        //            var tempModelManager = _teamRepository.GetProjectRepository(tp);
        //            var modelfiles = tempModelManager.ModelFiles.ToList();
        //            if (modelfiles.Any())
        //            {
        //                var filenames = modelfiles.Select(m => m.FileName).Distinct().ToList();
        //                foreach (var fn in filenames)
        //                {
        //                    var tempfile = modelfiles.Where(n => n.FileName == fn).Max(f => f.VersionNo);
        //                    var curFile = tempModelManager.ModelFiles.FirstOrDefault(f => f.VersionNo == tempfile && f.FileName == fn);
        //                    var tempfilepath = curFile.FilePath;
        //                    var tempfilepathStr = tempfilepath.Substring(0, tempfilepath.IndexOf(separator + "UploadFileCommands"));
        //                    string tmpcurPrjPath = tempfilepathStr + separator + "UploadFileCommands" + separator + tp;

        //                    if (File.Exists(tempfilepath))
        //                    {
        //                        var newCopyFilePath = tempfilepath.Replace(tmpcurPrjPath, newpath);
        //                        var oldCopyDir = tempfilepath.Substring(0, tempfilepath.LastIndexOf(separator));
        //                        var newCopyDir = newCopyFilePath.Substring(0, newCopyFilePath.LastIndexOf(separator));
        //                        if (!Directory.Exists(newCopyDir))
        //                        {
        //                            Directory.CreateDirectory(newCopyDir);
        //                        }
        //                        File.Copy(tempfilepath, tempfilepath.Replace(oldCopyDir, newCopyDir), true);
        //                    }
        //                }

        //            }

        //            //获取非最新版本里程碑文件版本，排除
        //            var stonemilesfiles = tempModelManager.MilestoneFiles.ToList();
        //            if (stonemilesfiles.Any())
        //            {
        //                if (stonemilesfiles.Count > 1)
        //                {
        //                    var maxVer = stonemilesfiles.Max(s => s.VersionNo);
        //                    var oldStones = stonemilesfiles.Where(s => s.VersionNo < maxVer).ToList();
        //                    foreach (var sf in oldStones)
        //                    {
        //                        var tempfilepath = sf.SavePath;
        //                        var tempfilepathStr = tempfilepath.Substring(tempfilepath.IndexOf(separator + "ProjectDirectory") + 1, tempfilepath.LastIndexOf(separator) - tempfilepath.IndexOf(separator + "ProjectDirectory") - 1);
        //                        string oldstonepath = Path.Combine(backuppath, tempfilepathStr);
        //                        _logger.LogInformation(oldstonepath);
        //                        if (Directory.Exists(oldstonepath))
        //                        {
        //                            Directory.Delete(oldstonepath, true);
        //                        }
        //                    }
        //                }
        //            }

        //        }
        //        _logger.LogInformation("===================================结束备份模型数据库=========================");
        //        //记录mainproject下的teamprojectlist
        //        //tpList = UtilityHelper.CopyToObject(tpList);
        //        //记录MainProject下文件夹信息
        //        //filedirList = UtilityHelper.CopyToObject(filedirList);
        //        //记录volums和volumeVersion信息
        //        //volumeList = UtilityHelper.CopyToObject(volumeList);
        //        List<VolumeVersion> volVerList = new List<VolumeVersion>();
        //        foreach (var v in volumeList)
        //        {
        //            var volVer = _teamRepository.VolumeVersions.Where(a => a.VolumeId == v.VolumeId).OrderByDescending(a => a.VerNo).FirstOrDefault();
        //            if (volVer != null)
        //            {
        //                volVerList.Add(volVer);
        //                var dir = volVer.SavePath.Substring(volVer.SavePath.IndexOf("UploadFileCommands"), volVer.SavePath.LastIndexOf(@"\") - volVer.SavePath.IndexOf("UploadFileCommands") + 1);
        //                var filename = volVer.SavePath.Substring(volVer.SavePath.LastIndexOf(@"\") + 1, volVer.SavePath.Length - volVer.SavePath.LastIndexOf(@"\") - 1);
        //                _logger.LogInformation("dir==>" + dir + "======filename==>" + filename);
        //                var newVolVersionPath = Path.Combine(backuppath, dir);//volVer.SavePath.Replace(PBIMServerConfigurations.SystemConfig.UploadModelPath, backuppath);
        //                if (!Directory.Exists(dir))
        //                {
        //                    Directory.CreateDirectory(newVolVersionPath);
        //                }
        //                File.Copy(volVer.SavePath, newVolVersionPath + filename, true);
        //            }

        //        }
        //        //volVerList = UtilityHelper.CopyToObject(volVerList);


        //        //开始还原

        //        Dictionary<Guid, Guid> dicVolGuid = new Dictionary<Guid, Guid>();
        //        //MainProjects reMainProject = PBIMServerConfigurations.ReadCustomConfiguration<MainProjects>(unzipPath + @"\MainProjectInfo.xml");
        //        Dictionary<Guid, Guid> dicMainGuid = new Dictionary<Guid, Guid>();
        //        MainProject main = new MainProject
        //        {
        //            ID = Guid.NewGuid(),
        //            Name = string.IsNullOrEmpty(request.NewCustomMainprojectName) ? mainproject.Name + "_copy_" + DateTime.Now.ToString("yyyyMMddHHmmss") : request.NewCustomMainprojectName,

        //            Description = mainproject.Description,
        //            CreationTime = DateTime.Now,
        //            CreateUser = username,//reMainProject.CreateUser,
        //            RoleGroupId = mainproject.RoleGroupId,
        //            RoleString = mainproject.RoleString,
        //            LastUpdateTime = DateTime.Now,
        //            ExtendStr = mainproject.ExtendStr,
        //            MainProjectType = mainproject.MainProjectType,
        //            DesignStage = mainproject.DesignStage,
        //            EngineerType = mainproject.EngineerType
        //        };
        //        using (var scope = new TransactionScope(
        //                TransactionScopeOption.Required,
        //                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
        //                TransactionScopeAsyncFlowOption.Enabled))
        //        {
        //            var prj = _teamRepository.AddMainProject(main);
        //            if (prj != null)
        //            {
        //                //transaction.Commit();


        //                List<TeamMember> memberTmp = _teamRepository.Members.ToList();
        //                TeamMember member = memberTmp.FirstOrDefault(mem => mem.LoginName == username);
        //                MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == prj.ID);
        //                _teamRepository.AddMemberToMainProject(member, mp);

        //                member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
        //                _teamRepository.AddMemberToMainProject(member, mp);

        //                member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
        //                _teamRepository.AddMemberToMainProject(member, mp);

        //                x.MainProjectGuid = mp.ID.ToString();
        //                x.MainProjectName = mp.Name;

        //                //outMainprojectGuid = mp.ID.ToString();
        //                //outMainProjectName = mp.Name;
        //            }
        //            scope.Complete();
        //        }

        //        dicMainGuid.Add(mainproject.ID, main.ID);

        //        //记录filedirectoryguid
        //        Dictionary<Guid, Guid> dicFiledirGuid = new Dictionary<Guid, Guid>();
        //        List<FileDirectory> newFileDirList = new List<FileDirectory>();
        //        foreach (var fd in filedirList)
        //        {
        //            if (!dicFiledirGuid.ContainsKey(fd.ID))
        //            {
        //                dicFiledirGuid.Add(fd.ID, Guid.NewGuid());
        //            }

        //        }

        //        foreach (var fd in filedirList)
        //        {
        //            Guid temp = Guid.Empty; //文件夹ID更新
        //            Guid tmpP = Guid.Empty; //文件夹parentid更新
        //            dicFiledirGuid.TryGetValue(fd.ID, out temp);
        //            if (fd.ParentID != Guid.Empty)
        //            {
        //                dicFiledirGuid.TryGetValue(fd.ParentID, out tmpP);
        //            }
        //            FileDirectory f = new FileDirectory
        //            {
        //                ID = temp,
        //                Name = fd.Name,
        //                MainProjectId = main.ID,
        //                ParentID = tmpP,
        //                CreateUser = fd.CreateUser,
        //                RoleGroupId = fd.RoleGroupId,
        //                RoleString = fd.RoleString,
        //                OrderNo = fd.OrderNo,
        //                Type = fd.Type
        //            };
        //            newFileDirList.Add(f);

        //        }
        //        _teamRepository.AddFileDirectoreyList(newFileDirList);

        //        //还原volume和volumeversion信息
        //        List<Volume> newVolList = new List<Volume>();
        //        foreach (var v in volumeList)
        //        {
        //            Guid newVolumeid = Guid.NewGuid();
        //            Guid volfiledirid = Guid.Empty;
        //            if (v.FileDirectoryId != Guid.Empty)
        //            {
        //                dicFiledirGuid.TryGetValue(v.FileDirectoryId, out volfiledirid);
        //            }
        //            Volume vl = new Volume
        //            {
        //                VolumeId = newVolumeid,
        //                VolumeName = v.VolumeName,
        //                CreateTime = v.CreateTime,
        //                CreateUser = username,
        //                ExtendStr = v.ExtendStr,
        //                FileDirectoryId = volfiledirid,
        //                MainProjectId = main.ID

        //            };
        //            newVolList.Add(vl);
        //            if (!dicVolGuid.ContainsKey(v.VolumeId))
        //            {
        //                dicVolGuid.Add(v.VolumeId, newVolumeid);
        //            }
        //            if (!x.ChangeGuid.ContainsKey(v.VolumeId.ToString()))
        //            {
        //                x.ChangeGuid.Add(v.VolumeId.ToString(), newVolumeid.ToString());
        //            }
        //            var newVolPath = UploadModelPath + separator + "UploadFileCommands" + separator + vl.VolumeId;
        //            string currentVolumePath = backuppath + separator + "UploadFileCommands" + separator + v.VolumeId;


        //            if (Directory.Exists(currentVolumePath))
        //            {
        //                //复制文件夹
        //                UtilityHelper.CopyEntireDir(currentVolumePath, newVolPath);
        //            }

        //        }
        //        _teamRepository.AddVolumeList(newVolList);
        //        //处理VolumeVersion
        //        List<VolumeVersion> newVolVerList = new List<VolumeVersion>();
        //        foreach (var vv in volVerList)
        //        {
        //            Guid volid = Guid.Empty;
        //            dicVolGuid.TryGetValue(vv.VolumeId, out volid);
        //            string newSavePath = "";
        //            string str = vv.SavePath;
        //            string subString = vv.VolumeId.ToString();
        //            int index = str.IndexOf(subString);
        //            if (index != -1)
        //            {
        //                string result = str.Substring(index + subString.Length);
        //                var debugpath = UploadModelPath;
        //                newSavePath = debugpath + separator + "UploadFileCommands" + separator + volid + result;
        //            }
        //            VolumeVersion newVV = new VolumeVersion
        //            {
        //                VolumeId = volid,
        //                SavePath = newSavePath,
        //                Description = vv.Description,
        //                ExtendStr = vv.ExtendStr,
        //                FileName = vv.FileName,
        //                FileSize = vv.FileSize,
        //                FileType = vv.FileType,
        //                UploadTime = vv.UploadTime,
        //                UploadUser = username,
        //                VerNo = vv.VerNo
        //            };
        //            newVolVerList.Add(newVV);
        //        }
        //        _teamRepository.AddVolumeVersionList(newVolVerList);

        //        //处理teamproject
        //        Dictionary<Guid, Guid> dicTeamProjectGuid = new Dictionary<Guid, Guid>();
        //        foreach (var tp in tpList)
        //        {
        //            var oldDBName = "pkpm-pbimserver-modeldb-" + tp.ID;
        //            var oldsqlpath = backuppath + "\\" + oldDBName + ".sql";
        //            if (File.Exists(oldsqlpath))
        //            {
        //                FileInfo fileInfo = new FileInfo(oldsqlpath);
        //                long fileSize = fileInfo.Length; // 获取文件大小（字节数）
        //                if (fileSize < 4 * 1024)
        //                {
        //                    _logger.LogInformation(oldDBName + "文件小于4KB,判定为异常模型,无需还原 ");
        //                    continue;
        //                }
        //            }
        //            Guid fdirid = Guid.Empty;
        //            if (!string.IsNullOrEmpty(tp.FileDirectoryID) && tp.FileDirectoryID != Guid.Empty.ToString())
        //            {
        //                dicFiledirGuid.TryGetValue(Guid.Parse(tp.FileDirectoryID), out fdirid);
        //            }
        //            TeamProject newTp = new TeamProject
        //            {
        //                Name = tp.Name,
        //                Avatar = tp.Avatar,
        //                CreateUser = username,
        //                CreationTime = tp.CreationTime,
        //                Description = tp.Description,
        //                EnableAuthority = tp.EnableAuthority,
        //                EndTime = tp.EndTime,
        //                FileDirectoryID = fdirid.ToString(),
        //                FilePath = tp.FilePath,
        //                Leader = username,
        //                MainProjectID = main.ID.ToString(),
        //                ParentProjectID = tp.ParentProjectID,
        //                Progress = tp.Progress,
        //                ProjectType = tp.ProjectType,
        //                RoleGroupId = tp.RoleGroupId,
        //                RoleString = tp.RoleString,
        //                StartTime = tp.StartTime
        //            };
        //            var prj = _teamRepository.AddProject(newTp);
        //            if (prj != null)
        //            {
        //                if (!dicTeamProjectGuid.ContainsKey(tp.ID))
        //                {
        //                    dicTeamProjectGuid.Add(tp.ID, prj.ID);
        //                }
        //                //if (!outChangeGuid.ContainsKey(tp.ID))
        //                //{
        //                //    outChangeGuid.Add(tp.ID, prj.ID);
        //                //}
        //                if (!x.ChangeGuid.ContainsKey(tp.ID.ToString()))
        //                {
        //                    x.ChangeGuid.Add(tp.ID.ToString(), prj.ID.ToString());
        //                }


        //                var createmodeldb = _teamRepository.CreateModeldataBase(prj.ID);
        //                if (!createmodeldb)
        //                {
        //                    _logger.LogInformation("创建PKPM-PBIMServer-ModelDB-" + prj.ID + "数据库失败");
        //                    x.IsSuccess = false;
        //                    return x;
        //                }
        //                var copymodelDBName = "pkpm-pbimserver-modeldb-" + prj.ID;
        //                var oldmdb = "pkpm-pbimserver-modeldb-" + tp.ID;
        //                //string codetype = "chcp 65001";
        //                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        //                {
        //                    // string modelsqlpath = unzipPath + separator + oldmdb + ".sql";

        //                    var command = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + copymodelDBName + " < " + "\"" + backuppath + separator
        //+ oldDBName + ".sql" + "\"";
        //                    // var modelcommand = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + " < " + modelsqlpath + "";

        //                    //string modelscriptPath = unzipPath + separator + "modeltemp" + unzippathGuid + ".sh";
        //                    //ExcuteBash(modelcommand, modelscriptPath);
        //                    _logger.LogInformation("command命令行============》" + command);
        //                    string scriptPath = batPath + "tpbacktemp" + backzippathGuid + ".sh";  // 替换为实际路径
        //                    ExcuteBash(command, scriptPath);
        //                }
        //                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        //                {
        //                    //string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
        //                    //        + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + modelDBName + "<" + "\"" + unzipPath + "\\"
        //                    //        + oldmdb + ".sql" + "\"";
        //                    //string command = codetype + "\r\n" + cmdStr;
        //                    //string batpath = batPath + "modeltemp" + unzippathGuid + ".bat";
        //                    //ExcuteBat(command, batpath);
        //                    string mainprojectcmdstr = "\"" + UtilityHelper.getmysqlpath() + "\\mysql.exe\" -h " + databaseIp + " -P "
        //+ databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + copymodelDBName + "<" + "\"" + backuppath + "\\"
        //+ oldDBName + ".sql" + "\"";
        //                    //                    string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
        //                    //+ databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + modelDBName + "<" + "\"" + unzipPath + "\\"
        //                    //+ oldmdb + ".sql" + "\"";
        //                    _logger.LogInformation("mainprojectcmdstr:" + mainprojectcmdstr);
        //                    string command = codetype + "\r\n" + mainprojectcmdstr;
        //                    string batpath = batPath + "tpbacktemp" + backzippathGuid + ".bat";
        //                    ExcuteBat(command, batpath);
        //                }

        //                _teamRepository.ClearLockedComponents(prj.ID);
        //                _teamRepository.ClearLockModelfiles(prj.ID);
        //                var newPath = UploadModelPath + separator + "UploadFileCommands" + separator + prj.ID;
        //                var newPathStr = newPath.Replace(@"\", @"\\");
        //                _teamRepository.UpdateModelFileInfo(tp.ID, prj.ID, newPathStr);
        //                string currentProjectPath = backuppath + separator + "UploadFileCommands" + separator + tp.ID;
        //                if (Directory.Exists(currentProjectPath))
        //                {
        //                    //复制文件夹
        //                    UtilityHelper.CopyEntireDir(currentProjectPath, newPath);
        //                }


        //                var newWebPath = UploadModelPath + separator + "ProjectDirectory" + separator + main.Name;
        //                var newWebPathStr = newWebPath.Replace(@"\", @"\\");

        //                _teamRepository.UpdateMileStoneFileInfo(tp.ID, mainproject.Name, prj.ID, newWebPathStr);
        //                string currentMileStonePath = backuppath + separator + "ProjectDirectory" + separator + mainproject.Name;
        //                if (Directory.Exists(currentMileStonePath))
        //                {
        //                    UtilityHelper.CopyEntireDir(currentMileStonePath, newWebPath);
        //                }

        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        x.Message = ex.Message;
        //        x.IsSuccess = false;
        //        return x;
        //    }
        //    x.IsSuccess = true;
        //    return x;
        //}
        #endregion

        /// <summary>
        /// 复制项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<CopyMainprojectResponse> CopyMainproject(CopyMainprojectRequest request, ServerCallContext context)
        {
            var x = new CopyMainprojectResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (!Guid.TryParse(request.MainProjectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser == null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            string uploadRootPath = "";
            char separator = Path.DirectorySeparatorChar;
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                //uploadRootPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                uploadRootPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                uploadRootPath = _urls.LoadDataSavePath;
            }
            string username = currentUser.LoginName;
            string backupId = Guid.NewGuid().ToString();
            string assemblyDirectory = GetAssemblyDirectory();
            DirectoryInfo path_exe = new DirectoryInfo(assemblyDirectory);
            string baseBackupPath = path_exe.Parent.FullName;
            string backupRootPath = Path.Combine(baseBackupPath, "CopyMainProject"+ backupId);
            try
            {
                // 获取项目
                var mainProject = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
                if (mainProject == null)
                {
                    x.Message = $"项目不存在: {mainProjectGuid}";
                    return x;
                }
                // 获取模型
                var teamProjects = _teamRepository.Projects.Where(t => request.GuidList.Contains(t.ID.ToString()) && !string.IsNullOrEmpty(t.MainProjectID) && t.MainProjectID.ToLower() == mainProjectGuid.ToString().ToLower()).ToList();
                // 获取文件夹
                var fileDirectories = _teamRepository.FileDirectories.Where(f => request.GuidList.Contains(f.ID.ToString()) && f.MainProjectId == mainProjectGuid).ToList();
                // 获取卷册图档
                var volumes = _teamRepository.Volumes.Where(v => request.GuidList.Contains(v.VolumeId.ToString()) && v.MainProjectId == mainProjectGuid).ToList();
                // 获取模型id
                var teamProjectIds = teamProjects.Select(t => t.ID).ToList();

                // 创建备份目录

                if (!Directory.Exists(backupRootPath))
                {
                    Directory.CreateDirectory(backupRootPath);
                }

                // 复制项目目录（里程碑文件）
                string mainProjectDirPath = Path.Combine(uploadRootPath, "ProjectDirectory", mainProject.Name);
                string backupProjectPath = Path.Combine(backupRootPath, "ProjectDirectory", mainProject.Name);
                if (Directory.Exists(mainProjectDirPath))
                {
                    UtilityHelper.CopyEntireDir(mainProjectDirPath, backupProjectPath);
                }

                _logger.LogInformation("===================================开始备份模型数据库=========================");
                //备份模型数据库
                var dbConfig = UtilityHelper.GetMysqlConnectionConfig();
                foreach (var modelId in teamProjectIds)
                {
                    string modelDbName = "pkpm-pbimserver-modeldb-" + modelId;
                    string sqlBackupPath = Path.Combine(backupRootPath, $"{modelDbName}.sql");

                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {

                        string command = $"mysqldump -h {dbConfig.IP} -u {dbConfig.User} -p{dbConfig.Password} {modelDbName} > \"{sqlBackupPath}\"";
                        string scriptPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.sh");
                        ExcuteBash(command, scriptPath);

                    }
                    else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        string mysqlPath = UtilityHelper.getmysqlpath();
                        string command = $"chcp 65001\r\n\"{mysqlPath}\\mysqldump.exe\" -h {dbConfig.IP} -P {dbConfig.Port} -u{dbConfig.User} -p{dbConfig.Password} {modelDbName} > \"{sqlBackupPath}\"";
                        string batPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.bat");
                        ExcuteBat(command, batPath);
                    }
                    // 备份模型文件（仅最新版本）
                    string sourceProjectPath = Path.Combine(uploadRootPath, "UploadFileCommands", modelId.ToString());
                    string targetProjectPath = Path.Combine(backupRootPath, "UploadFileCommands", modelId.ToString());

                    //modelfiles初始版本中的路径
                    var tempModelManager = _teamRepository.GetProjectRepository(modelId);
                    var modelFiles = tempModelManager.ModelFiles.ToList();
                    if (modelFiles.Any())
                    {
                        // 复制每个文件的最新版本
                        var distinctFileNames = modelFiles.Select(m => m.FileName).Distinct().ToList();
                        foreach (var fileName in distinctFileNames)
                        {
                            var latestVersion = modelFiles.Where(n => n.FileName == fileName).Max(f => f.VersionNo);
                            var fileToBackup = tempModelManager.ModelFiles.FirstOrDefault(f => f.VersionNo == latestVersion && f.FileName == fileName);
                            if (fileToBackup != null && File.Exists(fileToBackup.FilePath))
                            {
                                try
                                {
                                    // 构建新的文件路径
                                    string sourceFilePath = fileToBackup.FilePath;
                                    string targetFilePath = sourceFilePath.Replace(sourceProjectPath, targetProjectPath);

                                    // 创建目标目录
                                    string targetDir = Path.GetDirectoryName(targetFilePath);
                                    Directory.CreateDirectory(targetDir);

                                    // 复制文件
                                    File.Copy(sourceFilePath, targetFilePath, true);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"备份文件 {fileToBackup.FileName} 失败");
                                }
                            }
                        }
                        // 清理旧版本里程碑文件（仅保留最新版本）
                        var milestoneFiles = tempModelManager.MilestoneFiles.ToList();
                        if (milestoneFiles.Any())
                        {
                            if (milestoneFiles.Count > 1)
                            {
                                var maxVersion = milestoneFiles.Max(s => s.VersionNo);
                                var oldVersions = milestoneFiles.Where(s => s.VersionNo < maxVersion).ToList();
                                // 删除旧版本文件的备份目录
                                foreach (var milestoneFile in oldVersions)
                                {
                                    try
                                    {
                                        // 获取文件保存路径
                                        string savePath = milestoneFile.SavePath;

                                        // 提取ProjectDirectory之后的路径部分
                                        string projectDirSegment = separator + "ProjectDirectory";
                                        int startIndex = savePath.IndexOf(projectDirSegment);

                                        if (startIndex >= 0)
                                        {
                                            // 提取相对路径
                                            startIndex += projectDirSegment.Length;
                                            int endIndex = savePath.LastIndexOf(separator);

                                            if (endIndex > startIndex)
                                            {
                                                string relativePath = savePath.Substring(startIndex, endIndex - startIndex);

                                                // 构建备份路径
                                                string backupDirPath = Path.Combine(backupRootPath, relativePath);

                                                // 记录日志
                                                _logger.LogInformation($"尝试删除旧版本目录: {backupDirPath}");

                                                // 如果目录存在则删除
                                                if (Directory.Exists(backupDirPath))
                                                {
                                                    Directory.Delete(backupDirPath, true);
                                                    _logger.LogInformation($"成功删除旧版本目录: {backupDirPath}");
                                                }
                                                else
                                                {
                                                    _logger.LogWarning($"目录不存在，跳过删除: {backupDirPath}");
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, $"删除旧版本目录时发生错误，文件: {milestoneFile.SavePath}");
                                        // 可以选择继续处理其他文件，或者根据需要抛出异常
                                    }
                                }
                            }
                        }

                    }
                    _logger.LogInformation("===================================结束备份模型数据库=========================");
                }
                // 备份卷册文件
                List<VolumeVersion> volumeVersions = new List<VolumeVersion>();
                foreach (var volume in volumes)
                {
                    var latestVersion = _teamRepository.VolumeVersions.Where(a => a.VolumeId == volume.VolumeId).OrderByDescending(a => a.VerNo).FirstOrDefault();
                    if (latestVersion != null)
                    {
                        volumeVersions.Add(latestVersion);
                        try
                        {
                            if (File.Exists(latestVersion.SavePath))
                            {
                                // 提取文件路径信息
                                string fileDirectory = Path.GetDirectoryName(latestVersion.SavePath);
                                string fileName = Path.GetFileName(latestVersion.SavePath);

                                // 构建新路径
                                string relativePath = fileDirectory.Substring(fileDirectory.IndexOf("UploadFileCommands"));
                                string targetDir = Path.Combine(backupRootPath, relativePath);
                                string targetPath = Path.Combine(targetDir, fileName);

                                // 创建目录并复制文件
                                Directory.CreateDirectory(targetDir);
                                File.Copy(latestVersion.SavePath, targetPath, true);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"备份卷册文件失败");
                        }

                    }
                }

                _logger.LogInformation("===================================开始还原=========================");

                Dictionary<Guid, Guid> volumeGuidMap = new Dictionary<Guid, Guid>();
                Dictionary<Guid, Guid> mainProjectGuidMap = new Dictionary<Guid, Guid>();
                var clientVersion = GrpcContextAccessor.GetClientVersion();
                var clientId = GrpcContextAccessor.GetClientId();
                var status = "creating";
                MainProject newMainProject = new MainProject
                {
                    ID = Guid.NewGuid(),
                    Name = string.IsNullOrEmpty(request.NewCustomMainprojectName) ? mainProject.Name + "_copy_" + DateTime.Now.ToString("yyyyMMddHHmmss") : request.NewCustomMainprojectName,

                    Description = mainProject.Description,
                    CreationTime = DateTime.Now,
                    CreateUser = username,//reMainProject.CreateUser,
                    RoleGroupId = mainProject.RoleGroupId,
                    RoleString = mainProject.RoleString,
                    LastUpdateTime = DateTime.Now,
                    ExtendStr = mainProject.ExtendStr,
                    MainProjectType = mainProject.MainProjectType,
                    DesignStage = mainProject.DesignStage,
                    EngineerType = mainProject.EngineerType,
                    clientId = clientId,
                    ClientVersion = clientVersion,
                    status = status
                };

                using (var scope = new TransactionScope(
                        TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                        TransactionScopeAsyncFlowOption.Enabled))
                {
                    var createdProject = _teamRepository.AddMainProject(newMainProject);
                    if (createdProject != null)
                    {
                        //transaction.Commit();

                        // 添加用户到新项目
                        List<TeamMember> members = _teamRepository.Members.ToList();

                        // 添加当前用户
                        TeamMember currentMember = members.FirstOrDefault(mem => mem.LoginName == username);
                        if (currentMember != null)
                        {
                            _teamRepository.AddMemberToMainProject(currentMember, createdProject);
                        }
                        // 添加系统管理员
                        TeamMember adminMember = members.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                        if (adminMember != null)
                        {
                            _teamRepository.AddMemberToMainProject(adminMember, createdProject);
                        }

                        // 添加系统超级管理员
                        TeamMember systemAdminMember = members.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                        if (systemAdminMember != null)
                        {
                            _teamRepository.AddMemberToMainProject(systemAdminMember, createdProject);
                        }
                        x.MainProjectGuid = createdProject.ID.ToString();
                        x.MainProjectName = createdProject.Name;
                    }
                    scope.Complete();
                }

                mainProjectGuidMap.Add(mainProject.ID, newMainProject.ID);

                // 处理文件夹映射
                Dictionary<Guid, Guid> fileDirectoryGuidMap = new Dictionary<Guid, Guid>();
                List<FileDirectory> newFileDirectories = new List<FileDirectory>();
                // 为每个文件夹生成新的GUID
                foreach (var fileDirectory in fileDirectories)
                {
                    if (!fileDirectoryGuidMap.ContainsKey(fileDirectory.ID))
                    {
                        fileDirectoryGuidMap.Add(fileDirectory.ID, Guid.NewGuid());
                    }

                }
                // 创建新的文件夹对象
                foreach (var fileDirectory in fileDirectories)
                {
                    Guid newDirectoryId = fileDirectoryGuidMap[fileDirectory.ID]; //文件夹ID更新
                    Guid newParentId = Guid.Empty; //文件夹parentid更新

                    if (fileDirectory.ParentID != Guid.Empty)
                    {
                        fileDirectoryGuidMap.TryGetValue(fileDirectory.ParentID, out newParentId);
                    }
                    FileDirectory newFileDirectory = new FileDirectory
                    {
                        ID = newDirectoryId,
                        Name = fileDirectory.Name,
                        MainProjectId = newMainProject.ID,
                        ParentID = newParentId,
                        CreateUser = fileDirectory.CreateUser,
                        RoleGroupId = fileDirectory.RoleGroupId,
                        RoleString = fileDirectory.RoleString,
                        OrderNo = fileDirectory.OrderNo,
                        Type = fileDirectory.Type
                    };
                    newFileDirectories.Add(newFileDirectory);

                }
                // 批量添加新文件夹
                _teamRepository.AddFileDirectoreyList(newFileDirectories);

                // 处理卷册信息
                List<Volume> newVolumes = new List<Volume>();
                foreach (var volume in volumes)
                {
                    Guid newVolumeId = Guid.NewGuid();
                    Guid newDirectoryId = Guid.Empty;
                    if (volume.FileDirectoryId != Guid.Empty)
                    {
                        fileDirectoryGuidMap.TryGetValue(volume.FileDirectoryId, out newDirectoryId);
                    }
                    Volume newVolume = new Volume
                    {
                        VolumeId = newVolumeId,
                        VolumeName = volume.VolumeName,
                        CreateTime = volume.CreateTime,
                        CreateUser = username,
                        ExtendStr = volume.ExtendStr,
                        FileDirectoryId = newDirectoryId,
                        MainProjectId = newMainProject.ID

                    };
                    newVolumes.Add(newVolume);

                    // 记录GUID映射
                    if (!volumeGuidMap.ContainsKey(volume.VolumeId))
                    {
                        volumeGuidMap.Add(volume.VolumeId, newVolumeId);
                    }
                    if (!x.ChangeGuid.ContainsKey(volume.VolumeId.ToString()))
                    {
                        x.ChangeGuid.Add(volume.VolumeId.ToString(), newVolumeId.ToString());
                    }
                    // 复制卷册文件
                    string sourceVolumePath = Path.Combine(backupRootPath, "UploadFileCommands", volume.VolumeId.ToString());
                    string targetVolumePath = Path.Combine(uploadRootPath, "UploadFileCommands", newVolumeId.ToString());

                    if (Directory.Exists(sourceVolumePath))
                    {
                        UtilityHelper.CopyEntireDir(sourceVolumePath, targetVolumePath);
                    }

                }
                // 批量添加新卷册
                _teamRepository.AddVolumeList(newVolumes);
                // 处理卷册版本信息
                List<VolumeVersion> newVolVerList = new List<VolumeVersion>();
                foreach (var volumeVersion in volumeVersions)
                {
                    Guid newVolumeId = volumeGuidMap.GetValueOrDefault(volumeVersion.VolumeId, Guid.Empty);
                    if (newVolumeId != Guid.Empty)
                    {
                        // 构建新的保存路径
                        string originalPath = volumeVersion.SavePath;
                        string volumeIdStr = volumeVersion.VolumeId.ToString();
                        int index = originalPath.IndexOf(volumeIdStr);
                        if (index != -1)
                        {
                            string remainingPath = originalPath.Substring(index + volumeIdStr.Length);
                            string newSavePath = Path.Combine(uploadRootPath, "UploadFileCommands", newVolumeId.ToString(), remainingPath);

                            VolumeVersion newVV = new VolumeVersion
                            {
                                VolumeId = newVolumeId,
                                SavePath = newSavePath,
                                Description = volumeVersion.Description,
                                ExtendStr = volumeVersion.ExtendStr,
                                FileName = volumeVersion.FileName,
                                FileSize = volumeVersion.FileSize,
                                FileType = volumeVersion.FileType,
                                UploadTime = volumeVersion.UploadTime,
                                UploadUser = username,
                                VerNo = volumeVersion.VerNo
                            };
                            newVolVerList.Add(newVV);
                        }

                    }
                }
                // 批量添加新卷册版本
                _teamRepository.AddVolumeVersionList(newVolVerList);

                //处理teamproject
                Dictionary<Guid, Guid> teamProjectGuidMap = new Dictionary<Guid, Guid>();
                foreach (var teamProject in teamProjects)
                {
                    string oldDbName = "pkpm-pbimserver-modeldb-" + teamProject.ID;
                    string sqlBackupPath = Path.Combine(backupRootPath, $"{oldDbName}.sql");
                    // 检查备份文件是否有效
                    if (File.Exists(sqlBackupPath))
                    {
                        FileInfo fileInfo = new FileInfo(sqlBackupPath);
                        long fileSize = fileInfo.Length; // 获取文件大小（字节数）
                        if (fileSize < 4 * 1024)
                        {
                            _logger.LogInformation(oldDbName + "文件小于4KB,判定为异常模型,无需还原 ");
                            continue;
                        }
                    }
                    // 获取文件夹ID映射
                    Guid newDirectoryId = Guid.Empty;
                    if (!string.IsNullOrEmpty(teamProject.FileDirectoryID) && teamProject.FileDirectoryID != Guid.Empty.ToString())
                    {
                        fileDirectoryGuidMap.TryGetValue(Guid.Parse(teamProject.FileDirectoryID), out newDirectoryId);
                    }
                    // 创建新的模型
                    TeamProject newTeamProject = new TeamProject
                    {
                        Name = teamProject.Name,
                        Avatar = teamProject.Avatar,
                        CreateUser = username,
                        CreationTime = teamProject.CreationTime,
                        Description = teamProject.Description,
                        EnableAuthority = teamProject.EnableAuthority,
                        EndTime = teamProject.EndTime,
                        FileDirectoryID = newDirectoryId.ToString(),
                        FilePath = teamProject.FilePath,
                        Leader = username,
                        MainProjectID = newMainProject.ID.ToString(),
                        ParentProjectID = teamProject.ParentProjectID,
                        Progress = teamProject.Progress,
                        ProjectType = teamProject.ProjectType,
                        RoleGroupId = teamProject.RoleGroupId,
                        RoleString = teamProject.RoleString,
                        StartTime = teamProject.StartTime
                    };
                    // 添加新子模型
                    var createdProject = _teamRepository.AddProject(newTeamProject);
                    if (createdProject != null)
                    {
                        // 记录GUID映射
                        if (!teamProjectGuidMap.ContainsKey(teamProject.ID))
                        {
                            teamProjectGuidMap.Add(teamProject.ID, createdProject.ID);
                        }
                        //if (!outChangeGuid.ContainsKey(tp.ID))
                        //{
                        //    outChangeGuid.Add(tp.ID, prj.ID);
                        //}
                        if (!x.ChangeGuid.ContainsKey(teamProject.ID.ToString()))
                        {
                            x.ChangeGuid.Add(teamProject.ID.ToString(), createdProject.ID.ToString());
                        }


                        var createModelDb = _teamRepository.CreateModeldataBase(createdProject.ID);
                        if (!createModelDb)
                        {
                            _logger.LogError($"创建数据库失败: PKPM-PBIMServer-ModelDB-{createdProject.ID}");
                            SafeDeleteDirectory(backupRootPath, true);
                            x.IsSuccess = false;
                            return x;
                        }
                        var newDbName = "pkpm-pbimserver-modeldb-" + createdProject.ID;
                        // 还原数据库
                        try
                        {
                            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                            {
                                string command = $"mysql -h {dbConfig.IP} -u {dbConfig.User} -p{dbConfig.Password} {newDbName} < \"{sqlBackupPath}\"";
                                string scriptPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.sh");
                                ExcuteBash(command, scriptPath);
                            }
                            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                            {
                                string mysqlPath = UtilityHelper.getmysqlpath();
                                string command = $"chcp 65001\r\n\"{mysqlPath}\\mysql.exe\" -h {dbConfig.IP} -P {dbConfig.Port} -u {dbConfig.User} -p{dbConfig.Password} {newDbName} < \"{sqlBackupPath}\"";
                                string batPath = Path.Combine(assemblyDirectory, $"tpbacktemp{backupId}.bat");
                                ExcuteBat(command, batPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"还原数据库失败: {newDbName}");
                            continue;
                        }

                        // 清理锁定状态
                        _teamRepository.ClearLockedComponents(createdProject.ID);
                        _teamRepository.ClearLockModelfiles(createdProject.ID);

                        // 更新模型文件路径
                        var newProjectPath = Path.Combine(uploadRootPath, "UploadFileCommands", createdProject.ID.ToString());
                        var escapedNewProjectPath = newProjectPath.Replace(@"\", @"\\");
                        _teamRepository.UpdateModelFileInfo(teamProject.ID, createdProject.ID, escapedNewProjectPath);

                        // 复制项目文件
                        string sourceProjectPath = Path.Combine(backupRootPath, "UploadFileCommands", teamProject.ID.ToString());
                        if (Directory.Exists(sourceProjectPath))
                        {
                            UtilityHelper.CopyEntireDir(sourceProjectPath, newProjectPath);
                        }

                        // 更新里程碑文件路径
                        string newWebPath = Path.Combine(uploadRootPath, "ProjectDirectory", newMainProject.Name);
                        string escapedNewWebPath = newWebPath.Replace(@"\", @"\\");
                        _teamRepository.UpdateMileStoneFileInfo(teamProject.ID, mainProject.Name, createdProject.ID, escapedNewWebPath);

                        // 复制里程碑文件
                        string sourceMilestonePath = Path.Combine(backupRootPath, "ProjectDirectory", mainProject.Name);
                        if (Directory.Exists(sourceMilestonePath))
                        {
                            UtilityHelper.CopyEntireDir(sourceMilestonePath, newWebPath);
                        }
                        newMainProject.status = "success";
                        _teamRepository.UpdateMainprojectInfo(newMainProject);
                    }
                }
            }
            catch (Exception ex)
            {
                SafeDeleteDirectory(backupRootPath, true);
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            SafeDeleteDirectory(backupRootPath, true);
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 导入项目
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<RestoreDBByMainProjectIdResponse> RestoreDBByMainProjectId(RestoreDBByMainProjectIdRequest request, ServerCallContext context)
        {
            var x = new RestoreDBByMainProjectIdResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            //string oldFileName = request.OldFileName;
            string username = currentUser.LoginName;
            string zippath = "";
            string locker = "";
            string UploadModelPath = "";
            char separator = Path.DirectorySeparatorChar;
            var mysqlpath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // 获取 Windows 系统中 MyDocuments 目录路径
                UploadModelPath = _urls.UploadRootPath;
                mysqlpath = UtilityHelper.getmysqlpath();
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                // 获取 Linux 系统中 MY_DOCUMENTS 环境变量指定的路径
                UploadModelPath = _urls.LoadDataSavePath;
            }
            _logger.LogInformation("ImportMainProject mysqlpath:" + mysqlpath);
            _logger.LogInformation("ImportMainProject UploadModelPath:" + UploadModelPath);

            try
            {
                // 获取程序集所在目录
                string batPath = GetAssemblyDirectory();
                _logger.LogInformation("ImportMainProject batPath:" + batPath);

                // 检查目录是否存在，若不存在则创建
                if (!Directory.Exists(UploadModelPath))
                {
                    Directory.CreateDirectory(UploadModelPath);
                }

                // 根据程序集目录创建 DirectoryInfo 对象
                DirectoryInfo path_exe = new DirectoryInfo(batPath);
                // 获取程序集目录的父目录路径
                String path = path_exe.Parent.FullName;
                _logger.LogInformation("ImportMainProject path:" + path);
                // 生成一个新的 Guid 并转换为字符串
                var uploadRequestId = request.UploadRequestId;
                string unzippathGuid = string.Empty;

                if (uploadRequestId != 0)
                {
                    unzippathGuid = uploadRequestId.ToString();
                }
                else
                {
                    unzippathGuid = Guid.NewGuid().ToString();
                }

                Guid.NewGuid().ToString();
                // 构建解压目标路径
                string unzipPath = Path.Combine(path, "unzip" + unzippathGuid);

                // 检查解压目标路径是否存在，若存在则删除该目录及其内容
                if (Directory.Exists(unzipPath))
                {
                    Directory.Delete(unzipPath, true);
                }
                // 检查解压目标路径是否存在，若不存在则创建该目录
                if (!Directory.Exists(unzipPath))
                {
                    Directory.CreateDirectory(unzipPath);
                }

                string savePath = Path.Combine(path, "zip" + unzippathGuid);

                var requestFilename = request.OldFileName;

                // 将文件名从默认编码转换为 UTF8 编码
                string utf8FileName = Encoding.UTF8.GetString(Encoding.Default.GetBytes(requestFilename));
                var oldfilename = utf8FileName;
                _logger.LogInformation("ImportMainProject utf8FileName:" + oldfilename);

                // 将请求中的文件数据转换为字节数组
                var fileData = request.FileData.ToByteArray();
                // 判断 uploadRequestId 是否不等于 0
                if (uploadRequestId != 0)
                {
                    lock (locker)
                    {
                        _logger.LogInformation("上传文件大于500M");
                        Stopwatch oncefile = new Stopwatch();
                        oncefile.Start();
                        _logger.LogInformation("zip路径:" + savePath);
                        // 检查保存路径的目录是否存在，若不存在则创建
                        if (!Directory.Exists(savePath))
                        {
                            Directory.CreateDirectory(savePath);
                        }
                        savePath = savePath + separator + oldfilename;
                        oncefile.Stop();
                        _logger.LogInformation("上传文件大于500M，配置文件路径，获取文件版本耗时：" + oncefile.ElapsedMilliseconds);
                        _logger.LogInformation("上传文件大于500M，开始文件流写入");
                        oncefile.Restart();

                        // 创建文件流以打开或创建文件
                        var fs = new FileStream(savePath, FileMode.OpenOrCreate);

                        _logger.LogInformation("fileData.Length:" + fileData.Length);
                        fs.Position = fs.Length;
                        _logger.LogInformation("fs.Position:" + fs.Position);

                        // 将文件数据写入文件流
                        fs.Write(fileData, 0, fileData.Length);
                        _logger.LogInformation("已上传文件大小:" + fs.Length);
                        fs.Flush();
                        fs.Close();
                        fs.Dispose();

                        oncefile.Stop();
                        _logger.LogInformation("上传文件大于500M，结束文件流写入，文件流操作耗时：" + oncefile.ElapsedMilliseconds);
                    }
                }
                else
                {
                    lock (locker)
                    {
                        _logger.LogInformation("上传文件小于500M");
                        Stopwatch oncefile = new Stopwatch();
                        oncefile.Start();
                        _logger.LogInformation("小于500M,zip路径:" + savePath);

                        // 检查保存路径的目录是否存在，若不存在则创建
                        if (!Directory.Exists(savePath))
                        {
                            Directory.CreateDirectory(savePath);
                        }

                        savePath = savePath + separator + oldfilename;
                        _logger.LogInformation("小于500M,oldfilename文件路径:" + savePath);
                        oncefile.Stop();
                        _logger.LogInformation("上传文件小于500M，配置文件路径，获取文件版本耗时：" + oncefile.ElapsedMilliseconds);
                        _logger.LogInformation("上传文件小于500M，开始文件流写入");
                        oncefile.Restart();
                        // 使用 using 语句确保文件流正确释放资源
                        using (var fs1 = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
                        {
                            _logger.LogInformation("传输文件");
                            byte[] byteData = fileData;
                            fs1.Write(byteData, 0, byteData.Length);
                            fs1.Flush();
                            fs1.Close();
                            fs1.Dispose();
                        }
                        oncefile.Stop();
                        _logger.LogInformation("上传文件小于500M，结束文件流写入，文件流操作耗时：" + oncefile.ElapsedMilliseconds);
                    }
                }

                // 判断是否为最后一次上传
                if (!request.IsLastUpload)
                {
                    x.IsSuccess = true;
                    return x;
                }

                // 设置 zippath 为保存路径
                zippath = savePath;
                ZipHelper zipHelper = new ZipHelper();
                List<string> outunzipstr = new List<string>();
                // 解压文件并获取解压后的文件列表
                var filelist = zipHelper.UnZip(zippath, unzipPath);
                // 遍历解压后的文件列表并记录日志
                foreach (var item in filelist)
                {
                    _logger.LogInformation(item);
                }


                // 检查解压后指定文件是否存在
                if (!File.Exists(unzipPath + separator + "MainProjectInfo.xml"))
                {
                    _logger.LogInformation("解压后未找到所需文件");
                    x.IsSuccess = false;
                    return x;
                }

                Dictionary<string, string> dicVolGuid = new Dictionary<string, string>();
                MainProject reMainProject = BimBaseServerConfigurations.ReadCustomConfiguration<MainProject>(unzipPath + separator + "MainProjectInfo.xml");
                Dictionary<Guid, Guid> dicMainGuid = new Dictionary<Guid, Guid>();
                string newCustomMainprojectName = "";
                if (!string.IsNullOrEmpty(oldfilename))
                {
                    newCustomMainprojectName = oldfilename.Substring(0, oldfilename.LastIndexOf("."));
                }
                var clientVersion = GrpcContextAccessor.GetClientVersion();
                var clientId = GrpcContextAccessor.GetClientId();
                var status = "creating";
                MainProject main = new MainProject
                {
                    ID = Guid.NewGuid(),
                    Name = string.IsNullOrEmpty(newCustomMainprojectName) ? reMainProject.Name + "_导入_" + DateTime.Now.ToString("yyyyMMddHHmmss") : newCustomMainprojectName + "_导入_" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    Description = reMainProject.Description,
                    CreationTime = DateTime.Now,
                    CreateUser = username,//reMainProject.CreateUser,
                    RoleGroupId = reMainProject.RoleGroupId,
                    RoleString = reMainProject.RoleString,
                    LastUpdateTime = DateTime.Now,
                    ExtendStr = reMainProject.ExtendStr,
                    MainProjectType = reMainProject.MainProjectType,
                    IsDelete = reMainProject.IsDelete,
                    clientId = clientId,
                    ClientVersion = clientVersion,
                    status = status
                };

                using (var scope = new TransactionScope(
                        TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                        TransactionScopeAsyncFlowOption.Enabled))
                {
                    var prj = _teamRepository.AddMainProject(main);
                    if (prj != null)
                    {

                        List<TeamMember> memberTmp = _teamRepository.Members.ToList();
                        TeamMember member = memberTmp.FirstOrDefault(mem => mem.LoginName == username);
                        MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == prj.ID);
                        _teamRepository.AddMemberToMainProject(member, mp);

                        member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                        _teamRepository.AddMemberToMainProject(member, mp);

                        member = memberTmp.FirstOrDefault(mem => mem.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                        _teamRepository.AddMemberToMainProject(member, mp);


                        scope.Complete();

                        x.MainProjectName = mp.Name;
                        x.MainprojectGuid = mp.ID.ToString();


                    }
                }


                dicMainGuid.Add(reMainProject.ID, main.ID);

                x.OldMainProjectGuid = reMainProject.ID.ToString();
                //记录filedirectoryguid
                Dictionary<Guid, Guid> dicFiledirGuid = new Dictionary<Guid, Guid>();
                List<FileDirectory> listFiledir = BimBaseServerConfigurations.ReadCustomConfiguration<List<FileDirectory>>(unzipPath + separator + "FiledirectoryInfo.xml");
                List<FileDirectory> newFileDirList = new List<FileDirectory>();
                foreach (var fd in listFiledir)
                {
                    if (!dicFiledirGuid.ContainsKey(fd.ID))
                    {
                        dicFiledirGuid.Add(fd.ID, Guid.NewGuid());
                    }

                }

                foreach (var fd in listFiledir)
                {
                    Guid temp = Guid.Empty; //文件夹ID更新
                    Guid tmpP = Guid.Empty; //文件夹parentid更新
                    dicFiledirGuid.TryGetValue(fd.ID, out temp);
                    if (fd.ParentID != Guid.Empty)
                    {
                        dicFiledirGuid.TryGetValue(fd.ParentID, out tmpP);
                    }
                    FileDirectory f = new FileDirectory
                    {
                        ID = temp,
                        Name = fd.Name,
                        MainProjectId = main.ID,
                        ParentID = tmpP,
                        CreateUser = fd.CreateUser,
                        RoleGroupId = fd.RoleGroupId,
                        RoleString = fd.RoleString,
                        OrderNo = fd.OrderNo,
                        Type = fd.Type
                    };
                    newFileDirList.Add(f);

                    //if (!dicVolGuid.ContainsKey(fd.ID))
                    //{
                    //    dicVolGuid.Add(fd.ID, temp);
                    //}
                }
                _teamRepository.AddFileDirectoreyList(newFileDirList);
                //还原volume和volumeversion信息
                List<Volume> oldVolList = BimBaseServerConfigurations.ReadCustomConfiguration<List<Volume>>(unzipPath + separator + "VolumeInfo.xml");

                List<Volume> newVolList = new List<Volume>();
                foreach (var v in oldVolList)
                {
                    Guid newVolumeid = Guid.NewGuid();
                    Guid volfiledirid = Guid.Empty;
                    if (v.FileDirectoryId != Guid.Empty)
                    {
                        dicFiledirGuid.TryGetValue(v.FileDirectoryId, out volfiledirid);
                    }
                    Volume vl = new Volume
                    {
                        VolumeId = newVolumeid,
                        VolumeName = v.VolumeName,
                        CreateTime = v.CreateTime,
                        CreateUser = username,
                        ExtendStr = v.ExtendStr,
                        FileDirectoryId = volfiledirid,
                        MainProjectId = main.ID

                    };
                    newVolList.Add(vl);
                    if (!dicVolGuid.ContainsKey(v.VolumeId.ToString()))
                    {
                        dicVolGuid.Add(v.VolumeId.ToString(), newVolumeid.ToString());
                    }



                    var newVolPath = UploadModelPath + separator + "UploadFileCommands" + separator + vl.VolumeId;
                    string currentVolumePath = unzipPath + separator + "UploadFileCommands" + separator + v.VolumeId;

                    if (Directory.Exists(currentVolumePath))
                    {
                        //复制文件夹
                        UtilityHelper.CopyEntireDir(currentVolumePath, newVolPath);
                    }

                }
                _teamRepository.AddVolumeList(newVolList);


                List<VolumeVersion> oldVolVerList = BimBaseServerConfigurations.ReadCustomConfiguration<List<VolumeVersion>>(unzipPath + separator + "VolumeVersionInfo.xml");
                List<VolumeVersion> newVolVerList = new List<VolumeVersion>();
                foreach (var vv in oldVolVerList)
                {
                    string volid = Guid.Empty.ToString();
                    dicVolGuid.TryGetValue(vv.VolumeId.ToString(), out volid);
                    string newSavePath = "";
                    string str = vv.SavePath;
                    string subString = vv.VolumeId.ToString();
                    int index = str.IndexOf(subString);
                    if (index != -1)
                    {
                        string result = str.Substring(index + subString.Length);
                        var debugpath = UploadModelPath;
                        //var debugpath = "E:\\debugServerPath";
                        newSavePath = debugpath + separator + "UploadFileCommands" + separator + volid + result;
                    }
                    VolumeVersion newVV = new VolumeVersion
                    {
                        VolumeId = Guid.Parse(volid),
                        SavePath = newSavePath,
                        Description = vv.Description,
                        ExtendStr = vv.ExtendStr,
                        FileName = vv.FileName,
                        FileSize = vv.FileSize,
                        FileType = vv.FileType,
                        UploadTime = vv.UploadTime,
                        UploadUser = username,
                        VerNo = vv.VerNo
                    };
                    newVolVerList.Add(newVV);
                }
                _teamRepository.AddVolumeVersionList(newVolVerList);
                //处理teamproject
                Dictionary<string, string> dicTeamProjectGuid = new Dictionary<string, string>();
                List<TeamProject> listTP = BimBaseServerConfigurations.ReadCustomConfiguration<List<TeamProject>>(unzipPath + separator + "TeamProjectListInfo.xml");

                foreach (var tp in listTP)
                {
                    Guid fdirid = Guid.Empty;
                    if (!string.IsNullOrEmpty(tp.FileDirectoryID) && tp.FileDirectoryID != Guid.Empty.ToString())
                    {
                        dicFiledirGuid.TryGetValue(Guid.Parse(tp.FileDirectoryID), out fdirid);
                    }
                    TeamProject newTp = new TeamProject
                    {
                        Name = tp.Name,
                        Avatar = tp.Avatar,
                        CreateUser = username,
                        CreationTime = tp.CreationTime,
                        Description = tp.Description,
                        EnableAuthority = tp.EnableAuthority,
                        EndTime = tp.EndTime,
                        FileDirectoryID = fdirid.ToString(),
                        FilePath = tp.FilePath,
                        Leader = username,
                        MainProjectID = main.ID.ToString(),
                        ParentProjectID = tp.ParentProjectID,
                        Progress = tp.Progress,
                        ProjectType = tp.ProjectType,
                        RoleGroupId = tp.RoleGroupId,
                        RoleString = tp.RoleString,
                        StartTime = tp.StartTime
                    };
                    var prj = _teamRepository.AddProject(newTp);


                    if (prj != null)
                    {
                        if (!dicTeamProjectGuid.ContainsKey(tp.ID.ToString()))
                        {
                            dicTeamProjectGuid.Add(tp.ID.ToString(), prj.ID.ToString());
                        }


                        var conf = UtilityHelper.GetMysqlConnectionConfig();
                        var databaseIp = conf.IP;
                        var databasePort = conf.Port;
                        var dbusername = conf.User;
                        var dbpassword = conf.Password;

                        var createmodeldb = _teamRepository.CreateModeldataBase(prj.ID);
                        if (!createmodeldb)
                        {
                            _logger.LogInformation("创建PKPM-PBIMServer-ModelDB-" + prj.ID + "数据库失败");
                            x.IsSuccess = false;
                            return x;
                        }
                        var modelDBName = "pkpm-pbimserver-modeldb-" + prj.ID;
                        var oldmdb = "pkpm-pbimserver-modeldb-" + tp.ID;
                        //string codetype = "chcp 65001";
                        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                        {
                            string modelsqlpath = unzipPath + separator + oldmdb + ".sql";
                            var modelcommand = "mysql -h " + databaseIp + " -u " + dbusername + " -p" + dbpassword + " " + modelDBName + " < " + modelsqlpath + "";
                            _logger.LogInformation("modelcommand:====>" + modelcommand);

                            string modelscriptPath = unzipPath + separator + "modeltemp" + unzippathGuid + ".sh";

                            ExcuteBash(modelcommand, modelscriptPath);
                        }
                        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                        {
                            string codetype = "chcp 65001";
                            string cmdStr = "\"" + mysqlpath + "\\mysql.exe\" -h " + databaseIp + " -P "
                                    + databasePort + " -u " + dbusername + " -p" + dbpassword + "  " + modelDBName + "<" + "\"" + unzipPath + "\\"
                                    + oldmdb + ".sql" + "\"";
                            string command = codetype + "\r\n" + cmdStr;
                            string batpath = batPath + "modeltemp" + unzippathGuid + ".bat";
                            ExcuteBat(command, batpath);
                        }




                        _teamRepository.ClearLockedComponents(prj.ID);
                        _teamRepository.ClearLockModelfiles(prj.ID);
                        var newPath = UploadModelPath + separator + "UploadFileCommands" + separator + prj.ID;
                        var newPathStr = newPath.Replace(@"\", @"\\");
                        _teamRepository.UpdateModelFileInfo(tp.ID, prj.ID, newPathStr);

                        string currentProjectPath = unzipPath + separator + "UploadFileCommands" + separator + tp.ID;
                        if (Directory.Exists(currentProjectPath))
                        {
                            //复制文件夹
                            UtilityHelper.CopyEntireDir(currentProjectPath, newPath);
                        }
                        var newWebPath = UploadModelPath + separator + "ProjectDirectory" + separator + main.Name;

                        var newWebPathStr = newWebPath.Replace(@"\", @"\\");

                        _teamRepository.UpdateMileStoneFileInfo(tp.ID, reMainProject.Name, prj.ID, newWebPathStr);

                        string currentMileStonePath = unzipPath + separator + "ProjectDirectory" + separator + reMainProject.Name;
                        Console.WriteLine("currentMileStonePath===============>" + currentMileStonePath);
                        if (Directory.Exists(currentMileStonePath))
                        {
                            Console.WriteLine("ProjectDirectory===============>" + currentMileStonePath);
                            UtilityHelper.CopyEntireDir(currentMileStonePath, newWebPath);
                        }

                    }

                }

                main.status = "success";
                _teamRepository.UpdateMainprojectInfo(main);
                x.DicProjectGuid.Add(dicTeamProjectGuid);
                x.DicVolumeGuid.Add(dicVolGuid);
                x.IsSuccess = true;
            }
            catch (Exception ex)
            {
                x.Message = ex.Message;
                x.IsSuccess = false;
                return x;
            }
            return x;
        }



        // <summary>
        /// 安全删除目录（如果存在）
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径</param>
        /// <param name="recursive">是否递归删除子目录</param>
        /// <returns>删除操作是否成功</returns>
        private bool SafeDeleteDirectory(string directoryPath, bool recursive)
        {
            if (string.IsNullOrEmpty(directoryPath))
            {
                _logger.LogWarning("尝试删除空路径，操作已取消");
                return false;
            }

            try
            {
                if (Directory.Exists(directoryPath))
                {
                    Directory.Delete(directoryPath, recursive);
                    _logger.LogInformation($"成功删除目录: {directoryPath}");
                    return true;
                }
                else
                {
                    _logger.LogWarning($"目录不存在，跳过删除: {directoryPath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除目录失败: {directoryPath}");
                return false;
            }
        }
    }
}
