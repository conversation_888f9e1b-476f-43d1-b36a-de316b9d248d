﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Common
{
    public static class ObjectHelper
    {
		public static string GetEnumDescription<T>(T value)
		{

			FieldInfo fi = value.GetType().GetField(value.ToString());

			DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

			if (attributes != null && attributes.Length > 0)
				return attributes[0].Description;
			else
				return value.ToString();
		}

		public static void CopyPropertyValueNull<TFrom, TTo>(TFrom objFrom, TTo objTo)
		{
			Type typeFromHandle = typeof(TFrom);
			Type typeFromHandle2 = typeof(TTo);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			PropertyInfo[] properties2 = typeFromHandle2.GetProperties();
			foreach (PropertyInfo propertyInfo in properties)
			{
				foreach (PropertyInfo propertyInfo2 in properties2)
				{
					if (propertyInfo.Name == propertyInfo2.Name && propertyInfo.PropertyType == propertyInfo2.PropertyType && propertyInfo2.CanWrite)
					{
						object value = propertyInfo.GetValue(objFrom, null);
						if (value != null)
						{
							propertyInfo2.SetValue(objTo, value, null);
							break;
						}
					}
					else if (propertyInfo.Name == propertyInfo2.Name && propertyInfo2.CanWrite)
					{
						if (!propertyInfo2.PropertyType.IsGenericType)
						{
							object value = propertyInfo.GetValue(objFrom, null);
							if (value != null)
							{
								propertyInfo2.SetValue(objTo, Convert.ChangeType(value, propertyInfo2.PropertyType), null);
							}
						}
						else
						{
							Type genericTypeDefinition = propertyInfo2.PropertyType.GetGenericTypeDefinition();
							if (genericTypeDefinition == typeof(Nullable<>))
							{
								object value = propertyInfo.GetValue(objFrom, null);
								if (value != null)
								{
									propertyInfo2.SetValue(objTo, Convert.ChangeType(value, Nullable.GetUnderlyingType(propertyInfo2.PropertyType)), null);
								}
							}
						}
					}
				}
			}
		}
	}
}
