﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// add by asdf 2018-07-07
    /// 记录服务端上传操作是否完成，客户端由于网络原因等异常后，
    /// 可以根据请求id，查询服务端是否执行成功
    /// </summary>
    public class CheckInRequestRecord
    {
        [System.ComponentModel.DataAnnotations.Key]
        public Int64 Id { get; set; }
        /// <summary>
        /// 每次上传请求唯一id
        /// </summary>
        
        public string RequestId { get; set; }

        
        public DateTime RequestAt { get; set; }
        /// <summary>
        /// 服务端写数据操作是否成功
        /// </summary>
        
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 当前请求对应的版本号
        /// </summary>
        
        public int VersionNO { get; set; }
    }
}
