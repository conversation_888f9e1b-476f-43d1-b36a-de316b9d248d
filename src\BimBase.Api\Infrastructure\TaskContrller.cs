﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

namespace BimBase.Api.Infrastructure
{
    public class TaskContrller
    {
        static PerformanceCounter _cpuCounter;
        static TaskContrller _self;
        static Queue<AutoResetEvent> _callBackHanlders = new Queue<AutoResetEvent>();

        private TaskContrller()
        {
            _cpuCounter = new PerformanceCounter
            {
                CategoryName = "Processor",
                CounterName = "% Processor Time",
                InstanceName = "_Total"
            };
            _cpuCounter.NextValue();
        }

        public static TaskContrller GetInstance
        {
            get
            {
                if (_self == null)
                {
                    _self = new TaskContrller();
                    var sessionTimer = new System.Timers.Timer
                    {
                        //Interval = 1000 * BimBaseServerConfigurations.SystemConfig.TaskInterval
                    };
                    sessionTimer.Elapsed += TaskTimerElapsed;
                    sessionTimer.Start();
                }
                return _self;
            }
        }

        //return a value ----true:need wait for running task; false:can run immediately；
        public bool RegistHanlder(AutoResetEvent _event)
        {
            if (!Worker(_event))
            {
                _callBackHanlders.Enqueue(_event);
                return true;
            }
            return false;
        }

        public static int GetCpuUsage()
        {
            if (_cpuCounter == null)
                return -1;

            return (int)_cpuCounter.NextValue();
        }

        static void TaskTimerElapsed(object sender, ElapsedEventArgs e)
        {
            if (_callBackHanlders.Count > 0)
                Worker(_callBackHanlders.Dequeue());
        }

        static bool Worker(AutoResetEvent hanlder)
        {
            var usage = GetCpuUsage();
            if (usage < BimBaseServerConfigurations.SystemConfig.MaxCPUUsage)
            {
                return hanlder.Set();
            }
            return false;
        }


    }
}
