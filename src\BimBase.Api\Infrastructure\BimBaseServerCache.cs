﻿using BimBase.Api.Infrastructure.Common;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    internal class BimBaseServerCache
    {
        private static Lazy<ConnectionMultiplexer> LazyConnection;
        private static ConfigurationOptions configurationOptions;

        static BimBaseServerCache()
        {
            string result = SettingsConfigHelper.AppSetting("redis") ?? "localhost";

            var configurationOptions = new StackExchange.Redis.ConfigurationOptions
            {
                EndPoints = { result },
                //https://docs.microsoft.com/en-us/azure/redis-cache/cache-how-to-troubleshoot#stackexchangeredis-timeout-exceptions
                SyncTimeout = 1000 * 60 * 10,
                //https://gist.github.com/JonCole/36ba6f60c274e89014dd#file-se-redis-setabortconnecttofalse-md
                //设置AbortConnect为false，然后让ConnectionMultiplexer自动重新连接
                AbortOnConnectFail = false,
                ConnectRetry = 10,
                ConnectTimeout = 30,
                AllowAdmin = true   //下发指令必须使用允许管理员模式
            };

            LazyConnection = new Lazy<ConnectionMultiplexer>(() => ConnectionMultiplexer.Connect(configurationOptions));
        }

        //internal static ConnectionMultiplexer Connection => LazyConnection.Value;
        internal static ConnectionMultiplexer Connection
        {
            get
            {
                return GetConnection();
            }
        }

        //private static Lazy<ConnectionMultiplexer> lazyConnection = new Lazy<ConnectionMultiplexer>(() =>
        //{
        //    return ConnectionMultiplexer.Connect("contoso5.redis.cache.windows.net,abortConnect=false,ssl=true,password=...");
        //});

        private static ConnectionMultiplexer GetConnection()
        {
            try
            {
                if (!LazyConnection.IsValueCreated)
                {
                    if (configurationOptions == null)
                    {
                        string result = SettingsConfigHelper.AppSetting("redis") ?? "localhost";
                        configurationOptions = new ConfigurationOptions
                        {
                            EndPoints = { result },
                            SyncTimeout = 1000 * 60 * 10,
                            AbortOnConnectFail = false,
                            ConnectRetry = 10,
                            ConnectTimeout = 30,
                            AllowAdmin = true   //下发指令必须使用允许管理员模式
                        };
                    }
                    LazyConnection = new Lazy<ConnectionMultiplexer>(() => ConnectionMultiplexer.Connect(configurationOptions));
                }
                return LazyConnection.Value;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        internal static IDatabase RedisCache
        {
            get
            {
                if (Connection.IsConnected)
                    return Connection.GetDatabase();
                else
                    return null;
            }
        }

        internal static IServer Server
        {
            get
            {
                if (Connection.IsConnected)
                {
                    var r = Connection.GetEndPoints()
                                       .Select(endpoint =>
                                       {
                                           var server = Connection.GetServer(endpoint);
                                           return server;
                                       }).FirstOrDefault();
                    return r;
                }
                else
                {
                    return null;
                }

            }
        }
    }
}
