using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志RabbitMQ服务接口，定义日志消息的发布方法
    /// </summary>
    public interface IMQRabbitMQService
    {
        /// <summary>
        /// 发布接口日志
        /// </summary>
        Task PublishInterfaceLogAsync(object logData);
        
        /// <summary>
        /// 发布错误日志
        /// </summary>
        Task PublishErrorLogAsync(object logData);
    }
} 