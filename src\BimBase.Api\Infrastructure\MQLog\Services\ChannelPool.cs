using RabbitMQ.Client;
using System.Collections.Concurrent;
using System.Threading;
using System;

namespace BimBase.Api.Infrastructure.MQLog.Services
{
    public class ChannelPool : IChannelPool
    {
        private readonly IConnection _connection;
        private readonly ConcurrentBag<IModel> _channels = new();
        private readonly int _maxSize;
        private int _count;

        public ChannelPool(IConnection connection, int maxSize = 100)
        {
            _connection = connection;
            _maxSize = maxSize;
        }

        public IModel Rent()
        {
            if (_channels.TryTake(out var channel) && channel.IsOpen)
                return channel;

            if (Interlocked.Increment(ref _count) <= _maxSize)
                return _connection.CreateModel();

            Interlocked.Decrement(ref _count);
            throw new InvalidOperationException("Channel pool exhausted");
        }

        public void Return(IModel channel)
        {
            if (channel.IsOpen)
                _channels.Add(channel);
            else
                channel.Dispose();
        }

        public void Dispose()
        {
            foreach (var channel in _channels)
                channel.Dispose();
        }
    }
} 