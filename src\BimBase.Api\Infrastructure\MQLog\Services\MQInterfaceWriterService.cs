using System.Threading.Tasks;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.MQLog;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using BimBase.Api.Infrastructure.Services;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志写入服务实现，负责自动补全日志字段并通过RabbitMQ发送日志
    /// </summary>
    public class MQInterfaceWriterService : IMQInterfaceWriterService
    {
        private readonly IMQRabbitMQService _rabbitMQService;
        private readonly IConfiguration _config;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public MQInterfaceWriterService(IMQRabbitMQService rabbitMQService, IConfiguration config, IHttpContextAccessor httpContextAccessor)
        {
            _rabbitMQService = rabbitMQService;
            _config = config;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 写入接口日志，自动补全字段后发送到MQ
        /// </summary>
        public async Task WriteInterfaceLogAsync(MQInterfaceLog log)
        {
          //  MQLogEnricher.EnrichInterfaceLog(log, _httpContextAccessor.HttpContext, _config);
            await _rabbitMQService.PublishInterfaceLogAsync(log);
        }

    }
} 