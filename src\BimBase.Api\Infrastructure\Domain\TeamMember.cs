﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    [XmlRoot(Namespace = "")]
    public class TeamMember
    {
        public TeamMember()
        {
            ID = Guid.NewGuid();
            CreateId = Guid.Empty;
        }

        public int CompareTo(object obj)
        {
            return LoginName.CompareTo((obj as TeamMember).LoginName);
        }

        [Key]
        public Guid ID { get; set; }
        /// <summary>
        /// 登陆名。
        /// </summary>
        [Required]
        public String LoginName { get; set; }
        /// <summary>
        /// 登陆密码的MD5。
        /// </summary>
        public String PasswordMD5 { get; set; }
        /// <summary>
        /// 显示名称。
        /// </summary>
        public String DisplayName { get; set; }

        public int Color { get; set; }

        public string Email { get; set; }

        public string Department { get; set; }

        public string Avatar { get; set; }

        public Guid CreateId { get; set; }
        /// <summary>
        /// 头像图片数据
        /// </summary>
        public byte[] AvatarData { get; set; }
        /// <summary>
        /// 图片扩展名
        /// </summary>
        public string AvatarExtType { get; set; }
        /// <summary>
        /// 角色集合。
        /// </summary>

        //virtual public List<TeamProject> Projects { get; set; }
    }
}
