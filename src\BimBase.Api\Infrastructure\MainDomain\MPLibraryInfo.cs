﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public enum LibType
    {
        /// <summary>
        /// 元件库和等级库
        /// </summary>
        enCatalog_Lib = 0,
        /// <summary>
        /// 参数化组件库
        /// </summary>
        enPara_Comp_Lib = 1,
        /// <summary>
        /// 图纸和报表库
        /// </summary>
        enDraw_Report_Lib = 2,
        /// <summary>
        /// 设计配置库
        /// </summary>
        enDesign_Config_Lib = 3,
        /// <summary>
        /// 公共资源库
        /// </summary>
        enPublic_Resource_Lib = 4,

        /// <summary>
        /// 类型及属性库
        /// </summary>
        enType_Attribute_Lib = 5



    }

    /// <summary>
    /// 库信息表（元件库，等级库，组件库等等）
    /// </summary>
    [XmlRoot(Namespace = "")]
    public class MPLibraryInfo
    {
        public MPLibraryInfo()
        {
            CreateTime = DateTime.Now;
        }
        /// <summary>
        /// 库表示Guid，由服务端创建后返回
        /// </summary>
        
        [Key]
        public Guid LibId { get; set; }
        /// <summary>
        /// name
        /// </summary>
        
        public string LibName { get; set; }
        /// <summary>
        /// description
        /// </summary>
        
        public string LibDescription { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        
        public DateTime CreateTime { get; set; }


        
        [Required, EnumDataType(typeof(LibType))]
        public LibType LibType { get; set; }
        /// <summary>
        /// 预留扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }
    }

    /// <summary>
    /// 元件库，等级库，组件库等等锁定表
    /// </summary>
    public class MPLibLock
    {
        /// <summary>
        /// 自增id
        /// </summary>
        [Key]
        
        public int Id { get; set; }
        /// <summary>
        /// 锁定人loginname
        /// </summary>
        
        public string LockUserName { get; set; }
        /// <summary>
        /// 库guid
        /// </summary>
        
        public Guid LibId { get; set; }

        /// <summary>
        /// 锁类型 0：独占锁，1：共享锁
        /// </summary>
        
        public int LockType { get; set; }

    }
}
