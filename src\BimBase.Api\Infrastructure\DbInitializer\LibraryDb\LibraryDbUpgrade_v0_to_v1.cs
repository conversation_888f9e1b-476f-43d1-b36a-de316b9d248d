using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using BimBase.Api.Infrastructure.LibDomain;
using System;
using System.Linq;

namespace BimBase.Api.Infrastructure.DbInitializer.LibraryDb
{
    /// <summary>
    /// LibraryDb从v0升级到v1
    /// 升级内容：
    /// 1. 创建数据库基础结构（如果需要）
    /// 2. 初始化基础数据
    /// </summary>
    public class LibraryDbUpgrade_v0_to_v1 : AbstractLibraryDbUpgrade
    {
        public LibraryDbUpgrade_v0_to_v1(ILogger logger) : base(logger) { }

        public override string FromVersion => "v0";
        public override string ToVersion => "v1";
        public override string Description => "LibraryDb初始化升级 - 创建基础数据库结构";

        protected override async Task ExecuteUpgradeAsync(LibraryDbContext context)
        {
            Logger.LogInformation("开始执行LibraryDb v0->v1升级");

            // 1. 确保数据库结构存在
            Logger.LogInformation("检查并创建数据库结构...");
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                Logger.LogInformation("LibraryDb数据库结构已创建");
            }
            else
            {
                Logger.LogInformation("LibraryDb数据库结构已存在，开始执行升级");

                // 2. 创建DatabaseVersion表（版本管理基础设施）
                await EnsureDatabaseVersionTableAsync(context);

                // 3. 创建ClientVersionCompatibility表（如不存在）
                await EnsureClientVersionCompatibilityTableAsync(context);

                // 4. 其他表结构升级（如有）
            }

            // 5. 初始化基础数据
            await InitializeBasicDataAsync(context);

            Logger.LogInformation("LibraryDb v0->v1升级完成");
        }

        /// <summary>
        /// 确保DatabaseVersion表存在
        /// </summary>
        private async Task EnsureDatabaseVersionTableAsync(LibraryDbContext context)
        {
            var tableDefinition = @"
                    `Id` INT NOT NULL AUTO_INCREMENT,
                    `Version` VARCHAR(50) NOT NULL,
                    `UpgradedAt` DATETIME NOT NULL,
                    `Description` VARCHAR(500) NULL,
                    PRIMARY KEY (`Id`),
                    UNIQUE INDEX `UX_Version` (`Version`)
                ";
            await CreateTableIfNotExistsAsync(context, "DatabaseVersion", tableDefinition);
        }

        /// <summary>
        /// 确保ClientVersionCompatibility表存在
        /// </summary>
        private async Task EnsureClientVersionCompatibilityTableAsync(LibraryDbContext context)
        {
            var tableDefinition = @"
                    `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                    `ClientId` varchar(40) NULL COMMENT '客户端ID',
                    `ClientVersion` varchar(40) NULL COMMENT '客户端版本',
                    `MinCompatibleVersion` varchar(40) NULL COMMENT '可兼容的最小版本',
                    PRIMARY KEY (`ID`),
                    INDEX `IDX_CLIENT_ID` (`ClientId`)";
            await CreateTableIfNotExistsAsync(context, "clientversioncompatibility", tableDefinition);
        }

        /// <summary>
        /// 创建表（如果不存在）
        /// </summary>
        private async Task CreateTableIfNotExistsAsync(LibraryDbContext context, string tableName, string tableDefinition)
        {
            if (!await TableExistsAsync(context, tableName))
            {
                var sql = $"CREATE TABLE `{tableName}` ({tableDefinition}) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已创建表: {tableName}");
            }
            else
            {
                Logger.LogInformation($"表已存在，跳过创建: {tableName}");
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private async Task<bool> TableExistsAsync(LibraryDbContext context, string tableName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}'";
            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        /// <summary>
        /// 初始化基础数据
        /// </summary>
        private async Task InitializeBasicDataAsync(LibraryDbContext context)
        {
            Logger.LogInformation("开始初始化LibraryDb基础数据...");
            // 初始化ClientVersionCompatibilities数据
            if (!await context.ClientVersionCompatibilities.AnyAsync())
            {
                context.ClientVersionCompatibilities.Add(new BimBase.Api.Infrastructure.LibDomain.ClientVersionCompatibility
                {
                    ClientId = "PKPM-Plant",
                    ClientVersion = "BIMBase-2025R01.01",
                    MinCompatibleVersion = "BIMBase-2025R01.01"
                });
                Logger.LogInformation("添加初始客户端版本兼容性配置: PKPM-Plant");
                await context.SaveChangesAsync();
            }
            else
            {
                // 检查是否已存在PKPM-Plant的配置
                var existingConfig = await context.ClientVersionCompatibilities
                    .FirstOrDefaultAsync(c => c.ClientId == "PKPM-Plant" && c.ClientVersion == "BIMBase-2025R01.01");

                if (existingConfig == null)
                {
                    context.ClientVersionCompatibilities.Add(new BimBase.Api.Infrastructure.LibDomain.ClientVersionCompatibility
                    {
                        ClientId = "PKPM-Plant",
                        ClientVersion = "BIMBase-2025R01.01",
                        MinCompatibleVersion = "BIMBase-2025R01.01"
                    });
                    // 不在这里调用SaveChangesAsync，让外层事务统一处理
                    Logger.LogInformation("准备插入PKPM-Plant客户端版本兼容性配置");
                }
                else
                {
                    Logger.LogInformation("PKPM-Plant客户端版本兼容性配置已存在，跳过插入");
                }
            }
            Logger.LogInformation("LibraryDb基础数据初始化完成");
            await Task.CompletedTask;
        }
    }
} 