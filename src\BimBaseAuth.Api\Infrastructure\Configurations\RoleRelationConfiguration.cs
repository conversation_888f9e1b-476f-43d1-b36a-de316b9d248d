﻿using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Configurations
{
    public partial class RoleRelationConfiguration : IEntityTypeConfiguration<RoleRelation>
    {
        public void Configure(EntityTypeBuilder<RoleRelation> entity)
        {
            entity.ToTable("rolerelation");
            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.AppId)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.Auth)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.CreateId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.RoleId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<RoleRelation> entity);
    }
}
