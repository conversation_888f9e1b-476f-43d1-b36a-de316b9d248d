using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb升级管理器，负责计算和执行升级路径
    /// </summary>
    public class TeamDbUpgradeManager
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, Dictionary<string, ITeamDbUpgrade>> _upgradeMap;

        public TeamDbUpgradeManager(ILogger logger)
        {
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger.Instance;
            _upgradeMap = BuildUpgradeMap();
        }

        /// <summary>
        /// 构建升级映射表
        /// </summary>
        private Dictionary<string, Dictionary<string, ITeamDbUpgrade>> BuildUpgradeMap()
        {
            var map = new Dictionary<string, Dictionary<string, ITeamDbUpgrade>>();

            // 注册所有升级类
            var upgrades = new List<ITeamDbUpgrade>
            {
                new TeamDbUpgrade_v0_to_v1(_logger),
                new TeamDbUpgrade_v1_to_v2(_logger),
                new TeamDbUpgrade_v2_to_v3(_logger),
                new TeamDbUpgrade_v3_to_v4(_logger),
                new TeamDbUpgrade_v4_to_v5(_logger),
                new TeamDbUpgrade_v5_to_v6(_logger),
                new TeamDbUpgrade_v6_to_v7(_logger)
            };

            foreach (var upgrade in upgrades)
            {
                if (!map.ContainsKey(upgrade.SourceVersion))
                {
                    map[upgrade.SourceVersion] = new Dictionary<string, ITeamDbUpgrade>();
                }
                map[upgrade.SourceVersion][upgrade.TargetVersion] = upgrade;
            }

            return map;
        }

        /// <summary>
        /// 计算升级路径
        /// </summary>
        /// <param name="currentVersion">当前版本</param>
        /// <param name="targetVersion">目标版本</param>
        /// <returns>升级路径列表</returns>
        public List<ITeamDbUpgrade> CalculateUpgradePath(string currentVersion, string targetVersion)
        {
            if (string.IsNullOrEmpty(currentVersion) || string.IsNullOrEmpty(targetVersion))
            {
                throw new ArgumentException("版本不能为空");
            }

            if (currentVersion == targetVersion)
            {
                return new List<ITeamDbUpgrade>();
            }

            // 使用广度优先搜索查找最短升级路径
            var path = FindShortestUpgradePath(currentVersion, targetVersion);
            
            if (path == null || !path.Any())
            {
                throw new InvalidOperationException($"无法找到从 {currentVersion} 到 {targetVersion} 的升级路径");
            }

            return path;
        }

        /// <summary>
        /// 计算回滚路径
        /// </summary>
        /// <param name="currentVersion">当前版本</param>
        /// <param name="targetVersion">目标版本</param>
        /// <returns>回滚路径列表</returns>
        public List<ITeamDbUpgrade> CalculateRollbackPath(string currentVersion, string targetVersion)
        {
            if (string.IsNullOrEmpty(currentVersion) || string.IsNullOrEmpty(targetVersion))
            {
                throw new ArgumentException("版本不能为空");
            }

            if (currentVersion == targetVersion)
            {
                return new List<ITeamDbUpgrade>();
            }

            // 获取从目标版本到当前版本的升级路径，然后反转
            var upgradePath = FindShortestUpgradePath(targetVersion, currentVersion);
            
            if (upgradePath == null || !upgradePath.Any())
            {
                throw new InvalidOperationException($"无法找到从 {currentVersion} 到 {targetVersion} 的回滚路径");
            }

            // 反转路径顺序，用于回滚
            upgradePath.Reverse();
            return upgradePath;
        }

        /// <summary>
        /// 执行升级路径
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="upgradePath">升级路径</param>
        public async Task ExecuteUpgradePathAsync(TeamDbContext context, List<ITeamDbUpgrade> upgradePath)
        {
            _logger.LogInformation($"开始执行升级路径，包含 {upgradePath.Count} 个步骤");

            foreach (var upgrade in upgradePath)
            {
                _logger.LogInformation($"执行升级: {upgrade.SourceVersion} -> {upgrade.TargetVersion}");
                await upgrade.UpgradeAsync(context);
            }

            _logger.LogInformation("升级路径执行完成");
        }

        /// <summary>
        /// 执行回滚路径
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <param name="rollbackPath">回滚路径</param>
        public async Task ExecuteRollbackPathAsync(TeamDbContext context, List<ITeamDbUpgrade> rollbackPath)
        {
            _logger.LogInformation($"开始执行回滚路径，包含 {rollbackPath.Count} 个步骤");

            foreach (var upgrade in rollbackPath)
            {
                _logger.LogInformation($"执行回滚: {upgrade.TargetVersion} -> {upgrade.SourceVersion}");
                await upgrade.RollbackAsync(context);
            }

            _logger.LogInformation("回滚路径执行完成");
        }

        /// <summary>
        /// 使用广度优先搜索查找最短升级路径
        /// </summary>
        private List<ITeamDbUpgrade> FindShortestUpgradePath(string start, string end)
        {
            var queue = new Queue<string>();
            var visited = new HashSet<string>();
            var predecessors = new Dictionary<string, (string version, ITeamDbUpgrade upgrade)>();

            queue.Enqueue(start);
            visited.Add(start);

            while (queue.Count > 0)
            {
                var current = queue.Dequeue();

                if (current == end)
                    break;

                if (_upgradeMap.ContainsKey(current))
                {
                    foreach (var (nextVersion, upgrade) in _upgradeMap[current])
                    {
                        if (!visited.Contains(nextVersion))
                        {
                            queue.Enqueue(nextVersion);
                            visited.Add(nextVersion);
                            predecessors[nextVersion] = (current, upgrade);
                        }
                    }
                }
            }

            if (!predecessors.ContainsKey(end) && start != end)
                return null;

            // 重建路径
            var path = new List<ITeamDbUpgrade>();
            var currentVersion = end;

            while (predecessors.ContainsKey(currentVersion))
            {
                var (prevVersion, upgrade) = predecessors[currentVersion];
                path.Add(upgrade);
                currentVersion = prevVersion;
            }

            path.Reverse();
            return path;
        }


    }
} 