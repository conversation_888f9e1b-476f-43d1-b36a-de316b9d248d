﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public static class BuildinAdministrators
    {
        static String _sBuildInAdminRole = "Administrators";
        static public String BuildInAdministratorRoleName
        {
            get { return _sBuildInAdminRole; }
            set { _sBuildInAdminRole = value; }
        }

        static String _buildInTeamAdminUser = "admin";

        static String _buildInTeamAdminPassword = "admin";

        static Guid _buildInAdminGuid = Guid.Parse("1284529a-cc6e-43f0-b817-4b8ac862d926");

        static String _buildInAdminAvatar = "/resources/images/defautAvatar.png";

        static String _buildInSystemAdmin = "system";

        static Guid _buildInSystemAdminGuid = Guid.Parse("e68488c9-6058-4881-b075-16cb7cf4f77d");

        static String _buildInSystemAdminPassword = "system@pkpm";

        static String _buildInSystemAdminAvatar = "/resources/images/defautAvatar.png";

        static String _buildInSystemModelAdmin = "systemmodel";

        static Guid _buildInSystemModelAdminGuid = Guid.Parse("e79a548e-e31f-465c-a12a-************");

        static String _buildInSystemModelAdminPassword = "systemmodel@pkpm";

        static Guid _buildinTeamAdminRoleGuid = Guid.Parse("96539b22-ba10-4099-baee-27ce31a80e4e");

        static Guid _buildinSystemAdminRoleGuid = Guid.Parse("c052f969-ca78-42d6-89a8-54e7eda77327");

        static Guid _buildinProjectAdminRoleGuid = Guid.Parse("acc3e016-4d39-465e-ba00-8d128fc58257");

        public static Guid BuildInTeamAdministratorRoleGuid
        {
            get { return _buildinTeamAdminRoleGuid; }
        }

        public static Guid BuildInSystemAdministratorRoleGuid
        {
            get { return _buildinSystemAdminRoleGuid; }
        }

        public static Guid BuildInProjectAdministratorRoleGuid
        {
            get { return _buildinProjectAdminRoleGuid; }
        }

        public static String BuildInTeamAdministratorLoginName
        {
            get { return _buildInTeamAdminUser; }
        }


        public static String BuildInTeamAdministratorPassword
        {
            get { return _buildInTeamAdminPassword; }
        }
        public static Guid BuildInAdministratorGuid
        {
            get { return _buildInAdminGuid; }
        }
        public static String BuildInAdministratorAvatar
        {
            get { return _buildInAdminAvatar; }
        }

        public static String BuildInSystemAdminLoginName
        {
            get { return _buildInSystemAdmin; }
        }

        public static Guid BuildInSystemAdminGuid
        {
            get { return _buildInSystemAdminGuid; }
        }
        public static String BuildInSystemAdminAvatar
        {
            get { return _buildInSystemAdminAvatar; }
        }
        public static String BuildInSystemAdminPassword
        {
            get { return _buildInSystemAdminPassword; }
        }
        public static String BuildInSystemModelAdminLoginName
        {
            get { return _buildInSystemModelAdmin; }
        }

        public static Guid BuildInSystemModelAdminGuid
        {
            get { return _buildInSystemModelAdminGuid; }
        }
        public static String BuildInSystemModelAdminPassword
        {
            get { return _buildInSystemModelAdminPassword; }
        }

    }
}
