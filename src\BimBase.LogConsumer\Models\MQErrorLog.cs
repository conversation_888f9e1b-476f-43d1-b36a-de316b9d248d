using System;
using System.Text.Json.Serialization;

namespace BimBase.LogConsumer.Models
{
    /// <summary>
    /// MQ错误信息日志模型
    /// </summary>
    public class MQErrorLog
    {
        public int Id { get; set; }
        /// <summary>
        /// 错误ID，系统生成唯一 ID
        /// </summary>
        [JsonPropertyName("errorId")]
        public string ErrorId { get; set; }
        /// <summary>
        /// 请求ID
        /// </summary>
        [JsonPropertyName("requestId")]
        public string RequestId { get; set; }
        /// <summary>
        /// 项目id，程序获取
        /// </summary>
        [JsonPropertyName("projectId")]
        public string ProjectId { get; set; }
        /// <summary>
        /// 项目名称，程序获取
        /// </summary>
        [JsonPropertyName("projectName")]
        public string ProjectName { get; set; }
        /// <summary>
        /// sessid，程序获取
        /// </summary>
        [JsonPropertyName("sessionId")]
        public string SessionId { get; set; }
        /// <summary>
        /// 客户端IP，从HttpContext.Connection.RemoteIpAddress获取，注意长度255，入库要截断
        /// </summary>
        [JsonPropertyName("clientIp")]
        public string ClientIp { get; set; }
        /// <summary>
        /// 服务端IP
        /// </summary>
        [JsonPropertyName("serverIp")]
        public string ServerIp { get; set; }
        /// <summary>
        /// 服务端名称
        /// </summary>
        [JsonPropertyName("serverName")]
        public string ServerName { get; set; }
        /// <summary>
        /// 用户ID，程序获取
        /// </summary>
        [JsonPropertyName("userId")]
        public string UserId { get; set; }
        /// <summary>
        /// 用户名，程序获取
        /// </summary>
        [JsonPropertyName("userName")]
        public string UserName { get; set; }
        /// <summary>
        /// 应用名称,从配置文件中获取
        /// </summary>
        [JsonPropertyName("appName")]
        public string AppName { get; set; }
        /// <summary>
        /// 应用版本,从配置文件中获取
        /// </summary>
        [JsonPropertyName("appVersion")]
        public string AppVersion { get; set; }
        /// <summary>
        /// 环境,从配置文件中获取
        /// </summary>
        [JsonPropertyName("environment")]
        public string Environment { get; set; }
        /// <summary>
        /// 日志级别，程序获取
        /// </summary>
        [JsonPropertyName("logLevel")]
        public string LogLevel { get; set; }
        /// <summary>
        /// 日志类型，程序获取
        /// </summary>
        [JsonPropertyName("logType")]
        public string LogType { get; set; }
        /// <summary>
        /// 错误类型，获取异常对象类型名称
        /// </summary>
        [JsonPropertyName("errorType")]
        public string ErrorType { get; set; }
        /// <summary>
        /// 错误消息，获取异常对象的Message属性
        /// </summary>
        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 错误代码，程序获取
        /// </summary>
        [JsonPropertyName("errorCode")]
        public string ErrorCode { get; set; }
        /// <summary>
        /// 错误堆栈，获取异常对象的StackTrace属性
        /// </summary>
        [JsonPropertyName("errorStackTrace")]
        public string ErrorStackTrace { get; set; }
        /// <summary>
        /// 输入参数，从方法调用处获取参数值
        /// </summary>
        [JsonPropertyName("inputParams")]
        public string InputParams { get; set; }
        /// <summary>
        /// 源类名，从异常堆栈信息解析
        /// </summary>
        [JsonPropertyName("sourceClassName")]
        public string SourceClassName { get; set; }
        /// <summary>
        /// 源方法名，从异常堆栈信息解析
        /// </summary>
        [JsonPropertyName("sourceMethodName")]
        public string SourceMethodName { get; set; }
        /// <summary>
        /// 附加数据
        /// </summary>
        [JsonPropertyName("additionalData")]
        public string AdditionalData { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        [JsonPropertyName("addTime")]
        public DateTime? AddTime { get; set; }
    }
} 