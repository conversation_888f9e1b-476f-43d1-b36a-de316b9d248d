﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;
using BimBase.Api.Infrastructure.MainDomain;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.LibDomain
{
    [XmlRoot(Namespace = "")]
    public class CLCompanyLibInfo
    {
        public CLCompanyLibInfo()
        {
            CreateTime = DateTime.Now;
        }
        /// <summary>
        /// 库表示Guid，由服务端创建后返回
        /// </summary>
        
        [Key]
        public Guid LibId { get; set; }
        /// <summary>
        /// name
        /// </summary>
        
        public string LibName { get; set; }
        /// <summary>
        /// description
        /// </summary>
        
        public string LibDescription { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        
        public string CreateUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        
        public DateTime CreateTime { get; set; }


        
        [Required, EnumDataType(typeof(LibType))]
        public LibType LibType { get; set; }
        /// <summary>
        /// 预留扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }

        /// <summary>
        /// 客户端版本号
        /// </summary>
        public string ClientVersion { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string clientId { get; set; }
    }
}
