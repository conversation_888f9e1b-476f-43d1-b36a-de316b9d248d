using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.ModelDb
{
    /// <summary>
    /// ModelDb数据库升级接口
    /// </summary>
    public interface IModelDbUpgrade
    {
        /// <summary>
        /// 源版本
        /// </summary>
        string FromVersion { get; }
        
        /// <summary>
        /// 目标版本
        /// </summary>
        string ToVersion { get; }
        
        /// <summary>
        /// 升级描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 执行升级
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <returns></returns>
        Task UpgradeAsync(ModelDbContext context);
        
        /// <summary>
        /// 回滚升级（如果支持）
        /// </summary>
        /// <param name="context">数据库上下文</param>
        /// <returns></returns>
        Task RollbackAsync(ModelDbContext context);
        
        /// <summary>
        /// 是否支持回滚
        /// </summary>
        bool SupportsRollback { get; }
    }
} 