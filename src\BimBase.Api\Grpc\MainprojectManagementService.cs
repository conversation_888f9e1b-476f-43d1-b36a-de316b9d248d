﻿using AutoMapper;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using Google.Protobuf;
using Grpc.Core;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Org.BouncyCastle.Ocsp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Policy;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using static BimBase.Api.Protos.GrpcMainprojectManagement;

namespace BimBase.Api.Grpc
{
    public class MainprojectManagementService:GrpcMainprojectManagementBase
    {
        private readonly UrlsConfig _urls;
        private readonly ITeamRepository _teamRepository;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        private readonly ILogger<MainprojectManagementService> _logger;
        private readonly ILibraryRepository _libRepository;
        private readonly char separator = Path.DirectorySeparatorChar;
        public MainprojectManagementService(IOptions<UrlsConfig> config, ITeamRepository teamRepository,ILibraryRepository libRepository
            , IMapper mapper
            , ILogger<MainprojectManagementService> logger)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _libRepository = libRepository?? throw new ArgumentNullException(nameof(libRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        private bool CreateInfoConfig(Guid projectGuid, string templateName)
        {
            var projectRepo = _teamRepository.GetProjectRepository(projectGuid);
            var infos = BimBaseServerTemplates.ReadTemplateFile<List<ReleaseInformation>>(BimBaseServerTemplates.ReleaseRoot + templateName + ".tplt");
            if (infos != null)
            {
                foreach (var info in infos)
                {
                    projectRepo.AddReleaseInformation(info);
                }
                return true;
            }
            return false;
        }

        private bool CreateProjectRoleConfig(Guid projectGuid, string username, string templateName, out string message)
        {
            message = string.Empty;

            var roleTemplates = BimBaseServerTemplates.ReadTemplateFile<List<RoleTemplate>>(BimBaseServerTemplates.ProjectRoleRoot + templateName + ".tplt");
            var teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == username);
            if (null == teamMember)
            {
                message = "用户登录失效";
                return false;
            }

            if (roleTemplates != null)
            {
                var defaultRole = roleTemplates.SingleOrDefault(r => r.Name == "项目默认角色");

                if (null == defaultRole)
                {
                    var defaultRoleTemplate = BimBaseServerConfigurations.ReadConfiguration<RoleTemplate>("RoleTemplate.config");
                    GrpcRole defRole = new GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = defaultRoleTemplate.Name,
                        Status = defaultRoleTemplate.Status,
                        Type = defaultRoleTemplate.Type
                    };


                    var ret = _authorityManager.AddProjectRole(projectGuid.ToString(), defRole, teamMember.ID, out message);

                    if (!ret)
                        return false;

                    ret = _authorityManager.GiveRoleAuth(defRole.Id, defaultRoleTemplate.AuthInfos, teamMember.ID, out message);
                    if (!ret)
                        return false;
                }
                foreach (var roleTemplate in roleTemplates)
                {
                    GrpcRole role = new GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = roleTemplate.Name,
                        Status = roleTemplate.Status,
                        Type = roleTemplate.Type
                    };

                    var ret = _authorityManager.AddProjectRole(projectGuid.ToString(), role, teamMember.ID, out message);
                    if (!ret)
                        return false;

                    ret = _authorityManager.GiveRoleAuth(role.Id, roleTemplate.AuthInfos, teamMember.ID, out message);
                    if (!ret)
                        return false;
                }
                return true;
            }
            return false;

        }
        private bool AddDefaultUserToRole(Infrastructure.Domain.TeamProject prj, string templateName, Infrastructure.Domain.TeamMember defaultMember, out string message)
        {
            var sw = new Stopwatch();
            sw.Start();
            List<GrpcRole> roles;
            message = "";
            var ret = _authorityManager.GetProjectRole(prj.ID.ToString(), out roles, out message);
            if (!ret)
                return false;
            var role = roles.SingleOrDefault(r => r.Name == "项目默认角色");

            var members = BimBaseServerTemplates.ReadTemplateFile<List<Infrastructure.Domain.TeamMember>>(BimBaseServerTemplates.ProjectMemberRoot + templateName + ".tplt");
            if (members != null)
            {
                var memList = _teamRepository.Members.ToList().FindAll(mem => (members.Exists(member => member.LoginName == mem.LoginName)));
                foreach (var member in memList)
                {
                    _teamRepository.AddMemberToProject(member, prj);

                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { member.ID },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                sw.Stop();
                ////PbimLog.Info("AddDefaultUserToRole===>AddMemberToProject====>" + sw.ElapsedMilliseconds);
                sw.Restart();
                if (!members.Exists(m => m.LoginName == defaultMember.LoginName))
                {
                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { defaultMember.ID },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                if (!members.Exists(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName))
                {
                    if (null != role)
                    {
                        ret = _authorityManager.GiveRoleUser(role.Id, new List<Guid> { BuildinAdministrators.BuildInAdministratorGuid },
                            BuildinAdministrators.BuildInAdministratorGuid, out message);
                        if (!ret)
                            return false;
                    }
                }
                sw.Stop();
                return true;
            }
            return false;
        }

        public override async Task<GrpcResult> CheckServiceAlive(SessionRequest request, ServerCallContext context)
        {
            
            GrpcResult x = new GrpcResult();
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                _logger.LogInformation($"CheckServiceAlive:{request.SessionId} 已经失效");
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                _logger.LogInformation($"CheckServiceAlive:{request.SessionId} 已经失效：x.message:{x.Message}");
                x.IsSuccess = false;
                return x;
            }
            x.IsSuccess = true;
            return x;
        }
        public override async Task<GrpcResult> RemoveBPExobjectFromProvideVersion(RemoveBPExobjectFromProvideVersionRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mainVersionName = request.MainVersionName;
            var subVersionName = request.SubVersionName; 
            var username = currentUser.LoginName;
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var exsist = mainProjectManager.ProvideVersionInfoes.FirstOrDefault(s => s.VersionName == mainVersionName
            && s.SubVersionName == subVersionName);
            if (exsist == null)
            {
                x.IsSuccess = true;
                return x;
            }
            if (exsist.CheckState >= 1)
            {
                x.Message = "不能删除已经发布的版本";
                x.IsSuccess = false;
                return x;
            }
            //删除版本中的物项
            mainProjectManager.RemoveBPExObjectPublishsWithVersionName( mainVersionName, subVersionName);

            mainProjectManager.UpdateProvideVersionItemCount(mainVersionName, subVersionName, 0);
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 创建提资版本
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> CreateProvideVersion(CreateProvideVersionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            try
            {
                var provideVersionInfo = _mapper.Map<ProvideVersionInfo>(request.ProvideVersionInfo);
                var username = currentUser.LoginName;
                var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
                var exsist = mainProjectManager.ProvideVersionInfoes.FirstOrDefault(s => s.VersionName == provideVersionInfo.VersionName
                && s.SubVersionName == provideVersionInfo.SubVersionName);
                if (exsist != null)
                {
                    x.Message = "版本名称重复";
                    return x;
                }
                provideVersionInfo.CreateTime = DateTime.Now;
                provideVersionInfo.CreateUser = username;
                var ret = mainProjectManager.AddProvideVersionInfo(provideVersionInfo);
                x.IsSuccess = ret;
            }
            catch (Exception ex)
            {
                x.IsSuccess = false;
                x.Message = ex.Message;
                _logger.LogInformation("CreateProvideVersion err==>"+ex.Message);
            }
            
            return x;
        }

        /// <summary>
        /// 删除尚未发布的版本
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> RemoveProvideVersion(RemoveProvideVersionRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mainVersionName = request.MainVersionName;
            var subVersionName = request.SubVersionName;
            var username = currentUser.LoginName;
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var exsist = mainProjectManager.ProvideVersionInfoes.FirstOrDefault(s => s.VersionName == mainVersionName
            && s.SubVersionName == subVersionName);
            if (exsist == null)
            {
                x.IsSuccess = true;
                return x;
            }
            if (exsist.CheckState >= 1)
            {
                x.Message = "不能删除已经发布的版本";
                x.IsSuccess = false;
                return x;
            }
            mainProjectManager.RemoveProvideVersionInfo(mainVersionName, subVersionName);
            //删除版本中的
            mainProjectManager.RemoveBPExObjectPublishsWithVersionName(mainVersionName, subVersionName);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 上传提资数据到版本中
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> UploadBPExObjectPublishToVersion(UploadBPExObjectPublishToVersionRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mainVersionName = request.MainVersionName;
            var subVersionName = request.SubVersionName;
            var username = currentUser.LoginName;
            var isSync = request.IsSync;
            var publishInfoList = _mapper.Map<List<BPExObjectPublish>>(request.PublishInfoList);
            var deleteInfoList = _mapper.Map<List<BPExObjectPublish>>(request.DeleteInfoList);
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (isSync)
            {
                //需要同步到提资信息表
                //将BPExObjectPublish 拼装出BPExObject
                if (publishInfoList != null)
                {
                    List<BPExObject> bpexobjectlist = publishInfoList.Select(p => new BPExObject
                    {
                        ProvideUser = p.ProvideUser,
                        ProvideTime = p.ProvideTime,
                        ProvideNotes = p.ProvideNotes,
                        ProvideState = p.ProvideState,
                        AcceptUser = p.AcceptUser,
                        AcceptTime = p.AcceptTime,
                        AcceptState = p.AcceptState,
                        StructNotes = p.StructNotes,
                        Type = p.Type,
                        Domain = p.Domain,
                        LoadName = p.LoadName,
                        GUID = p.GUID,
                        ExtendField = p.ExtendField
                    }).ToList();
                    mainProjectManager.AddBPExObjects(bpexobjectlist);
                }

            }
                    ;
            var ret = mainProjectManager.CheckInBPExObjectPublishsWithVersionName(publishInfoList, deleteInfoList, mainVersionName, subVersionName);
            //更新提资物数量
            var bpExObjectCount = mainProjectManager.BPExObjectPublishs.Where(s => s.VersionName == mainVersionName && s.SubVersionName == subVersionName).ToList();
            var count = bpExObjectCount.Count();
            mainProjectManager.UpdateProvideVersionItemCount(mainVersionName, subVersionName, count);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 发布提资版本（提资发布人需与版本创建人相同）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> PublishProvideVersion(PublishProvideVersionRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            int state = request.State;
            var mainVersionName = request.MainVersionName;
            var subVersionName = request.SubVersionName;
            var username = currentUser.LoginName;
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var exsist = mainProjectManager.ProvideVersionInfoes.FirstOrDefault(s => s.VersionName == mainVersionName
            && s.SubVersionName == subVersionName);
            if (exsist == null)
            {
                x.Message = "发布提资版本失败,未找到该版本";
                x.IsSuccess = false;
                return x;
            }
            //if (exsist.CreateUser != username)
            //{
            //    x.IsSuccess = false;
            //    x.Message = "发布提资版本失败,只有版本创建人可以发布版本";
            //    return x;
            //}
            var bpExObjectCount = mainProjectManager.BPExObjectPublishs.Where(s => s.VersionName == mainVersionName && s.SubVersionName == subVersionName).ToList();
            var count = bpExObjectCount.Count();
            mainProjectManager.PublishProvideVersion(mainVersionName, subVersionName, state, username, count);
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GrpcResult> UpdateProvideVersionInfo(UpdateProvideVersionInfoRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            int state = request.State;
            var mainVersionName = request.MainVersionName;
            var subVersionName = request.SubVersionName;
            var username = currentUser.LoginName;
            var newVersionInfo = _mapper.Map<ProvideVersionInfo>(request.NewVersionInfo);
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var exsist = mainProjectManager.ProvideVersionInfoes.FirstOrDefault(s => s.VersionName == mainVersionName
            && s.SubVersionName == subVersionName);
            if (exsist == null)
            {
                x.Message = "修改提资版本失败,未找到该版本";
                x.IsSuccess = false;
                return x;
            }
            mainProjectManager.ModifyProvideVersion(mainVersionName, subVersionName, state, newVersionInfo);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 上传提资信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> UploadProvideInfomationData(UploadProvideInfomationDataRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var provideDataList = _mapper.Map<List<BPExObject>>(request.ProvideDataList);
            var username = currentUser.LoginName;
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var ret = mainProjectManager.AddBPExObjects(provideDataList);
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GrpcResult> RemoveProvideInfomationData(RemoveProvideInfomationDataRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var deleteGuidList = _mapper.Map<List<Guid>>(request.DeleteGuidList);
            var username = currentUser.LoginName;
            var mainProjectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var ret = mainProjectManager.DeleteBPExObjects(deleteGuidList);
            x.IsSuccess = ret;
            return x;
        }

        /// <summary>
        /// 添加项目用户组
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<AddMPUserGroupResponse> AddMPUserGroup(AddMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new AddMPUserGroupResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mPUserGroup = request.MPUserGroup;
            var username = currentUser.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var mpgroup = mainprojectManager.AddMPUserGroup(_mapper.Map<MPUserGroup>(mPUserGroup));
            if (mpgroup!=null)
            {
                x.IsSuccess = true;
                x.Mpgroup = _mapper.Map<GrpcMPUserGroup>(mpgroup);
            }
            
            return x;
        }
        /// <summary>
        /// 删除项目用户组
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> DeleteMPUserGroup(DeleteMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mPUserGroupId = request.MPUserGroupId;
            var username = currentUser.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var ret = mainprojectManager.DeleteMPUserGroup(mPUserGroupId);
            
            x.IsSuccess = ret;
            return x;
        }
        /// <summary>
        /// 修改用户组（名称）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> UpdateMPUserGroup(UpdateMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mPUserGroup = request.MPUserGroup;
            var username = currentUser.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            var ret = mainprojectManager.UpdateMPUsergroup(_mapper.Map<MPUserGroup>(mPUserGroup));

            x.IsSuccess = ret;
            return x;
        }
        /// <summary>
        /// 向项目中的用户组添加成员
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddMemberToMPUserGroup(AddMemberToMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var mPUserGroupId = request.MPUserGroupId;
            var username = currentUser.LoginName;
            var memberIdList =_mapper.Map<List<Guid>>(request.MemberIdList);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            var mainproject = _teamRepository.MainProjects.FirstOrDefault(s => s.ID == mainprojectGuid);

            //获取要加入角色的成员对象列表
            List<TeamMember> members = memberTmp.Where(na => memberIdList.Contains(na.ID)).ToList();//.FindAll(mem => guidList.Exists(na => na == mem.ID));
            var mpUserGroup = mainprojectManager.MPUserGroups.FirstOrDefault(u => u.ID == mPUserGroupId);

            if (members.Any() && mpUserGroup != null)
            {
                List<MPMessage> messages = new List<MPMessage>();
                var memberIds = members.Select(u => u.ID).ToList();
                //查询已经加入的
                var groupMembers = mainprojectManager.MPUserGroupMembers
                    .Where(g => g.UserGroupId == mpUserGroup.ID && memberIds.Contains(g.TeamMemberId))
                    .Select(g => g.TeamMemberId).ToList();
                var toAddGuid = memberIds.Except(groupMembers).ToList();

                if (!groupMembers.Any())
                {
                    bool flag = mainprojectManager.AddTeamMemberListToMPUsergroup(members, mpUserGroup);
                    if (flag)
                    {
                        _teamRepository.AddTeamMemberListToMPUsergroup(memberIds, mpUserGroup.ID, mpUserGroup.UserGroupName ,mainprojectGuid);
                        _teamRepository.AddMemberListToMainProject(members, mainproject);
                        foreach (var name in members)
                        {
                            MPMessage pm = new MPMessage
                            {
                                FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                                ToUser = name.ID,
                                MPUserGroupId = 0,
                                MsgTextContent = "权限变更",
                                MsgState = 0,
                                MsgType = 8,//模型树节点权限变更
                                MsgCreateTime = DateTime.Now,
                                MsgExData = null,
                                SubProjectId = Guid.Empty
                            };
                            messages.Add(pm);
                        }
                    }

                }
                else
                {
                    var toAddMembers = members.Where(m => toAddGuid.Contains(m.ID)).ToList();
                    bool flag = mainprojectManager.AddTeamMemberListToMPUsergroup(toAddMembers, mpUserGroup);
                    if (flag)
                    {
                        _teamRepository.AddTeamMemberListToMPUsergroup(toAddGuid, mpUserGroup.ID, mpUserGroup.UserGroupName, mainprojectGuid);
                        _teamRepository.AddMemberListToMainProject(toAddMembers, mainproject);
                        foreach (var name in toAddMembers)
                        {
                            MPMessage pm = new MPMessage
                            {
                                FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                                ToUser = name.ID,
                                MPUserGroupId = 0,
                                MsgTextContent = "权限变更",
                                MsgState = 0,
                                MsgType = 8,//模型树节点权限变更
                                MsgCreateTime = DateTime.Now,
                                MsgExData = null,
                                SubProjectId = Guid.Empty
                            };
                            messages.Add(pm);
                        }
                    }

                }

                await Task.Run(() =>
                {
                    try
                    {
                        mainprojectManager.AddMPMessageList(messages);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInformation("AddMemberToMPUserGroup send authchange message error===>" + ex.Message);
                        //Common.BimException.ExceptionHandler.HandleException(username, ex);
                    }
                });
            }

            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 将用户从项目中的所有用户组中移除
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> RemoveMemberFromAllMPUserGroup(RemoveMemberFromAllMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            var memberIds = _mapper.Map<List<Guid>>(request.MemberIds);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            var allgroups = mainprojectManager.MPUserGroups.ToList();
            var allgroupsidlist = allgroups.Select(s => s.ID).ToList();
            List<MPMessage> messages = new List<MPMessage>();

            bool flag = mainprojectManager.RemoveTeamMemberListFromMPUserGroupList(memberIds, allgroups);
            if (flag)
            {
                _teamRepository.RemoveTeamMemberListFromMPUsergroupList(memberIds, allgroupsidlist, mainProjectGuid);

            }
            messages = memberTmp.Select(member => new MPMessage
            {
                FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                ToUser = member.ID,
                MPUserGroupId = 0,
                MsgTextContent = "权限变更",
                MsgState = 0,
                MsgType = 8,//模型树节点权限变更
                MsgCreateTime = DateTime.Now,
                MsgExData = null,
                SubProjectId = Guid.Empty
            }).ToList();

            
            await Task.Run(() =>
            {
                try
                {
                    mainprojectManager.AddMPMessageList(messages);

                }
                catch (Exception ex)
                {
                    _logger.LogInformation("RemoveMemberFromAllMPUserGroup send authchange message error===>" + ex.Message);
                }
            });

            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 从用户组中移除成员
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> RemoveMemberFromMPUserGroup(RemoveMemberFromMPUserGroupRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            var mPUserGroupId = request.MPUserGroupId;
            var memberIdList = _mapper.Map<List<Guid>>(request.MemberIdList);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            List<TeamMember> memberTmp = _teamRepository.Members.ToList();
            List<Guid> guidList = new List<Guid>();
            
            //获取要加入角色的成员对象列表
            List<TeamMember> members = memberTmp.Where(mem => memberIdList.Contains(mem.ID)).ToList();
            var mpUserGroup = mainprojectManager.MPUserGroups.FirstOrDefault(u => u.ID == mPUserGroupId);
            if (members.Any() && mpUserGroup != null)
            {
                bool flag = mainprojectManager.RemoveTeamMemberListFromMPUsergroup(members, mpUserGroup);
                if (flag)
                {
                    _teamRepository.RemoveTeamMemberListFromMPUsergroup(memberIdList, mpUserGroup.ID, mainProjectGuid);
                    List<MPMessage> messages = new List<MPMessage>();
                    foreach (var name in members)
                    {
                        MPMessage pm = new MPMessage
                        {
                            FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                            ToUser = name.ID,
                            MPUserGroupId = 0,
                            MsgTextContent = "权限变更",
                            MsgState = 0,
                            MsgType = 8,//模型树节点权限变更
                            MsgCreateTime = DateTime.Now,
                            MsgExData = null,
                            SubProjectId = Guid.Empty
                        };
                        messages.Add(pm);
                    }
                    await Task.Run(() =>
                    {
                        try
                        {
                            mainprojectManager.AddMPMessageList(messages);

                        }
                        catch (Exception ex)
                        {
                            _logger.LogInformation("RemoveMemberFromMPUserGroup send authchange message error===>" + ex.Message);
                            //Common.BimException.ExceptionHandler.HandleException(username, ex);
                        }
                    });
                }
                else
                {
                    var errMsg = "RemoveMemberFromMPUserGroup 移出组" + mpUserGroup.UserGroupName + "失败";
                    _logger.LogInformation(username + "==>" + errMsg);
                }

            }

            x.IsSuccess = true;
            return x;
        }

        public override async Task<CreateMPTeamProjectResponse> CreateMPTeamProject(CreateMPTeamProjectRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new CreateMPTeamProjectResponse();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (request.Project is null || request.Template is null || String.IsNullOrEmpty(request.Project.Name))
                return x;

            Infrastructure.Domain.TeamProject prj;
            string userName = currentUser.LoginName;
            var project = request.Project;
            Guid newGuid = Guid.NewGuid();
            project.Id = newGuid.ToString();
            project.CreateUser = userName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            TeamProject oldtp = new TeamProject
            {
                ID = newGuid,
                Avatar = project.Avatar,
                CreateUser = userName,
                CreationTime = DateTime.Now,
                Description = project.Description,
                EnableAuthority = project.EnableAuthority,
                EndTime = project.EndTime.ToDateTime(),
                ExtendProperty = project.ExtendProperty,
                FileDirectoryID = project.FileDirectoryID ?? Guid.Empty.ToString(),
                FilePath = project.FilePath,
                Leader = project.Leader,
                MainProjectID = mainProjectGuid.ToString(),
                Name = project.Name,
                Progress = project.Progress,
                ParentProjectID = project.ParentProjectId ?? Guid.Empty.ToString(),
                ProjectType = project.ProjectType,
                RoleString = "111110100",
                RoleGroupId = 0,
                StartTime = project.StartTime == null ? DateTime.Now : project.StartTime.ToDateTime()
            };
            prj = _teamRepository.AddProject(oldtp);
            if (prj != null)
            {
                var newmpteamproject = mainprojectManager.AddMPTeamProject(_mapper.Map<MPTeamProject>(project));
                
                using (var scope = new TransactionScope(
                    TransactionScopeOption.Required,
                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                    TransactionScopeAsyncFlowOption.Enabled))
                {
                    request.Project.Id = Guid.NewGuid().ToString();


                    var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
                    if (teamMember == null)
                    {
                        return x;
                    }

                    _teamRepository.AddMemberToProject(teamMember, prj, true);

                    teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName);
                    _teamRepository.AddMemberToProject(teamMember, prj);

                    teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName);
                    _teamRepository.AddMemberToProject(teamMember, prj);
                    scope.Complete();
                }


                string releaseinformation = request.Template.Releaseinformation;
                string domaincls = request.Template.DomainClass;
                string role = request.Template.ProjectRole;
                string mem = request.Template.ProjectMember;
                string workGroup = request.Template.WorkGroup;



                string message;

                if (!String.IsNullOrEmpty(role))
                {
                    CreateProjectRoleConfig(prj.ID, userName, role, out message);
                }


                var defaultMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == userName);
                if (!String.IsNullOrEmpty(mem))
                {
                    AddDefaultUserToRole(prj, mem, defaultMember, out message);
                }
                x.ProjectId = prj.ID.ToString();
            }

            x.IsSuccess = true;
            return x;
        }

        public override async Task<GrpcResult> DeleteMPTeamProject(DeleteMPTeamProjectRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.ProjectGuid, out var projectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.ProjectGuid} is error！";
                return x;
            }

            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            mainprojectManager.RemoveMPTeamProject(projectGuid);
            //删除项目角色及授权等信息
            var ret = _authorityManager.DeleteProject(projectGuid, out var message);
            if (ret)
            {
                //删除项目信息
                if (_teamRepository.DeleteProject(projectGuid) != null)
                {
                    x.IsSuccess = true;
                }
            }
            else
            {
                x.Message = message;
            }

            return x;
        }

        public override async Task<GrpcResult> UpdateMPTeamProjectInfo(UpdateMPTeamProjectInfoRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            var project = _mapper.Map<MPTeamProject>(request.Project);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            MPTeamProject teamProject = mainprojectManager.MPTeamProjects.FirstOrDefault(t => t.ID == project.ID);//GetTeamProject(project.ID);
            if (teamProject != null)
            {
                if (project.Name != teamProject.Name && !mainprojectManager.CheckProjectName(project.Name))
                {
                    //已存在同名项目
                    x.Message  = "已存在同名项目";
                    return x;
                }
                mainprojectManager.UpdateMPTeamProject(project);
                x.IsSuccess = true;
                return x;
            }

            return x;
        }
        /// <summary>
        /// 用户组添加权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> GiveMPUserGroupAuthMultiple(GiveMPUserGroupAuthMultipleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            string username = currentUser.LoginName;
            var mPUserGroupAuthList = request.MPUserGroupAuthList;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mem = _teamRepository.Members.FirstOrDefault(s => s.LoginName == username);
            var mPGrpcUserGroupAuth = mPUserGroupAuthList.Select(s => s.MPUserGroupAuth).FirstOrDefault();
            var mPUserGroupAuthEntity = _mapper.Map<MPUserGroupAuth>(mPGrpcUserGroupAuth);
            List<MPUserGroupAuth> toAddAuthList = new List<MPUserGroupAuth>();
            foreach (var mPUserGroupAuth in mPUserGroupAuthList)
            {
                List<MPAuthInfo> authList = _mapper.Map<List<MPAuthInfo>>(mPUserGroupAuth.MPAuthInfos);
                MPUserGroupAuth userGroupAuth = _mapper.Map<MPUserGroupAuth>(mPUserGroupAuth.MPUserGroupAuth);
                userGroupAuth.AuthInfo = JsonSerializer.Serialize(authList);
                toAddAuthList.Add(userGroupAuth);
            }
            var ret = mainprojectManager.GiveAuthToMPUserGroupMultiple(toAddAuthList);
            if (ret)
            {
                //同步teamdb数据
                _teamRepository.GiveAuthToMPUserGroupMultiple(mainprojectGuid, toAddAuthList);
                int groupId = int.Parse(mPUserGroupAuthEntity.GroupOrMemberId);
                if (groupId > 0)
                {
                    //var groupId = projectMessage.MPUserGroupId;
                    var groupusers = mainprojectManager.MPUserGroupMembers.Where(l => l.UserGroupId == groupId).ToList();
                    if (groupusers.Any())
                    {
                        var groupUserTeamIds = groupusers.Select(s => s.TeamMemberId).ToList();
                        await Task.Run(() =>
                        {
                            try
                            {
                                using (var scope = new TransactionScope(
                                    TransactionScopeOption.Required,
                                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                    TransactionScopeAsyncFlowOption.Enabled))
                                {
                                    var groupUserList = _teamRepository.Members.Where(p => groupUserTeamIds.Contains(p.ID)).ToList();
                                    List<MPMessage> messages = new List<MPMessage>();
                                    foreach (var gu in groupUserList)
                                    {
                                        MPMessage pm = new MPMessage
                                        {
                                            FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                                            ToUser = gu.ID,
                                            MPUserGroupId = 0,
                                            MsgTextContent = "权限变更",
                                            MsgState = 0,
                                            MsgType = 8,//模型树节点权限变更
                                            MsgCreateTime = DateTime.Now,
                                            MsgExData = null,
                                            SubProjectId = mPUserGroupAuthEntity.ObjectId
                                        };
                                        messages.Add(pm);
                                    }
                                    mainprojectManager.AddMPMessageList(messages);
                                    scope.Complete();
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogInformation("GiveMPUserGroupAuth===>" + ex.Message);
                            }
                        });
                    }
                    else
                    {
                        _logger.LogInformation(username + "==>GiveMPUserGroupAuth to AddMPMessage 用户组没有成员");
                    }
                }
                x.IsSuccess = true;
            }
            else
            {
                x.Message = "配置权限失败";
                x.IsSuccess = false;
            }
            
            return x;
        }

        public override async Task<GrpcResult> GiveMPUserGroupAuth(GiveMPUserGroupAuthRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            string username = currentUser.LoginName;
            var tempauth = request.MPUserGroupAuth;
            var mPUserGroupAuth = _mapper.Map<MPUserGroupAuth>(tempauth);
            var authList = _mapper.Map<List<MPAuthInfo>>(request.AuthList);
            _logger.LogInformation("==>mainprojectGuid:" + mainprojectGuid);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mem = _teamRepository.Members.FirstOrDefault(s => s.LoginName == username);
            var ret = mainprojectManager.GiveAuthToMPUserGroup(mPUserGroupAuth, authList);
            if (ret)
            {
                //同步teamdb数据
                _teamRepository.GiveAuthToMPUserGroup(mainprojectGuid, mPUserGroupAuth, authList);
                int groupId = int.Parse(mPUserGroupAuth.GroupOrMemberId);
                if (groupId > 0)
                {
                    //var groupId = projectMessage.MPUserGroupId;
                    var groupusers = mainprojectManager.MPUserGroupMembers.Where(l => l.UserGroupId == groupId).ToList();
                    if (groupusers.Any())
                    {
                        var groupUserTeamIds = groupusers.Select(s => s.TeamMemberId).ToList();
                        await Task.Run(() =>
                        {
                            try
                            {
                                using (var scope = new TransactionScope(
                                    TransactionScopeOption.Required,
                                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                    TransactionScopeAsyncFlowOption.Enabled))
                                {
                                    var groupUserList = _teamRepository.Members.Where(p => groupUserTeamIds.Contains(p.ID)).ToList();
                                    List<MPMessage> messages = new List<MPMessage>();
                                    foreach (var gu in groupUserList)
                                    {
                                        MPMessage pm = new MPMessage
                                        {
                                            FromUser = BuildinAdministrators.BuildInSystemAdminGuid,
                                            ToUser = gu.ID,
                                            MPUserGroupId = 0,
                                            MsgTextContent = "权限变更",
                                            MsgState = 0,
                                            MsgType = 8,//模型树节点权限变更
                                            MsgCreateTime = DateTime.Now,
                                            MsgExData = null,
                                            SubProjectId = mPUserGroupAuth.ObjectId
                                        };
                                        messages.Add(pm);
                                    }
                                    mainprojectManager.AddMPMessageList(messages);
                                    scope.Complete();
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogInformation("GiveMPUserGroupAuth===>" + ex.Message);
                            }
                        });
                    }
                    else
                    {
                        _logger.LogInformation(username + "==>GiveMPUserGroupAuth to AddMPMessage 用户组没有成员");
                    }
                }
                x.IsSuccess = true;
            }
            else
            {
                x.Message = "配置权限失败";
                x.IsSuccess = false;
            }

            return x;
        }

        public override async Task<CreateMPProjectTreeNodeResponse> CreateMPProjectTreeNode(CreateMPProjectTreeNodeRequest request, ServerCallContext context)
        {
            var x = new CreateMPProjectTreeNodeResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            var mPProjectTreeNode = _mapper.Map<MPProjectTreeNode>(request.MPProjectTreeNode);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mptreenode = mainprojectManager.AddMPProjectTreeNode(mPProjectTreeNode);
            if (mptreenode != null)
            {
                x.IsSuccess = true;
                x.Mptreenode = _mapper.Map<GrpcMPProjectTreeNode>(mptreenode);
                return x;
            }
            return x;
        }
        /// <summary>
        /// 删除树节点
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> DeleteMPProjectTreeNode(DeleteMPProjectTreeNodeRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var ret = mainprojectManager.RemoveMPProjectTreeNode(request.NodeId);
            
            x.IsSuccess = ret;
            return x;
        }
        private bool CheckModelDataCommandWithConflict(IProjectRepository _modelManager, IEnumerable<GrpcMPModelDataCommand> cmds, out List<long> repeatedataid)
        {
            //GetParamtersXmlString(cmds);
            //_projectManager.ModelManager.Datas    服务器端保存的已有数据
            //_projectManager.ModelManager.Relationships    服务器端保存的关系数据
            //ModelDataCommands   将要提交到数据库的命令，即构件变更的信息
            repeatedataid = new List<long>();
            try
            {

                //参数检验
                if (cmds == null || !cmds.Any()) return false;

                var adDcommands = from cmd in cmds where (cmd.Type.Equals(GrpcMPCommandType.Add)) select cmd;   //ADD指令集合
                if (adDcommands.Any())
                {

                    List<Int64> modelDataInstanceIdList = new List<Int64>();    //临时保存数据ID
                    //var modelDataInstanceIdList = null;
                    foreach (var addcommands in adDcommands)
                    {
                        if (null != addcommands.Buffer && addcommands.Buffer.Count > 0)
                        {
                            modelDataInstanceIdList = addcommands.Buffer.Select(d => d.InstanceId).ToList();
                            //var modelDataInstanceIdandClassNameList = addcommands.Buffer.Select(d => d.InstanceId).ToList();
                            //add by asdf 2018-02-10 可优化为下面方式：
                            var hashset = new HashSet<long>(modelDataInstanceIdList);
                            if (modelDataInstanceIdList.Count != hashset.Count)
                            {
                                var query = modelDataInstanceIdList.GroupBy(x => x)
                                                  .Where(g => g.Count() > 1)
                                                  .Select(y => y.Key)
                                                  .ToList();
                                if (query.Count > 20)
                                {
                                    query = query.Take(20).ToList();
                                }
                                var temp = addcommands.Buffer.Where(b => query.Contains(b.InstanceId)).ToList();
                                var t = temp.Select(x => x.DomainClassName).Distinct();
                                var str = "";
                                foreach (var i in t)
                                {
                                    str += i + ":";
                                    var y = temp.Where(x => x.DomainClassName == i).Select(s => s.InstanceId).ToList();
                                    str += string.Join(",", y);
                                }
                                //var str = string.Join(",", query);
                                //throw new DataConflictException("上传数据ID重复,请联系技术支持人员！" + str);
                            }
                        }

                        string repeateDataIds = "";
                        string repeateRelationIds = "";
                        List<long> repeateDatas = new List<long>();
                        List<long> repeateRelations = new List<long>();
                        if (_modelManager.IsRepeatIdInServer(modelDataInstanceIdList, null, out repeateDataIds, out repeateRelationIds, out repeateDatas, out repeateRelations))
                        {
                            if (repeateDatas.Count > 0)
                            {
                                var temp = addcommands.Buffer.Where(b => repeateDatas.Contains(b.InstanceId)).ToList();
                                var t = temp.Select(x => x.DomainClassName).Distinct();
                                var str = "";
                                foreach (var i in t)
                                {
                                    str += i + ":";
                                    var y = temp.Where(x => x.DomainClassName == i).Select(s => s.InstanceId).Take(3).ToList();
                                    str += string.Join(",", y);
                                }
                                repeateDataIds = repeateDataIds + "==重复数据在上传参数中的信息：" + str + "==";
                            }
                            // throw new DataConflictException("与数据库的ID重复，请联系技术支持人员！" + repeateDataIds + ";" + repeateRelationIds);
                        }
                        repeatedataid = repeateDatas;
                    }
                }
            }
            catch (Exception ex)
            {
                
                //throw;
            }

            return true;
        }





        /// <summary>
        /// 保存后查询冲突节点信息
        /// </summary>
        /// <param name="mainProjectManager">主项目仓库</param>
        /// <param name="addTreeDatas">要添加的树节点数据</param>
        /// <param name="subProjectGuid">子项目ID</param>
        /// <returns>冲突节点列表</returns>
        private async Task<List<GrcpConflictNodeList>> GetConflictNodesAfterSave(IMainProjectRepository mainProjectManager, List<GrpcMPProjectTreeNode> addTreeDatas, Guid subProjectGuid)
        {
            var conflictNodeList = new List<GrcpConflictNodeList>();
            try
            {
                // 获取节点名称检查配置
                var nodeNameCheckConfigs = await GetNodeNameCheckConfigs();

                // 用HashSet存储本次上传的节点ID，提升查找效率
                var uploadedNodeIds = new HashSet<long>(addTreeDatas.Select(n => n.NodeId));

                // 查询本次上传且有冲突标记的节点
                var conflictNodes = mainProjectManager.MPProjectTreeNodes
                    .Where(n => n.subProjectld == subProjectGuid && n.HasConflict == 1 && uploadedNodeIds.Contains(n.NodeId))
                    .ToList();

                foreach (var conflictNode in conflictNodes)
                {
                    // 根据节点类型确定冲突检查类型
                    var checkType = GetCheckTypeForNodeType(conflictNode.NodeType, nodeNameCheckConfigs);

                    var conflictRecord = new GrcpConflictNodeList
                    {
                        CheckType = checkType, // 根据配置确定检查类型
                        NodeId = conflictNode.NodeId,
                        OldNodeName = conflictNode.OriginalNodeName ?? conflictNode.NodeName,
                        NewNodeName = conflictNode.NodeName,
                        ParentNodeId = checkType == 1 ? conflictNode.ParentNodeId : 0, // 全局检查时父节点ID为空
                        ConflitNodeIdList = { conflictNode.NodeId }
                    };

                    conflictNodeList.Add(conflictRecord);

                    var checkTypeText = checkType == 0 ? "全局" : "同级节点";
                    _logger.LogInformation($"检测到本次上传的节点名称冲突: 节点ID={conflictNode.NodeId}, 节点类型={conflictNode.NodeType}, 检查类型={checkTypeText}, 原名称='{conflictNode.OriginalNodeName}', 新名称='{conflictNode.NodeName}'");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询本次上传后的冲突节点信息时发生异常");
            }

            return conflictNodeList;
        }

        /// <summary>
        /// 获取节点名称检查配置
        /// </summary>
        /// <returns>节点名称检查配置列表</returns>
        private async Task<List<NodeNameCheckConfig>> GetNodeNameCheckConfigs()
        {
            try
            {
                // 从Team数据库获取节点名称检查配置
                var teamRepository = _teamRepository;
                if (teamRepository?.NodeNameCheckConfigs != null)
                {
                    return teamRepository.NodeNameCheckConfigs.ToList();
                }
                return new List<NodeNameCheckConfig>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取节点名称检查配置时发生异常");
                return new List<NodeNameCheckConfig>();
            }
        }

        /// <summary>
        /// 根据节点类型获取检查类型
        /// </summary>
        /// <param name="nodeType">节点类型</param>
        /// <param name="configs">节点名称检查配置</param>
        /// <returns>检查类型（0=全局，1=同级节点）</returns>
        private int GetCheckTypeForNodeType(int nodeType, List<NodeNameCheckConfig> configs)
        {
            try
            {
                var config = configs.FirstOrDefault(c => c.NodeTypeNum == nodeType);
                return config?.CheckType ?? 1; // 默认使用同级节点检查
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"根据节点类型获取检查类型时发生异常: NodeType={nodeType}");
                return 1; // 默认使用同级节点检查
            }
        }

        /// <summary>
        /// 根据配置设置ConflictScope，实现数据库层面的动态冲突检测
        /// </summary>
        /// <param name="addTreeDatas">要添加的树节点数据</param>
        /// <param name="subProjectGuid">子项目ID</param>
        /// <returns>设置ConflictScope后的树节点数据</returns>
        private async Task<List<GrpcMPProjectTreeNode>> SetConflictScopeForTreeNodes(List<GrpcMPProjectTreeNode> addTreeDatas, Guid subProjectGuid)
        {
            try
            {
                // 获取节点名称检查配置
                var nodeNameCheckConfigs = await GetNodeNameCheckConfigs();
                
                // 预计算配置映射，避免在循环中重复查找
                var configMap = nodeNameCheckConfigs.ToDictionary(c => c.NodeTypeNum, c => c.CheckType);
                
                // 直接处理所有节点，避免分组和ToList()的开销
                foreach (var node in addTreeDatas)
                {
                    // 获取该节点类型的检查配置，使用预计算的映射
                    var checkType = configMap.TryGetValue(node.NodeType, out var type) ? type : 1; // 默认使用同级节点检查
                    
                    // 根据配置设置ConflictScope
                    string conflictScope;
                    if (checkType == 0) // 全局检测
                    {
                        // 全局检测：使用固定值作为范围标识，确保整个子项目内唯一
                        conflictScope = "GLOBAL";
                        _logger.LogDebug($"设置全局冲突检测：节点ID={node.NodeId}, 节点类型={node.NodeType}, ConflictScope={conflictScope}");
                    }
                    else // 同级检测
                    {
                        // 同级检测：使用父节点ID作为范围标识
                        conflictScope = node.ParentNodeId.ToString();
                        _logger.LogDebug($"设置同级冲突检测：节点ID={node.NodeId}, 节点类型={node.NodeType}, 父节点ID={node.ParentNodeId}, ConflictScope={conflictScope}");
                    }
                    
                    // 设置ConflictScope字段
                    node.ConflictScope = conflictScope;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置ConflictScope时发生异常");
                // 发生异常时返回原始数据
                return addTreeDatas;
            }

            return addTreeDatas;
        }


        public override async Task<UploadTreeNodeDataResponse> UploadTreeNodeData(UploadTreeNodeDataRequest request, ServerCallContext context)
        {
            var x = new UploadTreeNodeDataResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            if (!Guid.TryParse(request.SubProjectGuid, out var subProjectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.SubProjectGuid} is error！";
                return x;

            }
            var username = teamMember.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);

            var _modelManager = _teamRepository.GetProjectRepository(subProjectGuid);
            var _projectManager = _teamRepository.GetProjectRepository(subProjectGuid);
            var projectMember = _projectManager.Members.SingleOrDefault(m => m.TeamMemberID == teamMember.ID);
            //PbimLog.Info(username + "==>SaveVersionInfo");
            string desc = username + "上传项目树";
            var ret = _modelManager.SaveVersionInfo(new VersionData { Author = username, Description = desc, IsComplete = 0 });
            List<long> repeateDataIds = new List<long>();
            var dataCommands = request.DataCommands;
            CheckModelDataCommandWithConflict(_modelManager, dataCommands, out repeateDataIds);
            if (repeateDataIds.Any())
            {
                //构件数据有与服务器数据库重复的数据
                string strRepeate = string.Join(",", repeateDataIds);
                _logger.LogInformation("本次上传发生ADD重复的构件instanceid：" + strRepeate);
                //PbimLog.Info("本次上传发生ADD重复的构件instanceid：" + strRepeate);
            }
            
            //PbimLog.Info(username + "==>组装提交数据");
            List<GrpcModelData> addToModifyDatas = (dataCommands.Where(a => a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<GrpcMPModelDataCommand>())
                                        .SelectMany(c => c.Buffer.ToList() ?? new List<GrpcModelData>()).Where(d => repeateDataIds.Contains(d.InstanceId))
                                        .ToList();
            //提交数据
            List<GrpcModelData> addDatas = (dataCommands.Where(a => a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<GrpcMPModelDataCommand>())
                                        .SelectMany(c => c.Buffer.ToList() ?? new List<GrpcModelData>()).Where(d => !repeateDataIds.Contains(d.InstanceId))
                                        .ToList();
            var addids = addDatas.Select(s=>s.InstanceId).ToList();
            _logger.LogInformation("add构件instanceid：" + string.Join(",", addids));
            List<GrpcModelData> modifyDatas = (dataCommands.Where(a => a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<GrpcMPModelDataCommand>())
                                        .SelectMany(c => c.Buffer.ToList() ?? new List<GrpcModelData>())
                                        .Union(addToModifyDatas)
                                        .ToList();
            var modids = modifyDatas.Select(s => s.InstanceId).ToList();
            _logger.LogInformation("modids构件instanceid：" + string.Join(",", modids));
            List<GrpcModelData> deleteDatas = (dataCommands.Where(a => a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<GrpcMPModelDataCommand>())
                                        .SelectMany(c => c.Buffer.ToList() ?? new List<GrpcModelData>())
                                        .ToList();
            var treeNodes = request.TreeNodes;
            //需要增加的项目树列表
            List<GrpcMPProjectTreeNode> addtreeDatas = (treeNodes.Where(a => a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<GrpcTreeNodeDataCommand>())
                                        .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPProjectTreeNode>())
                                        .ToList();
            //PbimLog.Info("UploadTreeNodeData==>addtreeDatas：" + addtreeDatas.Count);
            //需要修改的
            List<GrpcMPProjectTreeNode> modifytreeDatas = (treeNodes.Where(a => a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<GrpcTreeNodeDataCommand>())
                                        .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPProjectTreeNode>())
                                        .ToList();
            //PbimLog.Info("UploadTreeNodeData==>modifytreeDatas：" + modifytreeDatas.Count);
            //需要删除的
            List<GrpcMPProjectTreeNode> deltreeDatas = (treeNodes.Where(a => a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<GrpcTreeNodeDataCommand>())
                                        .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPProjectTreeNode>())
                                        .ToList();
            //PbimLog.Info("UploadTreeNodeData==>deltreeDatas：" + deltreeDatas.Count);

            // 移除前置冲突检查，改为在数据库层面处理冲突
            // 冲突信息将在保存后通过查询数据库获取
            var instanceIds = (addDatas.Select(m => m.InstanceId)
                            .Union(modifyDatas.Select(n => n.InstanceId))
                            .Union(deleteDatas.Select(d => d.InstanceId))
                            ).ToList();

            var retVal = _modelManager.UnlockDatasNew(projectMember, instanceIds,subProjectGuid);
            if (!retVal)
            {
                //PbimLog.Info("UploadTreeNodeData==>" + username + "未能成功解锁锁定构件");
            }

            //PbimLog.Info(username + "==>UploadTreeNodeData==>直接持久化到数据库");
            //直接持久化到数据库

            //Task.Run(() =>
            //{

            //});
            try
            {
                var modelman = _teamRepository.GetProjectRepository(subProjectGuid);
                modelman.CheckInWithDatas(_teamRepository,_mapper.Map<List<ModelData>>(addDatas), _mapper.Map<List<ModelData>>(modifyDatas), _mapper.Map<List<ModelData>>(deleteDatas),
                        new List<Relationship>(), new List<Relationship>(), new List<Relationship>(), projectMember, true, username, ret,
                    desc);

                //上传构件完成之后，要将treenode数据写入项目数据库中的mpprojecttreenodes表中
                var mainprojectmanager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
                
                // 根据配置设置ConflictScope，实现数据库层面的动态冲突检测
                var processedAddTreeDatas = await SetConflictScopeForTreeNodes(addtreeDatas, subProjectGuid);
                
                mainprojectmanager.CheckInTreeNodes(_mapper.Map<List<MPProjectTreeNode>>(processedAddTreeDatas), _mapper.Map<List<MPProjectTreeNode>>(modifytreeDatas), _mapper.Map<List<MPProjectTreeNode>>(deltreeDatas));

                // 保存后查询冲突信息
                var conflictNodes = await GetConflictNodesAfterSave(mainprojectmanager, processedAddTreeDatas, subProjectGuid);
                if (conflictNodes.Any())
                {
                    _logger.LogInformation($"检测到 {conflictNodes.Count} 个树节点名称冲突");
                    x.ConflictNodeList.AddRange(conflictNodes);
                }
            }
            catch (Exception ex)
            {
                //PbimLog.Info(username + "==>UploadTreeNodeData===>持久化到数据库 ex.StackTrace:" + ex.StackTrace);
                //PbimLog.Info(username + "==>UploadTreeNodeData===>持久化到数据库 error:" + ex.Message);
            }

            _modelManager.UpdataVersionInfo(ret, 1);
            x.VersionNo = ret;
            x.IsSuccess = true;
            return x;
        }

        public override async Task<CreateMPLibraryInfoResponse> CreateMPLibraryInfo(CreateMPLibraryInfoRequest request, ServerCallContext context)
        {
            var x = new CreateMPLibraryInfoResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);

            if (teamMember is null)
            {
                x.Message = "用户未登录！";
                return x;
            }


            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.IsSuccess = false;
                x.Message = $"参数: {request.MainprojectGuid} is error！";
                return x;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mPLibraryInfo = _mapper.Map<MPLibraryInfo>(request.MPLibraryInfo);
            mPLibraryInfo.LibId = Guid.NewGuid();
            mPLibraryInfo.CreateTime = DateTime.Now;
            var mplib = mainprojectManager.AddMPLibraryInfo(mPLibraryInfo);
            if (mplib != null)
            {
                _libRepository.InitMainprojectLib(mainprojectGuid, mplib.LibId, (int)mplib.LibType);
                //log.Info(username + "==>CreateMPLibraryInfo succ;mplibguid=" + mplib.LibId);
                x.Mplib = _mapper.Map<GrpcMPLibraryInfo>(mplib);
                x.IsSuccess = true;
            }
            return x;
        }

        /// <summary>
        /// 设置等级库和元件库、参数化组件库、图纸和报表模板库、设计配置库权限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> SetUserLibAuth(SetUserLibAuthRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            var mPUserGroupLibAuths = _mapper.Map<List<MPUserGroupLibAuth>>(request.MPUserGroupLibAuths);
            using (var scope = new TransactionScope(
                                    TransactionScopeOption.Required,
                                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                    TransactionScopeAsyncFlowOption.Enabled))
            {
                var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
                foreach (var item in mPUserGroupLibAuths)
                {
                    mainprojectManager.SetMPUserGroupLibAuth(item);
                    _teamRepository.SetMPUserGroupLibAuth(mainProjectGuid, item);
                    //log.Info("SetUserLibAuth==>LibType:" + item.LibType.ToString() + "||Permission:" + item.Permission.ToString());
                }
                scope.Complete();
            }

            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 发布（云）链接文件
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<UploadMPLinkFileResponse> UploadMPLinkFile(UploadMPLinkFileRequest request, ServerCallContext context)
        {
            var x = new UploadMPLinkFileResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            int currentvNo = 0;
            int vNo = 0;
            string UploadModelPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var mPCloudLinkFile = _mapper.Map<MPCloudLinkFile>(request.MPCloudLinkFile);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var temp = mainprojectManager.MPLibCloudLinks.Where(a => a.ProjectId == mPCloudLinkFile.ProjectId);
            
            if (temp.Any())
            {
                vNo = temp.Max(v => v.VersionNo);
            }
            string linkfilepath = UploadModelPath + @"\" + @"LinkFile\" + mainprojectGuid + @"\" + mPCloudLinkFile.ProjectId + @"\";
            linkfilepath = linkfilepath.Replace("\\", separator.ToString());
            if (!Directory.Exists(linkfilepath))
            {
                //errMsg = "未找到对应的云链接文件夹";
                //log.Info("发布路径不存在,自动创建路径" + linkfilepath);
                //currentvNo = vNo + 1;
                Directory.CreateDirectory(linkfilepath);
                //return false;
            }
            string savepath = linkfilepath + (vNo + 1) + @"\";
            savepath = savepath.Replace("\\", separator.ToString());
            if (!Directory.Exists(savepath))
            {
                Directory.CreateDirectory(savepath);
            }
            Regex regex = new Regex(@"[<>:""/\\|?*]");
            if (regex.IsMatch(mPCloudLinkFile.FileName))
            {
                x.Message = "文件名包含特殊字符";
                return x;
            }
            var fileData = request.FileData.ToByteArray();
            string saveFilePath = savepath + mPCloudLinkFile.FileName + "." + mPCloudLinkFile.FileType;
            using (var fs1 = new FileStream(saveFilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
            {
                byte[] byteData = fileData;
                Task task = fs1.WriteAsync(byteData, 0, byteData.Length);
            }
            currentvNo = vNo + 1;
            mPCloudLinkFile.SavePath = saveFilePath;
            mPCloudLinkFile.VersionNo = currentvNo;
            mainprojectManager.AddMPCloudLinkFile(mPCloudLinkFile);

            x.IsSuccess = true;
            x.CurrentvNo = currentvNo;
            return x;
        }
        /// <summary>
        /// 增加发布设置
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddMPReleaseConfig(AddMPReleaseConfigRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            MPReleaseConfig mPReleaseConfig = _mapper.Map<MPReleaseConfig>(request.MPReleaseConfig);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainProjectGuid;
                x.IsSuccess = false;
                return x;

            }
            var ret = mainprojectManager.AddMPReleaseConfig(mPReleaseConfig);

            if (ret != null)
            {
                //log.Info("AddMPReleaseConfig succ");
                x.IsSuccess = true;
                return x;
            }
            else
            {
                x.Message = "添加失败";
                x.IsSuccess = false;
                return x;
            }
        }
        /// <summary>
        /// 修改发布设置
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> ModifyMPReleaseConfig(ModifyMPReleaseConfigRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            MPReleaseConfig mPReleaseConfig = _mapper.Map<MPReleaseConfig>(request.MPReleaseConfig);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainProjectGuid;
                x.IsSuccess = false;
                return x;

            }
            var ret = mainprojectManager.AddMPReleaseConfig(mPReleaseConfig);

            if (ret != null)
            {
                //log.Info("AddMPReleaseConfig succ");
                x.IsSuccess = true;
                return x;
            }
            else
            {
                x.Message = "修改失败";
                x.IsSuccess = false;
                return x;
            }
        }
        /// <summary>
        /// 删除发布设置
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> DeleteMPReleaseConfig(DeleteMPReleaseConfigRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            MPReleaseConfig mPReleaseConfig = _mapper.Map<MPReleaseConfig>(request.MPReleaseConfig);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainProjectGuid;
                x.IsSuccess = false;
                return x;

            }
            var ret = mainprojectManager.RemoveMPReleaseConfig(mPReleaseConfig);

            if (ret)
            {
                //log.Info("AddMPReleaseConfig succ");
                x.IsSuccess = true;
                return x;
            }
            else
            {
                x.Message = "修改失败";
                x.IsSuccess = false;
                return x;
            }
        }

        public override async Task<AddMPMessageResponse> AddMPMessage(AddMPMessageRequest request, ServerCallContext context)
        {
            var x = new AddMPMessageResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            int ret = -1;
            var username = currentUser.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mem = _teamRepository.Members.FirstOrDefault(s => s.LoginName == username);//  _projectManager.GetProjectMember(username);
                                                                                        //var members = _projectManager.Members.ToList();
            List<TeamMember> members = _teamRepository.GetTeamMembersByObjectId(mainprojectGuid, mainprojectGuid, 1);

            var projectMessage = _mapper.Map<MPMessage>(request.ProjectMessage);
            //MPUserGroupId不为0，说明是发送的组消息
            if (projectMessage.MPUserGroupId > 0)
            {
                var groupId = projectMessage.MPUserGroupId;
                var groupusers = mainprojectManager.MPUserGroupMembers.Where(l => l.UserGroupId == groupId).ToList();
                if (groupusers.Any())
                {
                    var groupUserTeamIds = groupusers.Select(s => s.TeamMemberId).ToList();
                    if (!groupUserTeamIds.Contains(mem.ID))
                    {
                        //在发组消息的时候登录用户已经被移除出组
                        ret = -2;
                    }
                    else
                    {
                        await Task.Run(() =>
                        {
                            try
                            {
                                using (var scope = new TransactionScope(
                                    TransactionScopeOption.Required,
                                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                    TransactionScopeAsyncFlowOption.Enabled))
                                {
                                    var groupUserList = members.Where(p => groupUserTeamIds.Contains(p.ID)).ToList();
                                    foreach (var gu in groupUserList)
                                    {
                                        if (gu.ID != mem.ID)
                                        {
                                            MPMessage pm = new MPMessage
                                            {
                                                FromUser = projectMessage.FromUser,
                                                ToUser = gu.ID,
                                                MPUserGroupId = projectMessage.MPUserGroupId,
                                                MsgTextContent = projectMessage.MsgTextContent,
                                                MsgState = 0,
                                                MsgType = projectMessage.MsgType,
                                                MsgCreateTime = projectMessage.MsgCreateTime,
                                                MsgExData = projectMessage.MsgExData,
                                                SubProjectId = projectMessage.SubProjectId
                                            };
                                            mainprojectManager.AddMPMessage(pm);
                                        }

                                    }

                                    scope.Complete();
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "发送项目消息给组内其他成员时发生异步处理异常");
                            }
                        });
                        ret = 1;
                    }
                }
                else
                {
                    _logger.LogInformation(username + "==>AddMPMessage to AddGroupMessage 除自己外没有其他成员");
                }
            }
            else
            {
                //MPUserGroupId=0,ToUser=Guid.Empty 发送全员消息
                if (projectMessage.ToUser == Guid.Empty)
                {
                    //log.Info("AddMPMessage===>发送全员消息：member count:" + members.Count);
                    await Task.Run(() =>
                    {
                        try
                        {
                            using (var scope = new TransactionScope(
                                TransactionScopeOption.Required,
                                new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                TransactionScopeAsyncFlowOption.Enabled))
                            {
                                //var groupUserList = members.Where(p => groupUserTeamIds.Contains(p.ID)).ToList();
                                foreach (var gu in members)
                                {
                                    //if (gu.ID != mem.ID)
                                    //{

                                    //}
                                    MPMessage pm = new MPMessage
                                    {
                                        FromUser = projectMessage.FromUser,
                                        ToUser = gu.ID,
                                        MPUserGroupId = projectMessage.MPUserGroupId,
                                        MsgTextContent = projectMessage.MsgTextContent,
                                        MsgState = 0,
                                        MsgType = projectMessage.MsgType,
                                        MsgCreateTime = projectMessage.MsgCreateTime,
                                        MsgExData = projectMessage.MsgExData,
                                        SubProjectId = projectMessage.SubProjectId
                                    };
                                    mainprojectManager.AddMPMessage(pm);

                                }

                                scope.Complete();
                            }

                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "AddMPMessage异步处理失败");
                        }
                    });
                }
                else
                {
                    using (var scope = new TransactionScope(
                        TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                        TransactionScopeAsyncFlowOption.Enabled))
                    {
                        projectMessage.MsgState = 0;
                        var retmessage = mainprojectManager.AddMPMessage(projectMessage);
                        if (retmessage != null)
                        {
                            ret = retmessage.MsgServerID;
                        }
                        else
                        {
                            x.IsSuccess = false;
                            ret = -1;
                        }
                        scope.Complete();
                    }
                }
            }
            x.Ret = ret;
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 发送组消息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> AddMPGroupMessage(AddMPGroupMessageRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            var mem = _teamRepository.Members.FirstOrDefault(s => s.LoginName == username);//  _projectManager.GetProjectMember(username);
                                                                                           //var members = _projectManager.Members.ToList();
            List<TeamMember> members = _teamRepository.GetTeamMembersByObjectId(mainprojectGuid, mainprojectGuid, 1);

            var projectMessage = _mapper.Map<MPMessage>(request.ProjectMessage);
            //MPUserGroupId不为0，说明是发送的组消息
            if (projectMessage.MPUserGroupId > 0)
            {
                var groupId = projectMessage.MPUserGroupId;
                var groupusers = mainprojectManager.MPUserGroupMembers.Where(l => l.UserGroupId == groupId).ToList();
                if (groupusers.Any())
                {
                    var groupUserTeamIds = groupusers.Select(s => s.TeamMemberId).ToList();
                    if (!groupUserTeamIds.Contains(mem.ID))
                    {
                        //在发组消息的时候登录用户已经被移除出组
                        x.Message = "当前用户已经被移除出组";
                        x.IsSuccess = false;
                    }
                    else
                    {
                        await Task.Run(() =>
                        {
                            try
                            {
                                using (var scope = new TransactionScope(
                                    TransactionScopeOption.Required,
                                    new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                                    TransactionScopeAsyncFlowOption.Enabled))
                                {
                                    var groupUserList = members.Where(p => groupUserTeamIds.Contains(p.ID)).ToList();
                                    foreach (var gu in groupUserList)
                                    {
                                        if (gu.ID != mem.ID)
                                        {
                                            MPMessage pm = new MPMessage
                                            {
                                                FromUser = projectMessage.FromUser,
                                                ToUser = gu.ID,
                                                MPUserGroupId = projectMessage.MPUserGroupId,
                                                MsgTextContent = projectMessage.MsgTextContent,
                                                MsgState = 0,
                                                MsgType = projectMessage.MsgType,
                                                MsgCreateTime = projectMessage.MsgCreateTime,
                                                MsgExData = projectMessage.MsgExData,
                                                SubProjectId = projectMessage.SubProjectId
                                            };
                                            mainprojectManager.AddMPMessage(pm);
                                        }

                                    }

                                    scope.Complete();
                                    x.IsSuccess = true;
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "发送组内消息时发生异步处理异常");
                                x.IsSuccess = false;
                                x.Message = "发送组内消息失败";
                            }
                        });
                    }
                }
                else
                {
                    x.Message = "用户组不存在";

                    x.IsSuccess = false;
                }
            }
            return x;
        }
        /// <summary>
        /// 解锁图纸
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> UnlockMPDrawing(UnlockMPDrawingRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (!Guid.TryParse(request.SubProjectGuid, out var subProjectGuid))
            {
                x.Message = $"参数{request.SubProjectGuid}格式有误！";
                return x;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainProjectGuid;
                x.IsSuccess = false;
                return x;

            }
            var instanceIdList = request.InstanceIdList;
            //查询被自己锁定的
            var selfLocked = mainprojectManager.MPDrawings.Where(d => d.SubProjectId == subProjectGuid && instanceIdList.Contains(d.InstanceId) && d.LockUser == currentUser.LoginName).ToList();
            if (selfLocked.Any())
            {
                var selfLockedId = selfLocked.Select(s => s.InstanceId).ToList();
                //解锁
                var ret = mainprojectManager.UnlockMPDrawing(subProjectGuid, selfLockedId, currentUser.LoginName);
                x.IsSuccess = ret;
            }
            return x;
        }

        /// <summary>
        /// 锁定图纸
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> LockMPDrawing(LockMPDrawingRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (!Guid.TryParse(request.SubProjectGuid, out var subProjectGuid))
            {
                x.Message = $"参数{request.SubProjectGuid}格式有误！";
                return x;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainProjectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainProjectGuid;
                x.IsSuccess = false;
                return x;

            }
            var islocked = false;
            string errMsg = "";
            var dicInstanceIdAndVersion = request.DicInstanceIdAndVersion;
            foreach (var instanceIdVersion in dicInstanceIdAndVersion)
            {
                //判断是否被其他人锁定
                var instanceId = instanceIdVersion.Key;
                var version = instanceIdVersion.Value;
                var draw = mainprojectManager.MPDrawings.FirstOrDefault(c => c.SubProjectId == subProjectGuid && c.InstanceId == instanceId);
                if (draw != null)
                {
                    var ver = draw.VersionNo;
                    if (ver > version)
                    {
                        islocked = true;
                    }
                    var lockuser = draw.LockUser;
                    if (!string.IsNullOrEmpty(lockuser) && lockuser != currentUser.LoginName)
                    {
                        //被其他人锁定，把islocked置为true
                        errMsg = draw.InstanceId + "被" + lockuser + "锁定";
                        islocked = true;
                        //return false;
                    }
                }
                if (islocked)
                {
                    //log.Info(errMsg);
                    x.Message = errMsg;
                    x.IsSuccess = false;
                    return x;
                }

            }
            if (!islocked)
            {
                var instanceIdList = dicInstanceIdAndVersion.Keys.ToList();
                //没有被其他人锁定
                var ret = mainprojectManager.LockMPDrawing(subProjectGuid, instanceIdList, currentUser.LoginName);
                x.IsSuccess = ret;
                return x;
            }
            return x;
        }

        public override async Task<UploadMPDrawingWithFileDataResponse> UploadMPDrawingWithFileData(UploadMPDrawingWithFileDataRequest request, ServerCallContext context)
        {
            var x = new UploadMPDrawingWithFileDataResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            int versionNo = 1;
            string UploadModelPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainprojectGuid;
                x.IsSuccess = false;
                return x;

            }
            var fileData = request.FileData.ToByteArray();
            var fileMD5 = request.FileMD5;
            var mPDrawing = _mapper.Map<MPDrawing>(request.MPDrawing);
            var subProjectId = mPDrawing.SubProjectId;
            var instanceid = mPDrawing.InstanceId;
            var curDrawing = mainprojectManager.MPDrawings.FirstOrDefault(s => s.SubProjectId == subProjectId && s.InstanceId == instanceid);
            var newDrawingId = Guid.NewGuid();
            if (curDrawing != null)
            {
                var ver = curDrawing.VersionNo;
                versionNo = ver + 1;
                newDrawingId = curDrawing.DrawingID;
            }

            var _uploadPath = UploadModelPath + @"\" + @"DrawingFile\" + mainprojectGuid + @"\" + newDrawingId + @"\" + versionNo + @"\";
            //log.Info(@"UploadMPDrawingWithFileData 上传路径==>" + _uploadPath);
            _uploadPath = _uploadPath.Replace("\\", separator.ToString());

            var savePath = _uploadPath + request.FileName;
            //log.Info(username + "==>UploadMPDrawingWithFileData savePath=" + savePath);

            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }
            using (var fs1 = new FileStream(savePath, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
            {
                //log.Info("传输文件");
                fs1.Write(fileData, 0, fileData.Length);
                fs1.Flush();
                fs1.Close();
                fs1.Dispose();
            }

            //更新serverfilepath
            if (curDrawing != null)
            {
                mainprojectManager.UpdateMPDrawing(subProjectId, instanceid, versionNo, savePath, fileMD5);
            }
            else
            {
                mPDrawing.DrawingID = newDrawingId;
                mPDrawing.VersionNo = versionNo;
                mPDrawing.FullPath = savePath;
                mPDrawing.FileMD5 = fileMD5;
                mainprojectManager.AddMPDrawing(mPDrawing);
            }
            x.IsSuccess = true;
            return x;
        }
        
        /// <summary>
        /// 上传库增量db文件，合并db文件
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> CombineLibFile(CombineLibFileRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (!Guid.TryParse(request.LibGuid, out var libGuid))
            {
                x.Message = $"参数{request.LibGuid}格式有误！";
                return x;
            }
            string UploadModelPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainprojectGuid;
                x.IsSuccess = false;
                return x;

            }
            var _uploadPath = "";
            var dataId = request.DataId;
            var fileName = request.FileName;
            var fileMD5 = request.FileMD5;
            var fileData = request.FileData.ToByteArray();
            var cllibdata = mainprojectManager.MPLibraryDatas.FirstOrDefault(s => s.LibId == libGuid && s.DataId == dataId);
            var filepath = cllibdata.ServerFilePath;

            _uploadPath = Path.GetDirectoryName(filepath);
            //PBIMServerConfigurations.SystemConfig.UploadModelPath + @"\" + @"LibraryFile\" + libGuid + @"\" + dataId + @"\";
            _logger.LogInformation(@"CombineLibFile 上传路径==>" + _uploadPath);
            _uploadPath = _uploadPath.Replace("\\", separator.ToString());
            var savePath = _uploadPath + @"\" + fileName;
            savePath = savePath.Replace("\\", separator.ToString());
            _logger.LogInformation("==>CombineLibFile savePath=" + savePath);

            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }
            Regex regex = new Regex(@"[<>:""/\\|?*]");
            if (regex.IsMatch(fileName))
            {
                x.Message = "文件名包含特殊字符";
                return x;
            }
            try
            {
                using (var fs1 = new FileStream(savePath, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
                {
                    //log.Info("传输文件");
                    fs1.Write(fileData, 0, fileData.Length);
                    fs1.Flush();
                    fs1.Close();
                    fs1.Dispose();
                }

                //调用合并程序，合并文件
                string batPath = AppDomain.CurrentDomain.BaseDirectory;

                DirectoryInfo path_exe = new DirectoryInfo(batPath);
                string path = path_exe.FullName;//.Parent.FullName;
                string MigratorWorkingDirectory = path + @"MergeDB";
                Process process = new Process();
                process.StartInfo.FileName = "dotnet"; //MigratorWorkingDirectory + "PipeLibDBMergerN.dll";
                                                       //process.StartInfo.WorkingDirectory = MigratorWorkingDirectory;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardInput = false;
                process.StartInfo.RedirectStandardOutput = true;//由调用程序获取输出信息
                process.StartInfo.RedirectStandardError = true;//重定向标准错误输出
                process.StartInfo.CreateNoWindow = false;//不显示程序窗口
                process.StartInfo.Arguments = "\""+ MigratorWorkingDirectory + "/PipeLibDBMergerN.dll"+ "\"" + " \"" + savePath + "\" \"" + filepath + "\"";


                _logger.LogInformation("WorkingDirectory:" + process.StartInfo.WorkingDirectory);
                _logger.LogInformation("Arguments:" + process.StartInfo.Arguments);
                process.Start();//启动程序
                string output = process.StandardOutput.ReadToEnd();
                //string error = process.StandardError.ReadToEnd();
                process.WaitForExit();//等待程序执行完退出进程
                int code = process.ExitCode;
                if (code != 0)
                {
                    _logger.LogInformation("ExitCode:" + code);
                    process.Close();
                }
                else
                {
                    _logger.LogInformation("迁移程序执行结束output" + output);
                    process.Close();
                }
                mainprojectManager.UpdateLibDataFileServerPath(dataId, filepath, fileMD5);
                x.IsSuccess = true;
            }
            catch (Exception ex)
            {

                _logger.LogInformation("CombineLibFile err=>" + ex.Message);
                x.Message = ex.Message;
                x.IsSuccess = false;
            }
            
            return x;
        }

        public override async Task<GrpcResult> UploadLibFileDataWithMD5(UploadLibFileDataWithMD5Request request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (!Guid.TryParse(request.LibGuid, out var libGuid))
            {
                x.Message = $"参数{request.LibGuid}格式有误！";
                return x;
            }
            string UploadModelPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                UploadModelPath = _urls.UploadRootPath;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                UploadModelPath = _urls.LoadDataSavePath;
            }
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainprojectGuid;
                x.IsSuccess = false;
                return x;

            }
            var dataId = request.DataId;
            var fileName = request.FileName;
            var fileMD5 = request.FileMD5;
            var fileData = request.FileData.ToByteArray();

            var _uploadPath = UploadModelPath + @"\" + @"MPLibraryFile\" + libGuid + @"\" + dataId + @"\";
            _uploadPath = _uploadPath.Replace("\\", separator.ToString());
            //PBIMServerConfigurations.SystemConfig.UploadModelPath + @"\" + @"LibraryFile\" + libGuid + @"\" + dataId + @"\";
            //log.Info(@"CombineLibFile 上传路径==>" + _uploadPath);
            var savePath = _uploadPath + @"\" + fileName;
            savePath = savePath.Replace("\\", separator.ToString());
            //log.Info(username + "==>CombineLibFile savePath=" + savePath);

            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }
            Regex regex = new Regex(@"[<>:""/\\|?*]");
            if (regex.IsMatch(fileName))
            {
                x.Message = "文件名包含特殊字符";
                return x;
            }
            using (var fs1 = new FileStream(savePath, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite, fileData.Length, FileOptions.Asynchronous))
            {
                //log.Info("传输文件");
                fs1.Write(fileData, 0, fileData.Length);
                fs1.Flush();
                fs1.Close();
                fs1.Dispose();
            }

            //更新serverfilepath

            mainprojectManager.UpdateLibDataFileServerPath(dataId, savePath, fileMD5);
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GrpcResult> SetMPTeamProjectInitState(SetMPTeamProjectInitStateRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.MainprojectGuid, out var mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            if (!Guid.TryParse(request.SubProjectGuid, out var subProjectGuid))
            {
                x.Message = $"参数{request.SubProjectGuid}格式有误！";
                return x;
            }
            var errMsg = "";
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            if (mainprojectManager == null)
            {
                x.Message = "未找到项目，项目id：" + mainprojectGuid;
                x.IsSuccess = false;
                return x;

            }
            var initState = request.InitState;
            var mptp = mainprojectManager.MPTeamProjects.FirstOrDefault(c => c.ID == subProjectGuid);
            if (mptp != null)
            {
                mptp.InitState = initState;
                var ret = mainprojectManager.UpdateMPTeamProject(mptp);
                x.IsSuccess = ret;
                x.Message = "修改模型初始化状态失败";
                return x;

            }
            else
            {
                errMsg = "未找到id为" + subProjectGuid + "的模型";
                //log.Info(username + "==>GetMPTeamProjectInitState " + errMsg);
                x.Message = errMsg;
                x.IsSuccess = false;
                return x;
            }
        }


        /// <summary>
        /// 上传项目 库树数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<UploadLibraryTreeNodeDataResponse> UploadLibraryTreeNodeData(UploadLibraryTreeNodeDataRequest request, ServerCallContext context)
        {
            _logger.LogInformation("UploadLibraryTreeNodeData 001");
            var x = new UploadLibraryTreeNodeDataResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            _logger.LogInformation("UploadLibraryTreeNodeData 002");
            _logger.LogInformation("UploadLibraryTreeNodeData begin");
            Dictionary<string, int> version = new Dictionary<string, int>();
            string errMsg = "";
            var dicMainprojectGuidLibGuid = request.DicMainprojectGuidLibGuid;
            //PbimLog.Info(username + "==>UploadLibraryTreeNodeData begin");
            string desc = currentUser.LoginName + "上传项目树";
            var mainprojectList = dicMainprojectGuidLibGuid.Select(s => s.Mainprojectguid).ToList();
            _logger.LogInformation("UploadLibraryTreeNodeData 003");
            //PbimLog.Info(username + "==>UploadLibraryTreeNodeData mainprojectList count:" + mainprojectList.Count);
            foreach (var mainproject in dicMainprojectGuidLibGuid)
            {
                var libGuidList = new List<Guid>();
                if (!Guid.TryParse(mainproject.Mainprojectguid, out var mainprojectGuid))
                {
                    x.Message = $"参数{mainproject.Mainprojectguid}格式有误！";
                    return x;
                }
                libGuidList = _mapper.Map<List<Guid>>(mainproject.LibguidList);
                //PbimLog.Info(username + "==>UploadLibraryTreeNodeData libGuidList count:" + libGuidList.Count);
                var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
                var curMaxVer = 1;
                //查询当前最大版本号
                foreach (var libGuid in libGuidList)
                {
                    //PbimLog.Info(username + "==>UploadLibraryTreeNodeData libGuid:" + libGuid);
                    var curLibVerList = mainprojectManager.MPTreeNodesVersions.Where(v => v.LibId == libGuid).ToList();
                    if (curLibVerList.Any())
                    {
                        curMaxVer = curLibVerList.Max(v => v.VersionNo);
                        curMaxVer += 1;
                    }
                    MPTreeNodeVersion newversion = new MPTreeNodeVersion
                    {
                        LibId = libGuid,
                        VersionNo = curMaxVer,
                        UserName = currentUser.LoginName,
                        UploadTime = DateTime.Now
                    };

                    var retNo = mainprojectManager.SaveLibVersion(newversion);
                    if (retNo <= 0)
                    {
                        errMsg = "生成版本号错误";
                        x.Message = errMsg;
                        return x;
                    }
                    if (!version.ContainsKey(libGuid.ToString()))
                    {
                        version.Add(libGuid.ToString(), retNo);
                    }
                    //PbimLog.Info(username + "==>组装提交数据");
                    var treeNodes = request.TreeNodes;
                    var libDatas = request.LibDatas;
                    //需要增加的项目树列表
                    _logger.LogInformation("UploadLibraryTreeNodeData 004");
                    List<GrpcMPCatalogTreeNode> addtreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibTreeNodeDataCommand>())
                                                .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    var tempadd = addtreeDatas.Take(3).Select(a => a.InstanceId).ToList();

                    var treeidlist = addtreeDatas.Where(a => a.ParentNodeId == -2).ToList();

                    _logger.LogInformation("UploadLibraryTreeNodeData 005");
                    //记录treeid到library库中

                    //PbimLog.Info(username + "==>UploadLibraryTreeNodeData tempadd:" + string.Join(",", tempadd));
                    //需要修改的
                    List<GrpcMPCatalogTreeNode> modifytreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibTreeNodeDataCommand>())
                                                .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    //需要删除的
                    List<GrpcMPCatalogTreeNode> deltreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibTreeNodeDataCommand>())
                                                .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    var deletetreeidlist = deltreeDatas.Where(a => a.ParentNodeId == -2).Select(s => s.TreeId).Distinct().ToList();
                    //需要增加的库数据
                    List<GrpcMPLibraryData> addLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibraryDataCommand>())
                                                .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    //需要修改的库
                    List<GrpcMPLibraryData> modifyLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibraryDataCommand>())
                                                .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    //需要删除的库
                    List<GrpcMPLibraryData> delLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibraryDataCommand>())
                                                .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                .ToList();
                    var instanceIds = (addtreeDatas.Select(m => m.InstanceId)
                                    .Union(modifytreeDatas.Select(n => n.InstanceId))
                                    .Union(deltreeDatas.Select(d => d.InstanceId))
                                    ).ToList();

                    _logger.LogInformation("UploadLibraryTreeNodeData 006");
                    var retVal = _libRepository.UnlockCLLibTreeNode(currentUser.LoginName, instanceIds, libGuid);
                    if (!retVal)
                    {
                        //PbimLog.Info("UploadLibraryTreeNodeData==>" + username + "未能成功解锁锁定构件");
                    }

                    using (var scope = new TransactionScope(
                        TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                        TransactionScopeAsyncFlowOption.Enabled))
                    {
                        try
                        {
                            _logger.LogInformation("UploadLibraryTreeNodeData CheckInCLTreeNode begin");
                            _logger.LogInformation("UploadLibraryTreeNodeData 007 "+ addtreeDatas.Count+ "-" + modifytreeDatas.Count + "-" + deltreeDatas.Count + "-");
                            //var mpManager = _teamManager.GetMainProjectManager(mainprojectGuid);
                            var uptreeret = mainprojectManager.CheckInCLTreeNode(_mapper.Map<List<MPCatalogTreeNode>>(addtreeDatas), _mapper.Map<List<MPCatalogTreeNode>>(modifytreeDatas), _mapper.Map<List<MPCatalogTreeNode>>(deltreeDatas), libGuid, retNo);
                            _logger.LogInformation("UploadLibraryTreeNodeData 008 " + addLibDatas.Count + "-" + modifyLibDatas.Count + "-" + delLibDatas.Count + "-");
                            var uplibret = mainprojectManager.CheckInCLLibData(_mapper.Map<List<MPLibraryData>>(addLibDatas), _mapper.Map<List<MPLibraryData>>(modifyLibDatas), _mapper.Map<List<MPLibraryData>>(delLibDatas), libGuid, retNo);
                            _logger.LogInformation("UploadLibraryTreeNodeData 009"); 
                            _libRepository.AddCLMainprojectLib(mainprojectGuid, libGuid, _mapper.Map<List<MPCatalogTreeNode>>(treeidlist));
                            _logger.LogInformation("UploadLibraryTreeNodeData 010");
                            _libRepository.DeleteMainprojectLib(mainprojectGuid, libGuid, deletetreeidlist);

                            scope.Complete(); 
                            _logger.LogInformation("UploadLibraryTreeNodeData 011");
                            _logger.LogInformation("UploadLibraryTreeNodeData CheckInCLTreeNode end");
                            //PbimLog.Info(username + "Task.Run==>UploadLibraryTreeNodeData===> addtreeDatas:" + addtreeDatas.Count + "|modifytreeDatas:" + modifytreeDatas.Count + "|deltreeDatas:" + deltreeDatas.Count);
                            //PbimLog.Info(username + "Task.Run==>UploadLibraryTreeNodeData===> addLibDatas:" + addLibDatas.Count + "|modifyLibDatas:" + modifyLibDatas.Count + "|delLibDatas:" + delLibDatas.Count);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "UploadLibraryTreeNodeData CheckInCLTreeNode error");
                            x.IsSuccess = false;
                            x.Message = ex.Message;
                            //PbimLog.Info(username + "|libGuid==>" + libGuid + "==>UploadLibraryTreeNodeData===>持久化到数据库 error" + ex.Message);
                        }
                    }

                }
            }
            _logger.LogInformation("UploadLibraryTreeNodeData 012");

            x.IsSuccess = true;
            x.Version.Add(version);
            return x;
        }


        public override async Task<UploadLibraryTreeNodeDataLargeResponse> UploadLibraryTreeNodeDataLarge(UploadLibraryTreeNodeDataLargeRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter requestid:{requestid},sessionid:{sessionid}", context.Method, request.UploadRequestId,request.SessionId);
            var x = new UploadLibraryTreeNodeDataLargeResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            string errMsg = "";
            var username = currentUser.LoginName;
            var uploadRequestId = request.UploadRequestId;
            var dicMainprojectGuidLibGuid = request.DicMainprojectGuidLibGuid;
            var mainproject = dicMainprojectGuidLibGuid.FirstOrDefault();
            _logger.LogInformation(username + "==>UploadLibraryTreeNodeDataLarge begin;uploadRequestId:" + uploadRequestId);
            var isLastUpload = request.IsLastUpload;
            var libVersion = request.LibVersion.ToDictionary();
            Dictionary<string, int> version = new Dictionary<string, int>();
            var libGuidList = new List<Guid>();
            if (!Guid.TryParse(mainproject.Mainprojectguid, out var mainprojectGuid))
            {
                x.Message = $"参数{mainproject.Mainprojectguid}格式有误！";
                x.IsSuccess = false;
                return x;
            }
            libGuidList = _mapper.Map<List<Guid>>(mainproject.LibguidList);
            var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
            try
            {
                if (!isLastUpload)
                {
                    //libVersion无元素时为第一次上传，需要生成版本号
                    var curMaxVer = 1;
                    var treeNodes = request.TreeNodes;
                    var libDatas = request.LibDatas;
                    foreach (var libGuid in libGuidList)
                    {
                        var retNo = 0;
                        if (libVersion.Count<=0)
                        {
                            var curLibVerList = mainprojectManager.MPTreeNodesVersions.Where(v => v.LibId == libGuid).ToList();
                            if (curLibVerList.Any())
                            {
                                curMaxVer = curLibVerList.Max(v => v.VersionNo);
                                curMaxVer += 1;
                            }
                            MPTreeNodeVersion newversion = new MPTreeNodeVersion
                            {
                                LibId = libGuid,
                                VersionNo = curMaxVer,
                                UserName = currentUser.LoginName,
                                UploadTime = DateTime.Now
                            };

                            retNo = mainprojectManager.SaveLibVersion(newversion);
                            if (retNo <= 0)
                            {
                                errMsg = "生成版本号错误";
                                x.Message = errMsg;
                                x.IsSuccess = false;
                                return x;
                            }
                            if (!version.ContainsKey(libGuid.ToString()))
                            {
                                version.Add(libGuid.ToString(), retNo);
                            }
                        }
                        else
                        {
                            if (libVersion.TryGetValue(libGuid.ToString(), out retNo))
                            {
                                version.Add(libGuid.ToString(), retNo);
                            }
                        }
                        
                        //写入本地文件

                        //需要增加的项目树列表
                        List<GrpcMPCatalogTreeNode> addtreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var tempadd = addtreeDatas.Take(3).Select(a => a.InstanceId).ToList();

                        var treeidlist = addtreeDatas.Where(a => a.ParentNodeId == -2).ToList();

                        //记录treeid到library库中

                        //PbimLog.Info(username + "==>UploadLibraryTreeNodeData tempadd:" + string.Join(",", tempadd));
                        //需要修改的
                        List<GrpcMPCatalogTreeNode> modifytreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要删除的
                        List<GrpcMPCatalogTreeNode> deltreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var deletetreeidlist = deltreeDatas.Where(a => a.ParentNodeId == -2).Select(s => s.TreeId).Distinct().ToList();
                        //需要增加的库数据
                        List<GrpcMPLibraryData> addLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要修改的库
                        List<GrpcMPLibraryData> modifyLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要删除的库
                        List<GrpcMPLibraryData> delLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var instanceIds = (addtreeDatas.Select(m => m.InstanceId)
                                        .Union(modifytreeDatas.Select(n => n.InstanceId))
                                        .Union(deltreeDatas.Select(d => d.InstanceId))
                                        ).ToList();

                        mainprojectManager.WriteDataToLocalFile(uploadRequestId, _mapper.Map<List<MPCatalogTreeNode>>(addtreeDatas),
                            _mapper.Map<List<MPCatalogTreeNode>>(modifytreeDatas), _mapper.Map<List<MPCatalogTreeNode>>(deltreeDatas),
                            _mapper.Map<List<MPLibraryData>>(addLibDatas),
                            _mapper.Map<List<MPLibraryData>>(modifyLibDatas), _mapper.Map<List<MPLibraryData>>(delLibDatas), libGuid, retNo);

                        var retVal = _libRepository.UnlockCLLibTreeNode(currentUser.LoginName, instanceIds, libGuid);
                        if (!retVal)
                        {
                            _logger.LogInformation("UploadLibraryTreeNodeDataLarge==>" + username + "未能成功解锁锁定构件");
                        }

                        _libRepository.AddCLMainprojectLib(mainprojectGuid, libGuid, _mapper.Map<List<MPCatalogTreeNode>>(treeidlist));
                        _libRepository.DeleteMainprojectLib(mainprojectGuid, libGuid, deletetreeidlist);
                    }
                    x.Version.Add(version);
                    x.IsSuccess = true;
                    return x;
                }
                else
                {
                    var curMaxVer = 1;
                    var treeNodes = request.TreeNodes;
                    var libDatas = request.LibDatas;
                    foreach (var libGuid in libGuidList)
                    {
                        var retNo = 0;
                        if (libVersion.Count <= 0)
                        {
                            var curLibVerList = mainprojectManager.MPTreeNodesVersions.Where(v => v.LibId == libGuid).ToList();
                            if (curLibVerList.Any())
                            {
                                curMaxVer = curLibVerList.Max(v => v.VersionNo);
                                curMaxVer += 1;
                            }
                            MPTreeNodeVersion newversion = new MPTreeNodeVersion
                            {
                                LibId = libGuid,
                                VersionNo = curMaxVer,
                                UserName = currentUser.LoginName,
                                UploadTime = DateTime.Now
                            };

                            retNo = mainprojectManager.SaveLibVersion(newversion);
                            if (retNo <= 0)
                            {
                                errMsg = "生成版本号错误";
                                x.Message = errMsg;
                                x.IsSuccess = false;
                                return x;
                            }
                            if (!version.ContainsKey(libGuid.ToString()))
                            {
                                version.Add(libGuid.ToString(), retNo);
                            }
                        }
                        else
                        {
                            if (libVersion.TryGetValue(libGuid.ToString(), out retNo))
                            {
                                version.Add(libGuid.ToString(), retNo);
                            }
                        }

                        //写入本地文件

                        //需要增加的项目树列表
                        List<GrpcMPCatalogTreeNode> addtreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var tempadd = addtreeDatas.Take(3).Select(a => a.InstanceId).ToList();

                        var treeidlist = addtreeDatas.Where(a => a.ParentNodeId == -2).ToList();

                        //记录treeid到library库中

                        //PbimLog.Info(username + "==>UploadLibraryTreeNodeData tempadd:" + string.Join(",", tempadd));
                        //需要修改的
                        List<GrpcMPCatalogTreeNode> modifytreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要删除的
                        List<GrpcMPCatalogTreeNode> deltreeDatas = (treeNodes.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibTreeNodeDataCommand>())
                                                    .SelectMany(c => c.TreeNodeList.ToList() ?? new List<GrpcMPCatalogTreeNode>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var deletetreeidlist = deltreeDatas.Where(a => a.ParentNodeId == -2).Select(s => s.TreeId).Distinct().ToList();
                        //需要增加的库数据
                        List<GrpcMPLibraryData> addLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Add)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要修改的库
                        List<GrpcMPLibraryData> modifyLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Mod)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        //需要删除的库
                        List<GrpcMPLibraryData> delLibDatas = (libDatas.Where(a => a.MainProjectGuid == mainprojectGuid.ToString() && a.Type.Equals(GrpcMPCommandType.Del)) ?? new List<LibraryDataCommand>())
                                                    .SelectMany(c => c.LibDataList.ToList() ?? new List<GrpcMPLibraryData>()).Where(s => s.LibId == libGuid.ToString())
                                                    .ToList();
                        var instanceIds = (addtreeDatas.Select(m => m.InstanceId)
                                        .Union(modifytreeDatas.Select(n => n.InstanceId))
                                        .Union(deltreeDatas.Select(d => d.InstanceId))
                                        ).ToList();

                        mainprojectManager.WriteDataToLocalFile(retNo, _mapper.Map<List<MPCatalogTreeNode>>(addtreeDatas),
                            _mapper.Map<List<MPCatalogTreeNode>>(modifytreeDatas), _mapper.Map<List<MPCatalogTreeNode>>(deltreeDatas),
                            _mapper.Map<List<MPLibraryData>>(addLibDatas),
                            _mapper.Map<List<MPLibraryData>>(modifyLibDatas), _mapper.Map<List<MPLibraryData>>(delLibDatas), libGuid, retNo);

                        var retVal = _libRepository.UnlockCLLibTreeNode(currentUser.LoginName, instanceIds, libGuid);
                        if (!retVal)
                        {
                            _logger.LogInformation("UploadLibraryTreeNodeDataLarge==>" + username + "未能成功解锁锁定构件");
                        }

                        _libRepository.AddCLMainprojectLib(mainprojectGuid, libGuid, _mapper.Map<List<MPCatalogTreeNode>>(treeidlist));
                        _libRepository.DeleteMainprojectLib(mainprojectGuid, libGuid, deletetreeidlist);
                    }
                    _logger.LogInformation("UploadLibraryTreeNodeDataLarge CheckInMPLibDataToDBFromLocalFile begin");

                    mainprojectManager.CheckInMPLibDataToDBFromLocalFile(uploadRequestId);
                    _logger.LogInformation("UploadLibraryTreeNodeDataLarge CheckInMPLibDataToDBFromLocalFile end");
                    x.IsSuccess = true;
                    return x;
                }
            }
            catch (Exception ex)
            {
                _logger.LogInformation("UploadLibraryTreeNodeDataLarge error:"+ex.StackTrace);
                x.IsSuccess = false;
                x.Message = ex.Message;
            }
            return x;
        }


        public override async Task<LockLibNodeWithLockTypeForceResponse> LockLibNodeWithLockTypeForce(
    LockLibNodeWithLockTypeForceRequest request,
    ServerCallContext context)
        {
            var response = new LockLibNodeWithLockTypeForceResponse();
            var stopwatch = Stopwatch.StartNew();
            var totalStopwatch = Stopwatch.StartNew();

            try
            {
                // 1. 初始验证和日志记录 
                _logger.LogInformation("Begin gRPC call {Method} with session {SessionId}",
                    context.Method, request.SessionId);

                // 2. 用户验证 
                var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
                if (currentUser == null)
                {
                    response.Message = "用户未登录！";
                    return response;
                }

                var username = currentUser.LoginName;
                _logger.LogInformation("{Username} initiating LockLibNodeWithLockTypeForce", username);

                // 3. 参数验证 
                if (!Guid.TryParse(request.MainprojectGuid, out var mainProjectGuid))
                {
                    response.Message = $"参数MainprojectGuid {request.MainprojectGuid}格式有误！";
                    return response;
                }

                if (!Guid.TryParse(request.LibGuid, out var libGuid))
                {
                    response.Message = $"参数LibGuid {request.LibGuid}格式有误！";
                    return response;
                }

                // 4. 检查用户是否存在 
                var teamMember = _teamRepository.Members.FirstOrDefault(s => s.LoginName == username);
                if (teamMember == null)
                {
                    response.Message = "用户不存在";
                    response.IsSuccess = false;
                    return response;
                }

                // 5. 准备数据 
                var instanceIds = request.Instanceids.ToList();
                var shareLockInstanceIds = request.ShareLockInstanceIds.ToList();
                var allInstances = instanceIds.Union(shareLockInstanceIds).ToList();

                _logger.LogInformation("{Username} 请求锁定构件 - 独占锁: {InstanceIds}, 共享锁: {ShareLockIds}",
                    username,
                    string.Join(",", instanceIds.Take(20)),
                    string.Join(",", shareLockInstanceIds.Take(20)));

                // 6. 获取主项目仓库 
                var mainProjectRepository = _teamRepository.GetMainProjectRepository(mainProjectGuid);

                // 7. 处理锁冲突 
                stopwatch.Restart();
                var allLockedComponents = _libRepository.CLTreeNodesLocks
                    .Where(c => c.LibId == libGuid)
                    .ToList();

                // 处理独占锁冲突 
                var conflictLocks = allLockedComponents
                    .Where(a => instanceIds.Contains(a.NodeId) && a.LockUserName != username)
                    .ToList();

                if (conflictLocks.Any())
                {
                    foreach (var conflict in conflictLocks)
                    {
                        if (!response.SingleConfilict.ContainsKey(conflict.NodeId))
                        {
                            response.SingleConfilict.Add(conflict.NodeId, conflict.LockUserName);
                        }
                    }

                    instanceIds = instanceIds.Except(response.SingleConfilict.Keys).ToList();
                    _logger.LogInformation("{Username} 独占锁冲突: {Conflicts}",
                        username,
                        string.Join(",", response.SingleConfilict.Take(3)));
                }

                // 处理共享锁冲突 
                var shareConflictLocks = allLockedComponents
                    .Where(l => l.LockType == 0 && l.LockUserName != username && shareLockInstanceIds.Contains(l.NodeId))
                    .ToList();

                if (shareConflictLocks.Any())
                {
                    foreach (var conflict in shareConflictLocks)
                    {
                        if (!response.ShareConfilict.ContainsKey(conflict.NodeId))
                        {
                            response.ShareConfilict.Add(conflict.NodeId, conflict.LockUserName);
                        }
                    }

                    shareLockInstanceIds = shareLockInstanceIds.Except(response.ShareConfilict.Keys).ToList();
                    _logger.LogInformation("{Username} 共享锁冲突: {Conflicts}",
                        username,
                        string.Join(",", response.ShareConfilict.Take(3)));
                }
                stopwatch.Stop();
                _logger.LogInformation("{Username} 锁冲突检查耗时: {Elapsed}ms",
                    username, stopwatch.ElapsedMilliseconds);

                // 8. 版本验证 
                stopwatch.Restart();
                List<long> oldVersionDatas = new List<long>();
                if (mainProjectRepository != null && mainProjectGuid != Guid.Empty)
                {

                    ValidateComponentVersions(
                    mainProjectRepository,
                    shareLockInstanceIds,
                    request.ShareLockVersion.ToList(),
                    libGuid,
                    username,
                    response,
                    isShareLock: true,
                    out oldVersionDatas);
                    shareLockInstanceIds = shareLockInstanceIds.Except(oldVersionDatas).ToList();
                    ValidateComponentVersions(
                        mainProjectRepository,
                        instanceIds,
                        request.Versions.ToList(),
                        libGuid,
                        username,
                        response,
                        isShareLock: false,
                        out oldVersionDatas);
                    instanceIds = instanceIds.Except(oldVersionDatas).ToList();
                }
                else
                {
                    ValidataCompanyComponentVersions(
                        _libRepository,
                        shareLockInstanceIds,
                        request.ShareLockVersion.ToList(),
                        libGuid,
                        username,
                        response,
                        isShareLock: true,
                        out oldVersionDatas);
                    shareLockInstanceIds = shareLockInstanceIds.Except(oldVersionDatas).ToList();
                    ValidataCompanyComponentVersions(
                        _libRepository,
                        shareLockInstanceIds,
                        request.ShareLockVersion.ToList(),
                        libGuid,
                        username,
                        response,
                        isShareLock: false,
                        out oldVersionDatas);
                    instanceIds = instanceIds.Except(oldVersionDatas).ToList();
                }
                
                stopwatch.Stop();
                _logger.LogInformation("{Username} 版本验证耗时: {Elapsed}ms",
                    username, stopwatch.ElapsedMilliseconds);

                // 9. 准备锁定数据 
                stopwatch.Restart();
                var componentsToLock = new List<CLTreeNodeLock>();

                if (shareLockInstanceIds.Any())
                {
                    componentsToLock.AddRange(shareLockInstanceIds.Select(id => new CLTreeNodeLock
                    {
                        NodeId = id,
                        LockUserName = username,
                        LibId = libGuid,
                        LockType = 1 // 共享锁 
                    }));
                    response.ShareLockSuc.AddRange(shareLockInstanceIds);
                }

                if (instanceIds.Any())
                {
                    componentsToLock.AddRange(instanceIds.Select(id => new CLTreeNodeLock
                    {
                        NodeId = id,
                        LockUserName = username,
                        LibId = libGuid,
                        LockType = 0 // 独占锁 
                    }));
                    response.SingleLockSuc.AddRange(instanceIds);
                }
                stopwatch.Stop();
                _logger.LogInformation("{Username} 准备锁定数据耗时: {Elapsed}ms",
                    username, stopwatch.ElapsedMilliseconds);

                // 10. 执行锁定 
                if (request.DoLock && componentsToLock.Any())
                {
                    stopwatch.Restart();
                    var lockResult = _libRepository.LockCLLibTreeNode(componentsToLock);
                    stopwatch.Stop();

                    _logger.LogInformation("{Username} 锁定操作耗时: {Elapsed}ms",
                        username, stopwatch.ElapsedMilliseconds);

                    if (!lockResult)
                    {
                        response.Message = "锁定失败：锁定构件时出现异常";
                        return response;
                    }
                }

                response.IsSuccess = true;
                return response;
            }
            finally
            {
                totalStopwatch.Stop();
                _logger.LogInformation("LockLibNodeWithLockTypeForce 总耗时: {Elapsed}ms",
                    totalStopwatch.ElapsedMilliseconds);
            }
        }

        private void ValidataCompanyComponentVersions(
            ILibraryRepository libraryRepository,
            List<long> instanceIds,
            List<int> versions,
            Guid libGuid,
            string username,
            LockLibNodeWithLockTypeForceResponse response,
            bool isShareLock,
            out List<long> exDataIds)

        {
            exDataIds = new List<long>();
            if (!instanceIds.Any()) return;

            var versionDict = new Dictionary<long, int>();
            for (int i = 0; i < instanceIds.Count; i++)
            {
                if (!versionDict.ContainsKey(instanceIds[i]))
                {
                    versionDict.Add(instanceIds[i], versions[i]);
                }
            }

            var serverData = libraryRepository.CLCatalogTreeNodes
                .AsNoTracking()
                .InRange(a => a.InstanceId, 10000, instanceIds)
                //.Where(a => instanceIds.Contains(a.InstanceId))
                .Select(s => new { s.InstanceId, s.VersionNo })
                .ToList();

            // 检查是否存在无效构件 
            if (instanceIds.Count > serverData.Count)
            {
                var validIds = serverData.Select(a => a.InstanceId).ToList();
                var invalidIds = instanceIds.Except(validIds).ToList();

                foreach (var id in invalidIds)
                {
                    var conflictDict = isShareLock ? response.ShareConfilict : response.SingleConfilict;
                    if (!conflictDict.ContainsKey(id))
                    {
                        conflictDict.Add(id, "");
                    }
                }

                _logger.LogInformation("{Username} 无效构件: {InvalidIds}",
                    username, string.Join(",", invalidIds.Take(20)));
            }

            // 检查版本是否最新 
            var outdatedComponents = (from h in versionDict
                                      join d in serverData
                                      on h.Key equals d.InstanceId
                                      where h.Value < d.VersionNo
                                      select h.Key).ToList();

            if (outdatedComponents.Any())
            {
                foreach (var id in outdatedComponents)
                {
                    var conflictDict = isShareLock ? response.ShareConfilict : response.SingleConfilict;
                    if (!conflictDict.ContainsKey(id))
                    {
                        conflictDict.Add(id, "");
                    }
                }
                exDataIds = outdatedComponents;
                _logger.LogInformation("{Username} 版本过期的构件: {OutdatedIds}",
                    username, string.Join(",", outdatedComponents.Take(3)));
            }
        }

        private void ValidateComponentVersions(
            IMainProjectRepository mainProjectRepository,
            List<long> instanceIds,
            List<int> versions,
            Guid libGuid,
            string username,
            LockLibNodeWithLockTypeForceResponse response,
            bool isShareLock,
            out List<long> exDataIds)
        {
            exDataIds = new List<long>();

            if (!instanceIds.Any()) return;

            var versionDict = new Dictionary<long, int>();
            for (int i = 0; i < instanceIds.Count; i++)
            {
                if (!versionDict.ContainsKey(instanceIds[i]))
                {
                    versionDict.Add(instanceIds[i], versions[i]);
                }
            }

            var serverData = mainProjectRepository.MPCatalogTreeNodes
                .AsNoTracking()
                .InRange(a => a.InstanceId, 10000, instanceIds)
                //.Where(a => instanceIds.Contains(a.InstanceId))
                .Select(s => new { s.InstanceId, s.VersionNo })
                .ToList();

            // 检查是否存在无效构件 
            if (instanceIds.Count > serverData.Count)
            {
                var validIds = serverData.Select(a => a.InstanceId).ToList();
                var invalidIds = instanceIds.Except(validIds).ToList();

                foreach (var id in invalidIds)
                {
                    var conflictDict = isShareLock ? response.ShareConfilict : response.SingleConfilict;
                    if (!conflictDict.ContainsKey(id))
                    {
                        conflictDict.Add(id, "");
                    }
                }

                _logger.LogInformation("{Username} 无效构件: {InvalidIds}",
                    username, string.Join(",", invalidIds.Take(20)));
            }

            // 检查版本是否最新 
            var outdatedComponents = (from h in versionDict
                                      join d in serverData
                                      on h.Key equals d.InstanceId
                                      where h.Value < d.VersionNo
                                      select h.Key).ToList();

            if (outdatedComponents.Any())
            {
                foreach (var id in outdatedComponents)
                {
                    var conflictDict = isShareLock ? response.ShareConfilict : response.SingleConfilict;
                    if (!conflictDict.ContainsKey(id))
                    {
                        conflictDict.Add(id, "");
                    }
                }

                exDataIds = outdatedComponents;
                _logger.LogInformation("{Username} 版本过期的构件: {OutdatedIds}",
                    username, string.Join(",", outdatedComponents.Take(3)));
            }
        }
    }
}
