﻿using AutoMapper;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Infrastructure;
using BimBase.Api.Protos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data.Common;
using System;
using static BimBase.Api.Protos.ProjectFile.GrpcProjectFileQuery;
using System.Threading.Tasks;
using Grpc.Core;
using BimBase.Api.Infrastructure.Domain;
using System.Collections.Generic;
using BimBase.Api.Infrastructure.MainDomain;
using Serilog;
using System.Linq;
using BimBase.Api.Protos.Library;
using BimBase.Api.Infrastructure.LibDomain;
using System.Diagnostics;
using System.IO;
using Google.Protobuf;
using BimBase.Api.Protos.ProjectFile;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.DbInitializer;
namespace BimBase.Api.Grpc
{
    public class ProjectFileQueryService:GrpcProjectFileQueryBase
    {
        private readonly UrlsConfig _urls;
        private readonly ITeamRepository _teamRepository;
        private readonly ILogger<LibraryQueryService> _logger;
        private readonly ILibraryRepository _libRepository;
        private IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;


        public ProjectFileQueryService(IOptions<UrlsConfig> config, ITeamRepository teamRepository
            , ILogger<LibraryQueryService> logger
            , IHttpContextAccessor httpContextAccessor
            , IMapper mapper
            , LibraryDbContext libraryDbContext)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public override async Task<GetNewestVNoResponse> GetNewestVNo(GetNewestVNoRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetNewestVNoResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.Keytoken))
            {
                x.Message = $"参数 Keytoken 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.ProjectGuid, out var projectGuid))
            {
                x.Message = $"参数{request.ProjectGuid}格式有误！";
                return x;
            }
            var keytoken = request.Keytoken;
            var username = currentUser.LoginName;
            string errMsg = "";
            int maxVNo = 0;
            var _modelManager = _teamRepository.GetProjectRepository(projectGuid);
            var modelfiles = _modelManager.ModelFiles.Where(m => m.FileName == keytoken).ToList();
            if (!modelfiles.Any())
            {
                errMsg = "数据库中不存在此文件";
                _logger.LogInformation(errMsg);
                x.Message = errMsg;
                x.IsSuccess = false;
                x.MaxVNo = 0;
                return x;
            }
            maxVNo = modelfiles.Max(a => a.VersionNo);
            if (maxVNo > 0)
            {
                _logger.LogInformation(@"查询到最大版本号===》" + maxVNo);
                x.MaxVNo = maxVNo;
            }
            
            x.IsSuccess = true;
            return x;
        }

        public override async Task<DownloadNewestFileResponse> DownloadNewestFile(DownloadNewestFileRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new DownloadNewestFileResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrWhiteSpace(request.Keytoken))
            {
                x.Message = $"参数 Keytoken 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.ProjectGuid, out var projectGuid))
            {
                x.Message = $"参数{request.ProjectGuid}格式有误！";
                return x;
            }
            var keytoken = request.Keytoken;
            var username = currentUser.LoginName;
            string message = "";
            var requestId = request.RequestId;
            var _modelManager = _teamRepository.GetProjectRepository(projectGuid);
            var modelFiles = _modelManager.ModelFiles.ToList();//.Where(a => a.FileName == keytoken);
            _logger.LogInformation("开始下载文件");
            if (!modelFiles.Any())
            {
                message = "未上传过版本！";
                x.Message = message;
                x.RequestId = -1;
                x.IsSuccess = false;
                return x;
            }
            var maxNo = modelFiles.Max(v => v.VersionNo);
            var modelFile = _modelManager.ModelFiles.Where(a => a.VersionNo == maxNo).FirstOrDefault();//.Where(a => a.FileName == keytoken && a.VersionNo == maxNo).FirstOrDefault();
            if (modelFile == null)
            {
                message = "该版本文件未上传到服务端！";
                x.Message = message;
                x.IsSuccess = false;
                x.RequestId = -1;
                return x;
            }
            var readFileName = modelFile.FilePath;
            if (!File.Exists(readFileName))
            {
                message = "服务端不存在该文件!";
                x.Message = message;
                x.IsSuccess = false;
                x.RequestId = -1;
                return x;
            }
            _logger.LogInformation("服务端存在该文件");

            //判断是否需要分段,保证每段大小不大于500M
            int limitLength = 1024 * 1024 * 500;
            long dataLength = 0;//每次实际读取长度
            long currentFilesize = 0;

            var stream = File.OpenRead(readFileName);
            _logger.LogInformation("文件总长度" + stream.Length);
            string requestIdKey = "DownloadWithLargeData" + requestId;
            x.FileInfo = new ModelFileTransfer();
            if (stream.Length <= limitLength)
            {
                _logger.LogInformation("不分段下载");
                x.FileInfo.Length = stream.Length;
                x.FileInfo.Offset = 0;
                byte[] outFileData = new byte[stream.Length];
                stream.Position = 0;
                stream.Read(outFileData, 0, outFileData.Length);
                stream.Close();
                x.FileInfo.Data = ByteString.CopyFrom(outFileData);
                x.RequestId = 0;
                _logger.LogInformation("读取完毕-未分段" + "fileInfo.Length==" + x.FileInfo.Length);
            }
            else
            {
                _logger.LogInformation("分段下载");
                if (request.FilePosition > 0)
                {
                    currentFilesize = request.FilePosition;
                }
                using (stream)
                {
                    if ((stream.Length - currentFilesize) > limitLength)
                    {
                        dataLength = limitLength;
                    }
                    else
                    {
                        dataLength = stream.Length - currentFilesize;
                        requestId = 0;
                    }
                    _logger.LogInformation("datalength:" + dataLength);
                    var outFileData = new byte[dataLength];
                    stream.Position = currentFilesize + dataLength;
                    stream.Read(outFileData, 0, (int)dataLength);

                    x.FileInfo.Data = ByteString.CopyFrom(outFileData);
                    x.CurrentFilesize = currentFilesize + dataLength;
                    _logger.LogInformation("本次读取完毕" + "已下载数据大小" + x.CurrentFilesize);
                    stream.Close();
                }
                x.RequestId = requestId;
            }
            x.VersionNo = maxNo;
            x.IsSuccess = true;
            return x;
        }
    }
}
