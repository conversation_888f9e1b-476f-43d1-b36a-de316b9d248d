﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class SchemaVersion
    {
        
        public int ID { get; set; }
        
        public short MajorVersion { get; set; }
        
        public short MinorVersion { get; set; }
        
        public short DevelopVersion { get; set; }
        
        public string SdkVersion { get; set; }
        
        public byte[] SchemaData { get; set; }

        public override string ToString()
        {
            return MajorVersion.ToString() + "." + MinorVersion.ToString() + "." + DevelopVersion.ToString();
        }
    }
}
