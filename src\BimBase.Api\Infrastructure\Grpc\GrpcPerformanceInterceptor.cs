using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Grpc.Core;
using Grpc.Core.Interceptors;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using BimBase.Api.Infrastructure.Repositories;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.Net;
// ========== MQ日志功能新增依赖 ==========
using BimBase.Api.Infrastructure.MQLog;
using System.Text.Json;
using Azure;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// gRPC服务拦截器，用于记录性能日志、元数据和异常处理
    /// 
    /// 【功能说明】
    /// - 原有功能：gRPC拦截、用户信息提取、性能监控、异常处理、响应元数据添加
    /// - 新增功能：MQ响应日志记录（response阶段）和错误日志记录
    /// </summary>
    public class GrpcPerformanceInterceptor : Interceptor
    {
        private readonly ILogger<GrpcPerformanceInterceptor> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly string _serverIp;
        private readonly string _serverName;
        private readonly string _source;
        private readonly RequestParameterFormatter _parameterFormatter;
        private readonly bool _enableMQLogging;
        private readonly IConfiguration _configuration;

        public GrpcPerformanceInterceptor(
            ILogger<GrpcPerformanceInterceptor> logger, 
            IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration,
            RequestParameterFormatter parameterFormatter)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _parameterFormatter = parameterFormatter ?? throw new ArgumentNullException(nameof(parameterFormatter));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            // 获取服务器信息
            _serverIp = GetServerIp();
            _serverName = Environment.MachineName;
            _source = _configuration["RabbitMQ:Source"] ?? "BimBase.Api";

            // ========== MQ日志功能新增初始化 ==========
            _enableMQLogging = _configuration.GetValue<bool>("LogSettings:MQLogging:Enabled", false);
        }

        private string GetServerIp()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// 请求日志信息类
        /// 原有功能：用于记录gRPC请求的完整信息
        /// </summary>
        private class RequestLogInfo
        {
            public string RequestId { get; set; }
            public string SessionId { get; set; }
            public string UserId { get; set; }
            public string UserName { get; set; }
            public string Method { get; set; }
            public StatusCode StatusCode { get; set; }
            public double TotalMilliseconds { get; set; }
            public string ClientIp { get; set; }
            public string ClientVersion { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public string ServerIp { get; set; }
            public string ServerName { get; set; }
            public string InterfaceName { get; set; }
            public string Source { get; set; }
        }

        #region 拦截方法 - 原有功能保持不变

        /// <summary>
        /// 拦截一元调用
        /// 原有功能：拦截一元gRPC调用
        /// </summary>
        public override async Task<TResponse> UnaryServerHandler<TRequest, TResponse>(
            TRequest request,
            ServerCallContext context,
            UnaryServerMethod<TRequest, TResponse> continuation)
        {
            return await ProcessUnaryRequestAsync(
                request,
                context,
                continuation,
                "请求"
            );
        }

        /// <summary>
        /// 拦截服务端流调用
        /// 原有功能：拦截服务端流式gRPC调用
        /// </summary>
        public override async Task ServerStreamingServerHandler<TRequest, TResponse>(
            TRequest request,
            IServerStreamWriter<TResponse> responseStream,
            ServerCallContext context,
            ServerStreamingServerMethod<TRequest, TResponse> continuation)
        {
            await ProcessServerStreamingRequestAsync(
                request,
                responseStream,
                context,
                continuation,
                "服务端流式请求"
            );
        }

        /// <summary>
        /// 拦截客户端流调用
        /// 原有功能：拦截客户端流式gRPC调用
        /// </summary>
        public override async Task<TResponse> ClientStreamingServerHandler<TRequest, TResponse>(
            IAsyncStreamReader<TRequest> requestStream,
            ServerCallContext context,
            ClientStreamingServerMethod<TRequest, TResponse> continuation)
        {
            return await ProcessClientStreamingRequestAsync(
                requestStream,
                context,
                continuation,
                "客户端流式请求"
            );
        }

        /// <summary>
        /// 拦截双向流调用
        /// 原有功能：拦截双向流式gRPC调用
        /// </summary>
        public override async Task DuplexStreamingServerHandler<TRequest, TResponse>(
            IAsyncStreamReader<TRequest> requestStream,
            IServerStreamWriter<TResponse> responseStream,
            ServerCallContext context,
            DuplexStreamingServerMethod<TRequest, TResponse> continuation)
        {
            await ProcessDuplexStreamingRequestAsync(
                requestStream,
                responseStream,
                context,
                continuation,
                "双向流式请求"
            );
        }

        #endregion

        #region 请求处理方法 - 原有功能保持不变

        /// <summary>
        /// 处理一元请求
        /// 原有功能：处理一元gRPC请求的完整生命周期
        /// </summary>
        private async Task<TResponse> ProcessUnaryRequestAsync<TRequest, TResponse>(
            TRequest request,
            ServerCallContext context,
            UnaryServerMethod<TRequest, TResponse> continuation,
            string requestType)
            where TRequest : class
            where TResponse : class
        {
            // 自动注入 request 到 HttpContext.Items
            var httpContext = context.GetHttpContext();
            if (httpContext != null)
            {
                httpContext.Items["GrpcRequest"] = request;
            }
            RequestExecutionContext executionContext = null;

            try
            {
                // 初始化请求执行上下文
                executionContext = new RequestExecutionContext(context.Method, requestType, this, request);

                // 初始化请求上下文并记录开始日志
                await executionContext.InitializeAsync(context, request);

                // 执行实际处理方法
                var response = await continuation(request, context);

                // 记录成功的请求日志并获取日志信息
                var logInfo = executionContext.LogSuccess();

                // 添加尾随元数据
                AddResponseMetadata(context, logInfo);

                return response;
            }
            catch (Exception ex)
            {
                // 处理异常并记录
                if (executionContext == null)
                {
                    executionContext = new RequestExecutionContext(context.Method, requestType, this, request);
                    _logger.LogError(ex, $"初始化请求上下文异常: {context.Method}, RequestId: {RequestContext.Current?.RequestId ?? Guid.NewGuid().ToString()}");
                }

                return executionContext.HandleUnaryException<TResponse>(ex);
            }
        }

        /// <summary>
        /// 处理服务端流请求
        /// 原有功能：处理服务端流式gRPC请求
        /// </summary>
        private async Task ProcessServerStreamingRequestAsync<TRequest, TResponse>(
            TRequest request,
            IServerStreamWriter<TResponse> responseStream,
            ServerCallContext context,
            ServerStreamingServerMethod<TRequest, TResponse> continuation,
            string requestType)
            where TRequest : class
            where TResponse : class
        {
            // 自动注入 request 到 HttpContext.Items
            var httpContext = context.GetHttpContext();
            if (httpContext != null)
            {
                httpContext.Items["GrpcRequest"] = request;
            }
            RequestExecutionContext executionContext = null;

            try
            {
                // 初始化请求执行上下文
                executionContext = new RequestExecutionContext(context.Method, requestType, this, request);

                // 初始化请求上下文并记录开始日志
                await executionContext.InitializeAsync(context, request);

                // 执行实际处理方法
                await continuation(request, responseStream, context);

                // 记录成功的请求日志并获取日志信息
                var logInfo = executionContext.LogSuccess();

                // 添加尾随元数据
                AddResponseMetadata(context, logInfo);
            }
            catch (Exception ex)
            {
                // 处理异常并记录
                if (executionContext == null)
                {
                    executionContext = new RequestExecutionContext(context.Method, requestType, this, request);
                    _logger.LogError(ex, $"初始化请求上下文异常: {context.Method}, RequestId: {RequestContext.Current?.RequestId ?? Guid.NewGuid().ToString()}");
                }

                executionContext.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// 处理客户端流请求
        /// 原有功能：处理客户端流式gRPC请求
        /// </summary>
        private async Task<TResponse> ProcessClientStreamingRequestAsync<TRequest, TResponse>(
            IAsyncStreamReader<TRequest> requestStream,
            ServerCallContext context,
            ClientStreamingServerMethod<TRequest, TResponse> continuation,
            string requestType)
            where TRequest : class
            where TResponse : class
        {
            RequestExecutionContext executionContext = null;

            try
            {
                // 初始化请求执行上下文
                executionContext = new RequestExecutionContext(context.Method, requestType, this, null);

                // 初始化请求上下文并记录开始日志
                await executionContext.InitializeAsync(context, null);

                // 执行实际处理方法
                var response = await continuation(requestStream, context);

                // 记录成功的请求日志并获取日志信息
                var logInfo = executionContext.LogSuccess();

                // 添加尾随元数据
                AddResponseMetadata(context, logInfo);

                return response;
            }
            catch (Exception ex)
            {
                // 处理异常并记录
                if (executionContext == null)
                {
                    executionContext = new RequestExecutionContext(context.Method, requestType, this, null);
                    _logger.LogError(ex, $"初始化请求上下文异常: {context.Method}, RequestId: {RequestContext.Current?.RequestId ?? Guid.NewGuid().ToString()}");
                }

                return executionContext.HandleUnaryException<TResponse>(ex);
            }
        }

        /// <summary>
        /// 处理双向流请求
        /// 原有功能：处理双向流式gRPC请求
        /// </summary>
        private async Task ProcessDuplexStreamingRequestAsync<TRequest, TResponse>(
            IAsyncStreamReader<TRequest> requestStream,
            IServerStreamWriter<TResponse> responseStream,
            ServerCallContext context,
            DuplexStreamingServerMethod<TRequest, TResponse> continuation,
            string requestType)
            where TRequest : class
            where TResponse : class
        {
            RequestExecutionContext executionContext = null;

            try
            {
                // 初始化请求执行上下文
                executionContext = new RequestExecutionContext(context.Method, requestType, this, null);

                // 初始化请求上下文并记录开始日志
                await executionContext.InitializeAsync(context, null);

                // 执行实际处理方法
                await continuation(requestStream, responseStream, context);

                // 记录成功的请求日志并获取日志信息
                var logInfo = executionContext.LogSuccess();

                // 添加尾随元数据
                AddResponseMetadata(context, logInfo);
            }
            catch (Exception ex)
            {
                // 处理异常并记录
                if (executionContext == null)
                {
                    executionContext = new RequestExecutionContext(context.Method, requestType, this, null);
                    _logger.LogError(ex, $"初始化请求上下文异常: {context.Method}, RequestId: {RequestContext.Current?.RequestId ?? Guid.NewGuid().ToString()}");
                }

                executionContext.LogException(ex);
                throw;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 请求执行上下文，包含执行过程中需要的信息和通用处理逻辑
        /// 原有功能：封装gRPC请求执行的通用逻辑
        /// 新增功能：集成MQ日志记录功能
        /// </summary>
        private class RequestExecutionContext
        {
            public Stopwatch Stopwatch { get; }
            public string MethodName { get; }
            public string RequestType { get; }
            private readonly GrpcPerformanceInterceptor _interceptor;
            private readonly object _request;

            public RequestExecutionContext(string methodName, string requestType, GrpcPerformanceInterceptor interceptor, object request)
            {
                Stopwatch = Stopwatch.StartNew();
                MethodName = methodName;
                RequestType = requestType;
                _interceptor = interceptor;
                _request = request;
            }

            /// <summary>
            /// 初始化请求上下文并记录开始日志
            /// 原有功能：初始化RequestContext并记录请求开始日志
            /// </summary>
            public async Task InitializeAsync(ServerCallContext context, object request = null)
            {
                // 使用中间件已经初始化的RequestContext并补充gRPC特有的信息
                var requestContext = RequestContext.Current;
                if (requestContext == null)
                {
                    // 如果没有RequestContext（比如非gRPC请求），则创建一个
                    requestContext = RequestContext.Initialize();
                    requestContext.RequestId = Guid.NewGuid().ToString();
                    requestContext.MethodName = context.Method;
                }

                // 补充gRPC特有的信息
                await _interceptor.EnrichRequestContextWithGrpcInfoAsync(context, request, requestContext);

                // 检查是否应该记录请求参数
                if (_interceptor._parameterFormatter.ShouldLogRequestParameters(context.Method))
                {
                    // 只在配置启用且方法允许时才打印入参信息
                    var requestInfo = _interceptor._parameterFormatter.FormatRequestParametersDetailed(request);
                    _interceptor._logger.LogInformation($"[gRPC拦截器] 开始{RequestType}: {MethodName}, RequestId: {requestContext.RequestId}, 入参: {requestInfo}");
                }
                else
                {
                    // 默认只记录基本信息
                    _interceptor._logger.LogInformation($"[gRPC拦截器] 开始{RequestType}: {MethodName}, RequestId: {requestContext.RequestId}");
                }
            }

            /// <summary>
            /// 记录成功完成的日志并返回日志信息
            /// 原有功能：记录成功请求的日志
            /// 新增功能：异步记录MQ响应日志
            /// </summary>
            public RequestLogInfo LogSuccess()
            {
                // 停止计时
                Stopwatch.Stop();

                // 创建日志信息对象
                var logInfo = _interceptor.CreateRequestLogInfo(this, StatusCode.OK, RequestContext.Current);

                // 记录日志
                _interceptor._logger.LogInformation($"[gRPC拦截器] {RequestType}Logger: {JsonConvert.SerializeObject(logInfo)}");

                // ========== MQ日志功能新增：异步记录响应日志 ==========
                if (_interceptor._enableMQLogging)
                {
                    _ = Task.Run(async () => await LogResponseToMQAsync(null, null, _request));
                }

                return logInfo;
            }

            /// <summary>
            /// 处理异常并记录日志
            /// 原有功能：记录异常日志
            /// 新增功能：异步记录MQ错误日志
            /// </summary>
            public void LogException(Exception ex)
            {
                // 停止计时
                Stopwatch.Stop();

                // 记录异常日志
                _interceptor._logger.LogError(ex, $"[gRPC拦截器] {RequestType}异常: {MethodName}, 耗时: {Stopwatch.ElapsedMilliseconds}ms, RequestId: {RequestContext.Current?.RequestId ?? "Unknown"}");

                // ========== MQ日志功能新增：异步记录错误日志 ==========
                if (_interceptor._enableMQLogging)
                {
                    _ = Task.Run(async () => await LogResponseToMQAsync(ex, null, _request));
                }
            }

            /// <summary>
            /// 处理一元调用异常并返回适当的响应
            /// 原有功能：处理一元调用的异常并返回响应对象
            /// 新增功能：异步记录MQ错误日志
            /// </summary>
            public TResponse HandleUnaryException<TResponse>(Exception ex)
                where TResponse : class
            {
                // 记录异常详情
                LogException(ex);

                // 创建新的响应对象并设置异常信息
                var obj = (TResponse)Activator.CreateInstance(typeof(TResponse));
                _interceptor.SetExceptionMessageToResponseObject(obj, ex);

                //// ========== MQ日志功能新增：异步记录错误日志 ==========
                //if (_interceptor._enableMQLogging)
                //{
                //    _ = Task.Run(async () => await LogResponseToMQAsync(ex, null));
                //}

                return obj;
            }

            // ========== MQ日志功能新增方法：记录响应和错误日志 ==========
            /// <summary>
            /// 记录MQ响应日志（response阶段）和错误日志
            /// 来源：从MQGrpcLogInterceptor.cs转移过来的响应日志记录功能
            /// 功能：记录响应阶段的完整信息，包括处理时间、用户信息、异常详情等
            /// </summary>
            private async Task LogResponseToMQAsync(Exception ex = null, ServerCallContext context = null, object request = null)
            {
                try
                {
                    _interceptor._logger.LogInformation($"[MQ日志] 开始入队 Response 阶段日志, RequestId: {RequestContext.Current.RequestId}, 是否异常: {ex != null}");
                    using (var scope = _interceptor._serviceScopeFactory.CreateScope())
                    {
                        var logInterfaceWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                        var logErrorWriterService = scope.ServiceProvider.GetRequiredService<IMQErrorWriterService>();

                        // 构建响应日志对象（包含gRPC层面的完整信息）
                        var responseLog = new MQInterfaceLog
                        {
                            RequestId = RequestContext.Current.RequestId,
//                              RequestTime = RequestContext.Current.StartTime,
                            InterfaceName = MethodName,
                            HttpMethod = "gRPC",
                            RequestParams = request != null ? _interceptor._parameterFormatter.FormatRequestParametersDetailed(request) : null,
                            ClientIp = RequestContext.Current.ClientIp,
                            UserId = RequestContext.Current.UserId,
                            UserName = RequestContext.Current.UserName,
                            LogType = "interface",
                            LogLevel = ex == null ? "info" : "error",
                            ProjectId = context != null ? GetHeaderValue(context, "project-id") : null,
                            ProjectName = context != null ? GetHeaderValue(context, "project-name") : null,
                            SessionId = RequestContext.Current.SessionId,
                            ServerIp = _interceptor._serverIp,
                            ServerName = _interceptor._serverName,
                            AppName = _interceptor._configuration["LogSettings:AppName"],
                            AppVersion = _interceptor._configuration["LogSettings:AppVersion"],
                            Environment = _interceptor._configuration["LogSettings:Environment"],
                            AddTime = DateTime.Now,
                            ResponseTime = DateTime.Now,
                            IsSuccess = ex == null,
                            ResponseStatusCode = ex == null ? 200 : 500,
                            TotalMilliseconds = Stopwatch.ElapsedMilliseconds,
                            ErrorMessage = ex?.Message,
                            SourceClassName = ex?.TargetSite?.DeclaringType?.FullName,
                            SourceMethodName = ex?.TargetSite?.Name,
                            LogStage = "response" // 标识为响应阶段
                        };



                        // 无论正常响应还是异常响应，都要记录接口日志
                        await logInterfaceWriterService.WriteInterfaceLogAsync(responseLog);

                        _interceptor._logger.LogInformation($"[MQ日志] Response 阶段日志入队完成, RequestId: {responseLog.RequestId}, 耗时: {Stopwatch.ElapsedMilliseconds}ms");

                        // 如果有异常，额外记录错误日志
                        if (ex != null)
                        {
                            _interceptor._logger.LogInformation($"[MQ日志] 开始入队错误日志, RequestId: {responseLog.RequestId}, 异常类型: {ex.GetType().Name}");
                            var errorLog = new MQErrorLog
                            {
                                ErrorId = Guid.NewGuid().ToString(),
                                RequestId = responseLog.RequestId,
                                ProjectId = responseLog.ProjectId,
                                ProjectName = responseLog.ProjectName,
                                SessionId = responseLog.SessionId,
                                ServerIp = responseLog.ServerIp,
                                ServerName = responseLog.ServerName,
                                AppName = responseLog.AppName,
                                AppVersion = responseLog.AppVersion,
                                Environment = responseLog.Environment,
                                LogLevel = "error",
                                LogType = "error",
                                ErrorType = ex.GetType().FullName,
                                ErrorMessage = ex.Message,
                                ErrorCode = ex.HResult.ToString(),
                                ErrorStackTrace = ex.StackTrace,
                                InputParams = responseLog.RequestParams, // 为了安全考虑不记录输入参数
                                SourceClassName = ex.TargetSite?.DeclaringType?.FullName,
                                SourceMethodName = ex.TargetSite?.Name,
                                AdditionalData = ex.Data != null && ex.Data.Count > 0 ? JsonConvert.SerializeObject(ex.Data) : null,
                                AddTime = DateTime.Now,
                                UserId = responseLog.UserId,
                                UserName = responseLog.UserName,
                                ClientIp = responseLog.ClientIp
                            };

                            await logErrorWriterService.WriteErrorLogAsync(errorLog);

                            _interceptor._logger.LogInformation($"[MQ日志] 错误日志入队完成, RequestId: {responseLog.RequestId}, ErrorId: {errorLog.ErrorId}");
                        }
                    }
                }
                catch (Exception logEx)
                {
                    // MQ日志记录失败不影响主流程
                    _interceptor._logger.LogError(logEx, $"[MQ日志] Response 阶段日志入队失败: {RequestContext.Current?.RequestId ?? "Unknown"}, 异常: {logEx.Message}");
                }
            }

            /// <summary>
            /// 从gRPC请求头中获取指定值
            /// 来源：从MQGrpcLogInterceptor.cs转移过来的请求头获取功能
            /// </summary>
            private string GetHeaderValue(ServerCallContext context, string headerName)
            {
                if (context.RequestHeaders != null)
                {
                    var entry = context.RequestHeaders.Get(headerName);
                    return entry?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// 使用gRPC特有信息丰富RequestContext
        /// 原有功能：从gRPC请求中提取用户信息、会话信息等
        /// </summary>
        private async Task EnrichRequestContextWithGrpcInfoAsync(ServerCallContext context, object request, RequestContext requestContext)
        {
            // 1. 提取会话ID（优先从header获取，然后从request获取）
            ExtractSessionId(context, request, requestContext);

            // 2. 根据会话ID获取用户信息，如果无法获取则尝试从header获取
            await ExtractUserInfoAsync(context, requestContext);

            // 3. 提取其他请求头信息（除了已提取的用户相关信息）
            ExtractOtherHeaders(context, requestContext);

            // 4. 提取客户端IP（如果中间件没有设置或者gRPC有更精确的IP）
            ExtractClientIp(context, requestContext);
        }

        /// <summary>
        /// 提取会话ID（优先从请求头获取，然后从请求体获取）
        /// 原有功能：从gRPC请求中提取会话ID
        /// </summary>
        private void ExtractSessionId(ServerCallContext context, object request, RequestContext requestContext)
        {
            // 优先从header中获取sessionId
            if (context.RequestHeaders != null)
            {
                var sessionIdEntry = context.RequestHeaders.Get("session-id");
                if (sessionIdEntry != null && !string.IsNullOrEmpty(sessionIdEntry.Value))
                {
                    requestContext.SessionId = sessionIdEntry.Value;
                    return;
                }
            }

            // 如果header中没有，尝试从request对象中获取
            if (request != null)
            {
                // 尝试获取SessionId属性（考虑大小写）
                var sessionIdProperty = request.GetType().GetProperty("sessionId") ??
                                      request.GetType().GetProperty("SessionId");

                if (sessionIdProperty != null)
                {
                    string sessionId = sessionIdProperty.GetValue(request) as string;
                    if (!string.IsNullOrEmpty(sessionId))
                    {
                        requestContext.SessionId = sessionId;
                    }
                }
            }
        }

        /// <summary>
        /// 根据会话ID获取用户信息，若无法获取则尝试从请求头获取
        /// 原有功能：从数据库或请求头中获取用户信息
        /// </summary>
        private async Task ExtractUserInfoAsync(ServerCallContext context, RequestContext requestContext)
        {
            // 如果有sessionId，尝试从数据库获取用户信息
            if (!string.IsNullOrEmpty(requestContext.SessionId))
            {
                // 创建作用域以获取ITeamRepository
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var teamRepository = scope.ServiceProvider.GetRequiredService<ITeamRepository>();
                    var currentUser = await teamRepository.GetCurrentUserInfoAsync(requestContext.SessionId);
                    if (currentUser != null)
                    {
                        // 设置用户信息
                        requestContext.UserId = currentUser.ID.ToString();
                        requestContext.UserName = currentUser.LoginName;
                        return; // 成功获取用户信息，退出方法
                    }
                }
            }

            // 如果无法从sessionId获取用户信息，尝试从请求头中获取
            if (context.RequestHeaders != null)
            {
                // 获取userId
                var userIdEntry = context.RequestHeaders.Get("user-id");
                if (userIdEntry != null && !string.IsNullOrEmpty(userIdEntry.Value))
                {
                    requestContext.UserId = userIdEntry.Value;
                }

                // 获取userName
                var userNameEntry = context.RequestHeaders.Get("user-name");
                if (userNameEntry != null && !string.IsNullOrEmpty(userNameEntry.Value))
                {
                    requestContext.UserName = userNameEntry.Value;
                }
            }
        }

        /// <summary>
        /// 提取其他请求头信息（除了用户相关信息）
        /// 原有功能：从gRPC请求头中提取客户端信息
        /// </summary>
        private void ExtractOtherHeaders(ServerCallContext context, RequestContext requestContext)
        {
            if (context.RequestHeaders == null)
            {
                return;
            }

            // 提取客户端ID
            var clientIdEntry = context.RequestHeaders.Get("client-id");
            if (clientIdEntry != null)
            {
                requestContext.ClientId = clientIdEntry.Value;
            }

            // 提取客户端版本
            var clientVersionEntry = context.RequestHeaders.Get("client-version");
            if (clientVersionEntry != null)
            {
                requestContext.ClientVersion = clientVersionEntry.Value;
            }

            // 提取客户端IP（允许客户端手动指定，优先于自动获取的IP）
            var clientIpEntry = context.RequestHeaders.Get("client-ip");
            if (clientIpEntry != null && !string.IsNullOrEmpty(clientIpEntry.Value))
            {
                requestContext.ClientIp = clientIpEntry.Value;

                // 记录IP来源为手动设置
                requestContext.SetItem("ClientIpSource", "Header");
            }

            // 提取客户端物理地址
            var physicalAddressEntry = context.RequestHeaders.Get("physical-address");
            if (physicalAddressEntry != null)
            {
                requestContext.SetItem("PhysicalAddress", physicalAddressEntry.Value);
            }

            // 存储所有请求头信息
            foreach (var entry in context.RequestHeaders)
            {
                requestContext.SetItem($"Header:{entry.Key}", entry.Value);
            }
        }

        /// <summary>
        /// 提取客户端IP
        /// 原有功能：从gRPC连接中提取客户端IP地址
        /// </summary>
        private void ExtractClientIp(ServerCallContext context, RequestContext requestContext)
        {
            // 如果已经从header中设置了IP，则不再覆盖
            if (!string.IsNullOrEmpty(requestContext.ClientIp) &&
                requestContext.GetItem<string>("ClientIpSource") == "Header")
            {
                return;
            }

            var peerAddress = context.Peer; // 格式通常为 ipv4:127.0.0.1:12345 或 ipv6:[::1]:12345
            if (string.IsNullOrEmpty(peerAddress))
            {
                return;
            }

            // 解析IP地址
            var parts = peerAddress.Split(':');
            if (parts.Length < 2)
            {
                return;
            }

            if (peerAddress.StartsWith("ipv4:"))
            {
                requestContext.ClientIp = parts[1];

                // 记录IP来源为自动获取
                requestContext.SetItem("ClientIpSource", "Peer");
            }
            else if (peerAddress.StartsWith("ipv6:"))
            {
                // 对于IPv6，格式可能是 ipv6:[::1]:12345
                var ipv6WithBrackets = parts[1];
                requestContext.ClientIp = ipv6WithBrackets.TrimStart('[').TrimEnd(']');

                // 记录IP来源为自动获取
                requestContext.SetItem("ClientIpSource", "Peer");
            }
        }

        /// <summary>
        /// 向响应中添加元数据
        /// 原有功能：在gRPC响应中添加request-id和处理时间信息
        /// </summary>
        private void AddResponseMetadata(ServerCallContext context, RequestLogInfo logInfo)
        {
            var httpContext = context.GetHttpContext();

            if (httpContext.Request.Protocol.StartsWith("HTTP/2") ||
                httpContext.Request.Headers.ContainsKey("grpc-encoding"))
            {
                context.ResponseTrailers.Add("request-id", logInfo.RequestId);
                context.ResponseTrailers.Add("request-method", logInfo.Method);
                context.ResponseTrailers.Add("processing-time-ms", logInfo.TotalMilliseconds.ToString());
            }
            else
            {
                httpContext.Response.Headers.Append("request-id", logInfo.RequestId);
                context.ResponseTrailers.Add("request-method", logInfo.Method);
                httpContext.Response.Headers.Append("processing-time-ms", logInfo.TotalMilliseconds.ToString());
            }
        }

        /// <summary>
        /// 创建请求日志信息对象
        /// 原有功能：创建用于性能监控的日志信息对象
        /// </summary>
        private RequestLogInfo CreateRequestLogInfo(RequestExecutionContext executionContext, StatusCode statusCode, RequestContext requestContext)
        {
            return new RequestLogInfo
            {
                RequestId = RequestContext.Current.RequestId,
                SessionId = RequestContext.Current.SessionId,
                UserId = RequestContext.Current.UserId,
                UserName = RequestContext.Current.UserName ?? string.Empty,
                Method = executionContext.MethodName,
                StatusCode = statusCode,
                TotalMilliseconds = executionContext.Stopwatch.ElapsedMilliseconds,
                ClientIp = RequestContext.Current.ClientIp,
                ClientVersion = RequestContext.Current.ClientVersion,
                StartTime = DateTime.Now.AddMilliseconds(-executionContext.Stopwatch.ElapsedMilliseconds),
                EndTime = DateTime.Now,
                ServerIp = _serverIp,
                ServerName = _serverName,
                InterfaceName = executionContext.MethodName,
                Source = _source
            };
        }

        /// <summary>
        /// 将异常信息设置到响应对象
        /// 原有功能：将异常信息设置到gRPC响应对象的Message字段
        /// </summary>
        private void SetExceptionMessageToResponseObject<TResponse>(TResponse response, Exception ex)
            where TResponse : class
        {
            var prop = typeof(TResponse).GetProperty("Message");

            // 所有的返回值中必须含有Message字段
            if (prop != null)
            {
                if (ex.InnerException != null)
                {
                    prop.SetValue(response, ex.Message + Environment.NewLine + "InnerException: " + ex.InnerException, null);
                }
                else
                {
                    prop.SetValue(response, ex.Message, null);
                }
            }
        }

        #endregion
    }
} 