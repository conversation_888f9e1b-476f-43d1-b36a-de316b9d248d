using BimBase.Api.Config;
using BimBase.Api.Grpc;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.Database;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Infrastructure.Grpc;
using BimBase.Api.Infrastructure.Middleware;
using BimBase.Api.Infrastructure.MQLog;
using BimBase.Api.Infrastructure.MQLog.Services;
using BimBase.Api.Infrastructure.Repositories;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Data.Common;
using System.IO;
using System.Reflection;
using System.Text;

namespace BimBase.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<KestrelServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
                options.Limits.MaxRequestBodySize = int.MaxValue;
            });
            services.Configure<FormOptions>(options =>
            {
                options.ValueLengthLimit = int.MaxValue;
                options.MultipartBodyLengthLimit = int.MaxValue;
            });
            var redisConnectionstring = Configuration.GetConnectionString("RedisConnection");
            //https://stackoverflow.com/questions/59847571/differences-between-microsoft-extensions-cashing-redis-and-microsoft-extensions
            //Microsoft.Extensions.Caching.StackExchangeRedis instead Microsoft.Extensions.Caching.Redis
            services.AddStackExchangeRedisCache(options =>
            {
                options.InstanceName = "session";
                options.Configuration = redisConnectionstring;
            });

            services.AddDistributedMemoryCache();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            //认证
            //services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
            //{
            //    //string key = "abc=123";
            //    options.TokenValidationParameters = new TokenValidationParameters
            //    {
            //        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("your secrete code")),
            //        ValidateAudience = false,
            //        ValidateIssuer = false,
            //        ValidateActor = false,
            //        ValidateLifetime = true,
            //    };
            //});
            //授权
            //services.AddAuthorization(options =>
            //{
            //    options.AddPolicy(JwtBearerDefaults.AuthenticationScheme, policy =>
            //    {
            //        policy.AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme);
            //        policy.RequireClaim(ClaimTypes.Name);
            //    });
            //});


            services.AddOptions();
            services.Configure<UrlsConfig>(Configuration.GetSection(UrlsConfig.SectionName));
            services.Configure<DatabaseVersioningOptions>(Configuration.GetSection(DatabaseVersioningOptions.SectionName));
            services.Configure<MajorConfig>(Configuration.GetSection(MajorConfig.SectionName));
            string mySqlConnectionStr = Configuration.GetConnectionString("DefaultConnection");
            string myLogConnectionStr = Configuration.GetConnectionString("LoggerConnection");
            string myLibraryConnectionStr = Configuration.GetConnectionString("LibraryConnection");
            //Console.WriteLine("BimBase.Api mySqlConnectionStr ===== " + mySqlConnectionStr);

            // 注册拦截器服务
            services.AddScoped<LibraryDbContextInterceptor>();

            // 配置DbContext - 根据业务特点选择适合的注册方式
            services.AddDbContextPool<TeamDbContext>(options => options.UseMySql(mySqlConnectionStr
                , ServerVersion.AutoDetect(mySqlConnectionStr)
                , mySqlOptionsAction: sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly(typeof(Startup).GetTypeInfo().Assembly.GetName().Name);
                    // 启用查询拆分行为以优化复杂查询性能
                    sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                }).LogTo(Console.WriteLine, LogLevel.Error, DbContextLoggerOptions.DefaultWithLocalTime | DbContextLoggerOptions.SingleLine)
                //.EnableSensitiveDataLogging()
                .EnableDetailedErrors()
                .AddInterceptors(new TeamDbContextInterceptor()), 
                poolSize: 150); // 主业务数据库，并发访问频繁

            services.AddDbContextPool<LogDbContext>(options => options.UseMySql(myLogConnectionStr
                , ServerVersion.AutoDetect(myLogConnectionStr)
                , mySqlOptionsAction: sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly(typeof(Startup).GetTypeInfo().Assembly.GetName().Name);
                    sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                }).LogTo(Console.WriteLine, LogLevel.Error, DbContextLoggerOptions.DefaultWithLocalTime | DbContextLoggerOptions.SingleLine)
                //.EnableSensitiveDataLogging()
                .EnableDetailedErrors(),
                poolSize: 32); // 日志数据库，主要是写操作

            services.AddDbContext<LibraryDbContext>((serviceProvider, options) => options.UseMySql(myLibraryConnectionStr
                , ServerVersion.AutoDetect(myLibraryConnectionStr) // 修复：使用正确的连接字符串
                , mySqlOptionsAction: sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly(typeof(Startup).GetTypeInfo().Assembly.GetName().Name);
                    sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                })
            .LogTo(Console.WriteLine, LogLevel.Error, DbContextLoggerOptions.DefaultWithLocalTime | DbContextLoggerOptions.SingleLine)
            //.EnableSensitiveDataLogging()
            .EnableDetailedErrors()
            .AddInterceptors(serviceProvider.GetService<LibraryDbContextInterceptor>())); // 从服务容器获取拦截器实例

            services.AddHealthChecks()
                .AddCheck("self", () => HealthCheckResult.Healthy())
                .AddDbContextCheck<TeamDbContext>()
                .AddMySql(mySqlConnectionStr
                    , failureStatus: HealthStatus.Degraded
                    , name: "MySql"
                    , tags: new string[] { "team-db" })
                .AddRedis(redisConnectionstring);

            services.AddHealthChecksUI(opt =>
            {
                opt.SetEvaluationTimeInSeconds(300); //time in seconds between check    
                opt.MaximumHistoryEntriesPerEndpoint(60); //maximum history of checks    
                opt.SetApiMaxActiveRequests(1); //api requests concurrency 
                //https://github.com/Xabaril/AspNetCore.Diagnostics.HealthChecks/issues/410
                //opt.AddHealthCheckEndpoint("bimbase-api", "/hc"); //map health check api    
            })
            .AddInMemoryStorage();

            // 添加BimBase gRPC拦截器服务
            services.AddSingleton<GrpcPerformanceInterceptor>();
            
            // 添加请求参数格式化器
            services.AddSingleton<RequestParameterFormatter>();
            
            // 注册日志清理和整理服务
            services.AddHostedService<BimBase.Api.Infrastructure.Services.LogCleanupService>();
            
            // 注册MQ日志相关服务
            services.AddSingleton<IRabbitMQConnectionProvider, RabbitMQConnectionProvider>();
            services.AddSingleton<IChannelPool>(sp =>
            {
                var connection = sp.GetRequiredService<IRabbitMQConnectionProvider>().GetConnection();
                return new BimBase.Api.Infrastructure.MQLog.Services.ChannelPool(connection, maxSize: 1000);
            });
            services.AddSingleton<IMQRabbitMQService, MQRabbitMQService>();
            services.AddScoped<IMQInterfaceWriterService, MQInterfaceWriterService>();
            services.AddScoped<IMQErrorWriterService, MQErrorWriterService>();

            services.AddTransient<MQGrpcLogInterceptor>();

            services.AddGrpc(options =>
            {
                options.Interceptors.Add<GrpcPerformanceInterceptor>();
                options.Interceptors.Add<MQGrpcLogInterceptor>();
                options.EnableDetailedErrors = true;
                options.MaxReceiveMessageSize =  1024 * 1024 * 1024;
                options.MaxSendMessageSize = int.MaxValue;

                // 启用gRPC响应压缩
                options.ResponseCompressionAlgorithm = "gzip";
                options.ResponseCompressionLevel = System.IO.Compression.CompressionLevel.Optimal;
            });

            services.AddGrpcHttpApi();

            //在 ASP.NET Core 中使用 gRPCurl 来测试 gRPC 服务
            services.AddGrpcReflection();
            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "BimBase.Api", Version = "v1" });
                var basePath = AppContext.BaseDirectory;
                var fileName = typeof(Startup).GetTypeInfo().Assembly.GetName().Name + ".xml";
                c.IncludeXmlComments(Path.Combine(basePath, fileName));
                c.CustomSchemaIds(x => x.FullName);
                //c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                //{
                //    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                //    Name = "Authorization",
                //    Type = SecuritySchemeType.ApiKey,
                //});
                //c.AddSecurityRequirement(new OpenApiSecurityRequirement
                //{
                //    {
                //          new OpenApiSecurityScheme
                //            {
                //                Reference = new OpenApiReference
                //                {
                //                    Type = ReferenceType.SecurityScheme,
                //                    Id = "basic"
                //                }
                //            },
                //            new string[] { "Bearer" }
                //    }
                //});
                //c.OperationFilter<AuthorizeCheckOperationFilter>();
            });
            services.AddAutoMapper(typeof(Startup));
            services.AddGrpcSwagger();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<ITeamRepository, TeamRepository>();
            services.AddScoped<IAuthorityManager, AuthorityManager>();
            services.AddTransient<ILibraryRepository, LibraryRepository>();
            services.AddTransient<IMainProjectBulkOperation, MainProjectBulkOperation>();
            services.AddTransient<Func<DbConnection,  Guid, IProjectRepository>>(
                sp => (DbConnection dbConnection,  Guid projectId) => new ProjectRepository(dbConnection, projectId));
            //services.AddTransient<Func<DbConnection, ILibraryRepository>>(
            //    sp => (DbConnection dbConnection) => new LibraryRepository(dbConnection));
            services.AddTransient<Func<DbConnection, Guid, IMainProjectRepository>>(
                sp => (DbConnection dbConnection, Guid mainProjectId) => 
                {
                    var bulkOperation = sp.GetRequiredService<IMainProjectBulkOperation>();
                    return new MainProjectRepository(dbConnection, mainProjectId, bulkOperation);
                }
            );

            services.AddTransient<Func<DbContext, IRepository>>(
                sp => (DbContext db) => new BaseRepository(db));
            
            // 添加数据库升级服务
            services.AddDatabaseUpgradeServices();

            // 注册日志配置
            services.Configure<MQLogSettings>(Configuration.GetSection("LogSettings"));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            var logger = loggerFactory.CreateLogger<Startup>();
            
            // 添加响应完成监控中间件（必须在最前面）
            app.UseMiddleware<ResponseCompletionMiddleware>();
            
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                //app.UseSwagger();
                //app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "BimBase.Api v1"));
            }
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "BimBase.Api v1"));


            app.UseRouting();
            
            // 添加Session中间件（必须在UseRouting之后，UseEndpoints之前）
            app.UseSession();
            
            //认证
            //app.UseAuthentication();
            //授权
            //app.UseAuthorization();
            app.UseGrpcWeb();
            app.UseHealthChecksUI();

            app.UseEndpoints(endpoints =>
            {
                if (env.IsDevelopment())
                {
                    endpoints.MapGrpcReflectionService();
                }

                endpoints.MapGrpcService<ConnectionService>().EnableGrpcWeb();
                endpoints.MapGrpcService<TeamQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<TeamManagementService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ProjectQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ProjectManagementService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ModelQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ModelWriteService>().EnableGrpcWeb();
                endpoints.MapGrpcService<LibraryQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<LibraryManagementService>().EnableGrpcWeb();
                endpoints.MapGrpcService<MainprojectQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<MainprojectManagementService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ProjectFileManagementService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ProjectFileQueryService>().EnableGrpcWeb();
                endpoints.MapGrpcService<ClientModuleVersionManagementService>().EnableGrpcWeb();

                endpoints.MapGet("/", async context =>
                {
                    await context.Response.WriteAsync("Communication with gRPC endpoints must be made through a gRPC client. To learn how to create a client, visit: https://go.microsoft.com/fwlink/?linkid=2086909");
                });

                endpoints.MapGet("/_protos/", async ctx =>
                {
                    ctx.Response.ContentType = "text/plain; charset=utf-8";

                    logger.LogInformation($"ContentRootPath: {env.ContentRootPath}");
                    var protoDir = Path.Combine(Directory.GetParent(env.ContentRootPath).FullName, "Proto");

                    logger.LogInformation($"protoDir: {protoDir}");

                    if (!Directory.Exists(protoDir))
                    {
                        await ctx.Response.WriteAsync($"Directory {protoDir} is not exists!");
                        return;
                    }

                    //string[] files = Directory.GetFiles(Path.Combine(env.ContentRootPath, "Protos"), "*.proto");
                    string[] files = Directory.GetFiles(protoDir, "*.proto");
                    StringBuilder stringBuilder = new StringBuilder();
                    foreach (var file in files)
                    {
                        using var fs = new FileStream(file, FileMode.Open, FileAccess.Read);
                        using var sr = new StreamReader(fs);

                        var fileName = Path.GetFileName(file);

                        stringBuilder.AppendLine($"// **************{fileName}********START************************** //");

                        while (!sr.EndOfStream)
                        {
                            var line = await sr.ReadLineAsync();
                            if (line != "/* >>" || line != "<< */")
                            {
                                stringBuilder.AppendLine(line);
                            }
                        }

                        stringBuilder.AppendLine($"// ****************{fileName}******END************************** //");
                    }

                    await ctx.Response.WriteAsync(stringBuilder.ToString());
                });

                endpoints.MapControllers();

                endpoints.MapHealthChecks("/hc", new HealthCheckOptions()
                {
                    Predicate = _ => true,
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                });

                endpoints.MapHealthChecksUI(config => {
                    config.UIPath = "/hc-ui";
                });


            });
            
            // 注册MQ日志中间件（建议在异常处理中间件之后）
            app.UseMiddleware<BimBase.Api.Infrastructure.MQLog.MQLogMiddleware>();
        }

    }
}
