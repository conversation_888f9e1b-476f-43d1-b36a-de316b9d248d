using Microsoft.EntityFrameworkCore;
using BimBase.LogConsumer.Models;
using MySqlConnector;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure.Internal;
using Microsoft.Extensions.Configuration;

namespace BimBase.LogConsumer.Data
{
    public class LogDbContext : DbContext
    {
        private readonly string _connectionString;

        public DbSet<MQInterfaceLog> MQInterfaceLogs { get; set; }
        public DbSet<MQErrorLog> MQErrorLogs { get; set; }

        public LogDbContext(DbContextOptions<LogDbContext> options)
            : base(options)
        {
            _connectionString = options.FindExtension<MySqlOptionsExtension>()?.ConnectionString;
            EnsureDatabaseAndTableExists();
        }

        private void EnsureDatabaseAndTableExists()
        {
            try
            {
                // 从连接字符串中提取数据库名称
                var builder = new MySqlConnectionStringBuilder(_connectionString);
                var databaseName = builder.Database;
                builder.Database = null; // 移除数据库名称，以便连接到 MySQL 服务器

                // 创建数据库（如果不存在）
                using (var connection = new MySqlConnection(builder.ConnectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = $"CREATE DATABASE IF NOT EXISTS `{databaseName}`";
                        command.ExecuteNonQuery();
                    }
                }

                // 重新连接到新创建的数据库
                builder.Database = databaseName;
                using (var connection = new MySqlConnection(builder.ConnectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        // 创建表（如果不存在）
                        command.CommandText = @"
                            CREATE TABLE IF NOT EXISTS `mq_interface_logs` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `request_id` varchar(64) DEFAULT NULL,
                                `project_id` varchar(64) DEFAULT NULL,
                                `project_name` varchar(128) DEFAULT NULL,
                                `session_id` varchar(128) DEFAULT NULL,
                                `client_ip` varchar(128) DEFAULT NULL,
                                `server_ip` varchar(45) DEFAULT NULL,
                                `server_name` varchar(128) DEFAULT NULL,
                                `user_id` varchar(64) DEFAULT NULL,
                                `user_name` varchar(128) DEFAULT NULL,
                                `app_name` varchar(64) DEFAULT NULL,
                                `app_version` varchar(32) DEFAULT NULL,
                                `environment` varchar(32) DEFAULT NULL,
                                `request_time` datetime DEFAULT NULL,
                                `log_level` varchar(32) DEFAULT NULL,
                                `log_type` varchar(32) DEFAULT NULL,
                                `interface_name` varchar(128) DEFAULT NULL,
                                `http_method` varchar(8) DEFAULT NULL,
                                `request_params` text,
                                `response_status_code` int DEFAULT NULL,
                                `response_time` datetime DEFAULT NULL,
                                `is_success` bit(1) DEFAULT NULL,
                                `source_class_name` varchar(128) DEFAULT NULL,
                                `source_method_name` varchar(128) DEFAULT NULL,
                                `additional_data` text,
                                `error_message` text,
                                `total_milliseconds` double DEFAULT NULL,
                                `add_time` datetime DEFAULT NULL,
                                `log_stage` varchar(16) DEFAULT NULL,
                                `middle_total_milliseconds` double DEFAULT NULL,
                                `middle_response_time` datetime DEFAULT NULL,
                                PRIMARY KEY (`id`)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

                            CREATE TABLE IF NOT EXISTS `mq_error_logs` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `error_id` varchar(64) DEFAULT NULL,
                                `request_id` varchar(64) DEFAULT NULL,
                                `project_id` varchar(64) DEFAULT NULL,
                                `project_name` varchar(128) DEFAULT NULL,
                                `session_id` varchar(128) DEFAULT NULL,
                                `client_ip` varchar(128) DEFAULT NULL,
                                `server_ip` varchar(45) DEFAULT NULL,
                                `server_name` varchar(128) DEFAULT NULL,
                                `user_id` varchar(64) DEFAULT NULL,
                                `user_name` varchar(128) DEFAULT NULL,
                                `app_name` varchar(64) DEFAULT NULL,
                                `app_version` varchar(32) DEFAULT NULL,
                                `environment` varchar(32) DEFAULT NULL,
                                `log_level` varchar(32) DEFAULT NULL,
                                `log_type` varchar(32) DEFAULT NULL,
                                `error_type` varchar(255) DEFAULT NULL,
                                `error_message` text,
                                `error_code` varchar(64) DEFAULT NULL,
                                `error_stack_trace` text,
                                `input_params` text,
                                `source_class_name` varchar(128) DEFAULT NULL,
                                `source_method_name` varchar(128) DEFAULT NULL,
                                `additional_data` text,
                                `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
                        command.ExecuteNonQuery();

                        // 单独为request_id加唯一索引，捕获已存在异常
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD UNIQUE INDEX uq_request_id (`request_id`);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                            // 已有索引，忽略
                        }

                        // 在 EnsureDatabaseAndTableExists 方法中，表创建后添加 request_time 及联合索引
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD INDEX idx_request_time (`request_time`);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                        }
                        // 新增四个联合索引
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD INDEX idx_serverip_time (server_ip, request_time);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                        }
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD INDEX idx_requestid_time (request_id, request_time);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                        }
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD INDEX idx_username_time (user_name, request_time);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                        }
                        try
                        {
                            command.CommandText = "ALTER TABLE `mq_interface_logs` ADD INDEX idx_logstage_time (log_stage, request_time);";
                            command.ExecuteNonQuery();
                        }
                        catch (MySqlConnector.MySqlException ex)
                        {
                            if (!ex.Message.Contains("Duplicate key name"))
                                throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create database or table: {ex.Message}", ex);
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<MQInterfaceLog>(entity =>
            {
                entity.ToTable("mq_interface_logs");
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.RequestId).IsUnique();
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                entity.Property(e => e.RequestId).HasColumnName("request_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.ProjectId).HasColumnName("project_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.ProjectName).HasColumnName("project_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.SessionId).HasColumnName("session_id").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.ClientIp).HasColumnName("client_ip").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.ServerIp).HasColumnName("server_ip").HasMaxLength(45).IsRequired(false);
                entity.Property(e => e.ServerName).HasColumnName("server_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.UserId).HasColumnName("user_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.UserName).HasColumnName("user_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.AppName).HasColumnName("app_name").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.AppVersion).HasColumnName("app_version").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.Environment).HasColumnName("environment").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.RequestTime).HasColumnName("request_time").HasColumnType("datetime").IsRequired(false);
                entity.Property(e => e.LogLevel).HasColumnName("log_level").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.LogType).HasColumnName("log_type").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.InterfaceName).HasColumnName("interface_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.HttpMethod).HasColumnName("http_method").HasMaxLength(8).IsRequired(false);
                entity.Property(e => e.RequestParams).HasColumnName("request_params").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.ResponseStatusCode).HasColumnName("response_status_code").IsRequired(false);
                entity.Property(e => e.ResponseTime).HasColumnName("response_time").HasColumnType("datetime").IsRequired(false);
                entity.Property(e => e.IsSuccess).HasColumnName("is_success").HasColumnType("bit").IsRequired(false);
                entity.Property(e => e.SourceClassName).HasColumnName("source_class_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.SourceMethodName).HasColumnName("source_method_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.AdditionalData).HasColumnName("additional_data").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.ErrorMessage).HasColumnName("error_message").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.TotalMilliseconds).HasColumnName("total_milliseconds").HasColumnType("double").IsRequired(false);
                entity.Property(e => e.AddTime).HasColumnName("add_time").HasColumnType("datetime").HasDefaultValueSql("CURRENT_TIMESTAMP").IsRequired(false);
                entity.Property(e => e.LogStage).HasColumnName("log_stage").HasMaxLength(16).IsRequired(false);
                entity.Property(e => e.MiddleTotalMilliseconds).HasColumnName("middle_total_milliseconds").HasColumnType("double").IsRequired(false);
                entity.Property(e => e.MiddleResponseTime).HasColumnName("middle_response_time").HasColumnType("datetime").IsRequired(false);
                entity.HasIndex(e => e.RequestTime).HasDatabaseName("idx_request_time");
            });

            modelBuilder.Entity<MQErrorLog>(entity =>
            {
                entity.ToTable("mq_error_logs");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).HasColumnName("id").ValueGeneratedOnAdd();
                entity.Property(e => e.ErrorId).HasColumnName("error_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.RequestId).HasColumnName("request_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.ProjectId).HasColumnName("project_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.ProjectName).HasColumnName("project_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.SessionId).HasColumnName("session_id").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.ClientIp).HasColumnName("client_ip").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.ServerIp).HasColumnName("server_ip").HasMaxLength(45).IsRequired(false);
                entity.Property(e => e.ServerName).HasColumnName("server_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.UserId).HasColumnName("user_id").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.UserName).HasColumnName("user_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.AppName).HasColumnName("app_name").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.AppVersion).HasColumnName("app_version").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.Environment).HasColumnName("environment").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.LogLevel).HasColumnName("log_level").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.LogType).HasColumnName("log_type").HasMaxLength(32).IsRequired(false);
                entity.Property(e => e.ErrorType).HasColumnName("error_type").HasMaxLength(255).IsRequired(false);
                entity.Property(e => e.ErrorMessage).HasColumnName("error_message").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.ErrorCode).HasColumnName("error_code").HasMaxLength(64).IsRequired(false);
                entity.Property(e => e.ErrorStackTrace).HasColumnName("error_stack_trace").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.InputParams).HasColumnName("input_params").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.SourceClassName).HasColumnName("source_class_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.SourceMethodName).HasColumnName("source_method_name").HasMaxLength(128).IsRequired(false);
                entity.Property(e => e.AdditionalData).HasColumnName("additional_data").HasColumnType("text").IsRequired(false);
                entity.Property(e => e.AddTime).HasColumnName("add_time").HasColumnType("datetime").HasDefaultValueSql("CURRENT_TIMESTAMP").IsRequired(false);
            });
        }
    }
} 