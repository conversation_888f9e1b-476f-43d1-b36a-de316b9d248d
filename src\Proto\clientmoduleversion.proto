syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";

package bimbase.api.version;

// ClientModuleVersion 实体消息
message GrpcClientModuleVersion {
    int32 id = 1;
    string clientId = 2;
    string moduleId = 3;
    string version = 4;
    string projectId = 5;
    string clientIp = 6;
    string userId = 7;
    string userName = 8;
    google.protobuf.Timestamp createdAt = 9;
    google.protobuf.Timestamp updatedAt = 10;
}

// 批量操作的枚举
enum ClientModuleVersionOperation {
    ADD = 0;
    UPDATE = 1;
    REMOVE = 2;
}

// 单个模块版本信息（用于批量操作）
message ClientModuleVersionItem {
    string projectId = 1;
    string moduleId = 2;
    string version = 3;
    ClientModuleVersionOperation operation = 4; // 仅在批量更新时使用
}

// 批量覆盖请求
message BatchOverwriteClientModuleVersionsRequest {
    string sessionId = 1;
    repeated ClientModuleVersionItem items = 2;
}

// 批量覆盖响应
message BatchOverwriteClientModuleVersionsResponse {
    bool isSuccess = 1;
    string message = 2;
    int32 affectedCount = 3;
}

// 批量更新请求
message BatchUpdateClientModuleVersionsRequest {
    string sessionId = 1;
    repeated ClientModuleVersionItem items = 2;
}

// 批量更新响应
message BatchUpdateClientModuleVersionsResponse {
    bool isSuccess = 1;
    string message = 2;
    int32 addedCount = 3;
    int32 updatedCount = 4;
    int32 removedCount = 5;
}

// 单条新增请求
message AddClientModuleVersionRequest {
    string sessionId = 1;
    string projectId = 2;
    string moduleId = 3;
    string version = 4;
}

// 单条新增响应
message AddClientModuleVersionResponse {
    bool isSuccess = 1;
    string message = 2;
    GrpcClientModuleVersion clientModuleVersion = 3;
}

// 单条更新请求
message UpdateClientModuleVersionRequest {
    string sessionId = 1;
    string projectId = 2;
    string moduleId = 3;
    string version = 4;
}

// 单条更新响应
message UpdateClientModuleVersionResponse {
    bool isSuccess = 1;
    string message = 2;
    GrpcClientModuleVersion clientModuleVersion = 3;
}

// 单条删除请求
message DeleteClientModuleVersionRequest {
    string sessionId = 1;
    string projectId = 2;
    string moduleId = 3;
}

// 单条删除响应
message DeleteClientModuleVersionResponse {
    bool isSuccess = 1;
    string message = 2;
}

// 批量获取请求
message GetClientModuleVersionsRequest {
    string sessionId = 1;
    string projectId = 2;
}

// 批量获取响应
message GetClientModuleVersionsResponse {
    bool isSuccess = 1;
    string message = 2;
    repeated GrpcClientModuleVersion clientModuleVersions = 3;
}

// 单个获取请求
message GetClientModuleVersionRequest {
    string sessionId = 1;
    string projectId = 2;
    string moduleId = 3;
}

// 单个获取响应
message GetClientModuleVersionResponse {
    bool isSuccess = 1;
    string message = 2;
    GrpcClientModuleVersion clientModuleVersion = 3;
}

// ClientModuleVersion 管理服务
service GrpcClientModuleVersionManagement {
    // 批量覆盖模块版本
    rpc BatchOverwriteClientModuleVersions(BatchOverwriteClientModuleVersionsRequest) returns (BatchOverwriteClientModuleVersionsResponse) {
        option (google.api.http) = {
            post: "/v1/clientmoduleversions/batch-overwrite"
            body: "*"
        };
    }
    
    // 批量更新模块版本
    rpc BatchUpdateClientModuleVersions(BatchUpdateClientModuleVersionsRequest) returns (BatchUpdateClientModuleVersionsResponse) {
        option (google.api.http) = {
            post: "/v1/clientmoduleversions/batch-update"
            body: "*"
        };
    }
    
    // 新增单个模块版本
    rpc AddClientModuleVersion(AddClientModuleVersionRequest) returns (AddClientModuleVersionResponse) {
        option (google.api.http) = {
            post: "/v1/clientmoduleversions"
            body: "*"
        };
    }
    
    // 更新单个模块版本
    rpc UpdateClientModuleVersion(UpdateClientModuleVersionRequest) returns (UpdateClientModuleVersionResponse) {
        option (google.api.http) = {
            put: "/v1/clientmoduleversions/{projectId}/{moduleId}"
            body: "*"
        };
    }
    
    // 删除单个模块版本
    rpc DeleteClientModuleVersion(DeleteClientModuleVersionRequest) returns (DeleteClientModuleVersionResponse) {
        option (google.api.http) = {
            delete: "/v1/clientmoduleversions/{projectId}/{moduleId}"
        };
    }
    
    // 获取项目的所有模块版本
    rpc GetClientModuleVersions(GetClientModuleVersionsRequest) returns (GetClientModuleVersionsResponse) {
        option (google.api.http) = {
            get: "/v1/clientmoduleversions/{projectId}"
        };
    }
    
    // 获取单个模块版本
    rpc GetClientModuleVersion(GetClientModuleVersionRequest) returns (GetClientModuleVersionResponse) {
        option (google.api.http) = {
            get: "/v1/clientmoduleversions/{projectId}/{moduleId}"
        };
    }
} 