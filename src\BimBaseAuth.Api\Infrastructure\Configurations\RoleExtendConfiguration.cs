﻿using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Configurations
{
    public partial class RoleExtendConfiguration : IEntityTypeConfiguration<RoleExtend>
    {
        public void Configure(EntityTypeBuilder<RoleExtend> entity)
        {
            entity.ToTable("roleextend");
            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.AppId)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.CreateId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.Name)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.RelatedId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<RoleExtend> entity);
    }
}
