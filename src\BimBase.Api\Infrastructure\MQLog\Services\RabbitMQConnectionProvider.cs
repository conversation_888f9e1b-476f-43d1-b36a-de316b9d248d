using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using System;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// RabbitMQ连接提供者接口
    /// </summary>
    public interface IRabbitMQConnectionProvider : IDisposable
    {
        /// <summary>
        /// 获取一个共享的RabbitMQ连接
        /// </summary>
        /// <returns></returns>
        IConnection GetConnection();
    }

    /// <summary>
    /// RabbitMQ连接提供者实现类，负责创建和持有一个单例的IConnection
    /// </summary>
    public class RabbitMQConnectionProvider : IRabbitMQConnectionProvider
    {
        private readonly IConnection _connection;
        private readonly ILogger<RabbitMQConnectionProvider> _logger;

        public RabbitMQConnectionProvider(IOptions<MQLogSettings> settings, ILogger<RabbitMQConnectionProvider> logger)
        {
            _logger = logger;
            var mqSettings = settings.Value;

            var factory = new ConnectionFactory
            {
                HostName = mqSettings.RabbitMQ.HostName,
                Port = mqSettings.RabbitMQ.Port,
                UserName = mqSettings.RabbitMQ.UserName,
                Password = mqSettings.RabbitMQ.Password,
                VirtualHost = mqSettings.RabbitMQ.VirtualHost,
                // 推荐添加，有助于网络恢复
                AutomaticRecoveryEnabled = true,
                NetworkRecoveryInterval = TimeSpan.FromSeconds(10),
                RequestedConnectionTimeout = TimeSpan.FromMilliseconds(30000),
                SocketReadTimeout = TimeSpan.FromMilliseconds(30000),
                SocketWriteTimeout = TimeSpan.FromMilliseconds(30000)
            };

            try
            {
                _connection = factory.CreateConnection();
                _logger.LogInformation("Successfully created RabbitMQ connection.");
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to create RabbitMQ connection.");
                // 在关键服务无法连接时，让应用快速失败是一种有效策略
                throw;
            }
        }

        public IConnection GetConnection()
        {
            if (_connection == null || !_connection.IsOpen)
            {
                _logger.LogError("RabbitMQ connection is not available.");
                throw new InvalidOperationException("RabbitMQ connection is not open.");
            }
            return _connection;
        }

        public void Dispose()
        {
            try
            {
                _connection?.Close();
                _connection?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while disposing RabbitMQ connection.");
            }
        }
    }
} 