﻿using BimBase.Api.Infrastructure.ModelDomain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public interface IModelBulkOperation
    {
        int SaveVersionInfo(DbContext context, VersionData version);

        void SaveAddModelDatasToFile(List<ModelData> recordDatas, int? currentVersion, long requestId);
        void SaveModifyModelDatasToFile(DbContext context, List<ModelData> recordDatas, int? currentVersion, long requestId);
        void SaveDeleteIdToFile(List<ModelData> recordDatas, long requestId);
        void SaveAddRelationshipDataToFile(List<Relationship> recordDatas, int? currentVersion, long requestId);
        void SaveModifyRelationshipDataToFile(DbContext context, List<Relationship> recordDatas, int? currentVersion, long requestId);

        void SaveDeleteRelationshipIdToFile(List<Relationship> recordDatas,  long requestId);
        void SaveHistoryDataToFile(DbContext context, List<ModelData> addDatas, List<ModelData> modifyDatas, List<ModelData> delDatas, int? currentVersion, long requestId);
        void SaveHistoryRelationToFile(DbContext context, List<Relationship> addDatas, List<Relationship> modifyDatas, List<Relationship> delDatas, int? currentVersion, long requestId);
        void SaveModelToDB(DbContext context, long requestId);
        void SaveModelHistoryToDB(DbContext context, long requestId);
        void SaveRelationshipHistoryToDB(DbContext context, long requestId);
        void SaveModelDatas(DbContext context, List<ModelData> recordDatas, int? currentVersion);
        void SaveModelDatasForWeb(DbContext context, string path, int? currentVersion);
        void ModifyModelDatas(DbContext context, List<ModelData> recordDatas, int? currentVersion);
        void DeleteModelDatas(DbContext context, List<ModelData> delDatas);
        void SaveHistoryDatasForWeb(DbContext context, string path, int? currentVersion);
        void SaveResourceForWeb(DbContext context, string path, int? currentVersion);
        void SaveHistoryDatas(DbContext context, List<ModelData> addDatas, List<ModelData> modifyDatas, List<ModelData> delDatas, int? currentVersion);
        void SaveRelationshipDatasForWeb(DbContext context, string path, int? currentVersion);
        void SaveRelationshipDatas(DbContext context, List<Relationship> recordDatas, int? currentVersion);
        void ReplaceResourceClasses(DbContext context, List<ResourceClass> resourceClasses);
        void ModifyRelationshipDatas(DbContext context, List<Relationship> recordDatas, int? currentVersion);
        void DeleteRelationshipDatas(DbContext context, List<Relationship> delDatas);
        void SaveHistoryRelationshipDatasForWeb(DbContext context, string path, int? currentVersion);
        void SaveHistoryRelationshipDatas(DbContext context, List<Relationship> addDatas, List<Relationship> modifyDatas, List<Relationship> delDatas, int? currentVersion);

        void SaveSingletonClassDatasForWeb(DbContext context, string path, int? currentVersion);
    }
}
