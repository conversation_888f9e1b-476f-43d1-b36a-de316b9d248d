using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using BimBase.Api.Infrastructure.ModelDomain;
using System.Collections.Generic;
using System.Linq;

namespace BimBase.Api.Infrastructure.DbInitializer.ModelDb
{
    /// <summary>
    /// ModelDb升级：v0 -> v2
    /// 创建版本管理表和初始化数据
    /// </summary>
    public class ModelDbUpgrade_v0_to_v2 : AbstractModelDbUpgrade
    {
        public ModelDbUpgrade_v0_to_v2(ILogger logger) : base(logger)
        {
        }

        public override string FromVersion => "v0";
        public override string ToVersion => "v2";
        public override string Description => "创建ModelDb版本管理表和初始化StoreyLocks数据";
        public override bool SupportsRollback => true;

        protected override async Task ExecuteUpgradeAsync(ModelDbContext context)
        {
            Logger.LogInformation($"开始执行ModelDb {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"ModelDb {FromVersion} -> {ToVersion} 升级完成");
        }

        protected override async Task ExecuteRollbackAsync(ModelDbContext context)
        {
            Logger.LogInformation($"开始执行ModelDb {ToVersion} -> {FromVersion} 回滚...");

            await ExecuteRollbackStepsAsync(context);

            Logger.LogInformation($"ModelDb {ToVersion} -> {FromVersion} 回滚完成");
        }

        /// <summary>
        /// 执行 v1 到 v0 的具体回滚步骤
        /// </summary>
        private async Task ExecuteRollbackStepsAsync(ModelDbContext context)
        {
            // 1. 删除触发器
            await DropModelDatasTriggersAsync(context);
            
            // 2. 删除版本跟踪表
            await DropModelDatasVersionTrackerTableAsync(context);
            
            // 3. 删除统计汇总表
            await DropModelDatasStatisticsTableAsync(context);
            
            // 4. 删除统计汇总表新版本
            await DropModelDatasStatisticsNew2TableAsync(context);
            
            // 5. 回滚ModelDatas表结构
            await RollbackModelDatasTableAsync(context);
            
            // 6. 清理StoreyLocks数据
            await ClearStoreyLocksAsync(context);
            
            // 7. 删除DatabaseVersion表（如果需要完全回退到v0状态）
            await DropDatabaseVersionTableAsync(context);
        }

        /// <summary>
        /// 执行 v0 到 v1 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(ModelDbContext context)
        {
            // 1. 确保DatabaseVersion表存在
            await EnsureDatabaseVersionTableAsync(context);
            
            // 2. 升级ModelDatas表结构和索引
            await UpgradeModelDatasTableAsync(context);
            
            // 3. 创建版本跟踪表
            await CreateModelDatasVersionTrackerTableAsync(context);
            
            // 4. 创建触发器
            await CreateModelDatasTriggersAsync(context);
            
            // 5. 创建ModelDatas统计汇总表
            await CreateModelDatasStatisticsTableAsync(context);
            
            // 6. 创建ModelDatas统计汇总表新版本
            await CreateModelDatasStatisticsNew2TableAsync(context);
            
            // 7. 初始化StoreyLocks数据（迁移原有逻辑）
            await InitializeStoreyLocksAsync(context);
        }

        /// <summary>
        /// 确保版本管理表存在
        /// </summary>
        private async Task EnsureDatabaseVersionTableAsync(ModelDbContext context)
        {
            var tableDefinition = @"
                CREATE TABLE `DatabaseVersion` (
                    `Id` int NOT NULL AUTO_INCREMENT,
                    `Version` varchar(50) NOT NULL,
                    `UpgradedAt` datetime(6) NOT NULL,
                    `Description` longtext NULL,
                    PRIMARY KEY (`Id`)
                );";
            
            await CreateTableIfNotExistsAsync(context, "DatabaseVersion", tableDefinition);
        }

        /// <summary>
        /// 初始化StoreyLocks数据（迁移原有ModelDbInitializer的逻辑）
        /// </summary>
        private async Task InitializeStoreyLocksAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始初始化StoreyLocks数据...");

            // 检查StoreyLocks表是否存在且是否有数据
            bool storeyLocksTableExists = await TableExistsAsync(context, "StoreyLocks");
            
            if (storeyLocksTableExists && await context.StoreyLocks.AnyAsync())
            {
                Logger.LogInformation("StoreyLocks数据已存在，跳过初始化");
                return;
            }

            if (storeyLocksTableExists)
            {
                // 创建初始StoreyLocks数据
                var domainList = new List<int> { 1, 2, 3, 4, 5, 17, 13 };
                var storeyLockList = new List<StoreyLock>();
                int i = 1;
                foreach (var d in domainList)
                {
                    var storeyLock = new StoreyLock();
                    storeyLock.Id = i;
                    storeyLock.Domain = d;
                    storeyLock.VersionNo = -1;
                    storeyLockList.Add(storeyLock);
                    i++;
                }
                context.StoreyLocks.AddRange(storeyLockList);
                
                Logger.LogInformation($"添加了 {storeyLockList.Count} 条StoreyLocks初始数据");
            }
            else
            {
                Logger.LogInformation("StoreyLocks表不存在，跳过数据初始化");
            }
        }

        /// <summary>
        /// 升级ModelDatas表结构和索引
        /// </summary>
        private async Task UpgradeModelDatasTableAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始升级ModelDatas表结构...");

            // 1. 修改DomainClassName字段：从longtext改为char(190)
            await ModifyDomainClassNameColumnAsync(context);

            // 2. 添加新索引
            await AddModelDatasIndexesAsync(context);

            Logger.LogInformation("ModelDatas表结构升级完成");
        }

        /// <summary>
        /// 修改DomainClassName字段类型
        /// </summary>
        private async Task ModifyDomainClassNameColumnAsync(ModelDbContext context)
        {
            await ModifyColumnIfExistsAsync(context, "modeldatas", "DomainClassName", "char(190) DEFAULT NULL");
        }

        /// <summary>
        /// 添加ModelDatas表的新索引
        /// </summary>
        private async Task AddModelDatasIndexesAsync(ModelDbContext context)
        {
            // 添加DomainClassName索引
            await CreateIndexIfNotExistsAsync(context, "modeldatas", "idx_domainclassname", "`DomainClassName`");

            // 添加复合索引 (InstanceId, VersionNo)
            await CreateIndexIfNotExistsAsync(context, "modeldatas", "idx_instance_version", "`InstanceId`, `VersionNo`");
        }

        /// <summary>
        /// 创建ModelDatas版本跟踪表
        /// </summary>
        private async Task CreateModelDatasVersionTrackerTableAsync(ModelDbContext context)
        {
            var tableDefinition = @"
                CREATE TABLE `modeldatas_version_tracker` (
                    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '追踪记录ID',
                    `data_id` bigint(20) NOT NULL COMMENT '原表自增ID',
                    `instance_id` bigint(20) NOT NULL COMMENT '实例标识',
                    `max_version` int(11) NOT NULL COMMENT '当前实例最大版本号',
                    `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `uk_instance` (`instance_id`),
                    KEY `idx_data` (`data_id`),
                    KEY `idx_version` (`max_version`)
                ) ENGINE=InnoDB;";

            await CreateTableIfNotExistsAsync(context, "modeldatas_version_tracker", tableDefinition);
        }

        /// <summary>
        /// 创建ModelDatas相关触发器
        /// </summary>
        private async Task CreateModelDatasTriggersAsync(ModelDbContext context)
        {
            Logger.LogInformation("创建ModelDatas触发器...");

            // 创建版本跟踪触发器
            await CreateVersionTrackingTriggerAsync(context);

            // 创建删除清理触发器
            await CreateDeleteCleanupTriggerAsync(context);

            Logger.LogInformation("ModelDatas触发器创建完成");
        }

        /// <summary>
        /// 创建版本跟踪触发器
        /// </summary>
        private async Task CreateVersionTrackingTriggerAsync(ModelDbContext context)
        {
            var triggerDefinition = @"
                CREATE TRIGGER `trg_modeldatas_version_tracking`
                AFTER INSERT ON `modeldatas`
                FOR EACH ROW 
                BEGIN
                    INSERT INTO `modeldatas_version_tracker` (`data_id`, `instance_id`, `max_version`)
                    VALUES (NEW.id, NEW.instanceid, NEW.versionno)
                    ON DUPLICATE KEY UPDATE
                        `data_id` = IF(NEW.versionno >= `max_version`, NEW.id, `data_id`),
                        `max_version` = GREATEST(NEW.versionno, `max_version`);
                END;";

            await CreateOrReplaceTriggerAsync(context, "trg_modeldatas_version_tracking", triggerDefinition);
        }

        /// <summary>
        /// 创建删除清理触发器
        /// </summary>
        private async Task CreateDeleteCleanupTriggerAsync(ModelDbContext context)
        {
            var triggerDefinition = @"
                CREATE TRIGGER `trg_modeldatas_delete_cleanup`
                AFTER DELETE ON `modeldatas`
                FOR EACH ROW 
                BEGIN
                    IF (SELECT COUNT(*) FROM `modeldatas` WHERE `instanceid` = OLD.instanceid) = 0 THEN
                        DELETE FROM `modeldatas_version_tracker` 
                        WHERE `instance_id` = OLD.instanceid;
                    END IF;
                END;";

            await CreateOrReplaceTriggerAsync(context, "trg_modeldatas_delete_cleanup", triggerDefinition);
        }

        /// <summary>
        /// 创建ModelDatas统计汇总表
        /// </summary>
        private async Task CreateModelDatasStatisticsTableAsync(ModelDbContext context)
        {
            var tableDefinition = @"
                CREATE TABLE `modeldatas_domain_class_summary` (
                    `DomainClassName` varchar(190) NOT NULL,
                    `cnt` int(11) NOT NULL,
                    `UpdatedAt` datetime(6) NOT NULL,
                    PRIMARY KEY (`DomainClassName`)
                ) ENGINE=InnoDB;";

            await CreateTableIfNotExistsAsync(context, "modeldatas_domain_class_summary", tableDefinition);
        }

        /// <summary>
        /// 创建ModelDatas统计汇总表新版本
        /// </summary>
        private async Task CreateModelDatasStatisticsNew2TableAsync(ModelDbContext context)
        {
            var tableDefinition = @"
                CREATE TABLE `modeldatas_domain_class_summary_new2` (
                    `DomainClassName` char(190) NOT NULL,
                    `cnt` int(11) NOT NULL,
                    `UpdatedAt` datetime NOT NULL,
                    PRIMARY KEY (`DomainClassName`)
                ) ENGINE=InnoDB;";

            await CreateTableIfNotExistsAsync(context, "modeldatas_domain_class_summary_new2", tableDefinition);
        }

        #region 回滚方法

        /// <summary>
        /// 删除ModelDatas相关触发器
        /// </summary>
        private async Task DropModelDatasTriggersAsync(ModelDbContext context)
        {
            Logger.LogInformation("删除ModelDatas触发器...");

            // 删除版本跟踪触发器
            await DropTriggerIfExistsAsync(context, "trg_modeldatas_version_tracking");

            // 删除删除清理触发器
            await DropTriggerIfExistsAsync(context, "trg_modeldatas_delete_cleanup");

            Logger.LogInformation("ModelDatas触发器删除完成");
        }

        /// <summary>
        /// 删除ModelDatas版本跟踪表
        /// </summary>
        private async Task DropModelDatasVersionTrackerTableAsync(ModelDbContext context)
        {
            await DropTableIfExistsAsync(context, "modeldatas_version_tracker");
        }

        /// <summary>
        /// 删除ModelDatas统计汇总表
        /// </summary>
        private async Task DropModelDatasStatisticsTableAsync(ModelDbContext context)
        {
            await DropTableIfExistsAsync(context, "modeldatas_domain_class_summary");
        }

        /// <summary>
        /// 删除ModelDatas统计汇总表新版本
        /// </summary>
        private async Task DropModelDatasStatisticsNew2TableAsync(ModelDbContext context)
        {
            await DropTableIfExistsAsync(context, "modeldatas_domain_class_summary_new2");
        }

        /// <summary>
        /// 回滚ModelDatas表结构修改
        /// </summary>
        private async Task RollbackModelDatasTableAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始回滚ModelDatas表结构...");

            // 1. 删除新增的索引
            await DropModelDatasIndexesAsync(context);

            // 2. 回滚DomainClassName字段类型（从char(190)改回longtext）
            await RevertDomainClassNameColumnAsync(context);

            Logger.LogInformation("ModelDatas表结构回滚完成");
        }

        /// <summary>
        /// 删除ModelDatas表的索引
        /// </summary>
        private async Task DropModelDatasIndexesAsync(ModelDbContext context)
        {
            // 删除DomainClassName索引
            await DropIndexIfExistsAsync(context, "modeldatas", "idx_domainclassname");

            // 删除复合索引
            await DropIndexIfExistsAsync(context, "modeldatas", "idx_instance_version");
        }

        /// <summary>
        /// 恢复DomainClassName字段类型为longtext
        /// </summary>
        private async Task RevertDomainClassNameColumnAsync(ModelDbContext context)
        {
            await ModifyColumnIfExistsAsync(context, "modeldatas", "DomainClassName", "longtext");
        }

        /// <summary>
        /// 清理StoreyLocks数据
        /// </summary>
        private async Task ClearStoreyLocksAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始清理StoreyLocks数据...");

            if (await TableExistsAsync(context, "StoreyLocks"))
            {
                // 只删除特定的初始化数据，避免误删其他数据
                var domainList = new List<int> { 1, 2, 3, 4, 5, 17, 13 };
                var storeyLocksToRemove = await context.StoreyLocks
                    .Where(sl => domainList.Contains(sl.Domain) && sl.VersionNo == -1)
                    .ToListAsync();

                if (storeyLocksToRemove.Count > 0)
                {
                    context.StoreyLocks.RemoveRange(storeyLocksToRemove);
                    Logger.LogInformation($"清理了 {storeyLocksToRemove.Count} 条StoreyLocks初始数据");
                }
                else
                {
                    Logger.LogInformation("未找到需要清理的StoreyLocks数据");
                }
            }
            else
            {
                Logger.LogInformation("StoreyLocks表不存在，跳过数据清理");
            }
        }

        /// <summary>
        /// 删除DatabaseVersion表（慎重操作，完全回退到v0状态）
        /// </summary>
        private async Task DropDatabaseVersionTableAsync(ModelDbContext context)
        {
            // 注意：这会删除所有版本记录，只在完全回退到v0时使用
            Logger.LogWarning("正在删除DatabaseVersion表，这会丢失所有版本记录！");
            await DropTableIfExistsAsync(context, "DatabaseVersion");
        }

        #endregion
    }
} 