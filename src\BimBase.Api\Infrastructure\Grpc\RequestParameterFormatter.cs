using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// 请求参数格式化器，负责智能格式化gRPC请求参数
    /// </summary>
    public class RequestParameterFormatter
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<RequestParameterFormatter> _logger;

        public RequestParameterFormatter(IConfiguration configuration, ILogger<RequestParameterFormatter> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 判断是否应该记录请求参数
        /// </summary>
        public bool ShouldLogRequestParameters(string methodName)
        {
            try
            {
                // 检查全局开关
                var enableRequestLogging = _configuration.GetValue<bool>("Logging:EnableRequestParameters", false);
                if (!enableRequestLogging)
                {
                    return false;
                }

                // 获取配置的排除方法列表
                var excludeMethods = _configuration.GetSection("Logging:ExcludeRequestParameterMethods").Get<string[]>() ?? new string[0];
                
                // 检查是否在排除列表中
                return !excludeMethods.Any(excludeMethod => methodName.Contains(excludeMethod, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查是否应该记录请求参数时发生异常，默认不记录");
                return false;
            }
        }

        /// <summary>
        /// 快速格式化请求参数（性能优化版本）
        /// </summary>
        public string FormatRequestParametersQuickly(object request)
        {
            if (request == null)
            {
                return "null";
            }

            try
            {
                var requestType = request.GetType();
                
                // 只返回类型名和关键属性数量，避免复杂序列化
                var propertyCount = requestType.GetProperties().Length;
                return $"{{Type: {requestType.Name}, Properties: {propertyCount}}}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "快速格式化请求参数时发生异常: {RequestType}", request?.GetType()?.Name ?? "Unknown");
                return "{Error: 参数格式化异常}";
            }
        }

        /// <summary>
        /// 智能格式化请求参数（完整版本，仅在需要详细信息时使用）
        /// </summary>
        public string FormatRequestParametersDetailed(object request)
        {
            if (request == null)
            {
                return "null";
            }

            try
            {
                var requestType = request.GetType();
                
                // 如果是基本类型，直接返回
                if (IsSimpleType(requestType))
                {
                    return FormatSimpleValue(request);
                }
                
                // 如果是gRPC Message类型，尝试序列化为JSON
                if (IsGrpcMessageType(requestType))
                {
                    return FormatGrpcMessage(request);
                }
                
                // 其他复杂类型，返回类型信息
                return $"{{Type: {requestType.Name}}}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "详细格式化请求参数时发生异常: {RequestType}", request?.GetType()?.Name ?? "Unknown");
                return "{Error: 参数格式化异常}";
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 判断是否为简单类型
        /// </summary>
        private bool IsSimpleType(Type type)
        {
            return type.IsPrimitive || 
                   type == typeof(string) || 
                   type == typeof(DateTime) || 
                   type == typeof(decimal) || 
                   type == typeof(Guid) ||
                   type.IsEnum ||
                   (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
        }

        /// <summary>
        /// 格式化简单值
        /// </summary>
        private string FormatSimpleValue(object value)
        {
            try
            {
                if (value is string str)
                {
                    // 字符串长度限制
                    const int maxStringLength = 200;
                    if (str.Length > maxStringLength)
                    {
                        return $"\"{str.Substring(0, maxStringLength)}...\" (Length: {str.Length})";
                    }
                    return $"\"{str}\"";
                }
                
                return value.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "格式化简单值时发生异常");
                return "<格式化异常>";
            }
        }

        /// <summary>
        /// 判断是否为gRPC Message类型
        /// </summary>
        private bool IsGrpcMessageType(Type type)
        {
            try
            {
                // 检查是否继承自Google.Protobuf.IMessage
                return type.GetInterfaces().Any(i => 
                    i.FullName == "Google.Protobuf.IMessage" || 
                    i.Name == "IMessage");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查gRPC Message类型时发生异常: {TypeName}", type?.Name ?? "Unknown");
                return false;
            }
        }

        /// <summary>
        /// 格式化gRPC Message对象
        /// </summary>
        private string FormatGrpcMessage(object message)
        {
            HashSet<String> _testData = new HashSet<string>
                {
                    "临时解决内存益处问题，这里要处理一下性能问题",
                    "吕孟"
                };
            try
            {
                // 尝试使用JsonConvert序列化
              
                var json = JsonConvert.SerializeObject(_testData, new JsonSerializerSettings
                {
                    // 忽略null值
                    NullValueHandling = NullValueHandling.Ignore,
                    // 忽略默认值
                    DefaultValueHandling = DefaultValueHandling.Ignore,
                    // 格式化输出
                    Formatting = Formatting.None,
                    // 最大深度限制，防止循环引用
                    MaxDepth = 10
                });
                
                // 检查JSON长度
                const int maxJsonLength = 1000;
                if (json.Length > maxJsonLength)
                {
                    // 尝试提取关键字段
                    var summary = ExtractMessageSummary(_testData);
                    return $"{summary} (Full JSON Length: {json.Length})";
                }
                
                return json;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "格式化gRPC Message时发生异常: {MessageType}", _testData?.GetType()?.Name ?? "Unknown");
                // 如果JSON序列化失败，提取基本信息
                return ExtractMessageSummary(_testData) + " (JSON序列化异常)";
            }
        }

        /// <summary>
        /// 提取Message的关键信息摘要
        /// </summary>
        private string ExtractMessageSummary(object message)
        {
            try
            {
                var type = message.GetType();
                var summary = new List<string>();
                
                // 获取所有公共属性
                var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                
                foreach (var prop in properties.Take(5)) // 只取前5个属性
                {
                    try
                    {
                        var value = prop.GetValue(message);
                        if (value != null)
                        {
                            if (IsSimpleType(prop.PropertyType))
                            {
                                var formattedValue = FormatSimpleValue(value);
                                summary.Add($"{prop.Name}: {formattedValue}");
                            }
                            else if (value is System.Collections.ICollection collection)
                            {
                                summary.Add($"{prop.Name}: [Count: {collection.Count}]");
                            }
                            else
                            {
                                summary.Add($"{prop.Name}: {{Type: {prop.PropertyType.Name}}}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "提取属性 {PropertyName} 时发生异常", prop.Name);
                        summary.Add($"{prop.Name}: <提取异常>");
                    }
                }
                
                return $"{{Type: {type.Name}, {string.Join(", ", summary)}}}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取Message摘要时发生异常: {MessageType}", message?.GetType()?.Name ?? "Unknown");
                return $"{{Type: {message?.GetType()?.Name ?? "Unknown"}, Error: 摘要提取异常}}";
            }
        }

        #endregion
    }
} 