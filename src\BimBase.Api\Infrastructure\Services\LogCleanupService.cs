using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Globalization;

namespace BimBase.Api.Infrastructure.Services
{
    /// <summary>
    /// 日志清理和整理服务，定期将日志文件按日期移动到对应文件夹，并清理过期的日志
    /// </summary>
    public class LogCleanupService : BackgroundService
    {
        private readonly ILogger<LogCleanupService> _logger;
        private readonly string[] _logBasePaths = { "logs/app", "logs/grpc_performance" };
        private readonly TimeSpan _retentionDays = TimeSpan.FromDays(30);
        private readonly TimeSpan _checkInterval = TimeSpan.FromHours(1); // 每小时检查一次

        public LogCleanupService(ILogger<LogCleanupService> logger)
        {
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await OrganizeLogFilesByDateAsync();
                    await CleanupExpiredLogsAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "日志整理和清理过程中发生错误");
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        /// <summary>
        /// 将日志文件按日期整理到对应的文件夹中
        /// </summary>
        private async Task OrganizeLogFilesByDateAsync()
        {
            foreach (var basePath in _logBasePaths)
            {
                try
                {
                    if (!Directory.Exists(basePath))
                        continue;

                    var files = Directory.GetFiles(basePath, "*.txt", SearchOption.TopDirectoryOnly);
                    
                    foreach (var file in files)
                    {
                        await OrganizeSingleLogFileAsync(file, basePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "整理日志文件夹 {BasePath} 时出错", basePath);
                }
            }
        }

        /// <summary>
        /// 整理单个日志文件
        /// </summary>
        private async Task OrganizeSingleLogFileAsync(string filePath, string basePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileInfo = new FileInfo(filePath);
                
                // 尝试从文件名中提取日期（格式如：app-20250115.txt）
                var dateMatch = Regex.Match(fileName, @"(\d{8})");
                DateTime targetDate;

                if (dateMatch.Success && DateTime.TryParseExact(dateMatch.Groups[1].Value, "yyyyMMdd", 
                    CultureInfo.InvariantCulture, DateTimeStyles.None, out targetDate))
                {
                    // 从文件名中成功提取日期
                }
                else
                {
                    // 如果文件名中没有日期，使用文件的最后写入时间
                    targetDate = fileInfo.LastWriteTime.Date;
                }

                var dateFolder = targetDate.ToString("yyyyMMdd");
                var targetDirectory = Path.Combine(basePath, dateFolder);
                var targetFilePath = Path.Combine(targetDirectory, fileName);

                // 如果文件已经在正确的日期文件夹中，跳过
                if (Path.GetDirectoryName(filePath) == targetDirectory)
                    return;

                // 创建目标文件夹
                Directory.CreateDirectory(targetDirectory);

                // 移动文件
                if (!File.Exists(targetFilePath))
                {
                    File.Move(filePath, targetFilePath);
                    _logger.LogInformation("日志文件已移动: {From} -> {To}", filePath, targetFilePath);
                }
                else
                {
                    // 如果目标文件已存在，合并内容（简单的追加方式）
                    await using var sourceStream = File.OpenRead(filePath);
                    await using var targetStream = File.OpenWrite(targetFilePath);
                    targetStream.Seek(0, SeekOrigin.End);
                    await sourceStream.CopyToAsync(targetStream);
                    
                    File.Delete(filePath);
                    _logger.LogInformation("日志文件已合并: {From} -> {To}", filePath, targetFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "整理日志文件 {FilePath} 时出错", filePath);
            }
        }

        /// <summary>
        /// 清理过期的日志文件和空文件夹
        /// </summary>
        private async Task CleanupExpiredLogsAsync()
        {
            var cutoffDate = DateTime.Now.Subtract(_retentionDays);

            foreach (var basePath in _logBasePaths)
            {
                try
                {
                    if (!Directory.Exists(basePath))
                        continue;

                    // 清理过期的日期文件夹
                    var dateFolders = Directory.GetDirectories(basePath);
                    
                    foreach (var folder in dateFolders)
                    {
                        var folderName = Path.GetFileName(folder);
                        
                        // 检查是否是日期格式的文件夹 (yyyyMMdd)
                        if (DateTime.TryParseExact(folderName, "yyyyMMdd", 
                            CultureInfo.InvariantCulture, DateTimeStyles.None, out var folderDate))
                        {
                            if (folderDate < cutoffDate.Date)
                            {
                                Directory.Delete(folder, true);
                                _logger.LogInformation("已删除过期日志文件夹: {Folder}", folder);
                            }
                        }
                    }

                    // 清理根目录下过期的日志文件
                    var files = Directory.GetFiles(basePath, "*.txt", SearchOption.TopDirectoryOnly);
                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.LastWriteTime < cutoffDate)
                        {
                            File.Delete(file);
                            _logger.LogInformation("已删除过期日志文件: {File}", file);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理过期日志 {BasePath} 时出错", basePath);
                }
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("日志清理服务正在停止");
            await base.StopAsync(stoppingToken);
        }
    }
} 