kind: helm-release
apiVersion: 1.1
build:
  context: ..\..
  dockerfile: Dockerfile
install:
  chart: charts/bimbaseauthapi
  values:
  - values.dev.yaml?
  - secrets.dev.yaml?
  set:
    # Optionally, specify an array of imagePullSecrets. These secrets must be manually created in the namespace.
    # This will override the imagePullSecrets array in values.yaml file.
    # If the dockerfile specifies any private registry, the imagePullSecret for that registry must be added here.
    # ref: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
    #
    # For example, the following uses credentials from secret "myRegistryKeySecretName".
    #
    # imagePullSecrets:
    #   - name: myRegistryKeySecretName
    replicaCount: 1
    image:
      repository: bimbaseauthapi
      tag: $(tag)
      pullPolicy: Never
    ingress:
      annotations:
        kubernetes.io/ingress.class: traefik-azds
      hosts:
      # This expands to form the service's public URL: [space.s.][rootSpace.]bimbaseauthapi.<random suffix>.<region>.azds.io
      # Customize the public URL by changing the 'bimbaseauthapi' text between the $(rootSpacePrefix) and $(hostSuffix) tokens
      # For more information see https://aka.ms/devspaces/routing
      - $(spacePrefix)$(rootSpacePrefix)bimbaseauthapi$(hostSuffix)
configurations:
  develop:
    build:
      dockerfile: Dockerfile.develop
      useGitIgnore: true
      args:
        BUILD_CONFIGURATION: ${BUILD_CONFIGURATION:-Debug}
    container:
      sync:
      - "**/Pages/**"
      - "**/Views/**"
      - "**/wwwroot/**"
      - "!**/*.{sln,csproj}"
      command: [dotnet, run, --no-restore, --no-build, --no-launch-profile, -c, "${BUILD_CONFIGURATION:-Debug}"]
      iterate:
        processesToKill: [dotnet, vsdbg, BimBaseAuth.Api]
        buildCommands:
        - [dotnet, build, --no-restore, -c, "${BUILD_CONFIGURATION:-Debug}"]
