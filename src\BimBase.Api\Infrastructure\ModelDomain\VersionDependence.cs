﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class VersionDependence
    {
        [Key]
        public Int64 Id { get; set; }

        public int SourceVersionNo
        {
            get;
            set;
        }

        public int SourceDomain
        {
            get;
            set;
        }

        public int TargetVersionNo
        {
            get;
            set;
        }

        public int TargetDomain
        {
            get;
            set;
        }


    }
}
