﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class DBBackupData
    {
        [Key]
        public Guid ID { get; set; }

        
        public string DBBackupName { get; set; }

        
        public string DBBackupServerPath { get; set; }

        
        public string DBBackupVirtualPath { get; set; }

        
        public string DBBackupDescription { get; set; }

        
        public string DBBackupNotice { get; set; }
        
        public int DBBackupNoticeSentFlag { get; set; }

        
        public DateTime DBBackupNoticeTime { get; set; }

        
        public DateTime DBBackupBeginTime { get; set; }

        
        public DateTime DBBackupFinishTime { get; set; }

        
        public int DBBackupDuration { get; set; }

        
        public decimal DBBackupSize { get; set; }

        
        public string DBBackupState { get; set; }
        
        public int DBBackupStateFlag { get; set; }
        
        public string DBBackupStateHistory { get; set; }

        
        public DateTime DBBackupCreateTime { get; set; }

        
        public int DeleteFlag { get; set; }
    }
}
