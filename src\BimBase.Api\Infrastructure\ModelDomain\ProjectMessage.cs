﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class ProjectMessage
    {
        
        [Key]
        //消息在服务端的ID
        public int MsgServerID { get; set; }
        
        //消息发送者的ID
        public int FromUser { get; set; }
        
        //消息接收者的ID
        public int ToUser { get; set; }
        
        //消息的文本内容
        public string MsgTextContent { get; set; }
        
        //消息的3种状态，且仅能为其中一种。未读/历史/发送 “这条消息是否已经被消息接收者获取过？” 两种状态：是/否
        public int MsgState { get; set; }
        
        //消息的3种类型，且仅能为其中一种。文本/构件/图片
        public int MsgType { get; set; }
        
        //消息创建的时间，由客户端提供
        public DateTime MsgCreateTime { get; set; }
        
        //消息的扩展信息，如是构件消息，则是构件InstanceID;如是图片消息，则是图片的二进制流。
        public byte[] MsgExData { get; set; }

    }
}
