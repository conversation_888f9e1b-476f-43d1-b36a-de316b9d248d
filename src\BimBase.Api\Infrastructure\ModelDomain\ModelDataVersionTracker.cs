﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    [Table("modeldatas_version_tracker")]
    public class ModelDataVersionTracker
    {
        [Key]
        [Column("id")]
        public Int64 Id { get; set; }
        /// <summary>
        /// modeldata表中的自增id
        /// </summary>
        [Column("data_id")]
        public Int64 DataId { get; set; }

        [Column("instance_id")] 
        public Int64 InstanceId { get; set; }
        [Column("max_version")]
        public int MaxVersion { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("create_at")]
        public DateTime CreateAt { get; set; } = DateTime.Now;
    }
}
