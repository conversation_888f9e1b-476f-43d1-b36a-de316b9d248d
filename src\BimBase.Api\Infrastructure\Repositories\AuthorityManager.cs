﻿using BimBase.Api.Config;
using BimBase.Api.Grpc;
using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Nodes;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Repositories
{
    public class AuthorityManager : IAuthorityManager
    {
        private readonly UrlsConfig _urls;
        public AuthorityManager(IOptions<UrlsConfig> config)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
        }
        public bool AddProjectRole(string projectGuid, GrpcRole role, Guid creator, out string returnMessage)
        {
            returnMessage = "";

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetProjectRoleAsync(new GetProjectRoleRequest { Projectguid = projectGuid });
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            foreach (var item in ret.RoleList)
            {
                if (item.Name == role.Name)
                {
                    returnMessage = "已经存在名为：" + item.Name + " 的角色";
                    return false;
                }
            }

            var ret2 = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.AddProjectRoleAsync(new AddProjectRoleRequest 
                { 
                    RoleExtend = new GrpcRoleExtend
                    {
                        Id = role.Id,
                        RelatedId = projectGuid.ToString(),
                        CreateId = creator.ToString(),
                        Name = role.Name,
                        Status = role.Status,
                        Type = role.Type,
                        CreateTime = Timestamp.FromDateTimeOffset(DateTime.Now),
                        AppId = role.AppId,
                        DelFlag = role.DelFlag,
                        UpdateTime = role.UpdateTime,
                        IsAdmin = false,
                    }
                });
                return response;
            }).GetAwaiter().GetResult();

            if (!ret2.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }
            return true;
        }

        public bool AddRole(GrpcRole role,out string returnMessage)
        {
            returnMessage = "";
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.AddRoleAsync(new BimBaseAuth.Api.Protos.AddRoleRequest { Role = role });
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            return true;
        }

        public bool DeleteProject(Guid id, out string returnMessage)
        {
            returnMessage = "";
            //var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            //{
            //    var client = new AuthGrpc.AuthGrpcClient(channel);
            //    var response = await client.DeleteProjectAsync(new BimBaseAuth.Api.Protos.DeleteProjectRequest { ProjectId = id.ToString() });
            //    return response;
            //}).GetAwaiter().GetResult();

            //if (!ret.IsSuccess)
            //{
            //    returnMessage = ret.Message;
            //    return false;
            //}

            return true;
        }

        public bool DeleteProjectRoles(List<string> roleList, out string returnMessage)
        {
            returnMessage = "";
            var request = new BimBaseAuth.Api.Protos.DeleteProjectRoleRequest();
            request.RoleIds.AddRange(roleList);
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.DeleteProjectRoleAsync(request);
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            return true;
        }

        public bool DeleteRoleAuth(string roleId, out string returnMessage)
        {
            returnMessage = "";
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.DeleteRoleAuthAsync(new DeleteRoleAuthRequest {  RoleId = roleId });
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            return true;
        }

        public bool DeleteRoles(List<Guid> roleList, Guid operateMember, out string returnMessage)
        {
            throw new NotImplementedException();
        }

        public bool DeleteRoleUser(string roleId, List<string> userIds, out string returnMessage)
        {
            returnMessage = "";
            var p = new DeleteRoleUserRequest { RoleId = roleId };
            p.UserIds.AddRange(userIds);

            List<string> userIdList = new List<string>();

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.DeleteRoleUserAsync(p);
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            return true;
        }

        public bool GetProjectRole(string projectGuid, out List<GrpcRole> roles, out string returnMessage)
        {
            returnMessage = "";
            roles = new List<GrpcRole>();

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetProjectRoleAsync(new GetProjectRoleRequest { Projectguid = projectGuid });
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }
            return true;
        }

        public bool GetRoleUsers(string roleId, out List<Guid> memberGuids, out string returnMessage)
        {
            returnMessage = "";
            memberGuids = new List<Guid>();
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetUsersFromRoleIdAsync(new GetUsersFromRoleIdRequest { RoleId = roleId });
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            var p = ret.Result.Unpack<Value>();
            memberGuids = System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(p.StringValue);

            return true;
        }

        public bool GiveRoleAuth(string roleGuid, List<GrpcAuthInfo> authInfos, Guid operateMember, out string returnMessage)
        {
            returnMessage = "";

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);

                var requestParamter = new GiveRoleAuthRequest
                {
                    RoleId = roleGuid,
                };
                requestParamter.AuthInfoList.AddRange(authInfos);

                var response = await client.GiveRoleAuthAsync(requestParamter);
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }
            return true;
        }

        public bool GiveRoleUser(string roleGuid, List<Guid> userGuids, Guid operateMember, out string returnMessage)
        {
            returnMessage = "";

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);

                var requestParamter = new GiveRoleUserRequest
                {
                    RoleId = roleGuid,
                };
                requestParamter.UserId.AddRange(userGuids.Select(s=>s.ToString()));

                var response = await client.GiveRoleUserAsync(requestParamter);
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }
            return true;
        }

        public bool ProjectGiveRole(string projectGuid, Guid roleGuid, Guid operateMember, out string returnMessage)
        {
            throw new NotImplementedException();
        }

        public bool GetOwnRoleList(string memberGuid, out List<RoleDto> roles, out string returnMessage)
        {
            returnMessage = "";
            roles = new List<RoleDto>();
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetOwnRoleListAsync(new GetOwnRoleListRequest {  UserId = memberGuid });
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }
            foreach (var commonAuthRole in ret.RoleList)
            {
                roles.Add(
                    new RoleDto
                    {
                        Id = commonAuthRole.Id,
                        Name = commonAuthRole.Name,
                        Status = commonAuthRole.Status,
                        Type = commonAuthRole.Type
                    }
                    );
            }

            return true;
        }

        public bool GetRoleInfoByUserIdAndProjectId(Guid UserId, Guid projectId, out List<RoleDto> roles, out string returnMessage)
        {
            returnMessage = "";
            roles = new List<RoleDto>();
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetRoleInfoByUserIdAndProjectIdAsync(new GetRoleInfoByUserIdAndProjectIdRequest { UserId = UserId.ToString(), ProjecId = projectId.ToString() });
                return response;
            }).GetAwaiter().GetResult();


            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }


            foreach (var commonAuthRole in ret.RoleList)
            {
                roles.Add(
                    new RoleDto
                    {
                        Id = commonAuthRole.Id,
                        Name = commonAuthRole.Name,
                        Status = commonAuthRole.Status,
                        Type = commonAuthRole.Type
                    }
                    );
            }

            return true;
        }

        public bool GetProjectUserAuth(Guid memberId, Guid projectGuid, out List<GrpcAuthInfo> authInfos, out string returnMessage)
        {
            returnMessage = "";
            authInfos = new List<GrpcAuthInfo>();

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetProjectAuthByUserIdAsync(new GetProjectAuthByUserIdRequest { UserId = memberId.ToString(),  ProjectId = projectGuid.ToString()});
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            authInfos.AddRange(ret.AuthInfoList);
            return true;
        }

        public bool GetRoleAuthorities(string roleId, out List<GrpcAuthInfo> authInfos)
        {
            authInfos = new List<GrpcAuthInfo>();
            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetRoleAuthAsync(new GetRoleAuthRequest { RoleId = roleId });
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                return false;
            }

            authInfos = ret.AuthInfoList.ToList();
            //var commonAuth = JsonHelper.Instance.Deserialize<List<CommonAuth.Domain.AuthInfo>>(ret.Result);
            //foreach (var Auth in commonAuth)
            //{
            //    authInfos.Add(
            //        new AuthInfo
            //        {
            //            Code = Auth.Code,
            //            Name = Auth.Name,
            //            Type = Auth.Type,
            //            Permission = Auth.Permission,
            //            AuthId = Auth.RelatedId,
            //            ParentId = Auth.ParentId
            //        });
            //}

            return true;
        }

        public bool GetRoleAuth(string roleGuid, string projectGuid, out List<GrpcAuthInfo> authInfos, out string returnMessage)
        {
            returnMessage = "";
            authInfos = new List<GrpcAuthInfo>();
            //JsonObject ret = _auth.GetRoleAuth(roleGuid, projectGuid);

            var ret = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);
                var response = await client.GetRoleAuthAsync(new GetRoleAuthRequest { RoleId = roleGuid });
                return response;
            }).GetAwaiter().GetResult();

            if (!ret.IsSuccess)
            {
                returnMessage = ret.Message;
                return false;
            }

            authInfos = ret.AuthInfoList.ToList();

            //var commonAuthInfos = JsonHelper.Instance.Deserialize<List<CommonAuth.Domain.AuthInfo>>(ret.Result);

            //foreach (var commonAuthinfo in commonAuthInfos)
            //{
            //    authInfos.Add(new AuthInfo
            //    {
            //        Code = commonAuthinfo.Code,
            //        Name = commonAuthinfo.Name,
            //        Type = commonAuthinfo.Type,
            //        Permission = commonAuthinfo.Permission,
            //        AuthId = commonAuthinfo.RelatedId,
            //        ParentId = commonAuthinfo.ParentId
            //    });
            //}

            return true;
        }

    }
}
