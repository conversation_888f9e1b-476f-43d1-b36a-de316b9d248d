﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class MessageData
    {
        [Key]
        public int ID { get; set; }

        [Required]
        public Guid SenderID { get; set; }

        
        [Required]
        public Guid ReceiverID { get; set; } //同一条消息的多个接收人按一行一行的保存

        
        public DateTime Time { get; set; }

        
        public string Subject { get; set; }

        
        public string Content { get; set; }

        
        public bool IsRead { get; set; }

        
        public string Type { get; set; }

        public Guid ProjectId { get; set; }

        
        public virtual List<ObjectInfo> ObjectInfos { get; set; }

        //截图文件扩展
        
        public string FileType { get; set; }    //文件类型

        
        public string FileName { get; set; }    //文件名

        
        public byte[] File { get; set; }    //文件内容

    }
}
