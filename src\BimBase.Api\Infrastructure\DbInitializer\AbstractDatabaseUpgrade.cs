using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    /// <summary>
    /// 通用数据库字段信息
    /// </summary>
    public class DatabaseColumnInfo
    {
        public string ColumnType { get; set; }
        public string IsNullable { get; set; }
        public string ColumnDefault { get; set; }
        public string ColumnComment { get; set; }
    }

    /// <summary>
    /// 查询数据库字段信息的结果类
    /// </summary>
    public class DatabaseColumnInfoQueryResult
    {
        public string column_type { get; set; }
        public string is_nullable { get; set; }
        public string column_default { get; set; }
        public string column_comment { get; set; }
    }

    /// <summary>
    /// 抽象数据库升级基类
    /// 提供所有数据库升级操作的通用功能
    /// </summary>
    public abstract class AbstractDatabaseUpgrade<TContext> where TContext : DbContext
    {
        protected readonly ILogger Logger;
        
        protected AbstractDatabaseUpgrade(ILogger logger)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        /// <summary>
        /// 源版本
        /// </summary>
        public abstract string FromVersion { get; }
        
        /// <summary>
        /// 目标版本
        /// </summary>
        public abstract string ToVersion { get; }
        
        /// <summary>
        /// 升级描述
        /// </summary>
        public abstract string Description { get; }
        
        /// <summary>
        /// 是否支持回滚（默认不支持）
        /// </summary>
        public virtual bool SupportsRollback => false;
        
        #region 通用数据库检查方法
        
        /// <summary>
        /// 检查表是否存在
        /// </summary>
        protected async Task<bool> TableExistsAsync(TContext context, string tableName)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = '{tableName}'";

                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 检查列是否存在
        /// </summary>
        protected async Task<bool> ColumnExistsAsync(TContext context, string tableName, string columnName)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = '{tableName}' 
                      AND column_name = '{columnName}'";

                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 检查索引是否存在
        /// </summary>
        protected async Task<bool> IndexExistsAsync(TContext context, string tableName, string indexName)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.statistics 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = '{tableName}' 
                      AND index_name = '{indexName}'";

                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }
        
        #endregion
        
        #region 通用数据库操作方法
        
        /// <summary>
        /// 如果列不存在则添加列
        /// </summary>
        protected async Task AddColumnIfNotExistsAsync(TContext context, string tableName, string columnName, string columnDefinition)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                var sql = $"ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnDefinition}";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"成功为 {tableName} 表添加 {columnName} 列");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表的 {columnName} 列已存在，跳过添加");
            }
        }
        
        /// <summary>
        /// 如果列存在则删除列
        /// </summary>
        protected async Task DropColumnIfExistsAsync(TContext context, string tableName, string columnName)
        {
            if (await ColumnExistsAsync(context, tableName, columnName))
            {
                var sql = $"ALTER TABLE `{tableName}` DROP COLUMN `{columnName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"成功从 {tableName} 表删除 {columnName} 列");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表的 {columnName} 列不存在，跳过删除");
            }
        }
        
        /// <summary>
        /// 如果索引不存在则创建索引
        /// </summary>
        protected async Task CreateIndexIfNotExistsAsync(TContext context, string tableName, string indexName, string indexDefinition)
        {
            if (!await IndexExistsAsync(context, tableName, indexName))
            {
                var sql = $"CREATE INDEX `{indexName}` ON `{tableName}` {indexDefinition}";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"成功为 {tableName} 表创建 {indexName} 索引");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表的 {indexName} 索引已存在，跳过创建");
            }
        }
        
        /// <summary>
        /// 如果索引存在则删除索引
        /// </summary>
        protected async Task DropIndexIfExistsAsync(TContext context, string tableName, string indexName)
        {
            if (await IndexExistsAsync(context, tableName, indexName))
            {
                var sql = $"DROP INDEX `{indexName}` ON `{tableName}`";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"成功从 {tableName} 表删除 {indexName} 索引");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表的 {indexName} 索引不存在，跳过删除");
            }
        }
        
        /// <summary>
        /// 如果表不存在则创建表
        /// </summary>
        protected async Task CreateTableIfNotExistsAsync(TContext context, string tableName, string tableDefinition)
        {
            if (!await TableExistsAsync(context, tableName))
            {
                await context.Database.ExecuteSqlRawAsync(tableDefinition);
                Logger.LogInformation($"成功创建 {tableName} 表");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表已存在，跳过创建");
            }
        }
        
        /// <summary>
        /// 如果表存在则删除表
        /// </summary>
        protected async Task DropTableIfExistsAsync(TContext context, string tableName)
        {
            if (await TableExistsAsync(context, tableName))
            {
                await context.Database.ExecuteSqlRawAsync($"DROP TABLE `{tableName}`");
                Logger.LogInformation($"成功删除 {tableName} 表");
            }
            else
            {
                Logger.LogInformation($"{tableName} 表不存在，跳过删除");
            }
        }
        
        /// <summary>
        /// 获取字段信息
        /// </summary>
        protected async Task<DatabaseColumnInfo> GetColumnInfoAsync(TContext context, string tableName, string columnName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT column_type, is_nullable, column_default, column_comment
                FROM information_schema.columns 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND column_name = '{columnName}'";

            var result = await context.Database.SqlQueryRaw<DatabaseColumnInfoQueryResult>(sql).ToListAsync();
            var columnData = result.FirstOrDefault();
            
            return columnData != null ? new DatabaseColumnInfo
            {
                ColumnType = columnData.column_type,
                IsNullable = columnData.is_nullable,
                ColumnDefault = columnData.column_default,
                ColumnComment = columnData.column_comment
            } : null;
        }

        /// <summary>
        /// 确保字段存在且类型匹配（如果不存在则添加，如果类型不匹配则修改）
        /// </summary>
        protected async Task EnsureColumnWithTypeAsync(TContext context, string tableName, string columnName, string columnDefinition)
        {
            var columnInfo = await GetColumnInfoAsync(context, tableName, columnName);
            
            if (columnInfo == null)
            {
                // 字段不存在，直接添加
                var sql = $"ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnDefinition}";
                await context.Database.ExecuteSqlRawAsync(sql);
                Logger.LogInformation($"已添加列: {tableName}.{columnName}");
            }
            else
            {
                // 字段存在，检查类型是否匹配
                var normalizedExistingType = NormalizeColumnType(columnInfo.ColumnType);
                var normalizedRequiredType = NormalizeColumnType(columnDefinition);
                
                if (!IsColumnTypeCompatible(normalizedExistingType, normalizedRequiredType))
                {
                    // 类型不匹配，需要修改
                    var sql = $"ALTER TABLE `{tableName}` MODIFY COLUMN `{columnName}` {columnDefinition}";
                    await context.Database.ExecuteSqlRawAsync(sql);
                    Logger.LogInformation($"已修改列类型: {tableName}.{columnName} 从 '{columnInfo.ColumnType}' 改为 '{columnDefinition}'");
                }
                else
                {
                    Logger.LogInformation($"列已存在且类型匹配，跳过: {tableName}.{columnName}");
                }
            }
        }

        /// <summary>
        /// 规范化字段类型字符串，用于比较
        /// </summary>
        private string NormalizeColumnType(string columnType)
        {
            if (string.IsNullOrEmpty(columnType))
                return string.Empty;

            // 移除多余的空格并转换为小写
            var normalized = columnType.Trim().ToLowerInvariant();
            
            // 规范化常见的类型别名
            normalized = normalized.Replace("longtext", "longtext");
            normalized = normalized.Replace("char(190)", "char(190)");
            normalized = normalized.Replace("varchar(", "varchar(");
            normalized = normalized.Replace("int(11)", "int");
            normalized = normalized.Replace("int(", "int(");
            
            // 移除默认值和注释部分，只保留核心类型定义
            var parts = normalized.Split(new[] { " default ", " comment " }, StringSplitOptions.RemoveEmptyEntries);
            return parts[0].Trim();
        }

        /// <summary>
        /// 检查字段类型是否兼容
        /// </summary>
        private bool IsColumnTypeCompatible(string existingType, string requiredType)
        {
            // 移除 NULL/NOT NULL 等修饰符，只比较核心类型
            var cleanExistingType = ExtractCoreType(existingType);
            var cleanRequiredType = ExtractCoreType(requiredType);
            
            return string.Equals(cleanExistingType, cleanRequiredType, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 提取字段的核心类型（去除NULL约束等修饰符）
        /// </summary>
        private string ExtractCoreType(string columnType)
        {
            if (string.IsNullOrEmpty(columnType))
                return string.Empty;

            // 分割并取第一个部分作为核心类型
            var parts = columnType.Split(new[] { " not null", " null", " default" }, StringSplitOptions.RemoveEmptyEntries);
            return parts[0].Trim();
        }
        
        #endregion
    }
} 