﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public static class VersionTool
    {
        public static bool CalculateDatas(IProjectRepository modelManager, int targetVersionNo, long instanceId, out ModelData data, Guid projectGuid)
        {
            data = new ModelData();
            List<long> ids = new List<long>();
            ids.Add(instanceId);
            //如果是最新版本直接获取
            if (targetVersionNo == modelManager.CurrentVersionNo)
            {
                if (CacheHelper.IsRuning && CacheHelper.ProjectInCache(projectGuid))
                {
                    data = CacheHelper.FromCacheModelData(projectGuid, ids).FirstOrDefault();
                }
                else
                {
                    data = modelManager.Datas.Where(d => d.InstanceId == instanceId).FirstOrDefault();
                }
                return true;
            }
            var lastUpdatedData = modelManager.AllHistoryDatas.Where(hist => hist.VersionNo == targetVersionNo && hist.InstanceId == instanceId).FirstOrDefault();
            if (lastUpdatedData != null && lastUpdatedData.OperationRecordType != OperationRecordType.Delete)
            {
                var temp = new ModelData
                {
                    InstanceId = lastUpdatedData.InstanceId,
                    //DomainClassID = lastUpdatedData.DomainClassID,
                    DomainClassName = lastUpdatedData.DomainClassName,
                    ECSchemaName = lastUpdatedData.ECSchemaName,
                    StoreyID = lastUpdatedData.StoreyID,
                    Domain = lastUpdatedData.Domain,
                    Data = lastUpdatedData.Data,
                    VersionNo = lastUpdatedData.VersionNo
                };
                data = temp;
            }
            return true;
        }

        public static bool CalculateVersionDatas(IProjectRepository modelManager,
                                                int targetVersionNo,
                                                out List<ModelData> datas,
                                                out List<Relationship> relationships,
                                                Guid projectGuid)
        {
            datas = new List<ModelData>();
            relationships = new List<Relationship>();
            //如果是最新版本直接获取
            if (targetVersionNo == modelManager.CurrentVersionNo)
            {
                if (CacheHelper.IsRuning && CacheHelper.ProjectInCache(projectGuid))
                {
                    datas = CacheHelper.FromCacheModelData(projectGuid);
                    relationships = CacheHelper.FromCacheRelationShip(projectGuid);
                }

                if (datas == null || !datas.Any())
                {
                    datas = modelManager.Datas.ToList();
                }

                if (relationships == null || !relationships.Any())
                {
                    relationships = modelManager.Relationships.AsNoTracking().ToList();
                }
                return true;
            }

            if (!modelManager.AllHistoryDatas.Where(h => h.VersionNo == targetVersionNo).Any())
            {
                return true;
            }

            var allUpdatedData =
            modelManager.AllHistoryDatas.Where(hist => hist.VersionNo <= targetVersionNo).ToList().GroupBy(hist => hist.InstanceId);

            foreach (var histData in allUpdatedData)
            {
                var lastUpdatedData =
                histData.OrderBy(d => d.VersionNo).OrderByDescending(d => d.Id).FirstOrDefault();

                if (lastUpdatedData != null && lastUpdatedData.OperationRecordType != OperationRecordType.Delete)
                {
                    var data = new ModelData
                    {
                        InstanceId = lastUpdatedData.InstanceId,
                        //DomainClassID = lastUpdatedData.DomainClassID,
                        DomainClassName = lastUpdatedData.DomainClassName,
                        ECSchemaName = lastUpdatedData.ECSchemaName,
                        StoreyID = lastUpdatedData.StoreyID,
                        Domain = lastUpdatedData.Domain,
                        Data = lastUpdatedData.Data,
                        VersionNo = lastUpdatedData.VersionNo,
                        StoreyGuid = lastUpdatedData.StoreyGuid
                    };
                    datas.Add(data);
                }
            }

            var allUpdatedRelationshipData =
                modelManager.AllHistoryRelationships.Where(hist => hist.VersionNo <= targetVersionNo).ToList().GroupBy(hist => hist.InstanceId);

            foreach (var relationshipData in allUpdatedRelationshipData)
            {
                var lastUpdatedData =
                    relationshipData.OrderByDescending(hist => hist.Id).FirstOrDefault();
                if (lastUpdatedData != null && lastUpdatedData.OperationRecordType != OperationRecordType.Delete)
                {
                    var relationship = new Relationship()
                    {
                        InstanceId = lastUpdatedData.InstanceId,
                        //DomainClassID = lastUpdatedData.DomainClassID,
                        DomainClassName = lastUpdatedData.DomainClassName,
                        ECSchemaName = lastUpdatedData.ECSchemaName,
                        SourceID = lastUpdatedData.SourceID,
                        //SourceDomainClassID = lastUpdatedData.SourceDomainClassID,
                        SourceDomainClassName = lastUpdatedData.SourceDomainClassName,
                        SourceECSchemaName = lastUpdatedData.SourceECSchemaName,
                        TargetID = lastUpdatedData.TargetID,
                        //TargetDomainClassID = lastUpdatedData.TargetDomainClassID,
                        TargetDomainClassName = lastUpdatedData.TargetDomainClassName,
                        TargetECSchemaName = lastUpdatedData.TargetECSchemaName,
                        Data = lastUpdatedData.Data,
                        VersionNo = lastUpdatedData.VersionNo,
                        Type = lastUpdatedData.Type,
                        IsForward = lastUpdatedData.IsForward
                    };
                    relationships.Add(relationship);
                }
            }

            return true;
        }

        /// <summary>
        /// 按Domain获取版本数据
        /// </summary>
        /// <param name="modelManager"></param>
        /// <param name="domains"></param>
        /// <param name="targetVersionNo"></param>
        /// <param name="datas"></param>
        /// <param name="relationships"></param>
        /// <param name="projectGuid"></param>
        /// <returns></returns>
        public static bool CalculateVersionDatas(IProjectRepository modelManager,
                                                    List<int> domains,
                                                    int targetVersionNo,
                                                    out List<ModelData> datas,
                                                    out List<Relationship> relationships,
                                                    Guid projectGuid)
        {
            datas = new List<ModelData>();
            relationships = new List<Relationship>();

            List<ModelData> targetVersionDatas;
            List<Relationship> targetVersionRelationships;

            Stopwatch sw = new Stopwatch();
            sw.Start();
            bool status = CalculateVersionDatas(modelManager,
                                    targetVersionNo,
                                    out targetVersionDatas,
                                    out targetVersionRelationships,
                                    projectGuid);
            if (!status) return false;
            sw.Stop();

            Console.WriteLine("CalculateVersionDatas take:{0}", sw.ElapsedMilliseconds);

            sw.Restart();
            var dataIds = (targetVersionDatas.FindAll(d => domains.Contains(d.Domain)) ?? new List<ModelData>())
                                .Select(d => d.InstanceId).ToList();
            sw.Stop();
            Console.WriteLine("targetVersionDatas.FindAll take:{0}", sw.ElapsedMilliseconds);

            sw.Restart();
            //ModelQuery modelQuery = new ModelQuery(modelManager);
            sw.Stop();
            Console.WriteLine("FindRelatedDatas. take:{0}", sw.ElapsedMilliseconds);

            var hashids = new HashSet<long>(dataIds);

            sw.Restart();
            relationships = targetVersionRelationships.Where(rs => hashids.Contains(rs.SourceID)).ToList();
            datas = targetVersionDatas.FindAll(d => hashids.Contains(d.InstanceId));
            sw.Stop();
            Console.WriteLine("targetVersionRelationships and targetVersionDatas findall. take:{0}", sw.ElapsedMilliseconds);

            return true;
        }



        /// <summary>
        /// 按楼层获取版本数据
        /// </summary>
        /// <param name="modelManager"></param>
        /// <param name="storeyId"></param>
        /// <param name="targetVersionNo"></param>
        /// <param name="datas"></param>
        /// <param name="relationships"></param>
        /// <param name="projectGuid"></param>
        /// <returns></returns>
        public static bool CalculateVersionDatas(IProjectRepository modelManager, long storeyId, int targetVersionNo, out List<ModelData> datas,
            out List<Relationship> relationships, Guid projectGuid)
        {
            datas = new List<ModelData>();
            relationships = new List<Relationship>();

            List<ModelData> targetVersionDatas;
            List<Relationship> targetVersionRelationships;
            bool status = CalculateVersionDatas(modelManager, targetVersionNo, out targetVersionDatas, out targetVersionRelationships, projectGuid);
            if (!status) return false;

            var dataIds = targetVersionDatas.FindAll(d => d.StoreyID == storeyId).Select(d => d.InstanceId).ToList();

            //ModelQuery modelQuery = new ModelQuery(modelManager);
            dataIds = modelManager.FindRelatedDatas(dataIds);
            var hashids = new HashSet<long>(dataIds);

            relationships = targetVersionRelationships.
                Where(rs => (rs.Type == RelationshipType.Contain || rs.Type == RelationshipType.Assemble || rs.Type == RelationshipType.Dependence || rs.Type == RelationshipType.Reference)
                    && (hashids.Contains(rs.SourceID) && hashids.Contains(rs.TargetID))).ToList();
            datas = targetVersionDatas.FindAll(d => hashids.Contains(d.InstanceId));

            return true;
        }

        /// <summary>
        /// 按InstanceId集合获取版本数据
        /// </summary>
        /// <param name="modelManager"></param>
        /// <param name="instanceIds"></param>
        /// <param name="targetVersionNo"></param>
        /// <param name="datas"></param>
        /// <param name="relationships"></param>
        /// <param name="projectGuid"></param>
        /// <returns></returns>
        public static bool CalculateVersionDatas(IProjectRepository modelManager, List<long> instanceIds,
                                                int targetVersionNo, out List<ModelData> datas,
                                                out List<Relationship> relationships, Guid projectGuid)
        {
            datas = new List<ModelData>();
            relationships = new List<Relationship>();

            HashSet<long> instanceIdHashSet = new HashSet<long>(instanceIds);

            List<ModelData> targetVersionDatas;
            List<Relationship> targetVersionRelationships;
            bool status = CalculateVersionDatas(modelManager, targetVersionNo, out targetVersionDatas, out targetVersionRelationships, projectGuid);
            if (!status) return false;

            var dataIds = targetVersionDatas.FindAll(d => instanceIdHashSet.Contains(d.InstanceId)).Select(d => d.InstanceId).ToList();

            //ModelQuery modelQuery = new ModelQuery(modelManager);
            if (CacheHelper.IsRuning && CacheHelper.ProjectInCache(projectGuid))
                dataIds = modelManager.FindRelatedDatasFromCahce(projectGuid, dataIds, targetVersionRelationships);
            else
                dataIds = modelManager.FindRelatedDatas(dataIds);
            var hashids = new HashSet<long>(dataIds);

            relationships = targetVersionRelationships.
                Where(rs => (rs.Type == RelationshipType.Contain || rs.Type == RelationshipType.Assemble || rs.Type == RelationshipType.Dependence || rs.Type == RelationshipType.Reference)
                    && (hashids.Contains(rs.SourceID) && hashids.Contains(rs.TargetID))).ToList();
            datas = targetVersionDatas.FindAll(d => hashids.Contains(d.InstanceId));

            return true;
        }


        /// <summary>
        /// 按InstanceId集合获取最新版本数据
        /// </summary>
        /// <param name="modelManager"></param>
        /// <param name="instanceIds"></param>
        /// <param name="datas"></param>
        /// <param name="relationships"></param>
        /// <returns></returns>
        public static bool CalculateCurrentVersionDatas(IProjectRepository modelManager,
                                                        List<long> instanceIds,
                                                        out List<ModelData> datas,
                                                        out List<Relationship> relationships)
        {

            var dataIds = instanceIds;

            //ModelQuery modelQuery = new ModelQuery(modelManager);

            dataIds = modelManager.FindRelatedDatas(dataIds);
            var hashids = new HashSet<long>(dataIds);

            relationships = modelManager.Relationships.
                                            Where(rs => (rs.Type == RelationshipType.Contain
                                                            || rs.Type == RelationshipType.Assemble
                                                            || rs.Type == RelationshipType.Dependence
                                                            || rs.Type == RelationshipType.Reference
                                                        )
                                                        &&
                                                        (hashids.Contains(rs.SourceID) && hashids.Contains(rs.TargetID))
                                                ).ToList();

            datas = modelManager.Datas.Where(d => hashids.Contains(d.InstanceId)).ToList();
            return true;
        }

        /// <summary>
        /// 按InstanceId集合获取最新版本数据
        /// </summary>
        /// <param name="modelManager"></param>
        /// <param name="instanceIds"></param>
        /// <param name="datas"></param>
        /// <param name="relationships"></param>
        /// <param name="projectGuid"></param>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool CalculateCurrentVersionDatasFromCache(IProjectRepository modelManager,
                                                        List<long> instanceIds,
                                                        out List<ModelData> datas,
                                                        out List<Relationship> relationships,
                                                        Guid projectGuid,
                                                        IEnumerable<RelatedSearchDto> source)
        {
            relationships = new List<Relationship>();  //输出参数先赋值
            var dataIds = instanceIds;
            var sw = new Stopwatch();
            sw.Start();
            //ModelQuery modelQuery = new ModelQuery(modelManager);
            dataIds = modelManager.FindRelatedDatasFromCahce(projectGuid,dataIds, source);
            sw.Stop();
            Console.WriteLine("FindRelatedDatasFromCahce used {0} Milliseconds.", sw.ElapsedMilliseconds);

            sw.Restart();
            var hashids = new HashSet<long>(dataIds);

            var relatedList = CacheHelper.FromCacheRelatedSearchDto(projectGuid, hashids);
            var relationshipsIds = relatedList.Select(r => r.InstanceId).Distinct().ToList();

            sw.Stop();
            Console.WriteLine("FromCacheRelatedSearchDto used {0} Milliseconds.", sw.ElapsedMilliseconds);

            //modify by asdf 2018-07-13 relationshipsIds 没有数据会造成获取缓存中所有数据
            if (relationshipsIds != null && relationshipsIds.Any())
            {
                sw.Restart();

                var relationshipsFromCache = CacheHelper.FromCacheRelationShip(projectGuid, relationshipsIds);
                sw.Stop();
                Console.WriteLine("FromCacheRelationShip used {0} Milliseconds.", sw.ElapsedMilliseconds);

                sw.Restart();
                relationships = relationshipsFromCache.
                                                Where(rs => (rs.Type == RelationshipType.Contain
                                                                || rs.Type == RelationshipType.Assemble
                                                                || rs.Type == RelationshipType.Dependence
                                                                || rs.Type == RelationshipType.Reference
                                                            )
                                                            &&
                                                            (hashids.Contains(rs.SourceID) && hashids.Contains(rs.TargetID))
                                                    ).ToList();

                sw.Stop();
                Console.WriteLine("relationshipsFromCache.Where used {0} Milliseconds.", sw.ElapsedMilliseconds);

            }
            datas = CacheHelper.FromCacheModelData(projectGuid, hashids).ToList();
            return true;
        }

    }
}
