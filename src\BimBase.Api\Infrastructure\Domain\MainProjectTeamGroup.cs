﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    [XmlRoot(Namespace = "")]
    public class MainProjectTeamGroup
    {
        [Key]
        public int Id { get; set; }
        public Guid MainProjectId { get; set; }
        public Guid ObjectId { get; set; }
        public string GroupOrTeamMemberId { get; set; }

        /// <summary>
        /// 标记是用户组还是用户 0：用户组 1：用户
        /// </summary>
        public int MemberType { get; set; }
    }
}
