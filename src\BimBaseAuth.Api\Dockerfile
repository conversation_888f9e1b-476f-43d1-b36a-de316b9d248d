#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
#FROM mcr.microsoft.com/dotnet/sdk:5.0-alpine AS build
WORKDIR /src
COPY ["src/BimBaseAuth.Api/BimBaseAuth.Api.csproj", "src/BimBaseAuth.Api/"]
# Work around for broken dotnet restore
ADD http://ftp.us.debian.org/debian/pool/main/c/ca-certificates/ca-certificates_20250419_all.deb .
RUN dpkg -i ca-certificates_20250419_all.deb

RUN apt-get update && apt-get install -y \
	ca-certificates \
	&& update-ca-certificates \
	&& rm -rf /var/lib/apt/lists/*

RUN dotnet restore "src/BimBaseAuth.Api/BimBaseAuth.Api.csproj"
COPY . .
WORKDIR "/src/src/BimBaseAuth.Api"
RUN dotnet build "BimBaseAuth.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "BimBaseAuth.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .



#RUN apt-get -q update && apt-get -qy install netcat && apt-get install -y curl && apt-get install -y wget && apt-get clean
#ADD https://raw.githubusercontent.com/vishnubob/wait-for-it/master/wait-for-it.sh .
ADD https://cdn.jsdelivr.net/gh/vishnubob/wait-for-it@master/wait-for-it.sh .
RUN chmod +x wait-for-it.sh


#RUN GRPC_HEALTH_PROBE_VERSION=v0.3.6 && \
    #wget -qO/bin/grpc_health_probe https://github.com/grpc-ecosystem/grpc-health-probe/releases/download/${GRPC_HEALTH_PROBE_VERSION}/grpc_health_probe-linux-amd64 && \
    #chmod +x /bin/grpc_health_probe


#RUN echo $(ls -R)

#ENTRYPOINT ["./wait-for-it.sh"]
#CMD ["dotnet", "BimBaseAuth.Api.dll"]
ENTRYPOINT ["dotnet", "BimBaseAuth.Api.dll"]