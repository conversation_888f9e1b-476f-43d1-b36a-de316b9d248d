﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// 版本发布信息
    /// </summary>
    public class ReleaseInformation
    {
        public ReleaseInformation()
        {

        }
        public int ID { get; set; }

        public int Domain { get; set; }

        public ReleaseType Type { get; set; }

        public String Description { get; set; }
        //virtual public List<VersionData> ReleasedVersions { get; set; }
    }
}
