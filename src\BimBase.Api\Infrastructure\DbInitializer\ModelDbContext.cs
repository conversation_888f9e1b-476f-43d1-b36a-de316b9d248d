using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class ModelDbContext : DbContext
    {
        private string connectionString;
        public ModelDbContext(string nameOrConnectionString)
        {
            connectionString = nameOrConnectionString;
        }

        public ModelDbContext(DbContextOptions<ModelDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if(!optionsBuilder.IsConfigured)
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            optionsBuilder.LogTo(Console.WriteLine, LogLevel.Warning, DbContextLoggerOptions.DefaultWithLocalTime | DbContextLoggerOptions.SingleLine)
                //.EnableSensitiveDataLogging()
                .EnableDetailedErrors();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<HistoryData>(b =>
            {
                // 创建一个单列索引，对应于Blog的Url属性
                b.HasIndex(p => p.InstanceId);
                b.HasIndex(p => p.VersionNo);
                b.HasIndex(p => p.Domain);
            });
            modelBuilder.Entity<LockedComponents>()
                .HasIndex(u => new { u.InstanceId, u.LockUserId })
                .IsUnique();
            modelBuilder.Entity<ModelData>(b =>
            {
                // ModelDatas表的索引配置
                b.HasIndex(p => p.Domain)
                    .HasDatabaseName("IX_ModelDatas_Domain");
                    
                b.HasIndex(p => p.InstanceId)
                    .HasDatabaseName("IX_ModelDatas_InstanceId");
                    
                b.HasIndex(p => p.LockUserID)
                    .HasDatabaseName("IX_ModelDatas_LockUserID");
                    
                b.HasIndex(p => p.VersionNo)
                    .HasDatabaseName("IX_ModelDatas_VersionNo");
                    
                b.HasIndex(p => p.DomainClassName)
                    .HasDatabaseName("idx_domainclassname");
                    
                b.HasIndex(p => new { p.InstanceId, p.VersionNo })
                    .HasDatabaseName("idx_instance_version");
            });
            modelBuilder.Entity<ModelFile>()
                .HasIndex(u => new { u.FileName, u.VersionNo })
                .IsUnique();
            modelBuilder.Entity<ModelDomain.RepositoryInformation>()
        .HasOne(r => r.Owner)          // 导航属性 
        .WithMany()                    // 若ProjectMember无反向导航可省略参数 
        .HasForeignKey("Owner_ID")      // 自定义外键列名 
        .HasConstraintName("FK_Repository_Owner"); // 可选：约束名 
            modelBuilder.Entity<CheckInRequestRecord>().ToTable("checkinrequestrecords");

            // ModelDataVersionTracker 表的索引配置
            modelBuilder.Entity<ModelDataVersionTracker>(b =>
            {
                // 主键配置
                b.HasKey(p => p.Id);
                b.Property(p => p.Id)
                    .ValueGeneratedOnAdd()
                    .HasComment("追踪记录ID");
                
                // 字段配置和注释
                b.Property(p => p.DataId)
                    .IsRequired()
                    .HasComment("原表自增ID");
                    
                b.Property(p => p.InstanceId)
                    .IsRequired()
                    .HasComment("实例标识");
                    
                b.Property(p => p.MaxVersion)
                    .IsRequired()
                    .HasComment("当前实例最大版本号");
                    
                b.Property(p => p.CreateAt)
                    .IsRequired()
                    .HasColumnType("TIMESTAMP")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP()")
                    .HasComment("创建时间");
                
                // 唯一索引配置
                b.HasIndex(p => p.InstanceId)
                    .IsUnique()
                    .HasDatabaseName("uk_instance");
                
                // 普通索引配置
                b.HasIndex(p => p.DataId)
                    .HasDatabaseName("idx_data");
                    
                b.HasIndex(p => p.MaxVersion)
                    .HasDatabaseName("idx_version");
                
                // 表级配置
                b.ToTable("modeldatas_version_tracker");
            });

            // 其他配置...
            base.OnModelCreating(modelBuilder);
        }

        #region 版本管理相关表
        
        /// <summary>
        /// 数据库版本管理表
        /// </summary>
        public DbSet<ModelDomain.DatabaseVersion> DatabaseVersions { get; set; }
        
        #endregion

        #region 原有数据表
        
        //public DbSet<WorkSet> WorkSets { get; set; }
        //public DbSet<WorkGroup> WorkGroups { get; set; }
        public DbSet<ProjectMember> ProjectMembers { get; set; }
        public DbSet<ModelData> ModelDatas { get; set; }
        public DbSet<Relationship> Relationships { get; set; }

        public DbSet<VersionData> VersionDatas { get; set; }
        public DbSet<HistoryData> HistoryDatas { get; set; }
        public DbSet<HistoryRelationship> HistoryRelationships { get; set; }
        public DbSet<ReleaseInformation> ReleaseInformations { get; set; }

        public DbSet<ModelDomain.RepositoryInformation> RepositoryInformations { get; set; }

        //public DbSet<Requirement> DesignRequirements { get; set; }

        //public DbSet<OfflineMapToken> OfflineMapTokens { get; set; }

        //public DbSet<DomainRelationship> DomainRelationships { get; set; }

        public DbSet<VersionDomain> VersionDomains { get; set; }

        //public DbSet<VersionDependence> VersionDependences { get; set; }

        //public DbSet<VersionReleated> VersionReleateds { get; set; }


        //public DbSet<ChangesetFeedback> ChangesetFeedbacks { get; set; }

        //public DbSet<ProjectImodel> ProjectImodels { get; set; }

        public DbSet<ModelDomain.SchemaVersion> SchemaVersions { get; set; }

        public DbSet<StoreyLock> StoreyLocks { get; set; }
        public DbSet<ModelFile> ModelFiles { get; set; }

        public DbSet<MilestoneFiles> MilestoneFiles { get; set; }

        public DbSet<ModelLock> ModelLocks { get; set; }

        //public DbSet<ResourceLock> ResoucLocks { get; set; }

        public DbSet<SingletonClass> SingletonClasses { get; set; }

        public DbSet<ResourceClass> ResourceClasses { get; set; }

        
        //public DbSet<DiskDataRecord> DiskDataRecords { get; set; }
        /// <summary>
        /// add by asdf 2018-06-06
        /// </summary>
        public DbSet<LockedComponents> LockedComponents { get; set; }
        /// <summary>
        /// add by asdf 2018-07-17
        /// </summary>
        public DbSet<CheckInRequestRecord> CheckInRequestRecord { get; set; }

        //public DbSet<ProjectMessage> ProjectMessages { get; set; }

        /// <summary>
        /// 模型数据域类名统计表
        /// </summary>
        public DbSet<ModelDatasDomainClassSummary> ModelDatasDomainClassSummaries { get; set; }

        public DbSet<ModelDataVersionTracker> ModelDataVersionTrackers { get; set; }
        
        #endregion
    }
} 