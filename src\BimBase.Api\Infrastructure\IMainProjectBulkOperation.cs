﻿using BimBase.Api.Infrastructure.MainDomain;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System;
using BimBase.Api.Infrastructure.ModelDomain;

namespace BimBase.Api.Infrastructure
{
    public interface IMainProjectBulkOperation
    {
        void DeleteBPExObjects(DbContext context, List<Guid> deleteGuidList);
        void SaveBPExObjects(DbContext context, List<BPExObject> bpExObjects);

        void SaveBPExObjectPublishs(DbContext context, List<BPExObjectPublish> bpExObjectPublishs, int mainVersion, int subVersion);
        void SaveBPExObjectPublishsWithVersionName(DbContext context, List<BPExObjectPublish> recordDatas, string mainverName, string subverName);
        void DeleteBPExObjectPublishsWithVersionName(DbContext context, List<BPExObjectPublish> delDatas, string mainVersionName, string subVersionName);
        void SaveMPProjectTreeNodes(DbContext context, List<MPProjectTreeNode> addDatas);

        void DeleteMPProjectTreeNodes(DbContext context, List<MPProjectTreeNode> delDatas);

        void SaveTreeNodes(DbContext m_db, List<MPCatalogTreeNode> recordDatas, int version);

        void ModifyTreeNodes(DbContext m_db, List<MPCatalogTreeNode> recordDatas, int version);

        void DeleteTreeNodes(DbContext m_db, List<MPCatalogTreeNode> delDatas, Guid libId);

        void SaveAddMPTreeNodeDatasToFile(List<MPProjectTreeNode> recordDatas, long requestId);
        void SaveAddCataLogTreeNodeToFile(List<MPCatalogTreeNode> recordDatas, long requestId, int version);

        void SaveModifyCataLogTreeNodeToFile(DbContext context, List<MPCatalogTreeNode> recordDatas, long requestId, int version);

        void SaveDeleteCataLogTreeNodeIdToFile(List<MPCatalogTreeNode> deltreeDatas,long requestId);

        void SaveDeleteMPLibDataIdToFile(List<MPLibraryData> deltreeDatas, long requestId);

        void SaveMPLibDataHistoryToFile(DbContext context, List<MPLibraryData> addLibDatas, List<MPLibraryData> modifyLibDatas, List<MPLibraryData> delLibDatas,int versionNo,long requestId);

        void SaveAddMPLibDataToFile(List<MPLibraryData> recordDatas, long requestId, int version);

        void SaveModifyMPLibDataToFile(DbContext context, List<MPLibraryData> recordDatas, long requestId, int version);
        void SaveMPTreeNodeToDB(DbContext context, long requestId);
        void SaveMPLibDataToDB(DbContext context, long requestId);
        void SaveMPLibDataHistoryToDB(DbContext context, long requestId);

        void SaveMPCataLogTreeNodeToDB(DbContext context, long requestId);

        void SaveDeleteMPTreeNodeIdToFile(List<MPProjectTreeNode> recordDatas, long requestId);
        void SaveLibDatas(DbContext m_db, List<MPLibraryData> recordDatas, int version);
        void ModifyLibDatas(DbContext m_db, List<MPLibraryData> recordDatas, int version);
        void DeleteLibDatas(DbContext m_db, List<MPLibraryData> delDatas, Guid libId);
        void SaveHistoryLibData(DbContext context, List<MPLibraryData> addDatas, List<MPLibraryData> modifyDatas, List<MPLibraryData> delDatas, int? version);
    }
}
