# Windows Server 2019 BIMBase 性能优化指南

## 🚀 立即执行的排查步骤

### 1. 运行Windows性能排查脚本
```powershell
.\windows_performance_check.ps1
```

### 2. 重点关注指标
- **CPU使用率** > 80% 需要关注
- **内存使用率**：虽然有70G剩余，但要看具体分配
- **磁盘I/O**：是否存在瓶颈
- **MySQL进程资源占用**
- **数据库连接数和慢查询**

## 🔧 数据库层面优化（MySQL on Windows）

### A. MySQL配置文件优化

1. **找到MySQL配置文件位置**：
   ```powershell
   # 通常在以下位置之一：
   # C:\ProgramData\MySQL\MySQL Server 8.0\my.ini
   # C:\Program Files\MySQL\MySQL Server 8.0\my.ini
   ```

2. **备份当前配置**：
   ```powershell
   Copy-Item "C:\ProgramData\MySQL\MySQL Server 8.0\my.ini" "C:\ProgramData\MySQL\MySQL Server 8.0\my.ini.backup"
   ```

3. **应用优化配置**：
   ```powershell
   # 使用我们提供的 my.ini.optimized 替换现有配置
   Copy-Item "my.ini.optimized" "C:\ProgramData\MySQL\MySQL Server 8.0\my.ini"
   ```

4. **重启MySQL服务**：
   ```powershell
   Restart-Service -Name "MySQL80"  # 服务名可能不同，请根据实际情况调整
   # 或者使用：
   net stop MySQL80
   net start MySQL80
   ```

### B. 检查MySQL服务配置

```powershell
# 检查MySQL服务状态
Get-Service | Where-Object {$_.Name -like "*mysql*"}

# 查看MySQL进程详情
Get-Process | Where-Object {$_.ProcessName -like "*mysql*"} | Format-List *
```

### C. 数据库连接池优化

修改您的应用程序配置文件中的连接字符串：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "server=localhost;port=3306;database=pkpm-pbimserver-teamdb;user=root;password=************;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=20;Max Pool Size=200;Pooling=true;Connection Lifetime=600;Connection Reset=true;SslMode=None;",
    "LoggerConnection": "server=localhost;port=3306;database=bimbase-logdb;user=root;password=************;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=10;Max Pool Size=100;Pooling=true;Connection Lifetime=600;Connection Reset=true;SslMode=None;",
    "LibraryConnection": "server=localhost;port=3306;database=pkpm-pbimserver-librarydb;user=root;password=************;Persist Security Info=True;AllowLoadLocalInfile=true;Connect Timeout=300;Min Pool Size=10;Max Pool Size=100;Pooling=true;Connection Lifetime=600;Connection Reset=true;SslMode=None;"
  }
}
```

## 📊 应用层面优化

### A. .NET Core 应用优化

1. **添加环境变量**（在Windows服务或IIS中配置）：
   ```
   DOTNET_gcServer=1
   DOTNET_gcConcurrent=1
   DOTNET_GCHeapCount=8
   ASPNETCORE_ENVIRONMENT=Production
   ```

2. **IIS配置优化**（如果使用IIS）：
   ```xml
   <!-- web.config -->
   <configuration>
     <system.webServer>
       <aspNetCore processPath="dotnet" 
                   arguments=".\BimBase.Api.dll" 
                   stdoutLogEnabled="true" 
                   stdoutLogFile=".\logs\stdout" 
                   hostingModel="InProcess" />
       <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
         <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
         <dynamicTypes>
           <add mimeType="text/*" enabled="true" />
           <add mimeType="message/*" enabled="true" />
           <add mimeType="application/javascript" enabled="true" />
           <add mimeType="application/json" enabled="true" />
         </dynamicTypes>
       </httpCompression>
     </system.webServer>
   </configuration>
   ```

3. **Windows服务优化**（如果作为Windows服务运行）：
   ```powershell
   # 设置服务优先级
   sc config "你的服务名" start= auto
   sc config "你的服务名" type= own
   ```

### B. Redis配置（如果使用Redis）

1. **下载Windows版Redis**：
   - 从 GitHub 下载 Redis for Windows
   - 或使用 WSL2 运行 Linux 版本

2. **Redis配置文件优化**：
   ```
   # redis.conf
   maxmemory 4gb
   maxmemory-policy allkeys-lru
   tcp-keepalive 60
   timeout 300
   save 900 1
   save 300 10
   save 60 10000
   ```

## 🏗️ Windows Server 系统层面优化

### A. 虚拟内存设置

```powershell
# 检查当前虚拟内存设置
Get-WmiObject -Class Win32_PageFileUsage

# 建议设置虚拟内存为物理内存的1.5倍（如果您有足够的磁盘空间）
```

### B. 网络优化

```powershell
# 优化TCP窗口大小
netsh int tcp set global autotuninglevel=normal

# 启用TCP Chimney
netsh int tcp set global chimney=enabled

# 启用接收端缩放
netsh int tcp set global rss=enabled
```

### C. 磁盘I/O优化

1. **检查磁盘性能**：
   ```powershell
   # 使用性能监视器检查磁盘队列长度
   Get-Counter "\PhysicalDisk(*)\Current Disk Queue Length"
   ```

2. **如果是SSD**，确保启用TRIM：
   ```powershell
   fsutil behavior query DisableDeleteNotify
   # 应该返回0，如果返回1则执行：
   fsutil behavior set DisableDeleteNotify 0
   ```

### D. Windows服务优化

```powershell
# 禁用不必要的Windows服务（谨慎操作）
$servicesToDisable = @(
    "Fax",
    "WSearch"  # Windows Search (如果不需要)
)

foreach ($service in $servicesToDisable) {
    Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
}
```

## 🔍 性能监控工具

### A. 使用Windows性能监视器

1. **创建数据收集器集**：
   ```powershell
   # 启动性能监视器
   perfmon.exe
   ```

2. **监控关键计数器**：
   - `\Processor(_Total)\% Processor Time`
   - `\Memory\Available MBytes`
   - `\PhysicalDisk(_Total)\% Disk Time`
   - `\Process(mysql*)\Working Set`
   - `\Process(dotnet*)\Working Set`

### B. PowerShell监控脚本

```powershell
# 持续监控脚本
while ($true) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $cpu = Get-Counter "\Processor(_Total)\% Processor Time" | ForEach-Object {$_.CounterSamples.CookedValue}
    $memory = Get-Counter "\Memory\Available MBytes" | ForEach-Object {$_.CounterSamples.CookedValue}
    
    Write-Host "$timestamp - CPU: $([math]::Round($cpu, 2))%, Available Memory: $([math]::Round($memory, 0)) MB"
    
    Start-Sleep -Seconds 10
}
```

## 📈 数据库查询优化

### A. 慢查询分析

```sql
-- 检查慢查询日志
SHOW VARIABLES LIKE 'slow_query_log%';

-- 查看当前正在执行的查询
SHOW PROCESSLIST;

-- 检查最耗时的查询
SELECT 
    digest_text as query,
    count_star as exec_count,
    avg_timer_wait/1000000000 as avg_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
ORDER BY avg_timer_wait DESC 
LIMIT 10;
```

### B. 索引优化

```sql
-- 检查缺少索引的表
SELECT 
    object_schema,
    object_name,
    index_name,
    count_read,
    count_write,
    count_fetch,
    count_insert,
    count_update,
    count_delete
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE index_name IS NULL 
ORDER BY count_read DESC;
```

## 🚨 告警和监控

### A. Windows事件日志监控

```powershell
# 创建自定义事件日志监控
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\check_performance.ps1"
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5)
Register-ScheduledTask -TaskName "BIMBase性能监控" -Action $action -Trigger $trigger
```

### B. MySQL监控

```sql
-- 创建性能监控视图
CREATE VIEW performance_summary AS
SELECT 
    'Current Connections' as metric,
    variable_value as value
FROM performance_schema.global_status 
WHERE variable_name = 'Threads_connected'
UNION ALL
SELECT 
    'Max Connections',
    variable_value
FROM performance_schema.global_variables 
WHERE variable_name = 'max_connections';
```

## 📋 执行清单

### 立即执行（低风险）
- [ ] 运行 `windows_performance_check.ps1` 排查脚本
- [ ] 查看MySQL慢查询日志
- [ ] 检查应用程序事件日志
- [ ] 监控资源使用情况

### 需要测试环境验证（中等风险）
- [ ] 更新MySQL配置文件
- [ ] 优化数据库连接字符串
- [ ] 调整应用程序配置

### 需要计划停机（高风险）
- [ ] 重启MySQL服务应用新配置
- [ ] 重启应用程序服务
- [ ] 系统级优化设置

## ⚠️ 注意事项

1. **分阶段执行**：先运行排查脚本，根据结果有针对性地优化
2. **备份配置**：修改任何配置文件前都要备份
3. **测试环境**：重要的配置变更先在测试环境验证
4. **监控变化**：每次优化后持续监控性能指标
5. **回滚准备**：准备快速回滚到之前配置的方案

执行完排查脚本后，请将结果发给我，我会根据具体情况提供更精准的优化建议。 