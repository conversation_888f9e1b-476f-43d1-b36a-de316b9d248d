using System;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// gRPC上下文访问器，便于业务代码访问当前请求上下文
    /// </summary>
    public static class GrpcContextAccessor
    {
        /// <summary>
        /// 获取当前请求上下文
        /// </summary>
        /// <returns>请求上下文</returns>
        public static RequestContext GetContext()
        {
            return RequestContext.Current ?? 
                throw new InvalidOperationException("当前不在gRPC请求上下文中，无法访问RequestContext");
        }
        
        /// <summary>
        /// 尝试获取当前请求上下文
        /// </summary>
        /// <param name="context">获取到的上下文</param>
        /// <returns>是否成功获取</returns>
        public static bool TryGetContext(out RequestContext context)
        {
            context = RequestContext.Current;
            return context != null;
        }
        
        /// <summary>
        /// 获取当前请求ID
        /// </summary>
        /// <returns>请求ID</returns>
        public static string GetRequestId()
        {
            return RequestContext.Current?.RequestId;
        }
        
        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        public static string GetUserId()
        {
            return RequestContext.Current?.UserId;
        }
        
        /// <summary>
        /// 获取当前用户名
        /// </summary>
        /// <returns>用户名</returns>
        public static string GetUserName()
        {
            return RequestContext.Current?.UserName;
        }
        
        /// <summary>
        /// 获取当前会话ID
        /// </summary>
        /// <returns>会话ID</returns>
        public static string GetSessionId()
        {
            return RequestContext.Current?.SessionId;
        }
        
        /// <summary>
        /// 获取当前客户端ID
        /// </summary>
        /// <returns>客户端ID</returns>
        public static string GetClientId()
        {
            return RequestContext.Current?.ClientId;
        }
        
        /// <summary>
        /// 获取当前客户端版本
        /// </summary>
        /// <returns>客户端版本</returns>
        public static string GetClientVersion()
        {
            return RequestContext.Current?.ClientVersion;
        }
        
        /// <summary>
        /// 获取当前客户端IP
        /// </summary>
        /// <returns>客户端IP</returns>
        public static string GetClientIp()
        {
            return RequestContext.Current?.ClientIp;
        }
        
        /// <summary>
        /// 获取请求开始时间
        /// </summary>
        /// <returns>请求开始时间</returns>
        public static DateTime? GetStartTime()
        {
            return RequestContext.Current?.StartTime;
        }
        
        /// <summary>
        /// 获取请求方法名
        /// </summary>
        /// <returns>请求方法名</returns>
        public static string GetMethodName()
        {
            return RequestContext.Current?.MethodName;
        }
        
        /// <summary>
        /// 获取当前请求已经过去的毫秒数
        /// </summary>
        /// <returns>毫秒数</returns>
        public static long GetElapsedMilliseconds()
        {
            if (RequestContext.Current?.StartTime == null)
                return 0;
                
            return (long)(DateTime.Now - RequestContext.Current.StartTime).TotalMilliseconds;
        }
        
        /// <summary>
        /// 从请求上下文中获取数据项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>数据值</returns>
        public static T GetItem<T>(string key)
        {
            if (RequestContext.Current == null)
                return default;
                
            return RequestContext.Current.GetItem<T>(key);
        }
        
        /// <summary>
        /// 向请求上下文中设置数据项
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public static void SetItem<T>(string key, T value)
        {
            RequestContext.Current?.SetItem(key, value);
        }
    }
} 