using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.Domain
{
    /// <summary>
    /// 客户端版本兼容性配置表
    /// </summary>
    [Table("clientversioncompatibility")]
    public class ClientVersionCompatibility
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int ID { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [StringLength(40)]
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端版本
        /// </summary>
        [StringLength(40)]
        public string ClientVersion { get; set; }

        /// <summary>
        /// 可兼容的最小版本
        /// </summary>
        [StringLength(40)]
        public string MinCompatibleVersion { get; set; }
    }
} 