﻿using AutoMapper;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using Grpc.Core;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static BimBase.Api.Protos.GrpcProjectManagement;

namespace BimBase.Api.Grpc
{
    public class ProjectManagementService: GrpcProjectManagementBase
    {
        private readonly ITeamRepository _teamRepository;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        private readonly ILogger<ProjectManagementService> _logger;
        public ProjectManagementService(ITeamRepository teamRepository
            , IMapper mapper
            , IAuthorityManager authorityManager
            , ILogger<ProjectManagementService> logger)
        {
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _authorityManager = authorityManager ?? throw new ArgumentNullException(nameof(authorityManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }


        public override async Task<AddMembersResponse> AddMembers(AddMembersRequest request, ServerCallContext context)
        {
            var x = new AddMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.RoleGuid) || !Guid.TryParse(request.RoleGuid, out var roleId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (teamMember is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            TeamProject teamProject = _teamRepository.Projects.SingleOrDefault(p => p.ID == projectId);

            if (teamProject != null)
            {
                var memIDs = new List<int>();
                List<Guid> memberGuids = new List<Guid>();
                var memberNames = request.MemberNames.ToList();
                var members = _teamRepository.QueryFrom<TeamMember>(mem => memberNames.Exists(na => na == mem.LoginName));
                if (memberNames.Count() != members.Count())
                {
                    return x;
                }
                foreach (var member in members)
                {
                    var pMem = _teamRepository.AddMemberToProject(member, teamProject, false);
                    if (pMem != null)
                        memIDs.Add(pMem.ID);
                    memberGuids.Add(member.ID);
                }
                //增加默认权限
                var ret = _authorityManager.GiveRoleUser(roleId.ToString(), memberGuids, teamMember.ID, out var message);
                if (ret)
                {
                    x.IsSuccess = ret;
                    x.ProjectMemId.AddRange(memIDs);
                }
            }

            return x;
        }

        public override async Task<GrpcResult> DeleteMembers(DeleteMembersRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            var tMembers = _teamRepository.Members.Where(mem => (request.MemberNames.ToList().Exists(m => m == mem.LoginName)));

            if(tMembers is null || !tMembers.Any())
            {
                return x;
            }

            var prj = _teamRepository.Projects.FirstOrDefault(tp=>tp.ID == projectId);

            if(prj != null)
            {
                var projectRepo = _teamRepository.GetProjectRepository(projectId);

                foreach (var member in tMembers)
                {
                    _authorityManager.GetRoleInfoByUserIdAndProjectId(member.ID, projectId, out var roles, out var message);
                    foreach (var r in roles)
                    {
                        _authorityManager.DeleteRoleUser(r.Id, new List<string> { member.ID.ToString() }, out message);
                    }
                    //查找删除用户锁定的构件和楼层
                    var pjMem = projectRepo.Members.FirstOrDefault(p => p.TeamMemberID == member.ID);
                    //锁定构件
                    var lockDatas = projectRepo.LockedComponents.Where(l => l.LockUserId == pjMem.ID).ToList();
                    if (lockDatas.Any())
                    {
                        var lockIds = lockDatas.Select(s => s.InstanceId).ToList();
                        projectRepo.UnlockDatasNew(pjMem, lockIds, projectId);
                        _logger.LogInformation("DeleteMembers:" + member.LoginName + ",并解锁构件：" + string.Join(",", lockIds));
                    }
                    //锁定楼层
                    var lockStoreys = projectRepo.StoreyLocks.Where(s => s.LockUserId == pjMem.ID).ToList();
                    if (lockStoreys != null && lockStoreys.Any())
                    {
                        foreach (var d in lockStoreys)
                        {
                            var oldStoreyLock = projectRepo.StoreyLocks.SingleOrDefault(s => s.Id == d.Id);
                            oldStoreyLock.LockUserId = null;
                        }
                        _logger.LogInformation("DeleteMembers:" + member.LoginName + ",并解锁专业楼层：" + string.Join(",", lockStoreys.Select(l => l.Domain)));
                    }
                    _teamRepository.RemoveMemberFromProject(member, prj);
                }
                x.IsSuccess = true;
            }
            

            return x;
        }

        public override async Task<GrpcResult> SetProjectAdmin(SetProjectAdminRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            var modifyMem = _teamRepository.Members.SingleOrDefault(m => m.LoginName == request.MemberName);
            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var pm = projectRepo.Members.SingleOrDefault(mem => mem.TeamMemberID == modifyMem.ID);
            var ret = projectRepo.SetProjectAdmin(pm, request.IsProjectAdmin);
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GrpcResult> AddRole(AddRoleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var role = _mapper.Map<BimBaseAuth.Api.Protos.GrpcRole>(request.Role);
            var ret = _authorityManager.AddProjectRole(request.ProjectId, role, currentUser.ID, out var message);
            if (ret)
            {
                var authInfos = _mapper.Map<List<BimBaseAuth.Api.Protos.GrpcAuthInfo>>(request.AuthInfoList);
                ret = _authorityManager.GiveRoleAuth(role.Id, authInfos, currentUser.ID, out message);
            }

            x.Message = message;
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GrpcResult> DeleteRole(DeleteRoleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            var ret = _authorityManager.DeleteProjectRoles(request.RoleIdList.ToList(), out var message);
            x.Message = message;
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GrpcResult> ModifyProjectRole(ModifyProjectRoleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            //删除角色授权
            var ret = _authorityManager.DeleteRoleAuth(request.RoleGuid, out var message);
            if (ret)
            {
                var authList = _mapper.Map<List<BimBaseAuth.Api.Protos.GrpcAuthInfo>>(request.AuthInfoList);
                ret = _authorityManager.GiveRoleAuth(request.RoleGuid, authList, currentUser.ID, out message);
            }
            x.IsSuccess = ret;
            x.Message = message;
            return x;
        }

        public override async Task<AddRepositoryResponse> AddRepository(ProjectManagementRequest request, ServerCallContext context)
        {
            var x = new AddRepositoryResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var proRepo = _teamRepository.GetProjectRepository(projectId);

            Func<string,Guid> GetMemberIdByLoginName = (string loginName) => _teamRepository.Members.FirstOrDefault(tm => tm.LoginName == loginName).ID;

            var mem = proRepo.GetProjectMember(GetMemberIdByLoginName,currentUser.LoginName);

            if (mem != null || currentUser.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName || currentUser.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName || currentUser.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName)
            {
                var repository = proRepo.CreateRepository(mem);
                if (repository != null)
                {
                    x.InstanceId = repository.ID;
                }
            }

            return x;
        }

        public override async Task<GrpcResult> RemoveRepository(RemoveRepositoryRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var proRepo = _teamRepository.GetProjectRepository(projectId);

            var repository = proRepo.DeleteRepository(request.RepositoryId);
            if (repository != null)
            {
                x.IsSuccess = true;
            }

            return x;
        }

        public override async Task<GrpcResult> SetMemberToRole(SetMemberToRoleRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var proRepo = _teamRepository.GetProjectRepository(projectId);

            var member = (from mem in proRepo.Members where request.MemberIds.Contains(mem.ID) select mem).ToList();
            if (member.Count > 0)
            {
                var memberGuids = member.Select(m => m.TeamMemberID).ToList();
                var ret = _authorityManager.GiveRoleUser(request.RoleGuid, memberGuids, currentUser.ID, out var message);
                x.Message = message;
                x.IsSuccess = ret;
            }

            return x;
        }

        public override async Task<GrpcResult> DeleteTeamMembers(DeleteTeamMembersRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var ret = _authorityManager.DeleteRoleUser(request.RoleGuid, request.UserIds.ToList(), out var message);
            x.IsSuccess = ret;
            x.Message = message;
            return x;
        }

        public override async Task<LoadMemberRoleResponse> LoadMemberRole(LoadMemberRoleRequest request, ServerCallContext context)
        {
            var x = new LoadMemberRoleResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.MemberGuid) || !Guid.TryParse(request.MemberGuid, out var memberGuid))
            {
                x.Message = $"参数 MemberGuid 有误！";
                return x;
            }

            var ret = _authorityManager.GetRoleInfoByUserIdAndProjectId(memberGuid, projectId, out var roles, out var  message);
            x.IsSuccess = ret;
            x.Message = message;
            x.Roles.AddRange(roles);
            return x;
        }

        public override async Task<LoadMemberPermissionResponse> LoadMemberPermission(LoadMemberPermissionRequest request, ServerCallContext context)
        {
            var x = new LoadMemberPermissionResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.MemberGuid) || !Guid.TryParse(request.MemberGuid, out var memberGuid))
            {
                x.Message = $"参数 MemberGuid 有误！";
                return x;
            }

            var ret = _authorityManager.GetProjectUserAuth(memberGuid, projectId, out var authInfos, out var message);
            x.IsSuccess = ret;
            x.Message = message;
            x.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));
            return x;
        }

        public override async Task<GrpcResult> ImportProjectRoles(ImportProjectRolesRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (!string.IsNullOrEmpty(request.TemplatePath))
            {
                List<RoleTemplate> roleTemplates = BimBaseServerTemplates.ReadTemplateFile<List<RoleTemplate>>(BimBaseServerTemplates.ProjectRoleRoot + request.TemplatePath + ".tplt");
                var retRoles = _authorityManager.GetProjectRole(request.ProjectId, out var allrRoles, out var message);

                foreach (var roleTemplate in roleTemplates)
                {
                    BimBaseAuth.Api.Protos.GrpcRole role = new BimBaseAuth.Api.Protos.GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = roleTemplate.Name,
                        Status = roleTemplate.Status,
                        Type = roleTemplate.Type
                    };
                    var hasRole = allrRoles.FindAll(m => m.Name == role.Name);
                    if (hasRole.Any())
                    {
                        continue;
                    }
                    var ret = _authorityManager.AddProjectRole(request.ProjectId, role, currentUser.ID, out message);
                    if (!ret)
                    {
                        x.Message = message;
                        return x;
                    }
                        

                    ret = _authorityManager.GiveRoleAuth(role.Id, roleTemplate.AuthInfos, currentUser.ID, out message);
                    if (!ret)
                    {
                        x.Message = message;
                        return x;
                    }
                }
                x.IsSuccess = true;
            }


            return x;
        }

        public override async Task<GrpcResult> ExportProjectRoles(ExportProjectRolesRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrEmpty(request.TemplatePath) || ! request.RoleGuids.Any())
            {
                x.Message = "输入参数错误";
                return x;
            }

            List<RoleTemplate> roleTemplates = new List<RoleTemplate>();

            var ret = _authorityManager.GetProjectRole(request.ProjectId, out var roles, out var message);
            if (!ret)
            {
                x.Message = message;
                return x;
            }

            foreach (var roleGuid in request.RoleGuids)
            {
                var role = roles.SingleOrDefault(r => r.Id == roleGuid);
                if (null == role)
                {
                    x.Message = "无法找到需要导出的角色";
                    return x;
                }

                ret = _authorityManager.GetRoleAuth(roleGuid, request.ProjectId, out var authInfos, out message);
                if (!ret)
                {
                    x.Message = message;
                    return x;
                }

                RoleTemplate template = new RoleTemplate
                {
                    Id = Guid.Parse(roleGuid),
                    Name = role.Name,
                    Status = role.Status,
                    Type = role.Type,
                    AuthInfos = authInfos
                };

                roleTemplates.Add(template);
            }

            x.IsSuccess = BimBaseServerTemplates.SaveTemplateFile(roleTemplates, BimBaseServerTemplates.ProjectRoleRoot + request.TemplatePath + ".tplt");
            return x;
        }

        public override async Task<GrpcResult> ImportProjectMembers(ImportProjectMembersRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrEmpty(request.TemplatePath))
            {
                x.Message = "输入参数错误";
                return x;
            }
            var project = GetTeamProject(projectId);
            if (project != null)
            {
                List<TeamMember> members = BimBaseServerTemplates.ReadTemplateFile<List<TeamMember>>(BimBaseServerTemplates.ProjectMemberRoot + request.TemplatePath + ".tplt");
                var memList = _teamRepository.Members.ToList().FindAll(mem => (members.Exists(member => member.LoginName == mem.LoginName)));
                foreach (TeamMember member in memList)
                {
                    _teamRepository.AddMemberToProject(member, project, false);
                }

                x.IsSuccess = true;
            }
            return x;
        }

        private TeamProject GetTeamProject(Guid projectGuid)
        {
            return _teamRepository.Projects.SingleOrDefault(p => p.ID == projectGuid);
        }


        public override async Task<GrpcResult> ExportProjectMembers(ExportProjectMembersRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (request.LoginNames is null || !request.LoginNames.Any())
            {
                x.Message = $"参数 LoginNames 不能为空！";
                return x;
            }

            if (string.IsNullOrEmpty(request.TemplatePath))
            {
                x.Message = "输入参数错误";
                return x;
            }

            var members = _teamRepository.Members.ToList().FindAll(mem => (request.LoginNames.ToList().Exists(m => m == mem.LoginName)));
            var ret = BimBaseServerTemplates.SaveTemplateFile(members,BimBaseServerTemplates.ProjectMemberRoot + request.TemplatePath + ".tplt");
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GetTemplateProjectMembersResponse> GetTemplateProjectMembers(GetTemplateProjectMembersRequest request, ServerCallContext context)
        {
            var x = new GetTemplateProjectMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            if (string.IsNullOrEmpty(request.TemplatePath))
            {
                x.Message = "输入参数错误";
                return x;
            }
            var project = GetTeamProject(projectId);
            if (project != null)
            {
                List<TeamMember> members = BimBaseServerTemplates.ReadTemplateFile<List<TeamMember>>(BimBaseServerTemplates.ProjectMemberRoot + request.TemplatePath + ".tplt");
                x.IsSuccess = true;
                x.TeamMembers.AddRange(_mapper.Map<List<GrpcTeamMember>>(members));
            }

            return x;
        }

        public override async Task<ExportMembersByProjectIDResponse> ExportMembersByProjectID(ProjectManagementRequest request, ServerCallContext context)
        {
            var x = new ExportMembersByProjectIDResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var memberAndRoles = new GrpcTeamMemberAndRolesTemplate();
            var projectRepo = _teamRepository.GetProjectRepository(projectId);

            #region 项目成员
            var members = projectRepo.Members.ToList(); //GetProjectMembers(projectManager);

            string message = string.Empty;
            foreach (var mem in members)
            {
                //项目成员
                TeamMember teamMember = _teamRepository.Members.SingleOrDefault(memT => memT.ID == mem.TeamMemberID);
                if (teamMember.LoginName != "system")
                {
                     var teamMemberTemplate = new GrpcTeamMemberTemplate
                     {
                        Id = teamMember.ID.ToString(),
                        LoginName = teamMember.LoginName,
                        DisplayName = teamMember.DisplayName,
                        Color = teamMember.Color,
                        Avatar = teamMember.Avatar,
                    };
                    var ret = _authorityManager.GetRoleInfoByUserIdAndProjectId(teamMember.ID, projectId, out var roles, out message);
                    if (!ret)
                    {
                        x.Message = message;
                        return x;
                    }
                    foreach (var role in roles)
                    {
                        ret = _authorityManager.GetRoleAuth(role.Id, request.ProjectId, out var authInfos, out message);
                        if (!ret)
                            return null;

                        var template = new GrpcRoleTemplate
                        {
                            Id = role.Id,
                            Name = role.Name,
                            Status = role.Status,
                            Type = role.Type,
                        };
                        template.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));

                        teamMemberTemplate.RoleTemplates.Add(template);
                    }
                    memberAndRoles.MembersJoinRoles.Add(teamMemberTemplate);
                }
            }

            #endregion

            #region 项目下的角色列表
            //项目下的角色列表
            List<RoleTemplate> roleTemplates = new List<RoleTemplate>();
            var retRoles = _authorityManager.GetProjectRole(request.ProjectId, out var allrRoles, out message);
            if (!retRoles)
            {
                x.Message = message;
                return x;
            }
            foreach (var role in allrRoles)
            {

                retRoles = _authorityManager.GetRoleAuth(role.Id, request.ProjectId, out var authInfos, out message);
                if (!retRoles)
                {
                    x.Message = message;
                    return x;
                }
                var template = new GrpcRoleTemplate
                {
                    Id = role.Id,
                    Name = role.Name,
                    Status = role.Status,
                    Type = role.Type,
                    //AuthInfos = authInfos
                };
                template.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));
                memberAndRoles.AllRoles.Add(template);
            }
            #endregion
            x.IsSuccess = true;
            x.TeamMemberAndRoles = memberAndRoles;
            return x;
        }


        public override async Task<GrpcResult> ImportMembersAndRoles(ImportMembersAndRolesRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }
            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var project = _teamRepository.Projects.SingleOrDefault(p => p.ID == projectId);
            if (project != null)
            {
                var members = request.TeamMemberAndRoles.MembersJoinRoles;
                var memList = _teamRepository.Members.ToList().FindAll(mem => (members.ToList().Exists(member => member.LoginName == mem.LoginName)));
                foreach (TeamMember member in memList)
                {
                    _teamRepository.AddMemberToProject(member, project, false);
                }
                //模板中的角色列表
                var roleTemplates = request.TeamMemberAndRoles.AllRoles;;
                var retRoles = _authorityManager.GetProjectRole(request.ProjectId, out var allrRoles, out var message);
                var newRoles = new List<BimBaseAuth.Api.Protos.GrpcRole>();
                foreach (var roleTemplate in roleTemplates)
                {
                    var role = new BimBaseAuth.Api.Protos.GrpcRole
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = roleTemplate.Name,
                        Status = roleTemplate.Status,
                        Type = roleTemplate.Type
                    };
                    //若项目中已有该角色名称则不再添加
                    var hasRole = allrRoles.FindAll(m => m.Name == role.Name);
                    if (hasRole.Any())
                    {
                        continue;
                    }
                    newRoles.Add(role);
                    //将项目中没有的角色加入项目
                    var ret = _authorityManager.AddProjectRole(request.ProjectId, role, currentUser.ID, out message);
                    if (!ret)
                    {
                        x.Message = message;
                        return x;
                    }
                    //为新加入到角色设定权限

                    var grpcAuthInfo = _mapper.Map<List<BimBaseAuth.Api.Protos.GrpcAuthInfo>>(roleTemplate.AuthInfos);
                    ret = _authorityManager.GiveRoleAuth(role.Id, grpcAuthInfo, currentUser.ID, out message);
                    if (!ret)
                    {
                        x.Message = message;
                        return x;
                    }
                }
                List<Guid> memberGuids = new List<Guid>();
                List<Guid> oldMemberGuids;
                foreach (var m in members)
                {
                    var tmGuidId = Guid.Parse(m.Id);
                    foreach (var newRole in newRoles)
                    {
                        memberGuids.Clear();
                        if (m.RoleTemplates.First().Name == newRole.Name)
                        {
                            memberGuids.Add(tmGuidId);
                            //为新增加的角色添加用户（模板中的该角色下的用户）
                            var ret = _authorityManager.GiveRoleUser(newRole.Id, memberGuids.Distinct().ToList(), currentUser.ID, out message);
                        }
                    }
                    if (allrRoles.Count > 0)
                    {
                        foreach (var oldRole in allrRoles)
                        {
                            var ret = _authorityManager.GetRoleUsers(oldRole.Id, out oldMemberGuids, out message);
                            memberGuids.Clear();
                            if (oldMemberGuids.Any())
                            {
                                //项目中已有角色中增加模板中相比项目原有用户新增的该角色下的用户
                                if (m.RoleTemplates != null && m.RoleTemplates.First().Name == oldRole.Name && !oldMemberGuids.Contains(tmGuidId))
                                {
                                    memberGuids.Add(tmGuidId);
                                    ret = _authorityManager.GiveRoleUser(oldRole.Id, memberGuids.Distinct().ToList(), currentUser.ID, out message);
                                }
                            }
                            else
                            {
                                if (m.RoleTemplates != null && m.RoleTemplates.First().Name == oldRole.Name)
                                {
                                    memberGuids.Add(tmGuidId);
                                    ret = _authorityManager.GiveRoleUser(oldRole.Id, memberGuids.Distinct().ToList(), currentUser.ID, out message);
                                }
                            }

                        }
                    }
                }
                x.IsSuccess = true;
            }


            return x;
        }


    }
}
