using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.Grpc;
using BimBase.Api.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Grpc
{
    /// <summary>
    /// 客户端模块版本管理服务
    /// </summary>
    public class ClientModuleVersionManagementService : GrpcClientModuleVersionManagement.GrpcClientModuleVersionManagementBase
    {
        private readonly TeamDbContext _teamDbContext;
        private readonly ILogger<ClientModuleVersionManagementService> _logger;

        public ClientModuleVersionManagementService(
            TeamDbContext teamDbContext,
            ILogger<ClientModuleVersionManagementService> logger)
        {
            _teamDbContext = teamDbContext;
            _logger = logger;
        }

        /// <summary>
        /// 批量覆盖模块版本
        /// </summary>
        public override async Task<BatchOverwriteClientModuleVersionsResponse> BatchOverwriteClientModuleVersions(
            BatchOverwriteClientModuleVersionsRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new BatchOverwriteClientModuleVersionsResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数",
                        AffectedCount = 0
                    };
                }

                // 检查items是否为空
                if (request.Items == null || request.Items.Count == 0)
                {
                    return new BatchOverwriteClientModuleVersionsResponse
                    {
                        IsSuccess = false,
                        Message = "请求项目列表为空，请指定要操作的项目",
                        AffectedCount = 0
                    };
                }

                // 检查第一个项目的ProjectId是否为空
                var projectId = request.Items[0].ProjectId;
                if (string.IsNullOrEmpty(projectId))
                {
                    return new BatchOverwriteClientModuleVersionsResponse
                    {
                        IsSuccess = false,
                        Message = "项目ID不能为空，请指定项目ID",
                        AffectedCount = 0
                    };
                }

                _logger.LogInformation($"批量覆盖模块版本，ClientId: {clientId}, ProjectId: {projectId}, 项目数量: {request.Items.Count}");

                using var transaction = await _teamDbContext.Database.BeginTransactionAsync();
                try
                {
                    // 删除该客户端指定项目的所有现有记录
                    var existingRecords = await _teamDbContext.ClientModuleVersions
                        .Where(x => x.ClientId == clientId && x.ProjectId == projectId)
                        .ToListAsync();

                    if (existingRecords.Any())
                    {
                        _teamDbContext.ClientModuleVersions.RemoveRange(existingRecords);
                    }

                    // 添加新记录
                    var now = DateTime.Now;
                    var clientIp = GrpcContextAccessor.GetClientIp();
                    var userId = GrpcContextAccessor.GetUserId();
                    var userName = GrpcContextAccessor.GetUserName();
                    
                    var newRecords = request.Items.Select(item => new ClientModuleVersion
                    {
                        ClientId = clientId,
                        ProjectId = item.ProjectId,
                        ModuleId = item.ModuleId,
                        Version = item.Version,
                        ClientIp = clientIp,
                        UserId = userId,
                        UserName = userName,
                        CreatedAt = now,
                        UpdatedAt = now
                    }).ToList();

                    await _teamDbContext.ClientModuleVersions.AddRangeAsync(newRecords);
                    var affectedCount = await _teamDbContext.SaveChangesAsync();

                    await transaction.CommitAsync();

                    _logger.LogInformation($"批量覆盖完成，影响记录数: {affectedCount}");

                    return new BatchOverwriteClientModuleVersionsResponse
                    {
                        IsSuccess = true,
                        Message = "批量覆盖成功",
                        AffectedCount = affectedCount
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量覆盖模块版本失败");
                return new BatchOverwriteClientModuleVersionsResponse
                {
                    IsSuccess = false,
                    Message = $"批量覆盖失败: {ex.Message}",
                    AffectedCount = 0
                };
            }
        }

        /// <summary>
        /// 批量更新模块版本
        /// </summary>
        public override async Task<BatchUpdateClientModuleVersionsResponse> BatchUpdateClientModuleVersions(
            BatchUpdateClientModuleVersionsRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new BatchUpdateClientModuleVersionsResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                _logger.LogInformation($"批量更新模块版本，ClientId: {clientId}, 操作数量: {request.Items.Count}");

                var clientIp = GrpcContextAccessor.GetClientIp();
                var userId = GrpcContextAccessor.GetUserId();
                var userName = GrpcContextAccessor.GetUserName();
                
                int addedCount = 0, updatedCount = 0, removedCount = 0;

                using var transaction = await _teamDbContext.Database.BeginTransactionAsync();
                try
                {
                    foreach (var item in request.Items)
                    {
                        switch (item.Operation)
                        {
                            case ClientModuleVersionOperation.Add:
                                var existingForAdd = await _teamDbContext.ClientModuleVersions
                                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                                            x.ProjectId == item.ProjectId && 
                                                            x.ModuleId == item.ModuleId);
                                if (existingForAdd == null)
                                {
                                    var newRecord = new ClientModuleVersion
                                    {
                                        ClientId = clientId,
                                        ProjectId = item.ProjectId,
                                        ModuleId = item.ModuleId,
                                        Version = item.Version,
                                        ClientIp = clientIp,
                                        UserId = userId,
                                        UserName = userName,
                                        CreatedAt = DateTime.Now,
                                        UpdatedAt = DateTime.Now
                                    };
                                    await _teamDbContext.ClientModuleVersions.AddAsync(newRecord);
                                    addedCount++;
                                }
                                break;

                            case ClientModuleVersionOperation.Update:
                                var existingForUpdate = await _teamDbContext.ClientModuleVersions
                                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                                            x.ProjectId == item.ProjectId && 
                                                            x.ModuleId == item.ModuleId);
                                if (existingForUpdate != null)
                                {
                                    existingForUpdate.Version = item.Version;
                                    existingForUpdate.ClientIp = clientIp;
                                    existingForUpdate.UserId = userId;
                                    existingForUpdate.UserName = userName;
                                    existingForUpdate.UpdatedAt = DateTime.Now;
                                    updatedCount++;
                                }
                                break;

                            case ClientModuleVersionOperation.Remove:
                                var existingForRemove = await _teamDbContext.ClientModuleVersions
                                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                                            x.ProjectId == item.ProjectId && 
                                                            x.ModuleId == item.ModuleId);
                                if (existingForRemove != null)
                                {
                                    _logger.LogInformation($"删除模块版本记录，ClientId: {clientId}, ProjectId: {item.ProjectId}, ModuleId: {item.ModuleId}, Version: {existingForRemove.Version}");
                                    _teamDbContext.ClientModuleVersions.Remove(existingForRemove);
                                    removedCount++;
                                }
                                break;
                        }
                    }

                    await _teamDbContext.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger.LogInformation($"批量更新完成，新增: {addedCount}, 更新: {updatedCount}, 删除: {removedCount}");

                    return new BatchUpdateClientModuleVersionsResponse
                    {
                        IsSuccess = true,
                        Message = "批量更新成功",
                        AddedCount = addedCount,
                        UpdatedCount = updatedCount,
                        RemovedCount = removedCount
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新模块版本失败");
                return new BatchUpdateClientModuleVersionsResponse
                {
                    IsSuccess = false,
                    Message = $"批量更新失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 新增单个模块版本
        /// </summary>
        public override async Task<AddClientModuleVersionResponse> AddClientModuleVersion(
            AddClientModuleVersionRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new AddClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                // 检查是否已存在
                var existing = await _teamDbContext.ClientModuleVersions
                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                            x.ProjectId == request.ProjectId && 
                                            x.ModuleId == request.ModuleId);

                if (existing != null)
                {
                    return new AddClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "该模块版本记录已存在"
                    };
                }

                var newRecord = new ClientModuleVersion
                {
                    ClientId = clientId,
                    ProjectId = request.ProjectId,
                    ModuleId = request.ModuleId,
                    Version = request.Version,
                    ClientIp = GrpcContextAccessor.GetClientIp(),
                    UserId = GrpcContextAccessor.GetUserId(),
                    UserName = GrpcContextAccessor.GetUserName(),
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                await _teamDbContext.ClientModuleVersions.AddAsync(newRecord);
                await _teamDbContext.SaveChangesAsync();

                _logger.LogInformation($"新增模块版本成功，ClientId: {clientId}, ProjectId: {request.ProjectId}, ModuleId: {request.ModuleId}");

                return new AddClientModuleVersionResponse
                {
                    IsSuccess = true,
                    Message = "新增成功",
                    ClientModuleVersion = ConvertToGrpcModel(newRecord)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增模块版本失败");
                return new AddClientModuleVersionResponse
                {
                    IsSuccess = false,
                    Message = $"新增失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 更新单个模块版本
        /// </summary>
        public override async Task<UpdateClientModuleVersionResponse> UpdateClientModuleVersion(
            UpdateClientModuleVersionRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new UpdateClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                var existing = await _teamDbContext.ClientModuleVersions
                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                            x.ProjectId == request.ProjectId && 
                                            x.ModuleId == request.ModuleId);

                if (existing == null)
                {
                    return new UpdateClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "未找到要更新的模块版本记录"
                    };
                }

                existing.Version = request.Version;
                existing.ClientIp = GrpcContextAccessor.GetClientIp();
                existing.UserId = GrpcContextAccessor.GetUserId();
                existing.UserName = GrpcContextAccessor.GetUserName();
                existing.UpdatedAt = DateTime.Now;

                await _teamDbContext.SaveChangesAsync();

                _logger.LogInformation($"更新模块版本成功，ClientId: {clientId}, ProjectId: {request.ProjectId}, ModuleId: {request.ModuleId}");

                return new UpdateClientModuleVersionResponse
                {
                    IsSuccess = true,
                    Message = "更新成功",
                    ClientModuleVersion = ConvertToGrpcModel(existing)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模块版本失败");
                return new UpdateClientModuleVersionResponse
                {
                    IsSuccess = false,
                    Message = $"更新失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 删除单个模块版本
        /// </summary>
        public override async Task<DeleteClientModuleVersionResponse> DeleteClientModuleVersion(
            DeleteClientModuleVersionRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new DeleteClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                var existing = await _teamDbContext.ClientModuleVersions
                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                            x.ProjectId == request.ProjectId && 
                                            x.ModuleId == request.ModuleId);

                if (existing == null)
                {
                    return new DeleteClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "未找到要删除的模块版本记录"
                    };
                }

                _logger.LogInformation($"删除模块版本记录，ClientId: {clientId}, ProjectId: {request.ProjectId}, ModuleId: {request.ModuleId}, Version: {existing.Version}");
                _teamDbContext.ClientModuleVersions.Remove(existing);
                await _teamDbContext.SaveChangesAsync();

                _logger.LogInformation($"删除模块版本成功，ClientId: {clientId}, ProjectId: {request.ProjectId}, ModuleId: {request.ModuleId}");

                return new DeleteClientModuleVersionResponse
                {
                    IsSuccess = true,
                    Message = "删除成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除模块版本失败");
                return new DeleteClientModuleVersionResponse
                {
                    IsSuccess = false,
                    Message = $"删除失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取项目的所有模块版本
        /// </summary>
        public override async Task<GetClientModuleVersionsResponse> GetClientModuleVersions(
            GetClientModuleVersionsRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new GetClientModuleVersionsResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                var records = await _teamDbContext.ClientModuleVersions
                    .Where(x => x.ClientId == clientId && x.ProjectId == request.ProjectId)
                    .OrderBy(x => x.ModuleId)
                    .ToListAsync();

                var grpcRecords = records.Select(ConvertToGrpcModel).ToList();

                _logger.LogInformation($"获取模块版本列表成功，ClientId: {clientId}, ProjectId: {request.ProjectId}, 记录数: {records.Count}");

                return new GetClientModuleVersionsResponse
                {
                    IsSuccess = true,
                    Message = "获取成功",
                    ClientModuleVersions = { grpcRecords }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取模块版本列表失败");
                return new GetClientModuleVersionsResponse
                {
                    IsSuccess = false,
                    Message = $"获取失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取单个模块版本
        /// </summary>
        public override async Task<GetClientModuleVersionResponse> GetClientModuleVersion(
            GetClientModuleVersionRequest request, ServerCallContext context)
        {
            try
            {
                var clientId = GrpcContextAccessor.GetClientId();
                if (string.IsNullOrEmpty(clientId))
                {
                    return new GetClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "无法获取客户端ID，请检查请求头中的clientId参数"
                    };
                }

                var record = await _teamDbContext.ClientModuleVersions
                    .FirstOrDefaultAsync(x => x.ClientId == clientId && 
                                            x.ProjectId == request.ProjectId && 
                                            x.ModuleId == request.ModuleId);

                if (record == null)
                {
                    return new GetClientModuleVersionResponse
                    {
                        IsSuccess = false,
                        Message = "未找到指定的模块版本记录"
                    };
                }

                _logger.LogInformation($"获取单个模块版本成功，ClientId: {clientId}, ProjectId: {request.ProjectId}, ModuleId: {request.ModuleId}");

                return new GetClientModuleVersionResponse
                {
                    IsSuccess = true,
                    Message = "获取成功",
                    ClientModuleVersion = ConvertToGrpcModel(record)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取单个模块版本失败");
                return new GetClientModuleVersionResponse
                {
                    IsSuccess = false,
                    Message = $"获取失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 将数据库实体转换为gRPC模型
        /// </summary>
        private static GrpcClientModuleVersion ConvertToGrpcModel(ClientModuleVersion entity)
        {
            return new GrpcClientModuleVersion
            {
                Id = entity.Id,
                ClientId = entity.ClientId ?? "",
                ModuleId = entity.ModuleId ?? "",
                Version = entity.Version ?? "",
                ProjectId = entity.ProjectId ?? "",
                ClientIp = entity.ClientIp ?? "",
                UserId = entity.UserId ?? "",
                UserName = entity.UserName ?? "",
                CreatedAt = Timestamp.FromDateTime(entity.CreatedAt.ToUniversalTime()),
                UpdatedAt = Timestamp.FromDateTime(entity.UpdatedAt.ToUniversalTime())
            };
        }
    }
} 