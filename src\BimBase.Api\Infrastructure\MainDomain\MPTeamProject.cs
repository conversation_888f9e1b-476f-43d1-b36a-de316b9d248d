﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    /// <summary>
    /// 子项目（模型）
    /// </summary>
    public class MPTeamProject

    {
        public MPTeamProject()
        {
            ID = Guid.NewGuid();
            EnableAuthority = false;
        }
        /// <summary>
        /// 项目的唯一标识
        /// </summary>
        /// <remarks>以此GUID确认本地的项目数据是否从属于服务器上的特定项目。</remarks>
        [Key]
        
        public Guid ID { get; set; }
        /// <summary>
        /// 用于让用户识别项目的名字
        /// </summary>
        
        public string Name
        {
            get;
            set;
        }

        /// <summary>
        /// 项目的详细描述信息
        /// </summary>
        
        public String Description
        {
            get;
            set;
        }

        /// <summary>
        /// 负责人
        /// </summary>
        
        public string Leader
        {
            get;
            set;
        }

        
        public String Avatar
        {
            get;
            set;
        }

        
        public DateTime StartTime
        {
            get;
            set;
        }

        
        public DateTime EndTime
        {
            get;
            set;
        }
        /// <summary>
        /// 项目进度描述
        /// </summary>
        
        public String Progress
        {
            get;
            set;
        }

        /// <summary>
        /// 拓展属性
        /// </summary>
        
        public String ExtendProperty
        {
            get;
            set;
        }

        
        public DateTime CreationTime
        {
            get;
            set;
        }

        
        public bool EnableAuthority
        {
            get;
            set;
        }

        
        public string FilePath { get; set; }


        
        public string ParentProjectID { get; set; }


        /// <summary>
        /// 所属文件目录id
        /// </summary>
        
        public string FileDirectoryID { get; set; }

        
        public string CreateUser { get; set; }

        /// <summary>
        /// 0:构件级模型  1:文件级协同
        /// </summary>
        
        public int ProjectType { get; set; }

        /// <summary>
        /// 初始化状态：0-未初始化 1-初始化
        /// </summary>
        [DataMember]
        public int InitState { get; set; }

    }
}
