﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPTreeNodeVersion
    {
        /// <summary>
        /// 自增id
        /// </summary>
        [Key]
        
        public int Id { get; set; }

        //
        //public long NodeId { get; set; }
        /// <summary>
        /// 所属库guid
        /// </summary>
        
        public Guid LibId { get; set; }
        /// <summary>
        /// 上传人
        /// </summary>
        
        public string UserName { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        
        public int VersionNo { get; set; }

        
        public DateTime UploadTime { get; set; }
    }
}
