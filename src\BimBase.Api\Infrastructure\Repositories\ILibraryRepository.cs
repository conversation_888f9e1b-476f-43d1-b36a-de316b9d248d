﻿using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using MySqlConnector;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
namespace BimBase.Api.Infrastructure.Repositories
{
    public interface ILibraryRepository : IRepository
    {
        
        IQueryable<CLCatalogTreeNode> CLCatalogTreeNodes { get; }
        //IQueryable<CLCatalogTreeNodeHistory> CLCatalogTreeNodeHistories { get; }
        bool RemoveCatalogTreeNodesByLibIds(List<Guid> delLibGuidList);
        bool RemoveCLLibraryDatasByLibIds(List<Guid> delLibGuidList);
        bool RemoveCLMainProjectLibsInfo(Guid mainprojectGuid);
        IQueryable<CLTreeNodeLock> CLTreeNodesLocks { get; }
        IQueryable<CLTreeNodeVersion> CLTreeNodesVersions { get; }

        int SaveLibVersion(CLTreeNodeVersion versionData);

        bool CheckInCLTreeNode(List<CLCatalogTreeNode> addDatas,
            List<CLCatalogTreeNode> modifyDatas,
            List<CLCatalogTreeNode> delDatas, Guid libGuid, int version);
        bool CheckInCLLibData(List<CLLibraryData> addDatas,
            List<CLLibraryData> modifyDatas,
            List<CLLibraryData> delDatas,
            Guid libGuid, int version);
        /// <summary>
        /// 批量锁定
        /// </summary>
        /// <param name="memberGuid"></param>
        /// <param name="locklibList"></param>
        /// <returns></returns>
        bool LockCLLibTreeNode(List<CLTreeNodeLock> locklibList);

        /// <summary>
        /// 批量解锁
        /// </summary>
        /// <returns></returns>
        bool UnlockCLLibTreeNode(string memberName, List<long> unlockLibList, Guid libGuid);
        bool UnlockCLLibTreeNodeByUser(string memberName);
        IQueryable<CLLibraryData> CLLibraryDatas { get; }

        IQueryable<CLLibraryDataHistory> CLLibraryDataHistories { get; }

        bool UpdateLibDataFileServerPath(long dataId, string serverPath, string fileMD5);

        IQueryable<CLCompanyLibInfo> CLCompanyLibInfos { get; }

        IQueryable<CLCompanyLibInfo> InitAllCLCompanyLibInfo();

        CLCompanyLibInfo AddCLCompanyLibInfo(CLCompanyLibInfo info);

        IQueryable<CLMainprojectLib> CLMainprojectLibs { get; }

        bool AddCLMainprojectLib(Guid mainprojectGuid, Guid libGuid, List<MPCatalogTreeNode> treeIdList);

        bool DeleteMainprojectLib(Guid mainprojectGuid, Guid libGuid, List<long> treeIdList);

        bool InitMainprojectLib(Guid mainprojectGuid, Guid libGuid, int libType);

        bool AddCLMainprojectLibList(List<CLMainprojectLib> cLMainprojectLibs);
        bool Destroy();
        bool CheckInCLLibDataToDBFromLocalFile(long uploadRequestId);

        bool WriteDataToLocalFile(long requestId, List<CLCatalogTreeNode> addtreeDatas,
                            List<CLCatalogTreeNode> modifytreeDatas, List<CLCatalogTreeNode> deltreeDatas,
                            List<CLLibraryData> addLibDatas,
                            List<CLLibraryData> modifyLibDatas, List<CLLibraryData> delLibDatas, Guid libGuid, int retNo);
    }
}
