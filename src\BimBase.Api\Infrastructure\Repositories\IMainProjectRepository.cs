﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Protos;
using Google.Protobuf;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BimBase.Api.Infrastructure.Repositories
{
    public interface IMainProjectRepository:IRepository
    {

        #region BPExObject数据操作
        /// <summary>
        /// 查询提资数据
        /// </summary>
        IQueryable<BPExObject> BPExObjects { get; }
        /// <summary>
        /// 添加提资数据
        /// </summary>
        /// <param name="bpExObjects"></param>
        /// <returns></returns>
        bool AddBPExObjects(List<BPExObject> bpExObjects);

        /// <summary>
        /// 删除提资信息
        /// </summary>
        /// <param name="deleteGuidList"></param>
        /// <returns></returns>
        bool DeleteBPExObjects(List<Guid> deleteGuidList);
        #endregion

        #region BPExObjectPublish数据操作
        IQueryable<BPExObjectPublish> BPExObjectPublishs { get; }

        /// <summary>
        /// 添加发布提资数据
        /// </summary>
        /// <param name="bpExObjectPublishs"></param>
        /// <param name="mainVersion">大版本号</param>
        /// <param name="subVersion">小版本号</param>
        /// <returns></returns>
        bool AddBPExObjectPublishs(List<BPExObjectPublish> bpExObjectPublishs, int mainVersion, int subVersion);
        bool RemoveBPExObjectPublishsWithVersionName(string versionName, string subVersionName);

        bool RestoreMPDb(string sqlPath);
        bool CheckInBPExObjectPublishsWithVersionName(List<BPExObjectPublish> bpExObjectPublishs, List<BPExObjectPublish> delBPExObjectPublishs, string mainVersionName, string subVersionName);
        #endregion

        #region ProvideVersionInfo

        IQueryable<ProvideVersionInfo> ProvideVersionInfoes { get; }

        bool AddProvideVersionInfo(ProvideVersionInfo pvInfo);
        bool RemoveProvideVersionInfo(string mainVersionName, string subVersionName);

        bool PublishProvideVersion(string mainVersionName, string subVersionName, int state, string username, int count);

        bool ModifyProvideVersion(string mainVersionName, string subVersionName, int state, ProvideVersionInfo newVerisonInfo);

        bool UpdateProvideVersionItemCount(string mainVersionName, string subVersionName, int count);
        #endregion

        #region MPUserGroup
        /// <summary>
        /// 获取项目中用户组列表
        /// </summary>
        IQueryable<MPUserGroup> MPUserGroups { get; }
        /// <summary>
        /// 获取所有项目用户组组成员列表
        /// </summary>
        IQueryable<MPUserGroupMember> MPUserGroupMembers { get; }
        /// <summary>
        /// 添加用户组
        /// </summary>
        /// <param name="grp"></param>
        /// <returns></returns>
        MPUserGroup AddMPUserGroup(MPUserGroup grp);
        /// <summary>
        /// 修改用户组名称
        /// </summary>
        /// <param name="grp"></param>
        /// <returns></returns>
        bool UpdateMPUsergroup(MPUserGroup grp);
        /// <summary>
        /// 删除用户组
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        bool DeleteMPUserGroup(int groupId);
        /// <summary>
        /// 添加用户到用户组
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        bool AddTeamMemberToMPUsergroup(TeamMember teamMember, MPUserGroup mPUserGroup);

        bool AddTeamMemberListToMPUsergroup(List<TeamMember> teamMemberList, MPUserGroup mPUserGroup);
        /// <summary>
        /// 从用户组中移除用户
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        bool RemoveTeamMemberFromMPUsergroup(TeamMember teamMember, MPUserGroup mPUserGroup);

        bool RemoveTeamMemberListFromMPUsergroup(List<TeamMember> teamMemberList, MPUserGroup mPUserGroup);
        bool RemoveTeamMemberFromMPUsergroupList(TeamMember teamMember, List<MPUserGroup> mPUserGroupList);

        bool RemoveTeamMemberListFromMPUserGroupList(List<Guid> members, List<MPUserGroup> mPUserGroupList);
        #endregion


        #region MPUserGroupAuth

        IQueryable<MPUserGroupAuth> MPUserGroupAuths { get; }

        bool GiveAuthToMPUserGroup(MPUserGroupAuth teamGroupAuth, List<MPAuthInfo> authList);

        bool GiveAuthToMPUserGroupMultiple(List<MPUserGroupAuth> userGroupAuthList);

        /// <summary>
        /// 获取当前用户节点权限
        /// </summary>
        /// <param name="objectGuid">子项目id</param>
        /// <param name="treeid">树id</param>
        /// <param name="groupormemberid">用户或用户组id</param>
        /// <param name="objectType">权限节点类型1：项目2:文件夹3：模型4：节点。本函数中固定是4</param>
        /// <param name="instanceId">节点id</param>
        /// <param name="teammemberid">用户id</param>
        /// <returns></returns>
        List<MPAuthInfo> GetCurrentUserMPAuthInfosByObjectId(Guid objectGuid, long nodeid, Guid teammemberid, int objecttype);

        List<MPAuthInfo> GetMPAuthInfosByObjectId(Guid objectGuid, long nodeid, string groupormemberid, int objecttype);
        /// <summary>
        /// 获取所有用户对树节点的权限
        /// </summary>
        /// <param name="objectGuid"></param>
        /// <param name="treeid"></param>
        /// <param name="objectType"></param>
        /// <param name="instanceId"></param>
        /// <returns></returns>
        Dictionary<int, List<MPAuthInfo>> GetAllMembersAuthInfoByObjectId(Guid objectGuid, long nodeid, int objecttype);

        List<long> GetAllInstanceByMember(Guid objectGuid, int objecttype, Guid teammemberId);
        #endregion

        #region MPTeamProject
        IQueryable<MPTeamProject> MPTeamProjects { get; }

        bool AddMPTeamProjectList(List<MPTeamProject> mPTeamProjects);

        MPTeamProject AddMPTeamProject(MPTeamProject mpTeamProject);

        MPTeamProject RemoveMPTeamProject(Guid mpTeamProjectId);

        bool UpdateMPTeamProject(MPTeamProject mpTeamProject);

        bool UpdateMPTeamProjectIdAndName(Guid oldprojectid, Guid newprojectid, string newprojectname);

        bool CheckProjectName(String pName);
        bool CheckMPFileDirectoryName(MPFileDirectory filedir);
        MPFileDirectory GetMPFileDirectory(Guid mpFileDirectoryId);
        #endregion

        #region MPFileDirectory
        IQueryable<MPFileDirectory> MPFileDirectories { get; }

        MPFileDirectory AddMPFileDirectorey(MPFileDirectory filedirectory);

        bool AddMPFileDirectoreyList(List<MPFileDirectory> fileDicList);

        bool RemoveMPFileDirectorey(Guid fileId);


        bool UpdateMPFileDirectoryOrderNo(Guid dirGuid, string upOrDown);

        bool UpdateMPFileDirectoryInfo(MPFileDirectory mpFileDirectory);
        #endregion

        #region MPVolume
        IQueryable<MPVolume> MPVolumes { get; }

        MPVolume AddMPVolume(MPVolume mpVolume);

        bool AddMPVolumeList(List<MPVolume> mpVolumeList);

        bool RemoveMPVolume(Guid volumeId);

        #endregion

        #region MPVolumeVersion
        IQueryable<MPVolumeVersion> MPVolumeVersions { get; }

        bool AddMPVolumeVersion(MPVolumeVersion volVer);
        bool AddMPVolumeVersionList(List<MPVolumeVersion> volVerList);

        bool UpdateMPVolumeVersionFilePathInfo(MPVolumeVersion volVer);

        bool UpdateMPLibFilePath(string mainpjId, string newpath);
        bool UpdateMPLibFilePath(string mainpjId, string newpath, string oldlibGuid, string newlibguid);

        bool UpdateMPLinkFilePathWithNewProjectId(string mainpjId, string newpath, string oldprojectid, string newprojectid);
        bool UpdateMPDrawingPathWithNewProjectId(string mainpjId, string newpath, string oldprojectid, string newprojectid, string oldmainprjid);

        bool UpdateMPLinkFilePath(string mainpjId, string newpath);

        #endregion

        #region MPProjectTreeNode
        IQueryable<MPProjectTreeNode> MPProjectTreeNodes { get; }
        MPProjectTreeNode AddMPProjectTreeNode(MPProjectTreeNode mpProjectTreeNode);
        bool RemoveMPProjectTreeNode(long instanceid);

        bool CheckInTreeNodes(
            List<MPProjectTreeNode> addDatas,
            List<MPProjectTreeNode> modifyDatas,
            List<MPProjectTreeNode> delDatas);

        bool UpdateMPProjectTreeNodeSubprojectId(string mainpjId, string oldprojectid, string newprojectid);

        public bool WriteDataToLocalFile(long requestId, List<MPProjectTreeNode> addDatas, List<MPProjectTreeNode> modifyDatas,
                                    List<MPProjectTreeNode> deleteDatas);
        bool CheckInToDBFromLocalFile(long requestId);

        bool CheckInMPLibDataToDBFromLocalFile(long requestId);
        #endregion

        #region MPLibraryInfo
        IQueryable<MPLibraryInfo> MPLibraryInfos { get; }

        MPLibraryInfo AddMPLibraryInfo(MPLibraryInfo mpLibraryInfo);

        bool RemoveMPLibraryInfo(Guid LibId);

        bool UpdateMPLibraryInfoGuid(Guid oldlibid, Guid newlibid);
        #endregion


        #region MPUserGroupLibAuth

        IQueryable<MPUserGroupLibAuth> MPUserGroupLibAuths { get; }

        bool SetMPUserGroupLibAuth(MPUserGroupLibAuth mpUserGroupLibAuth);

        #endregion

        #region MPLibLock

        IQueryable<MPLibLock> MPLibLocks { get; }

        /// <summary>
        /// 批量锁定
        /// </summary>
        /// <param name="memberGuid"></param>
        /// <param name="locklibList"></param>
        /// <returns></returns>
        bool LockMPLib(List<MPLibLock> locklibList);
        /// <summary>
        /// 批量解锁
        /// </summary>
        /// <returns></returns>
        bool UnlockMPLib(string memberName, List<Guid> unlockLibList);
        #endregion

        #region

        IQueryable<MPCloudLinkFile> MPLibCloudLinks { get; }
        bool AddMPCloudLinkFile(MPCloudLinkFile clf);


        #endregion

        #region 
        IQueryable<MPReleaseConfig> MPLibReleaseConfigs { get; }
        MPReleaseConfig AddMPReleaseConfig(MPReleaseConfig mpReleaseConfig);

        bool RemoveMPReleaseConfig(MPReleaseConfig mPReleaseConfig);

        bool UpdateMPReleaseConfigInfo(string mainpjId, string oldprojectid, string newprojectid);
        bool UpdateMPUserGroupAuthInfo(string mainpjId, string oldprojectid, string newprojectid);

        bool UpdateMPUserGroupMemberInfo(string mainpjId, string oldteammemberid, string newteammemberid);
        #endregion

        #region MPMessage
        IQueryable<MPMessage> MPMessages { get; }

        bool UpdateMessageState(MPMessage message);
        bool UpdateMessageStateMul(List<int> messageIds);
        MPMessage AddMPMessage(MPMessage projectMessage);
        bool AddMPMessageList(List<MPMessage> projectMessage);

        #endregion


        #region MPDrawing

        IQueryable<MPDrawing> MPDrawings { get; }

        MPDrawing AddMPDrawing(MPDrawing drawing);

        bool LockMPDrawing(Guid subProjectId, List<long> instanceid, string lockuser);

        bool UnlockMPDrawing(Guid subProjectGuid, List<long> instanceid, string lockuser);

        bool UpdateMPDrawing(Guid subprojectGuid, long instanceid, int versionNo, string fullpath, string fileMD5);

        #endregion


        #region 
        IQueryable<MPTreeNodeVersion> MPTreeNodesVersions { get; }

        bool UpdateMPTreeNodeVersionInfo(string mainpjId, string oldlibGuid, string newlibGuid);
        int SaveLibVersion(MPTreeNodeVersion versionData);
        bool UpdateMPCataLogTreeNodeLibGuid(string mainpjId, string oldLibGuid, string newLibGuid);
        IQueryable<MPCatalogTreeNode> MPCatalogTreeNodes { get; }
        bool CheckInCLTreeNode(List<MPCatalogTreeNode> addDatas,
            List<MPCatalogTreeNode> modifyDatas,
            List<MPCatalogTreeNode> delDatas, Guid libGuid, int version);

        IQueryable<MPLibraryData> MPLibraryDatas { get; }
        bool CheckInCLLibData(List<MPLibraryData> addDatas,
            List<MPLibraryData> modifyDatas,
            List<MPLibraryData> delDatas,
            Guid libGuid, int version);
        IQueryable<MPLibraryDataHistory> MPLibraryDataHistories { get; }
        bool UpdateLibDataFileServerPath(long dataId, string serverPath, string fileMD5);
        #endregion


        bool WriteDataToLocalFile(long requestId, List<MPCatalogTreeNode> addtreeDatas, List<MPCatalogTreeNode> modifytreeDatas,
                                    List<MPCatalogTreeNode> deltreeDatas, List<MPLibraryData> addLibDatas,
                                    List<MPLibraryData> modifyLibDatas,
                                    List<MPLibraryData> delLibDatas,Guid libGuid, int verNo);

        bool Destroy();
    }
}
