﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    /// <summary>
    /// 用户-节点-权限表
    /// </summary>
    public class MPUserGroupAuth
    {
        
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// 用户组id或用户id
        /// </summary>
        
        public string GroupOrMemberId { get; set; }
        /// <summary>
        /// 标记是用户组还是用户 0：用户组 1：用户
        /// </summary>
        
        public int IsGroupOrTeamMember { get; set; }
        
        public string AuthInfo { get; set; }
        /// <summary>
        /// 权限对象类型0：特殊（是否项目管理员） 1：项目 2：文件夹 3：模型 4：树节点 
        /// </summary>
        
        public int ObjectType { get; set; }

        /// <summary>
        /// ojecttype为4时，该字段为子项目（模型）id;或者库的libid
        /// </summary>
        
        public Guid ObjectId { get; set; }

        /// <summary>
        /// 节点instanceid，objecttype为4时需赋值，用于pdms设置节点权限
        /// </summary>
        
        public long InstanceId { get; set; }
        /// <summary>
        /// 节点TreeId，objecttype为4时需赋值，用于pdms设置节点权限
        /// </summary>
        
        public long TreeId { get; set; }

    }
}
