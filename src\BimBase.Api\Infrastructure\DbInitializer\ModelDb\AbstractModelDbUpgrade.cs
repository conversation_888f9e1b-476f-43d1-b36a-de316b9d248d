using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace BimBase.Api.Infrastructure.DbInitializer.ModelDb
{
    /// <summary>
    /// ModelDb数据库升级抽象基类
    /// 继承通用数据库升级基类，提供ModelDb特定的升级功能
    /// </summary>
    public abstract class AbstractModelDbUpgrade : AbstractDatabaseUpgrade<ModelDbContext>, IModelDbUpgrade
    {
        protected AbstractModelDbUpgrade(ILogger logger) : base(logger)
        {
        }
        
        /// <summary>
        /// 源版本
        /// </summary>
        public override abstract string FromVersion { get; }
        
        /// <summary>
        /// 目标版本
        /// </summary>
        public override abstract string ToVersion { get; }
        
        /// <summary>
        /// 升级描述
        /// </summary>
        public override abstract string Description { get; }
        
        /// <summary>
        /// 是否支持回滚（默认不支持）
        /// </summary>
        public override bool SupportsRollback => false;
        
        /// <summary>
        /// 执行升级
        /// </summary>
        public async Task UpgradeAsync(ModelDbContext context)
        {
            Logger.LogInformation($"开始执行ModelDb升级: {FromVersion} -> {ToVersion}");
            Logger.LogInformation($"升级描述: {Description}");
            
            try
            {
                await ExecuteUpgradeAsync(context);
                await UpdateVersionRecordAsync(context);
                
                Logger.LogInformation($"ModelDb升级完成: {FromVersion} -> {ToVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"ModelDb升级失败: {FromVersion} -> {ToVersion}");
                throw;
            }
        }
        
        /// <summary>
        /// 回滚升级
        /// </summary>
        public async Task RollbackAsync(ModelDbContext context)
        {
            if (!SupportsRollback)
            {
                throw new NotSupportedException($"升级 {FromVersion} -> {ToVersion} 不支持回滚");
            }
            
            Logger.LogInformation($"开始回滚ModelDb升级: {ToVersion} -> {FromVersion}");
            
            try
            {
                await ExecuteRollbackAsync(context);
                await UpdateVersionRecordForRollbackAsync(context);
                Logger.LogInformation($"ModelDb回滚完成: {ToVersion} -> {FromVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"ModelDb回滚失败: {ToVersion} -> {FromVersion}");
                throw;
            }
        }
        
        /// <summary>
        /// 执行具体的升级逻辑（子类实现）
        /// </summary>
        protected abstract Task ExecuteUpgradeAsync(ModelDbContext context);
        
        /// <summary>
        /// 执行具体的回滚逻辑（子类可选实现）
        /// </summary>
        protected virtual Task ExecuteRollbackAsync(ModelDbContext context)
        {
            throw new NotSupportedException("此升级不支持回滚");
        }
        
        /// <summary>
        /// 更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordAsync(ModelDbContext context)
        {
            var versionRecord = new ModelDomain.DatabaseVersion
            {
                Version = ToVersion,
                UpgradedAt = DateTime.Now,
                Description = Description
            };
            
            context.DatabaseVersions.Add(versionRecord);
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }
        
        /// <summary>
        /// 回滚时更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordForRollbackAsync(ModelDbContext context)
        {
            // 删除当前版本记录
            var currentVersionRecord = await context.DatabaseVersions
                .Where(v => v.Version == ToVersion)
                .FirstOrDefaultAsync();
                
            if (currentVersionRecord != null)
            {
                context.DatabaseVersions.Remove(currentVersionRecord);
            }
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }
        
        #region ModelDb特定辅助方法

        /// <summary>
        /// 检查列是否存在
        /// </summary>
        protected async Task<bool> ColumnExistsAsync(ModelDbContext context, string tableName, string columnName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND column_name = '{columnName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查索引是否存在
        /// </summary>
        protected async Task<bool> IndexExistsAsync(ModelDbContext context, string tableName, string indexName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.statistics 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND index_name = '{indexName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 检查表是否存在
        /// </summary>
        protected async Task<bool> TableExistsAsync(ModelDbContext context, string tableName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        /// <summary>
        /// 检查触发器是否存在
        /// </summary>
        protected async Task<bool> TriggerExistsAsync(ModelDbContext context, string triggerName)
        {
            var databaseName = context.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.triggers 
                WHERE trigger_schema = '{databaseName}' 
                  AND trigger_name = '{triggerName}'";

            var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }
        
        /// <summary>
        /// 添加列（如果不存在）
        /// </summary>
        protected async Task AddColumnIfNotExistsAsync(ModelDbContext context, string tableName, string columnName, string columnDefinition)
        {
            if (await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogInformation($"{tableName}表{columnName}字段已存在，跳过添加");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnDefinition};
            ");
            Logger.LogInformation($"成功添加{columnName}列到{tableName}表");
        }
        
        /// <summary>
        /// 删除列（如果存在）
        /// </summary>
        protected async Task DropColumnIfExistsAsync(ModelDbContext context, string tableName, string columnName)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogInformation($"{tableName}表{columnName}字段不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` DROP COLUMN `{columnName}`;
            ");
            Logger.LogInformation($"成功从{tableName}表中移除{columnName}列");
        }

        /// <summary>
        /// 修改列定义（如果存在）
        /// </summary>
        protected async Task ModifyColumnIfExistsAsync(ModelDbContext context, string tableName, string columnName, string newColumnDefinition)
        {
            if (!await ColumnExistsAsync(context, tableName, columnName))
            {
                Logger.LogWarning($"{tableName}表{columnName}字段不存在，无法修改");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                ALTER TABLE `{tableName}` MODIFY COLUMN `{columnName}` {newColumnDefinition};
            ");
            Logger.LogInformation($"成功修改{tableName}表{columnName}列定义");
        }
        
        /// <summary>
        /// 创建索引（如果不存在）
        /// </summary>
        protected async Task CreateIndexIfNotExistsAsync(ModelDbContext context, string tableName, string indexName, string columnDefinition)
        {
            if (await IndexExistsAsync(context, tableName, indexName))
            {
                Logger.LogInformation($"{tableName}表{indexName}索引已存在，跳过创建");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                CREATE INDEX `{indexName}` ON `{tableName}` ({columnDefinition});
            ");
            Logger.LogInformation($"成功为{tableName}表创建索引{indexName}");
        }
        
        /// <summary>
        /// 删除索引（如果存在）
        /// </summary>
        protected async Task DropIndexIfExistsAsync(ModelDbContext context, string tableName, string indexName)
        {
            if (!await IndexExistsAsync(context, tableName, indexName))
            {
                Logger.LogInformation($"{tableName}表{indexName}索引不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($@"
                DROP INDEX `{indexName}` ON `{tableName}`;
            ");
            Logger.LogInformation($"成功移除{tableName}表{indexName}索引");
        }

        /// <summary>
        /// 创建表（如果不存在）
        /// </summary>
        protected async Task CreateTableIfNotExistsAsync(ModelDbContext context, string tableName, string tableDefinition)
        {
            if (await TableExistsAsync(context, tableName))
            {
                Logger.LogInformation($"{tableName}表已存在，跳过创建");
                return;
            }

            await context.Database.ExecuteSqlRawAsync(tableDefinition);
            Logger.LogInformation($"成功创建{tableName}表");
        }

        /// <summary>
        /// 删除表（如果存在）
        /// </summary>
        protected async Task DropTableIfExistsAsync(ModelDbContext context, string tableName)
        {
            if (!await TableExistsAsync(context, tableName))
            {
                Logger.LogInformation($"{tableName}表不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($"DROP TABLE `{tableName}`;");
            Logger.LogInformation($"成功删除{tableName}表");
        }

        /// <summary>
        /// 创建触发器（如果不存在，存在则先删除再创建）
        /// </summary>
        protected async Task CreateOrReplaceTriggerAsync(ModelDbContext context, string triggerName, string triggerDefinition)
        {
            MySqlConnector.MySqlConnection connection = null;
            try
            {
                // 构建新的连接字符串，确保完全脱离连接池和事务状态
                var connectionString = context.Database.GetConnectionString();
                
                // 添加连接池相关参数，确保获取新的干净连接
                var connectionStringBuilder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString)
                {
                    Pooling = false, // 禁用连接池，确保获取全新连接
                    AutoEnlist = false // 禁用自动加入分布式事务
                };
                
                connection = new MySqlConnector.MySqlConnection(connectionStringBuilder.ConnectionString);
                await connection.OpenAsync();
                
                Logger.LogInformation($"升级过程中触发器连接状态 - 数据库: {connection.Database}, 状态: {connection.State}");

                // 先删除可能存在的同名触发器
                using var dropCommand = connection.CreateCommand();
                dropCommand.CommandText = $"DROP TRIGGER IF EXISTS `{triggerName}`;";
                await dropCommand.ExecuteNonQueryAsync();
                Logger.LogInformation($"已删除旧触发器: {triggerName}");
                
                // 创建新触发器
                using var createCommand = connection.CreateCommand();
                createCommand.CommandText = triggerDefinition;
                await createCommand.ExecuteNonQueryAsync();
                Logger.LogInformation($"成功创建触发器{triggerName}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"创建触发器 {triggerName} 时发生错误");
                throw;
            }
            finally
            {
                if (connection != null)
                {
                    try
                    {
                        if (connection.State == System.Data.ConnectionState.Open)
                        {
                            await connection.CloseAsync();
                        }
                        await connection.DisposeAsync();
                        Logger.LogInformation($"升级过程中触发器连接已安全关闭");
                    }
                    catch (Exception ex)
                    {
                        Logger.LogWarning(ex, "关闭升级过程中触发器连接时发生错误");
                    }
                }
            }
        }

        /// <summary>
        /// 删除触发器（如果存在）
        /// </summary>
        protected async Task DropTriggerIfExistsAsync(ModelDbContext context, string triggerName)
        {
            if (!await TriggerExistsAsync(context, triggerName))
            {
                Logger.LogInformation($"触发器{triggerName}不存在，跳过删除");
                return;
            }

            await context.Database.ExecuteSqlRawAsync($"DROP TRIGGER `{triggerName}`;");
            Logger.LogInformation($"成功删除触发器{triggerName}");
        }

        #endregion
    }
} 