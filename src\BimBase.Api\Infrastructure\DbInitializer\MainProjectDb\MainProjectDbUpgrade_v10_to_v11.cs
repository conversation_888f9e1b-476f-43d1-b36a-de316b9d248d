using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb数据库从v10升级到v11
    /// 主要变更：
    /// 1. 添加ConflictScope字段，用于动态冲突检测范围标识
    /// 2. 移除旧的冲突检测索引
    /// 3. 添加基于ConflictScope的动态冲突检测索引
    /// </summary>
    public class MainProjectDbUpgrade_v10_to_v11 : AbstractMainProjectDbUpgrade
    {
        public override string FromVersion => "v10";
        public override string ToVersion => "v11";
        public override string Description => "添加ConflictScope字段，实现数据库层面的动态冲突检测";

        public MainProjectDbUpgrade_v10_to_v11(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{FromVersion} -> {ToVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v10 到 v11 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(MainProjectDbContext context)
        {
            // 1. 添加ConflictScope字段
            await AddConflictScopeFieldAsync(context);

            // 2. 移除旧的冲突检测索引
            await RemoveOldConflictIndexesAsync(context);

            // 3. 添加新的动态冲突检测索引
            await AddDynamicConflictIndexAsync(context);

            // 4. 初始化现有数据的ConflictScope
            await InitializeExistingConflictScopeAsync(context);
        }

        /// <summary>
        /// 添加ConflictScope字段
        /// </summary>
        private async Task AddConflictScopeFieldAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD COLUMN `ConflictScope` VARCHAR(190) NULL COMMENT '冲突检测范围标识（用于动态唯一索引）';
                ");
                Logger.LogInformation("成功添加 ConflictScope 字段");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加 ConflictScope 字段失败");
                throw;
            }
        }

        /// <summary>
        /// 移除旧的冲突检测索引
        /// </summary>
        private async Task RemoveOldConflictIndexesAsync(MainProjectDbContext context)
        {
            try
            {
                // 移除旧的同级节点冲突检测索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_subprojectId_parentId_nodename`;
                ");
                Logger.LogInformation("成功移除旧的同级节点冲突检测索引");

                // 移除旧的全局冲突检测索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_subprojectId_nodename`;
                ");
                Logger.LogInformation("成功移除旧的全局冲突检测索引");
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"移除旧索引时发生异常（可能索引不存在）: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加新的动态冲突检测索引
        /// </summary>
        private async Task AddDynamicConflictIndexAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD UNIQUE INDEX `idx_subprojectId_conflictScope_nodename` (`subProjectld`, `ConflictScope`, `NodeName`);
                ");
                Logger.LogInformation("成功添加动态冲突检测索引");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加动态冲突检测索引失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化现有数据的ConflictScope
        /// </summary>
        private async Task InitializeExistingConflictScopeAsync(MainProjectDbContext context)
        {
            try
            {
                // 为现有数据设置默认的ConflictScope（使用父节点ID，即同级检测）
                await context.Database.ExecuteSqlRawAsync(@"
                    UPDATE `mpprojecttreenodes` 
                    SET `ConflictScope` = CAST(`ParentNodeId` AS CHAR) 
                    WHERE `ConflictScope` IS NULL;
                ");
                Logger.LogInformation("成功初始化现有数据的 ConflictScope");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "初始化现有数据的 ConflictScope 失败");
                throw;
            }
        }

        protected override async Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {ToVersion} -> {FromVersion} 回滚...");

            try
            {
                // 移除新的动态冲突检测索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_subprojectId_conflictScope_nodename`;
                ");
                Logger.LogInformation("成功移除动态冲突检测索引");

                // 重新添加旧的索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD UNIQUE INDEX `idx_subprojectId_parentId_nodename` (`subProjectld`, `ParentNodeId`, `NodeName`);
                ");
                Logger.LogInformation("成功重新添加同级节点冲突检测索引");

                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD INDEX `idx_subprojectId_nodename` (`subProjectld`, `NodeName`);
                ");
                Logger.LogInformation("成功重新添加全局冲突检测索引");

                // 移除ConflictScope字段
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP COLUMN `ConflictScope`;
                ");
                Logger.LogInformation("成功移除 ConflictScope 字段");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "回滚操作失败");
                throw;
            }

            Logger.LogInformation($"{ToVersion} -> {FromVersion} 回滚完成");
        }
    }
} 