﻿using BimBase.Api.Infrastructure.ModelDomain;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Management;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Common
{
    public class CacheHelper
    {
        //SCREAMING_CAPS包含一到多个单词，每个单词的所有字母都大写，单词与单词之间用"_"连接，该风格目前在c#中只用于const常量

        private const string MODELDATA_INSTANCEID_KEY = "m-Ids:";
        private const string RELATIONSHIP_INSTANCEID_KEY = "r-Ids:";

        private const string MODELDATA_KEY = "m:";
        private const string RELATIONSHIP_KEY = "r:";

        private const string MODELDATA_HISTORY_KEY = "m-h:";
        private const string RELATIONSHIP_HISTORY_KEY = "r-h:";


        /// <summary>
        /// 根据模型文件查找到的relatonShipId,并进行缓存
        /// </summary>
        private const string RELATIONED_KEY = "m-r:";
        /// <summary>
        /// 根据DomainClassID 获取 modeldata instanced
        /// </summary>
        private const string MODELDATA_DOMAINCLASS_KEY = "m-dc:";
        /// <summary>
        /// 根据Domain 获取 modeldata instanced
        /// </summary>
        private const string MODELDATA_DOMAIN_KEY = "m-d:";

        /// <summary>
        /// 根据modeldata Instanceid 从relationship sourceid 获取所有又关系的其他 modeldata InstanceId 代替函数 ModelQuery FindRelatedDatas
        /// </summary>
        private const string RELATED_SOURCEID_KEY = "r-s:";
        /// <summary>
        /// 根据modeldata Instanceid 从relationship targetid 获取所有又关系的其他 modeldata InstanceId 代替函数 ModelQuery FindRelatedDatas
        /// </summary>
        private const string RELATED_TARGETID_KEY = "r-t:";
        /// <summary>
        /// 存储部分relationship 数据
        /// </summary>
        private const string RELATED_KEY = "rst:";

        private static ReaderWriterLockSlim _LockSlim = new ReaderWriterLockSlim();

        /// <summary>
        /// add by asdf 2018-08-03 存放项目id,加载项目到缓存后，将项目id存入，项目从缓存中移除后，将项目id从pool 中删除
        /// </summary>
        private static ConcurrentDictionary<Guid, string> projectPool = new ConcurrentDictionary<Guid, string>();

        public static bool SaveToCache<T>(string objKey, T obj)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            var serializedModel = JsonSerializer.Serialize(obj);
            var x = redis.StringSet(objKey, serializedModel);
            return x;
        }

        /// <summary>
        /// 增加Domain 缓存
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="domainSearchDtoList"></param>
        /// <returns></returns>
        public static bool CacheDomainSearchDto(Guid projectGuid, IEnumerable<DomainSearchDto> domainSearchDtoList)
        {
            if (domainSearchDtoList != null && domainSearchDtoList.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                var tasks = new List<Task<long>>();

                Parallel.ForEach(domainSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetAddAsync(MODELDATA_DOMAIN_KEY + projectId + ":" + s.DomainId, redisValues);
                        tasks.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(tasks.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }
        /// <summary>
        /// 删除Domin 缓存
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="domainSearchDtoList"></param>
        /// <returns></returns>
        public static bool RemoveDomainSearchDto(Guid projectGuid, IEnumerable<DomainSearchDto> domainSearchDtoList)
        {
            if (domainSearchDtoList != null && domainSearchDtoList.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                var tasks = new List<Task<long>>();

                Parallel.ForEach(domainSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetRemoveAsync(MODELDATA_DOMAIN_KEY + projectId + ":" + s.DomainId, redisValues);
                        tasks.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(tasks.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }

        /// <summary>
        /// 根据 domin 获取数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="domain"></param>
        /// <returns></returns>
        public static List<long> FromCacheDomainSearchDto(Guid projectGuid, int domain)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return null;
            string projectId = projectGuid.ToString("N");
            var redisValues = redis.SetMembers(MODELDATA_DOMAIN_KEY + projectId + ":" + domain);
            var ret = Array.ConvertAll(redisValues, item => (long)item);
            return ret.ToList();
        }

        /// <summary>
        /// 增加DomainClass 缓存
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="domainClassSearchDtoList"></param>
        /// <returns></returns>
        public static bool CacheDomainClassSearchDto(Guid projectGuid, IEnumerable<DomainClassSearchDto> domainClassSearchDtoList)
        {
            if (domainClassSearchDtoList != null && domainClassSearchDtoList.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;

                var tasks = new List<Task<long>>();

                Parallel.ForEach(domainClassSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetAddAsync(MODELDATA_DOMAINCLASS_KEY + projectId + ":" + s.DomainClassName, redisValues);
                        tasks.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(tasks.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }
        /// <summary>
        /// 删除DominClass 缓存
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="domainClassSearchDtoList"></param>
        /// <returns></returns>
        public static bool RemoveDomainClassSearchDto(Guid projectGuid, IEnumerable<DomainClassSearchDto> domainClassSearchDtoList)
        {
            if (domainClassSearchDtoList != null && domainClassSearchDtoList.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                var tasks = new List<Task<long>>();

                Parallel.ForEach(domainClassSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetRemoveAsync(MODELDATA_DOMAINCLASS_KEY + projectId + ":" + s.DomainClassName, redisValues);
                        tasks.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(tasks.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }


        public static List<long> FromCacheDomainClassSearchDto(Guid projectGuid, string domainClassName)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return null;
            string projectId = projectGuid.ToString("N");
            var redisValues = redis.SetMembers(MODELDATA_DOMAINCLASS_KEY + projectId + ":" + domainClassName);
            var ret = Array.ConvertAll(redisValues, item => (long)item);
            return ret.ToList();
        }

        /// <summary>
        /// 缓存relationship 部分数据set(无序排列)
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="relatedSearchDto"></param>
        /// <returns></returns>
        public static bool CacheRelatedSearchDto(Guid projectGuid, IEnumerable<RelatedSearchDto> relatedSearchDto)
        {
            if (relatedSearchDto != null && relatedSearchDto.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                var list = new List<Task<bool>>();
                Parallel.ForEach(relatedSearchDto, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var data = JsonSerializer.Serialize(s);
                        var task = batch.SetAddAsync(RELATED_SOURCEID_KEY + projectId + ":" + s.SourceID, data);
                        var task2 = batch.SetAddAsync(RELATED_TARGETID_KEY + projectId + ":" + s.TargetID, data);
                        list.Add(task);
                        list.Add(task2);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(list.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }
        /// <summary>
        /// 根据 modeldataIds 获取相关的 instanceid 
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="dataIds"></param>
        /// <returns></returns>
        public static List<RelatedSearchDto> FromCacheRelatedSearchDto(Guid projectGuid, ICollection<Int64> dataIds)
        {
            try
            {
                string projectId = projectGuid.ToString("N");
                if (dataIds != null && dataIds.Any())
                {
                    var redis = BimBaseServerCache.RedisCache;
                    if (redis == null) return null;
                    IBatch batch = redis.CreateBatch();
                    var list = new List<Task<RedisValue[]>>();
                    ParallelOptions options = new ParallelOptions();
                    options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                    List<RelatedSearchDto> relatedSearchDtoList = new List<RelatedSearchDto>();
                    Parallel.ForEach(dataIds, options, i =>
                    {
                        try
                        {
                            _LockSlim.EnterWriteLock();
                            var tmp = batch.SetMembersAsync(RELATED_SOURCEID_KEY + projectId + ":" + i);
                            var tmp2 = batch.SetMembersAsync(RELATED_TARGETID_KEY + projectId + ":" + i);
                            list.Add(tmp);
                            list.Add(tmp2);
                        }
                        finally
                        {
                            _LockSlim.ExitWriteLock();
                        }
                    });

                    batch.Execute();
                    var x = Task.WhenAll(list.ToArray());

                    if (!x.IsCompleted)
                    {
                        x.Wait();
                    }

                    Parallel.ForEach(x.Result, options, (m) =>
                    {
                        try
                        {
                            _LockSlim.EnterWriteLock();

                            foreach (var dto in m)
                            {
                                var models = JsonSerializer.Deserialize<RelatedSearchDto>(dto);
                                if (models != null)
                                {
                                    relatedSearchDtoList.Add(models);
                                }
                            }
                        }
                        finally
                        {
                            _LockSlim.ExitWriteLock();
                        }
                    });
                    return relatedSearchDtoList;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        /// <summary>
        /// 删除关系数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="relatedSearchDto"></param>
        /// <returns></returns>
        public static bool RemoveRelatedSearchDto(Guid projectGuid, IEnumerable<RelatedSearchDto> relatedSearchDto)
        {
            if (relatedSearchDto != null && relatedSearchDto.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                IBatch batch = redis.CreateBatch();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
                var list = new List<Task<bool>>();
                Parallel.ForEach(relatedSearchDto, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var data = JsonSerializer.Serialize(s);
                        var task = batch.SetRemoveAsync(RELATED_SOURCEID_KEY + projectId + ":" + s.SourceID, data);
                        var task2 = batch.SetRemoveAsync(RELATED_TARGETID_KEY + projectId + ":" + s.TargetID, data);
                        list.Add(task);
                        list.Add(task2);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(list.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                return true;
            }
            return true;
        }

        /// <summary>
        /// 使用默认的key缓存ModelDataInstanceId
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="instanceIds"></param>
        /// <returns></returns>
        public static bool CacheModelDataInstanceIds(Guid projectGuid, IEnumerable<long> instanceIds)
        {
            if (instanceIds != null && instanceIds.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                var redisValues = Array.ConvertAll(instanceIds.ToArray(), item => (RedisValue)item);
                var x = redis.SetAdd(MODELDATA_INSTANCEID_KEY + projectId, redisValues);
                if (x == instanceIds.Count())
                    return true;
            }
            return false;
        }
        /// <summary>
        /// 移除缓存中指定的ModelDataInstanceId
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="instanceIds"></param>
        /// <returns></returns>
        public static bool RemoveModelDataInstanceIds(Guid projectGuid, IEnumerable<long> removeInstanceIds)
        {
            if (removeInstanceIds != null && removeInstanceIds.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                var redisValues = Array.ConvertAll(removeInstanceIds.ToArray(), item => (RedisValue)item);
                //返回删除成功个数
                var x = redis.SetRemove(MODELDATA_INSTANCEID_KEY + projectId, redisValues);
                if (x == removeInstanceIds.Count())
                {
                    return true;
                }
            }
            return true;
        }

        /// <summary>
        /// 根据projectID 获取该项目 ModelData 的所有InstanceId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public static List<string> FromCacheModelDataInstanceIds(Guid projectGuid)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return null;
            string projectId = projectGuid.ToString("N");
            var x = redis.SetMembers(MODELDATA_INSTANCEID_KEY + projectId);
            return x.ToStringArray().ToList();
        }

        /// <summary>
        /// 根据projectID 获取该项目 ModelData
        /// 如果不传第二个参数，获取所有数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="dataIds"></param>
        /// <returns></returns>
        public static List<ModelData> FromCacheModelData(Guid projectGuid, ICollection<Int64> dataIds = null)
        {
            List<ModelData> modeldatas = new List<ModelData>();
            List<string> modelDataInstanceIds = new List<string>();
            string projectId = projectGuid.ToString("N");
            try
            {
                if (dataIds != null && dataIds.Any())
                {
                    foreach (var ids in dataIds)
                    {
                        modelDataInstanceIds.Add(ids.ToString());
                    }
                }
                else
                {
                    modelDataInstanceIds = FromCacheModelDataInstanceIds(projectGuid);
                }

                if (modelDataInstanceIds == null || !modelDataInstanceIds.Any())
                    return null;
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return null;
                IBatch batch = redis.CreateBatch();

                var list = new List<Task<RedisValue>>();
                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;

                Parallel.ForEach(modelDataInstanceIds, options, a =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var tmp = batch.StringGetAsync(MODELDATA_KEY + projectId + ":" + a);
                        //if (string.IsNullOrWhiteSpace(tmp.Result))
                        //{
                        //    string emptyID = a;
                        //}
                        list.Add(tmp);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(list.ToArray());

                Parallel.ForEach(x.Result, options, m =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        if (!string.IsNullOrWhiteSpace(m))
                        {
                            var models = JsonSerializer.Deserialize<ModelData>(m);
                            if (models != null)
                            {
                                modeldatas.Add(models);
                            }
                        }
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }

                });
                return modeldatas;
            }
            catch (Exception ex)
            {
                Console.WriteLine("FromCacheModelData Error:" + ex.Message);
                return null;
            }

        }

        /// <summary>
        /// 使用默认的key保存RelationShipInstanceId
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="instanceIds"></param>
        /// <returns></returns>
        public static bool CacheRelationShipInstanceIds(Guid projectGuid, IEnumerable<long> instanceIds)
        {
            if (instanceIds != null && instanceIds.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                var redisValues = Array.ConvertAll(instanceIds.ToArray(), item => (RedisValue)item);
                var x = redis.SetAdd(RELATIONSHIP_INSTANCEID_KEY + projectId, redisValues);
                if (x == instanceIds.Count())
                {
                    return true;
                }
            }
            return false;
        }
        /// <summary>
        /// 移除缓存中指定的RelationShipInstanceId
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="instanceIds"></param>
        /// <returns></returns>
        public static bool RemoveRelationShipInstanceIds(Guid projectGuid, IEnumerable<long> instanceIds)
        {
            if (instanceIds != null && instanceIds.Any())
            {
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return false;
                string projectId = projectGuid.ToString("N");
                var redisValues = Array.ConvertAll(instanceIds.ToArray(), item => (RedisValue)item);
                var x = redis.SetRemove(RELATIONSHIP_INSTANCEID_KEY + projectId, redisValues);
                if (x == instanceIds.Count())
                    return true;
            }
            return true;
        }

        /// <summary>
        /// 根据projectID 获取该项目 RelationShip 的所有InstanceId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public static List<string> FromCacheRelationShipInstanceIds(Guid projectGuid)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return null;
            string projectId = projectGuid.ToString("N");
            var x = redis.SetMembers(RELATIONSHIP_INSTANCEID_KEY + projectId);
            return x.ToStringArray().ToList();
        }

        /// <summary>
        /// 根据projectID 获取该项目 RelationShip,
        /// 特别注意：relationshipIds 数量为0 ，会获取该项目下所有数据
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public static List<Relationship> FromCacheRelationShip(Guid projectGuid, ICollection<Int64> relationshipIds = null)
        {
            List<Relationship> relationships = new List<Relationship>();
            List<string> relationshipInstanceIds = new List<string>();
            try
            {
                string projectId = projectGuid.ToString("N");
                if (relationshipIds != null && relationshipIds.Any())
                {
                    relationshipInstanceIds = Array.ConvertAll(relationshipIds.ToArray(), item => item.ToString()).ToList();
                }
                else
                {
                    relationshipInstanceIds = FromCacheRelationShipInstanceIds(projectGuid);
                }

                if (relationshipInstanceIds == null || !relationshipInstanceIds.Any())
                    return null;
                var redis = BimBaseServerCache.RedisCache;
                if (redis == null) return null;
                IBatch batch = redis.CreateBatch();

                var list = new List<Task<RedisValue>>();

                ParallelOptions options = new ParallelOptions();
                options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;

                Parallel.ForEach(relationshipInstanceIds.ToList(), options, i =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var tmp = batch.StringGetAsync(RELATIONSHIP_KEY + projectId + ":" + i);
                        list.Add(tmp);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });

                batch.Execute();
                var x = Task.WhenAll(list.ToArray());

                Parallel.ForEach(x.Result, options, m =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        if (!string.IsNullOrWhiteSpace(m))
                        {
                            var models = JsonSerializer.Deserialize<Relationship>(m);
                            if (models != null)
                            {
                                relationships.Add(models);
                            }
                        }
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }

                });
                return relationships;
            }
            catch (Exception ex)
            {
                Console.WriteLine("FromCacheRelationShip Error : " + ex.Message);
                return null;
            }

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="addDatas"></param>
        /// <param name="modifyDatas"></param>
        /// <param name="deleteDatas"></param>
        /// <param name="addRelationships"></param>
        /// <param name="modifyRelationships"></param>
        /// <param name="deleteRelationships"></param>
        /// <param name="versionNo">版本号，保存历史数据需要使用版本号</param>
        /// <returns></returns>
        //public static bool CacheCheckInDatas(Guid projectGuid,
        //                                    List<ModelData> addDatas,
        //                                    List<ModelData> modifyDatas,
        //                                    List<ModelData> deleteDatas,
        //                                    List<Relationship> addRelationships,
        //                                    List<Relationship> modifyRelationships,
        //                                    List<Relationship> deleteRelationships,
        //                                    int versionNo)
        //{
        //    var redis = BimBaseServerCache.RedisCache;
        //    if (redis == null) return false;
        //    IBatch batch = redis.CreateBatch();
        //    var list = new List<Task<bool>>();
        //    var list2 = new List<Task<RedisValue>>();

        //    //构造历史数据，将当前版本的历史数据保存到缓存，持久化数据库成功后，删除历史数据缓存
        //    List<HistoryData> historyDataList = new List<HistoryData>();
        //    List<HistoryRelationship> historyRelationshipList = new List<HistoryRelationship>();

        //    ParallelOptions options = new ParallelOptions();
        //    options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2;
        //    var projectId = projectGuid.ToString("N");
        //    if (addDatas.Any())
        //    {
        //        Parallel.ForEach(addDatas, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyAddData = s.CreateHistoryData(OperationRecordType.Add, versionNo);
        //                //要将版本号更新为最新的版本号，否则客户端删除操作会出现问题
        //                s.VersionNo = versionNo;
        //                //var task = batch.StringSetAsync(MODELDATA_KEY + s.InstanceId, JsonConvert.SerializeObject(s));
        //                var task = batch.StringSetAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
        //                list.Add(task);
        //                historyDataList.Add(historyAddData);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    if (modifyDatas.Any())
        //    {
        //        Parallel.ForEach(modifyDatas, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyModifyData = s.CreateHistoryData(OperationRecordType.Modify, versionNo);
        //                s.VersionNo = versionNo;
        //                //var task = batch.StringGetSetAsync(MODELDATA_KEY + s.InstanceId, JsonConvert.SerializeObject(s));
        //                var task = batch.StringGetSetAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
        //                list2.Add(task);
        //                historyDataList.Add(historyModifyData);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    if (deleteDatas.Any())
        //    {
        //        Parallel.ForEach(deleteDatas, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyDelData = s.CreateHistoryData(OperationRecordType.Delete, versionNo);
        //                var task = batch.KeyDeleteAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, CommandFlags.FireAndForget);
        //                list.Add(task);
        //                historyDataList.Add(historyDelData);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    if (addRelationships.Any())
        //    {
        //        Parallel.ForEach(addRelationships, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyAddRelationship = s.CreateHistoryRelationship(OperationRecordType.Add, versionNo);
        //                s.VersionNo = versionNo;
        //                //var task = batch.StringSetAsync(RELATIONSHIP_KEY + s.InstanceId, JsonConvert.SerializeObject(s));
        //                var task = batch.StringSetAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
        //                list.Add(task);
        //                historyRelationshipList.Add(historyAddRelationship);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    if (modifyRelationships.Any())
        //    {
        //        Parallel.ForEach(modifyRelationships, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyModifyRelationship = s.CreateHistoryRelationship(OperationRecordType.Modify, versionNo);
        //                s.VersionNo = versionNo;
        //                //var task = batch.StringGetSetAsync(RELATIONSHIP_KEY + s.InstanceId, JsonConvert.SerializeObject(s));
        //                var task = batch.StringGetSetAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
        //                list2.Add(task);
        //                historyRelationshipList.Add(historyModifyRelationship);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    if (deleteRelationships.Any())
        //    {
        //        Parallel.ForEach(deleteRelationships, options, (s) =>
        //        {
        //            try
        //            {
        //                _LockSlim.EnterWriteLock();
        //                var historyDelRelationship = s.CreateHistoryRelationship(OperationRecordType.Delete, versionNo);
        //                var task = batch.KeyDeleteAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, CommandFlags.FireAndForget);
        //                list.Add(task);
        //                historyRelationshipList.Add(historyDelRelationship);
        //            }
        //            finally
        //            {
        //                _LockSlim.ExitWriteLock();
        //            }
        //        });
        //    }

        //    batch.Execute();
        //    ////使用异步线程将历史数据插入缓存
        //    //Task.Factory.StartNew(() =>
        //    //{
        //    //    var a = SaveToCache(MODELDATA_HISTORY_KEY + projectId + ":" + versionNo, historyDataList);
        //    //    var b = SaveToCache(RELATIONSHIP_HISTORY_KEY + projectId + ":" + versionNo, historyRelationshipList);
        //    //}, TaskCreationOptions.LongRunning);

        //    //var redisServer = GetRedisServer();
        //    //redisServer.Save(SaveType.ForegroundSave, CommandFlags.PreferMaster);
        //    var x = Task.WhenAll(list.ToArray());
        //    var y = Task.WhenAll(list2.ToArray());
        //    if (!x.IsCompleted)
        //    {
        //        x.Wait();
        //    }
        //    if (!y.IsCompleted)
        //    {
        //        y.Wait();
        //    }
        //    //var redisServer = GetRedisServer();
        //    //redisServer.Save(SaveType.ForegroundSave, CommandFlags.PreferMaster);
        //    //使用异步线程将历史数据插入缓存
        //    Task.Factory.StartNew(() =>
        //    {
        //        var a = SaveToCache(MODELDATA_HISTORY_KEY + projectId + ":" + versionNo, historyDataList);
        //        var b = SaveToCache(RELATIONSHIP_HISTORY_KEY + projectId + ":" + versionNo, historyRelationshipList);
        //    }, TaskCreationOptions.LongRunning);
        //    if (x.IsCompleted && y.IsCompleted)
        //        return true;
        //    return false;
        //}




        /// <summary>
        /// 最新版本保存模型数据到缓存
        /// add by asdf 2018-08-08
        /// </summary>
        /// <param name="projectGuid">项目Id</param>
        /// <param name="addDatas">增加的数据</param>
        /// <param name="modifyDatas">修改的数据</param>
        /// <param name="deleteDatas">删除的数据</param>
        /// <param name="addRelationships">增加的关系数据</param>
        /// <param name="modifyRelationships">修改的关系数据</param>
        /// <param name="deleteRelationships">删除的关系书记</param>
        /// <param name="versionNo">版本号，保存历史数据需要使用版本号</param>
        /// <returns></returns>
        public static bool CacheCheckInDatas(Guid projectGuid,
                                            List<ModelData> addDatas,
                                            List<ModelData> modifyDatas,
                                            List<ModelData> deleteDatas,
                                            List<Relationship> addRelationships,
                                            List<Relationship> modifyRelationships,
                                            List<Relationship> deleteRelationships,
                                            int versionNo)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            IBatch batch = redis.CreateBatch();
            var list = new List<Task<bool>>();
            var list2 = new List<Task<RedisValue>>();

            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;
            var projectId = projectGuid.ToString("N");
            if (addDatas.Any())
            {
                Parallel.ForEach(addDatas, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        //要将版本号更新为最新的版本号，否则客户端删除操作会出现问题
                        s.VersionNo = versionNo;
                        var task = batch.StringSetAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (modifyDatas.Any())
            {
                Parallel.ForEach(modifyDatas, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        s.VersionNo = versionNo;
                        var task = batch.StringGetSetAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list2.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (deleteDatas.Any())
            {
                Parallel.ForEach(deleteDatas, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var task = batch.KeyDeleteAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, CommandFlags.FireAndForget);
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (addRelationships.Any())
            {
                Parallel.ForEach(addRelationships, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        s.VersionNo = versionNo;
                        var task = batch.StringSetAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (modifyRelationships.Any())
            {
                Parallel.ForEach(modifyRelationships, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        s.VersionNo = versionNo;
                        var task = batch.StringGetSetAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list2.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (deleteRelationships.Any())
            {
                Parallel.ForEach(deleteRelationships, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var task = batch.KeyDeleteAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, CommandFlags.FireAndForget);
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            batch.Execute();
            var x = Task.WhenAll(list.ToArray());
            var y = Task.WhenAll(list2.ToArray());
            if (!x.IsCompleted)
            {
                x.Wait();
            }
            if (!y.IsCompleted)
            {
                y.Wait();
            }

            if (x.IsCompleted && y.IsCompleted)
            {
                projectPool.AddOrUpdate(projectGuid, projectId, (proGuid, proId) => projectId);
                return true;
            }
            return false;
        }
        public static bool RemoveHistoryData(Guid projectGuid, int versionNo)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            string projectId = projectGuid.ToString("N");
            var x = redis.KeyDelete(MODELDATA_HISTORY_KEY + projectId + ":" + versionNo);
            var y = redis.KeyDelete(RELATIONSHIP_HISTORY_KEY + projectId + ":" + versionNo);
            if (x == true && y == true)
                return true;
            return false;
        }

        public static IServer GetRedisServer()
        {
            return BimBaseServerCache.Server;
        }

        public static bool IsRuning
        {
            get
            {
                //return false;
                var server = BimBaseServerCache.Server;
                if (server != null)
                {
                    if (!server.IsConnected)
                        return false;
                    try
                    {
                        //redis 延迟未获取关闭状态，发送ping 命令可能会抛出异常
                        var x = server.Ping();
                        if (x != null)
                        {
                            return true;
                        }
                        return false;
                    }
                    catch
                    {
                        return false;
                    }
                }
                return false;
            }
        }


        public static long CacheUsedMemory
        {
            get
            {
                var server = BimBaseServerCache.Server;
                if (server != null)
                {
                    if (!server.IsConnected)
                        return -1;
                    try
                    {
                        var x = server.Info("memory");
                        if (x != null && x.Any())
                        {
                            var memoryInfo = x.FirstOrDefault(a => a.Key.ToLower().Equals("memory"));
                            var usedMemoryProps = memoryInfo.FirstOrDefault(a => a.Key == "used_memory");

                            // KeyValuePair check null 
                            if (!usedMemoryProps.Equals(default(KeyValuePair<string, string>)))
                            {
                                long v = 0;
                                var b = long.TryParse(usedMemoryProps.Value, out v);
                                if (b)
                                    return v;  //byte / 1024 = kb   kb / 1024 = M 
                                return -1;
                            }
                        }
                        return -1;
                    }
                    catch
                    {
                        return -1;
                    }
                }
                return -1;
            }
        }

        public static bool HasMemory
        {
            get
            {
                try
                {
                    //获得物理内存 add refrence : System.Management.dll
                    ManagementClass mc = new ManagementClass("Win32_ComputerSystem");
                    ManagementObjectCollection moc = mc.GetInstances();
                    foreach (ManagementObject mo in moc)
                    {
                        if (mo["TotalPhysicalMemory"] != null)
                        {
                            var m_PhysicalMemory = long.Parse(mo["TotalPhysicalMemory"].ToString());
                            //物理内存的 45% ，超过限定不再往内存中添加数据
                            int memeryPercentage;
                            var x = int.TryParse(SettingsConfigHelper.AppSetting("redisMemeryPercentage") ?? "45", out memeryPercentage);
                            if (!x)
                            {
                                memeryPercentage = 45;
                            }
                            else
                            {
                                if (memeryPercentage <= 0)
                                    memeryPercentage = 45;
                                if (memeryPercentage >= 95)
                                    memeryPercentage = 45;
                            }

                            if (CacheUsedMemory >= (m_PhysicalMemory * memeryPercentage * 0.01))
                            {
                                return false;
                            }

                        }
                    }
                    return true;
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 未完成接口，目前未使用
        /// </summary>
        public static List<IGrouping<string, KeyValuePair<string, string>>> CacheServerInfo
        {
            get
            {
                var server = BimBaseServerCache.Server;
                if (server != null)
                {
                    if (!server.IsConnected)
                        return null;
                    var x = server.Info("memory");

                    //var memoryInfo = x.FirstOrDefault(a => a.Key.ToLower().Equals("memory"));
                    //var usedMemory = memoryInfo.FirstOrDefault(a => a.Key == "used_memory");



                    foreach (var k in x)
                    {
                        if (k.Key.ToLower().Equals("memory"))
                        {
                            foreach (var m in k)
                            {
                                var key = m.Key;
                                var v = m.Value;
                            }
                            //var obj = new Server();
                            //var props = obj.GetType().GetProperties();

                            //var c = k.Select(a => new Server {
                            //    arch_bits = a.Value.Select(v) (a.Key == "arch_bits")
                            //});



                        }
                    }


                    return x.ToList();
                }
                return null;
            }
        }

        /// <summary>
        /// 从数据库加载指定项目到缓存服务
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="modelDatas"></param>
        /// <param name="relationships"></param>
        /// <returns></returns>
        public static bool CacheDatasFromDataBase(Guid projectGuid, List<ModelData> modelDatas, List<Relationship> relationships)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            string projectId = projectGuid.ToString("N");
            List<long> modelInstanceIds = new List<long>();
            List<long> relationshipInstanceIds = new List<long>();

            bool a = false;
            bool b = false;

            //缓存DomainClass
            var domainClassSearchDtoList = (modelDatas ?? new List<ModelData>())
                                            .Select(i => new { i.InstanceId, i.DomainClassName })
                                            .GroupBy(d => d.DomainClassName, d => d.InstanceId,
                                                    (key, instanceIds) => new DomainClassSearchDto
                                                    {
                                                        DomainClassName = key,
                                                        InstanceIdList = instanceIds.ToList()
                                                    }).ToList();

            //缓存Domain
            var domainSearchDtoList = (modelDatas ?? new List<ModelData>())
                                            .Select(i => new { i.InstanceId, i.Domain })
                                            .GroupBy(d => d.Domain, d => d.InstanceId,
                                                    (key, instanceIds) => new DomainSearchDto
                                                    {
                                                        DomainId = key,
                                                        InstanceIdList = instanceIds.ToList()
                                                    }).ToList();

            IBatch batch = redis.CreateBatch();
            var list = new List<Task<bool>>();
            var list2 = new List<Task<long>>();
            ParallelOptions options = new ParallelOptions();
            options.MaxDegreeOfParallelism = Environment.ProcessorCount / 2 > 0 ? Environment.ProcessorCount / 2 : 1;

            List<RelatedSearchDto> relatedSearchDto = new List<RelatedSearchDto>();

            if (modelDatas.Any())
            {
                Parallel.ForEach(modelDatas, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        modelInstanceIds.Add(s.InstanceId);
                        var task = batch.StringSetAsync(MODELDATA_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (relationships.Any())
            {
                Parallel.ForEach(relationships, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        relationshipInstanceIds.Add(s.InstanceId);
                        relatedSearchDto.Add(new RelatedSearchDto
                        {
                            InstanceId = s.InstanceId,
                            SourceID = s.SourceID,
                            TargetID = s.TargetID,
                            IsForward = s.IsForward,
                            Type = s.Type
                        });
                        var task = batch.StringSetAsync(RELATIONSHIP_KEY + projectId + ":" + s.InstanceId, JsonSerializer.Serialize(s));
                        list.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (relatedSearchDto.Any())
            {
                Parallel.ForEach(relatedSearchDto, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var data = JsonSerializer.Serialize(s);
                        var task = batch.SetAddAsync(RELATED_SOURCEID_KEY + projectId + ":" + s.SourceID, data);
                        var task2 = batch.SetAddAsync(RELATED_TARGETID_KEY + projectId + ":" + s.TargetID, data);
                        list.Add(task);
                        list.Add(task2);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (domainClassSearchDtoList != null && domainClassSearchDtoList.Any())
            {
                Parallel.ForEach(domainClassSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetAddAsync(MODELDATA_DOMAINCLASS_KEY + projectId + ":" + s.DomainClassName, redisValues);
                        list2.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }

            if (domainSearchDtoList != null && domainSearchDtoList.Any())
            {
                Parallel.ForEach(domainSearchDtoList, options, (s) =>
                {
                    try
                    {
                        _LockSlim.EnterWriteLock();
                        var redisValues = Array.ConvertAll(s.InstanceIdList.ToArray(), item => (RedisValue)item);
                        var task = redis.SetAddAsync(MODELDATA_DOMAIN_KEY + projectId + ":" + s.DomainId, redisValues);
                        list2.Add(task);
                    }
                    finally
                    {
                        _LockSlim.ExitWriteLock();
                    }
                });
            }


            if (modelInstanceIds.Any())
                a = CacheModelDataInstanceIds(projectGuid, modelInstanceIds);
            if (relationshipInstanceIds.Any())
                b = CacheRelationShipInstanceIds(projectGuid, relationshipInstanceIds);
            else   //没有关系数据的模型
                b = true;

            if (a && b)
            {
                batch.Execute();
                var x = Task.WhenAll(list.ToArray());
                var y = Task.WhenAll(list2.ToArray());
                if (!x.IsCompleted)
                {
                    x.Wait();
                }
                if (!y.IsCompleted)
                {
                    y.Wait();
                }
                if (x.IsCompleted && y.IsCompleted)
                {
                    projectPool.AddOrUpdate(projectGuid, projectId, (proGuid, proId) => projectId);
                    return true;
                }
            }

            return false;
        }

        public static bool RemoveProjectData(Guid projectGuid)
        {
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            string projectId = projectGuid.ToString("N");
            var server = BimBaseServerCache.Server;

            var mKeys = server.Keys(0, MODELDATA_KEY + projectId + ":" + "*");
            var mdomainClasskeys = server.Keys(0, MODELDATA_DOMAINCLASS_KEY + projectId + ":" + "*");
            var mdomainkeys = server.Keys(0, MODELDATA_DOMAIN_KEY + projectId + ":" + "*");
            var rKeys = server.Keys(0, RELATIONSHIP_KEY + projectId + ":" + "*");
            var sourceKeys = server.Keys(0, RELATED_SOURCEID_KEY + projectId + ":" + "*");
            var targetKeys = server.Keys(0, RELATED_TARGETID_KEY + projectId + ":" + "*");

            var m = redis.KeyDeleteAsync(mKeys.ToArray());
            var dc = redis.KeyDeleteAsync(mdomainClasskeys.ToArray());
            var d = redis.KeyDeleteAsync(mdomainkeys.ToArray());
            var r = redis.KeyDeleteAsync(rKeys.ToArray());
            var s = redis.KeyDeleteAsync(sourceKeys.ToArray());
            var t = redis.KeyDeleteAsync(targetKeys.ToArray());

            var x = redis.KeyDeleteAsync(MODELDATA_INSTANCEID_KEY + projectId);
            var y = redis.KeyDeleteAsync(RELATIONSHIP_INSTANCEID_KEY + projectId);

            var delTask = Task.WhenAll(m, dc, d, r, s, t, x, y);

            if (!delTask.IsCompleted)
            {
                delTask.Wait();
            }

            if (projectPool.ContainsKey(projectGuid))
            {
                string removedProjectId;
                var isRemovedSuccess = projectPool.TryRemove(projectGuid, out removedProjectId);
                return isRemovedSuccess;
            }

            return true;
        }

        //public static bool ProjectInCache(string projectId)
        //{
        //    var redis = BimBaseServerCache.RedisCache;
        //    if (redis == null) return false;
        //    var x = redis.KeyExists(MODELDATA_INSTANCEID_KEY + projectId);
        //    return x;
        //}

        /// <summary>
        /// 新版本判断工程是否已经加载到缓存
        /// add by asdf 2018-08-08
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <returns></returns>
        public static bool ProjectInCache(Guid projectGuid)
        {
            if (projectPool.ContainsKey(projectGuid)) return true;
            //关闭服务器后，缓存没有关闭，下次启动服务器需要判断缓存服务器是否已经有项目数据
            var redis = BimBaseServerCache.RedisCache;
            if (redis == null) return false;
            string projectId = projectGuid.ToString("N");
            var x = redis.KeyExists(MODELDATA_INSTANCEID_KEY + projectId);
            return x;
        }
    }


    public class RelatedSearchDto
    {
        public Int64 InstanceId { get; set; }
        public Int64 SourceID { get; set; }

        //public int SourceDomainClassID { get; set; }

        public Int64 TargetID { get; set; }

        //public int TargetDomainClassID { get; set; }

        public ModelDomain.RelationshipType Type { get; set; }

        public bool IsForward { get; set; }
    }

    public class DomainClassSearchDto
    {
        public string DomainClassName { get; set; }
        public List<Int64> InstanceIdList { get; set; }
    }

    public class DomainSearchDto
    {
        public int DomainId { get; set; }
        public List<Int64> InstanceIdList { get; set; }
    }


}
