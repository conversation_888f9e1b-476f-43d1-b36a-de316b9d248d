using Autofac.Extensions.DependencyInjection;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Infrastructure.Database;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Principal;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Serilog.Core;
using Serilog.Events;
using Serilog.Enrichers.Span;
using Serilog.Sinks.MariaDB;
using Serilog.Sinks.MariaDB.Extensions;
using BimBase.Api.Config;
using Microsoft.Extensions.Options;
using Serilog.Sinks.SystemConsole.Themes;

namespace BimBase.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {

            //CreateHostBuilder(args).Build().Run();
            var host = CreateHostBuilder(args).Build();
            //测试dotnet的环境变量是否存在，如不存在会添加环境变量
            testDotnetPath();
            // 获取环境名称（自动处理null值）
            var env = host.Services.GetRequiredService<IHostEnvironment>();

            // 输出实际加载的配置文件 
            Console.WriteLine($"加载的配置源: {string.Join(" + ",
    ((IConfigurationRoot)host.Services.GetRequiredService<IConfiguration>())
    .Providers.Where(p => p is FileConfigurationProvider)
    .Select(p => ((FileConfigurationProvider)p).Source.Path))}");
            // ================================
            // 数据库版本管理已迁移到 TeamDbInitializer.Initialize() 中执行
            // ManageDatabaseVersioning(host);

            Log.Information("logTestInformation");
            Log.Debug("logTestDebug");
            Log.Warning("logTestWarning");
            Log.Error("logTestError");
            Log.Fatal("logTestFatal");
            string testMsg = "someTestMessage";
            Log.Fatal("logTestFatal:{someMessage}", testMsg);
            Log.Logger.LogWithCallerInfo(LogEventLevel.Error, "LogWithCallerInfoTest");
            CreateDbIfNotExists(host);
            host.Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    config
                        .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
                        .AddJsonFile("appsettings.json", true, true)
                        .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true)
                        .AddEnvironmentVariables();
                })
                .UseSerilog((hostingContext, services, loggerConfiguration) => loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration)
                    .Enrich.FromLogContext()
                    .Enrich.WithSpan()
                    .Enrich.With<BimLoggerEnricher>()
                    //.WriteTo.Async(a => a.Console(
                    //    theme: SystemConsoleTheme.Literate,
                    //    outputTemplate: "{Timestamp:HH:mm:ss} [{Level:u3}] {Message}{NewLine}{Exception}"
                    //), bufferSize: 500)  // 控制台异步缓冲区
                    .WriteTo.Async(a => a.File(
                        path: "logs/app/app-.txt",
                        rollingInterval: RollingInterval.Day,
                        fileSizeLimitBytes: 10 * 1024 * 1024,
                        retainedFileTimeLimit: TimeSpan.FromDays(30),
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {SourceContext}{NewLine}{Message}{NewLine}{Exception}{NewLine}",
                        rollOnFileSizeLimit: true,
                        buffered: false,
                        flushToDiskInterval: TimeSpan.FromSeconds(10)
                    ), bufferSize: 1000)
                    .WriteTo.Async(logger => logger.MariaDB(
                        connectionString: hostingContext.Configuration.GetConnectionString("LoggerConnection"),
                        tableName: "pbimlogs",
                        autoCreateTable: true,
                        options: new Serilog.Sinks.MariaDB.MariaDBSinkOptions
                        {
                            PropertiesToColumnsMapping = new Dictionary<string, string>
                            {
                                ["Timestamp"] = "Timestamp",
                                ["Level"] = "LogLevel",
                                ["ProcessId"] = "ProcessId",
                                ["ThreadId"] = "ThreadId",
                                ["UserId"] = "UserId",
                                ["Exception"] = "Exception",
                                ["Message"] = "Message",
                                ["SourceContext"] = "SourceContext",
                                ["LogType"] = "LogType",
                                ["Method"] = "Method",
                                ["CallerFilePath"] = "FilePath",
                                ["CallerMemberName"] = "FuctionName",
                                ["CallerLineNumber"] = "LineNumber"
                            },
                            TimestampInUtc = false
                        }
                    )))
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });

        private static void CreateDbIfNotExists(IHost host)
        {
            try
            {
                // 设置ProjectRepository的全局服务提供者，使用根ServiceProvider确保不会被销毁
                ProjectRepository.SetGlobalServiceProvider(host.Services);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置全局服务提供者失败: {ex.Message}");
                throw;
            }

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;

                try
                {
                    
                    // 初始化全局配置提供者
                    DatabaseVersioningOptionsProvider.Initialize(services);
                    
                    var context = services.GetRequiredService<TeamDbContext>();
                    TeamDbInitializer.Initialize(context, services);
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<Program>>();
                    logger.LogError(ex, "An error occurred creating TeamDb.");
                }
                try
                {
                    var context = services.GetRequiredService<LibraryDbContext>();
                    LibraryDbInitializer.Initialize(context,services);
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<Program>>();
                    logger.LogError(ex, "An error occurred creating LibraryDb.");
                }
                try
                {
                    
                    var context = services.GetRequiredService<LogDbContext>();
                    //logger.LogError("LogDbContext initdsfasdfasdfasdfasd");
                    LogDbInitializer.Initialize(context);
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<Program>>();
                    logger.LogError(ex, "An error occurred creating logdb");
                }
                
                // 注意：ModelDb不在这里初始化，因为它是项目特定的，在ProjectRepository中按需初始化
            }
        }

        /// <summary>
        /// 根据配置管理数据库版本。
        /// </summary>
        /// <param name="host">应用程序主机</param>
        private static void ManageDatabaseVersioning(IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var logger = services.GetRequiredService<ILogger<Program>>();
                var options = services.GetRequiredService<IOptions<DatabaseVersioningOptions>>().Value;

                if (!options.AutoExecute)
                {
                    logger.LogInformation("数据库自动版本控制已禁用 (DatabaseVersioningOptions.AutoExecute is false)，跳过执行。");
                    return;
                }
                
                try
                {
                    logger.LogInformation("开始执行数据库版本控制 (基于 DatabaseVersioningOptions.AutoExecute)...");
                    var dbUpgradeService = services.GetRequiredService<DatabaseUpgradeService>();
                    
                    // 调用无参数的 ApplyDatabaseVersioningAsync
                    // 该服务内部会从配置中获取目标版本
                    dbUpgradeService.ApplyDatabaseVersioningAsync().GetAwaiter().GetResult();
                    
                    logger.LogInformation("数据库版本控制执行完毕。");
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "执行数据库版本管理时发生错误。应用程序将无法启动。");
                    throw; 
                }
            }
        }

        /// <summary>
        /// 测试dotnet的环境变量是否存在，如不存在会添加环境变量
        /// </summary>
        private static void testDotnetPath()
        {
            Process process = new Process();
            try
            {
                System.Environment.SetEnvironmentVariable("PATH", System.Environment.GetEnvironmentVariable("PATH") + ";C:\\Program Files\\dotnet\\");
                
                process.StartInfo.FileName = "dotnet";
                
                process.Start();
            }
            catch (Exception ex)
            {
                Log.Error($"{ex.Message}");
                Log.Information($"=================");
                Log.Warning($"1. 请关闭当前应用程序");
                Log.Warning($"2. 手动运行如下程序：");
                Log.Warning($"{Environment.ProcessPath}");
                Log.Information($"=================");

            }
        }
    }

    public class BimLoggerEnricher : ILogEventEnricher
    {
        public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
        {
            // 获取当前进程 ID
            var processId = Process.GetCurrentProcess().Id;
            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("ProcessId", processId));

            // 获取当前线程 ID
            var threadId = Thread.CurrentThread.ManagedThreadId;
            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("ThreadId", threadId));

            // 获取当前用户 ID
            // 截止到当前，获取当前用户函数未提供，此处使用的是WindowsID
            var userId = WindowsIdentity.GetCurrent()?.User?.Value;
            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("UserId", userId ?? "Unknown"));

            // 获取类名和函数名
            // 从日志消息中提取调用者信息
            if (logEvent.MessageTemplate.Text.Contains("{CallerMemberName}") &&
                logEvent.MessageTemplate.Text.Contains("{CallerFilePath}") &&
                logEvent.MessageTemplate.Text.Contains("{CallerLineNumber}"))
            {
                foreach (var property in logEvent.Properties)
                {
                    switch (property.Key)
                    {
                        case "CallerMemberName":
                            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("CallerMemberName", 
                                property.Value.ToString()));
                            break;
                        case "CallerFilePath":
                            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("CallerFilePath", 
                                property.Value.ToString()));
                            break;
                        case "CallerLineNumber":
                            logEvent.AddOrUpdateProperty(propertyFactory.CreateProperty("CallerLineNumber", 
                                property.Value.ToString()));
                            break;
                    }
                }
            }
        }
    }

    public static class LoggerExtensions
    {
        //日志类扩展
        public static void LogWithCallerInfo(this Serilog.ILogger logger, LogEventLevel level, string messageTemplate,
            [CallerMemberName] string functionName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            logger.Write(level, messageTemplate + " {CallerMemberName} {CallerFilePath} {CallerLineNumber}",
                functionName, sourceFilePath, sourceLineNumber);
        }
    }
}
