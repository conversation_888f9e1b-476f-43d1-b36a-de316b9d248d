using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Text;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Serilog;
using BimBase.LogConsumer.Data;
using System.Net;
using Pomelo.EntityFrameworkCore.MySql;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using BimBase.LogConsumer.Services;
using BimBase.LogConsumer.Models;
using BimBase.LogConsumer.Constants;
using MySqlConnector;
using System.Reflection;
using System.Linq;

namespace BimBase.LogConsumer
{
    public class Program
    {
        public static void Main(string[] args)
        {
            // 创建并启动应用程序主机
            CreateHostBuilder(args).Build().Run();
        }
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    // 配置应用程序的配置源，包括JSON文件和环境变量
                    config
                        .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
                        .AddJsonFile("appsettings.json", optional: false)
                        .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", optional: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureServices((hostContext, services) =>
                {
                    // 注册数据库上下文
                    services.AddDbContext<LogDbContext>(options =>
                        options.UseMySql(
                            hostContext.Configuration.GetConnectionString("LoggerConnection"),
                            ServerVersion.AutoDetect(hostContext.Configuration.GetConnectionString("LoggerConnection")),
                            mySqlOptions => mySqlOptions
                                .EnableRetryOnFailure(
                                    maxRetryCount: 3,
                                    maxRetryDelay: TimeSpan.FromSeconds(5),
                                    errorNumbersToAdd: null)
                        ));

                    // 确保数据库已创建
                    using (var scope = services.BuildServiceProvider().CreateScope())
                    {
                        var dbContext = scope.ServiceProvider.GetRequiredService<LogDbContext>();
                        dbContext.Database.EnsureCreated();
                    }

                    // 注册日志消费服务为后台服务
                    services.AddHostedService<LogConsumerService>();

                    services.AddSingleton<ConnectionFactory>(sp =>
                    {
                        var factory = new ConnectionFactory()
                        {
                            HostName = hostContext.Configuration["LogSettings:RabbitMQ:HostName"] ?? "localhost",
                            UserName = hostContext.Configuration["LogSettings:RabbitMQ:UserName"] ?? "guest",
                            Password = hostContext.Configuration["LogSettings:RabbitMQ:Password"] ?? "guest",
                            VirtualHost = hostContext.Configuration["LogSettings:RabbitMQ:VirtualHost"] ?? "/",
                            Port = int.Parse(hostContext.Configuration["LogSettings:RabbitMQ:Port"] ?? "5672"),
                            DispatchConsumersAsync = true // 异步消费
                        };
                        return factory;
                    });
                })
                .UseSerilog((hostingContext, loggerConfiguration) => loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration));// 配置Serilog日志记录
    }

    /// <summary>
    /// 日志消费服务，从RabbitMQ接收日志消息并存储到数据库
    /// </summary>
    public class LogConsumerService : BackgroundService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<LogConsumerService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private IConnection _connection;// RabbitMQ连接
        private IModel _channel;// RabbitMQ通道

        public LogConsumerService(
            IConfiguration configuration,
            ILogger<LogConsumerService> logger,
            IServiceProvider serviceProvider)
        {
            _configuration = configuration;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// 服务的主要执行方法，初始化RabbitMQ连接并开始消费消息
        /// </summary>
        /// <param name="stoppingToken"></param>
        /// <returns></returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("[服务启动] 日志消费者服务开始启动");
            
            try
            {
                await InitializeRabbitMQAsync(stoppingToken);
                _logger.LogInformation("[服务启动] 日志消费者服务启动成功，开始监听队列消息");

                // 启动队列监控任务
                var monitoringTask = Task.Run(async () => await MonitorQueuesAsync(stoppingToken), stoppingToken);

                // 保持服务运行直到收到停止信号
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, stoppingToken);
                }

                // 等待监控任务完成
                await monitoringTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[服务启动] 日志消费者服务启动失败: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 初始化RabbitMQ连接、通道和消费者
        /// </summary>
        private async Task InitializeRabbitMQAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("[MQ初始化] 开始初始化RabbitMQ");
            
            // 从配置中获取RabbitMQ连接参数，设置自动恢复
            var factory = new ConnectionFactory
            {
                HostName = _configuration["LogSettings:RabbitMQ:HostName"] ?? "localhost",
                UserName = _configuration["LogSettings:RabbitMQ:UserName"] ?? "guest",
                Password = _configuration["LogSettings:RabbitMQ:Password"] ?? "guest",
                VirtualHost = _configuration["LogSettings:RabbitMQ:VirtualHost"] ?? "/",
                Port = int.Parse(_configuration["LogSettings:RabbitMQ:Port"] ?? "5672"),
                AutomaticRecoveryEnabled = true,
                NetworkRecoveryInterval = TimeSpan.FromSeconds(10)
            };

            // 创建连接和通道
            _connection = factory.CreateConnection();
            _channel = _connection.CreateModel();
            
            _logger.LogInformation("[MQ初始化] 连接成功: {HostName}:{Port}", factory.HostName, factory.Port);

            // 设置QoS，提高并发处理能力
            _channel.BasicQos(prefetchSize: 0, prefetchCount: 10, global: false);

            // 从配置获取交换器、队列和路由键信息
            var exchangeName = LogConstants.EXCHANGE_NAME;
            var interfaceQueue = LogConstants.INTERFACE_QUEUE;
            var errorQueue = LogConstants.ERROR_QUEUE;

            // 声明持久化的Topic交换器
            _channel.ExchangeDeclare(
                exchange: exchangeName,
                type: ExchangeType.Topic,
                durable: true,
                autoDelete: false);

            // 声明并绑定接口日志队列
            var interfaceQueueDeclare = _channel.QueueDeclare(
                queue: interfaceQueue,
                durable: true,
                exclusive: false,
                autoDelete: false);

            _channel.QueueBind(
                queue: interfaceQueue,
                exchange: exchangeName,
                routingKey: "log.interface.#");

            // 声明并绑定错误日志队列
            var errorQueueDeclare = _channel.QueueDeclare(
                queue: errorQueue,
                durable: true,
                exclusive: false,
                autoDelete: false);

            _channel.QueueBind(
                queue: errorQueue,
                exchange: exchangeName,
                routingKey: "log.*.error.#");

            _logger.LogInformation("[MQ监控] 队列状态 - 接口队列: {InterfaceQueueName}, 消息数: {InterfaceMessageCount}, 消费者数: {InterfaceConsumerCount}", 
                interfaceQueue, interfaceQueueDeclare.MessageCount, interfaceQueueDeclare.ConsumerCount);
            _logger.LogInformation("[MQ监控] 队列状态 - 错误队列: {ErrorQueueName}, 消息数: {ErrorMessageCount}, 消费者数: {ErrorConsumerCount}", 
                errorQueue, errorQueueDeclare.MessageCount, errorQueueDeclare.ConsumerCount);

            // 创建接口日志消费者
            var interfaceConsumer = new EventingBasicConsumer(_channel);

            interfaceConsumer.Received += async (model, ea) =>
            {
                MQInterfaceLog log = null;
                try
                {
                    var body = ea.Body.ToArray();
                    var message = Encoding.UTF8.GetString(body);

                    using var scope = _serviceProvider.CreateScope();
                    var scopedDbContext = scope.ServiceProvider.GetRequiredService<LogDbContext>();

                    await scopedDbContext.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
                    {
                        log = JsonSerializer.Deserialize<MQInterfaceLog>(message);
                        if (log != null)
                        {
                            // 使用Debug级别记录详细过程
                            _logger.LogDebug("[MQ消费] 收到消息, RequestId: {RequestId}, 阶段: {LogStage}", 
                                log.RequestId, log.LogStage);

                            var requestId = log.RequestId?.Trim();
                            var logType = typeof(MQInterfaceLog);
                            var props = logType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                .Where(p => p.Name != "Id").ToList();

                            string updates;
                            if (log.LogStage == "request")
                            {
                                // 只更新 RequestTime
                                updates = $"request_time = IFNULL(VALUES(request_time), request_time)";
                            }
                            else if (log.LogStage == "response")
                            {
                                var excludeUpdateFields = new[] { "Id", "RequestId", "RequestTime", "AddTime" };
                                var updateFields = props.Where(p => !excludeUpdateFields.Contains(p.Name)).Select(p => p.Name).ToList();
                                updates = string.Join(", ", updateFields.Select(f => $"{ToDbColumnName(f)} = IFNULL(VALUES({ToDbColumnName(f)}), {ToDbColumnName(f)})"));
                            }
                            else if (log.LogStage == "middle")
                            {
                                var middleFields = new[] { "MiddleTotalMilliseconds", "MiddleResponseTime" };
                                updates = string.Join(", ", middleFields.Select(f => $"{ToDbColumnName(f)} = IFNULL(VALUES({ToDbColumnName(f)}), {ToDbColumnName(f)})"));
                            }
                            else
                            {
                                // 其他阶段，什么都不做
                                updates = "id=id";
                                _logger.LogWarning("[MQ消费] 未知阶段 {LogStage}, RequestId: {RequestId}", 
                                    log.LogStage, log.RequestId);
                            }

                            var sql = $@"
INSERT INTO mq_interface_logs ({string.Join(", ", props.Select(p => ToDbColumnName(p.Name)))})
VALUES ({string.Join(", ", props.Select(p => "@" + p.Name))})
ON DUPLICATE KEY UPDATE {updates};";

                            var parameters = props.Select(p => new MySqlParameter($"@{p.Name}", p.GetValue(log) ?? (object)DBNull.Value)).ToArray();
                            
                            // 添加重试机制
                            const int maxRetries = 3;
                            for (int attempt = 1; attempt <= maxRetries; attempt++)
                            {
                                try
                                {
                                    await scopedDbContext.Database.ExecuteSqlRawAsync(sql, parameters);
                                    break; // 成功执行，退出重试循环
                                }
                                catch (Exception dbEx)
                                {
                                    if (attempt == maxRetries)
                                    {
                                        _logger.LogError(dbEx, "[MQ消费] 数据库操作最终失败, RequestId: {RequestId}, 阶段: {LogStage}, 已重试 {MaxRetries} 次", 
                                            log.RequestId, log.LogStage, maxRetries);
                                        throw;
                                    }
                                    
                                    _logger.LogWarning(dbEx, "[MQ消费] 数据库操作异常，准备重试, RequestId: {RequestId}, 阶段: {LogStage}, 尝试次数: {Attempt}", 
                                        log.RequestId, log.LogStage, attempt);
                                    
                                    await Task.Delay(100 * attempt); // 指数退避
                                }
                            }
                            
                            // 只在Info级别记录关键完成信息
                            _logger.LogInformation("[MQ消费] {LogStage} 阶段完成, RequestId: {RequestId}", 
                                log.LogStage, log.RequestId);
                        }
                        else
                        {
                            _logger.LogWarning("[MQ消费] 消息反序列化失败, DeliveryTag: {DeliveryTag}", ea.DeliveryTag);
                        }
                    });

                    _channel.BasicAck(ea.DeliveryTag, false);
                    // 使用Debug级别记录确认信息
                    _logger.LogDebug("[MQ确认] 消息确认成功, RequestId: {RequestId}, 阶段: {LogStage}", 
                        log?.RequestId, log?.LogStage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[MQ消费] 处理失败, RequestId: {RequestId}, 阶段: {LogStage}, 异常: {ErrorMessage}", 
                        log?.RequestId, log?.LogStage, ex.Message);
                    _channel.BasicNack(ea.DeliveryTag, false, true);
                    _logger.LogWarning("[MQ确认] 消息拒绝并重新入队, RequestId: {RequestId}, 阶段: {LogStage}", 
                        log?.RequestId, log?.LogStage);
                }
            };

            // 创建错误日志消费者
            var errorConsumer = new EventingBasicConsumer(_channel);
            errorConsumer.Received += async (model, ea) =>
            {
                MQErrorLog log = null;
                try
                {
                    var body = ea.Body.ToArray();
                    var message = Encoding.UTF8.GetString(body);

                    using var scope = _serviceProvider.CreateScope();
                    var scopedDbContext = scope.ServiceProvider.GetRequiredService<LogDbContext>();

                    await scopedDbContext.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
                    {
                        log = JsonSerializer.Deserialize<MQErrorLog>(message);
                        if (log != null)
                        {
                            _logger.LogInformation("[MQ消费] 收到错误日志, ErrorId: {ErrorId}, RequestId: {RequestId}", 
                                log.ErrorId, log.RequestId);

                            _logger.LogInformation("[MQ消费] 处理错误日志, ErrorId: {ErrorId}, RequestId: {RequestId}", 
                                log.ErrorId, log.RequestId);

                            // 生成 ErrorId（如果未提供）
                            if (string.IsNullOrEmpty(log.ErrorId))
                            {
                                log.ErrorId = Guid.NewGuid().ToString("N");
                            }

                            await scopedDbContext.MQErrorLogs.AddAsync(log);
                            await scopedDbContext.SaveChangesAsync();
                            
                            _logger.LogInformation("[MQ消费] 错误日志完成, ErrorId: {ErrorId}, RequestId: {RequestId}", 
                                log.ErrorId, log.RequestId);
                        }
                        else
                        {
                            _logger.LogWarning("[MQ消费] 错误日志反序列化失败, DeliveryTag: {DeliveryTag}", ea.DeliveryTag);
                        }
                    });

                    _channel.BasicAck(ea.DeliveryTag, false);
                    _logger.LogInformation("[MQ确认] 错误日志确认成功, ErrorId: {ErrorId}, RequestId: {RequestId}", 
                        log?.ErrorId, log?.RequestId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[MQ消费] 错误日志处理失败, RequestId: {RequestId}, 异常: {ErrorMessage}", 
                        log?.RequestId, ex.Message);
                    _channel.BasicNack(ea.DeliveryTag, false, true);
                    _logger.LogWarning("[MQ确认] 错误日志拒绝并重新入队, RequestId: {RequestId}", 
                        log?.RequestId);
                }
            };

            // 启动消费者
            _channel.BasicConsume(queue: interfaceQueue, autoAck: false, consumer: interfaceConsumer);
            _channel.BasicConsume(queue: errorQueue, autoAck: false, consumer: errorConsumer);

            _logger.LogInformation("[MQ初始化] 消费者启动完成");

            // 注册连接关闭事件处理
            _connection.ConnectionShutdown += (sender, e) =>
            {
                _logger.LogWarning("[MQ连接] 连接关闭, 原因: {ShutdownReason}, 尝试重连", e.Initiator);
                if (!stoppingToken.IsCancellationRequested)
                {
                    Task.Run(async () => await InitializeRabbitMQAsync(stoppingToken));
                }
            };
        }

        /// <summary>
        /// 优雅停止服务，关闭RabbitMQ连接和通道
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("[服务停止] 开始停止日志消费者服务");
            
            try
            {
                _channel?.Close();
                _connection?.Close();
                _logger.LogInformation("[服务停止] 日志消费者服务停止成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[服务停止] 停止日志消费者服务时发生错误: {ErrorMessage}", ex.Message);
            }
            await base.StopAsync(cancellationToken);
        }

        // 工具方法：属性名转下划线风格
        private static string ToDbColumnName(string propName)
        {
            return string.Concat(propName.Select((c, i) => i > 0 && char.IsUpper(c) ? "_" + char.ToLower(c) : char.ToLower(c).ToString()));
        }

        /// <summary>
        /// 定期监控队列状态
        /// </summary>
        private async Task MonitorQueuesAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(TimeSpan.FromMinutes(10), stoppingToken); // 每10分钟监控一次

                    if (_channel != null && !_channel.IsClosed)
                    {
                        var interfaceQueueDeclare = _channel.QueueDeclarePassive(LogConstants.INTERFACE_QUEUE);
                        var errorQueueDeclare = _channel.QueueDeclarePassive(LogConstants.ERROR_QUEUE);

                        _logger.LogInformation("[MQ监控] 队列状态监控 - 接口队列: {InterfaceQueueName}, 消息数: {InterfaceMessageCount}, 消费者数: {InterfaceConsumerCount}", 
                            LogConstants.INTERFACE_QUEUE, interfaceQueueDeclare.MessageCount, interfaceQueueDeclare.ConsumerCount);
                        _logger.LogInformation("[MQ监控] 队列状态监控 - 错误队列: {ErrorQueueName}, 消息数: {ErrorMessageCount}, 消费者数: {ErrorConsumerCount}", 
                            LogConstants.ERROR_QUEUE, errorQueueDeclare.MessageCount, errorQueueDeclare.ConsumerCount);

                        // 检查消息积压
                        if (interfaceQueueDeclare.MessageCount > 1000)
                        {
                            _logger.LogWarning("[MQ监控] 接口队列消息积压严重: {MessageCount} 条消息", interfaceQueueDeclare.MessageCount);
                        }
                        if (errorQueueDeclare.MessageCount > 100)
                        {
                            _logger.LogWarning("[MQ监控] 错误队列消息积压: {MessageCount} 条消息", errorQueueDeclare.MessageCount);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[MQ监控] 队列监控异常: {ErrorMessage}", ex.Message);
                }
            }
        }
    }
}