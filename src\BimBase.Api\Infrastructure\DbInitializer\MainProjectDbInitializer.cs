using BimBase.Api.Infrastructure.DbInitializer.MainProjectDb;
using BimBase.Api.Config;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    /// <summary>
    /// MainProjectDb数据库初始化器
    /// 继承通用数据库初始化器基类，负责MainProjectDb的创建、升级和数据初始化
    /// </summary>
    public class MainProjectDbInitializer : AbstractDatabaseInitializer<MainProjectDbContext>
    {        
        /// <summary>
        /// 静态初始化方法，保持原有接口不变
        /// </summary>
        public static void Initialize(MainProjectDbContext context, IServiceProvider serviceProvider)
        {
            Initialize<MainProjectDbInitializer>(context, serviceProvider);
        }
        
        #region 实现抽象基类方法
        
        protected override string GetDatabaseName() => "MainProjectDb";
        
        protected override async Task<bool> DatabaseVersionTableExistsAsync(MainProjectDbContext context)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = 'databaseversion'";

                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }
        
        protected override async Task<bool> HasAnyVersionRecordsAsync(MainProjectDbContext context)
        {
            try
            {
                if (!await DatabaseVersionTableExistsAsync(context))
                {
                    return false;
                }

                return await context.DatabaseVersions.AnyAsync();
            }
            catch
            {
                return false;
            }
        }
        
        protected override async Task<string> GetCurrentVersionAsync(MainProjectDbContext context, ILogger logger)
        {
            try
            {
                if (!await DatabaseVersionTableExistsAsync(context))
                {
                    logger.LogInformation("MainProjectDb DatabaseVersion表不存在，当前版本为 v0");
                    return InitialVersion;
                }

                var latestVersion = await context.DatabaseVersions
                    .OrderByDescending(v => v.Version)
                    .Select(v => v.Version)
                    .FirstOrDefaultAsync();

                var currentVersion = latestVersion ?? InitialVersion;
                logger.LogInformation($"MainProjectDb当前版本为 {currentVersion}");
                return currentVersion;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "获取MainProjectDb当前版本时发生错误，默认为 v0");
                return InitialVersion;
            }
        }
        
        protected override async Task ExecuteUpgradePathWithManagerAsync(MainProjectDbContext context, ILogger logger, string currentVersion, string targetVersion)
        {
            logger.LogInformation($"开始使用升级管理器执行MainProjectDb升级路径: {currentVersion} -> {targetVersion}");
            
            var upgradeManager = new MainProjectDbUpgradeManager(logger);
            
            // 检查是否已经有活跃的事务
            var existingTransaction = context.Database.CurrentTransaction;
            
            if (existingTransaction != null)
            {
                // 如果已经有事务，直接使用现有事务
                logger.LogInformation("检测到现有事务，使用现有事务执行升级");
                
                // 根据版本关系计算升级或回滚路径
                if (IsUpgrade(currentVersion, targetVersion))
                {
                    var upgradePath = upgradeManager.CalculateUpgradePath(currentVersion, targetVersion);
                    await upgradeManager.ExecuteUpgradePathAsync(context, upgradePath);
                }
                else
                {
                    var rollbackPath = upgradeManager.CalculateRollbackPath(currentVersion, targetVersion);
                    await upgradeManager.ExecuteRollbackPathAsync(context, rollbackPath);
                }
                
                // 统一保存所有更改（不提交事务，让外层负责）
                await context.SaveChangesAsync();
                logger.LogInformation("MainProjectDb升级路径执行完成（使用现有事务）");
            }
            else
            {
                // 如果没有事务，创建新事务
                using var transaction = await context.Database.BeginTransactionAsync();
                try
                {
                    // 根据版本关系计算升级或回滚路径
                    if (IsUpgrade(currentVersion, targetVersion))
                    {
                        var upgradePath = upgradeManager.CalculateUpgradePath(currentVersion, targetVersion);
                        await upgradeManager.ExecuteUpgradePathAsync(context, upgradePath);
                    }
                    else
                    {
                        var rollbackPath = upgradeManager.CalculateRollbackPath(currentVersion, targetVersion);
                        await upgradeManager.ExecuteRollbackPathAsync(context, rollbackPath);
                    }
                    
                    // 统一保存所有更改
                    await context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    logger.LogInformation("MainProjectDb升级路径执行完成");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    logger.LogError(ex, "MainProjectDb升级路径执行失败，已回滚事务");
                    throw;
                }
            }
        }
        
        protected override async Task CreateInitialDataAsync(MainProjectDbContext context, ILogger logger)
        {
            logger.LogInformation("开始创建MainProjectDb初始数据...");

            // 获取目标版本，让新建的数据库默认就是最高版本
            var targetVersion = DatabaseVersioningOptionsProvider.GetTargetVersion();
            
            // 初始化版本信息，直接插入目标版本
            if (await DatabaseVersionTableExistsAsync(context))
            {
                if (!await context.DatabaseVersions.AnyAsync())
                {
                    context.DatabaseVersions.Add(new BimBase.Api.Infrastructure.MainDomain.MPDatabaseVersion
                    {
                        Version = targetVersion,
                        UpgradeTime = DateTime.Now,
                        Description = $"初始化MainProjectDb数据库到 {targetVersion} 版本"
                    });
                    logger.LogInformation($"添加MainProjectDb初始版本信息: {targetVersion}");
                }
            }
            else
            {
                logger.LogInformation("MainProjectDb DatabaseVersion 表不存在，跳过版本信息初始化");
            }

            // 保存版本信息
            await context.SaveChangesAsync();
            
            logger.LogInformation("MainProjectDb初始数据创建完成");
        }
        
        /// <summary>
        /// 实现基类抽象方法：创建数据库触发器
        /// </summary>
        protected override async Task CreateTriggersAsync(MainProjectDbContext context, ILogger logger)
        {
            await CreateMPProjectTreeNodesTriggersAsync(context, logger);
        }
        
        #endregion
        
        #region 私有辅助方法
        
        /// <summary>
        /// 判断是否为升级操作（版本号递增）
        /// </summary>
        private static bool IsUpgrade(string currentVersion, string targetVersion)
        {
            return GetVersionNumber(currentVersion) < GetVersionNumber(targetVersion);
        }
        
        /// <summary>
        /// 从版本字符串中提取版本号（如 "v1" -> 1）
        /// </summary>
        private static int GetVersionNumber(string version)
        {
            if (string.IsNullOrEmpty(version) || !version.StartsWith("v"))
            {
                return 0;
            }
            
            if (int.TryParse(version.Substring(1), out int versionNumber))
            {
                return versionNumber;
            }
            
            return 0;
        }
        
        /// <summary>
        /// 创建MPProjectTreeNodes表的触发器
        /// </summary>
        private async Task CreateMPProjectTreeNodesTriggersAsync(MainProjectDbContext context, ILogger logger)
        {
            try
            {
                logger.LogInformation("开始创建MPProjectTreeNodes表的触发器...");

                // 检查表是否存在
                var tableExists = await TableExistsAsync(context, "mpprojecttreenodes");
                if (!tableExists)
                {
                    logger.LogInformation("MPProjectTreeNodes表不存在，跳过触发器创建");
                    return;
                }

                // 创建 BEFORE INSERT 触发器
                await CreateOrReplaceTriggerAsync(context, 
                    "trg_before_insert_indexcode2", 
                    @"CREATE TRIGGER trg_before_insert_indexcode2
                      BEFORE INSERT ON mpprojecttreenodes
                      FOR EACH ROW
                      BEGIN
                        SET NEW.indexCode2 = LEFT(NEW.indexCode, 190);
                      END",
                    logger);

                // 创建 BEFORE UPDATE 触发器
                await CreateOrReplaceTriggerAsync(context, 
                    "trg_before_update_indexcode2", 
                    @"CREATE TRIGGER trg_before_update_indexcode2
                      BEFORE UPDATE ON mpprojecttreenodes
                      FOR EACH ROW
                      BEGIN
                        SET NEW.indexCode2 = LEFT(NEW.indexCode, 190);
                      END",
                    logger);

                logger.LogInformation("MPProjectTreeNodes表的触发器创建完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "创建MPProjectTreeNodes表的触发器时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private async Task<bool> TableExistsAsync(MainProjectDbContext context, string tableName)
        {
            try
            {
                var connectionString = context.Database.GetConnectionString();
                using var connection = new MySqlConnector.MySqlConnection(connectionString);
                await connection.OpenAsync();
                
                var databaseName = connection.Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = '{tableName}'";

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                var result = await command.ExecuteScalarAsync();
                var count = Convert.ToInt32(result);
                return count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 创建或替换触发器
        /// </summary>
        private async Task CreateOrReplaceTriggerAsync(MainProjectDbContext context, string triggerName, string triggerSql, ILogger logger)
        {
            MySqlConnector.MySqlConnection connection = null;
            try
            {
                // 构建新的连接字符串，确保完全脱离连接池和事务状态
                var connectionString = context.Database.GetConnectionString();
                
                // 添加连接池相关参数，确保获取新的干净连接
                var connectionStringBuilder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString)
                {
                    Pooling = false, // 禁用连接池，确保获取全新连接
                    AutoEnlist = false // 禁用自动加入分布式事务
                };
                
                connection = new MySqlConnector.MySqlConnection(connectionStringBuilder.ConnectionString);
                await connection.OpenAsync();
                
                // 确保连接没有自动开启事务
                logger.LogInformation($"触发器连接状态 - 数据库: {connection.Database}, 状态: {connection.State}");

                // 检查触发器是否已存在
                var triggerExists = await TriggerExistsAsync(connection, triggerName, logger);
                
                if (triggerExists)
                {
                    logger.LogInformation($"触发器 {triggerName} 已存在，先删除旧触发器");
                    using var dropCommand = connection.CreateCommand();
                    dropCommand.CommandText = $"DROP TRIGGER IF EXISTS {triggerName}";
                    await dropCommand.ExecuteNonQueryAsync();
                    logger.LogInformation($"旧触发器 {triggerName} 删除成功");
                }

                logger.LogInformation($"创建触发器: {triggerName}");
                using var createCommand = connection.CreateCommand();
                createCommand.CommandText = triggerSql;
                await createCommand.ExecuteNonQueryAsync();
                logger.LogInformation($"触发器 {triggerName} 创建成功");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"创建触发器 {triggerName} 时发生错误");
                throw;
            }
            finally
            {
                if (connection != null)
                {
                    try
                    {
                        if (connection.State == System.Data.ConnectionState.Open)
                        {
                            await connection.CloseAsync();
                        }
                        await connection.DisposeAsync();
                        logger.LogInformation($"触发器连接已安全关闭");
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "关闭触发器连接时发生错误");
                    }
                }
            }
        }

        /// <summary>
        /// 检查触发器是否存在
        /// </summary>
        private async Task<bool> TriggerExistsAsync(MySqlConnector.MySqlConnection connection, string triggerName, ILogger logger)
        {
            try
            {
                var databaseName = connection.Database;
                
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.triggers 
                    WHERE trigger_schema = '{databaseName}' 
                      AND trigger_name = '{triggerName}'";

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                var result = await command.ExecuteScalarAsync();
                var count = Convert.ToInt32(result);
                return count > 0;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, $"检查触发器 {triggerName} 是否存在时发生错误，假设不存在");
                return false;
            }
        }
        
        #endregion
    }
} 