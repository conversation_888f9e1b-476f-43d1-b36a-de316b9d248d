﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class ModelData : BasicModelDataInfomation
    {
        public int? LockUserID { get; set; }
        [ForeignKey("LockUserID")]
        public virtual ProjectMember LockUser { get; set; }

        public Int64 StoreyID { get; set; }

        public int Domain { get; set; }

        public Guid StoreyGuid
        {
            get;
            set;
        }   //增加构件所属楼层Guid  2019-9-24 重构
        //to do, add versionNo parameter
        public HistoryData CreateHistoryData(OperationRecordType op, int versionNo = 0)
        {
            HistoryData hd = new HistoryData();
            base.CopyTo(ref hd);
            hd.InstanceId = InstanceId;
            hd.StoreyID = StoreyID;
            hd.Domain = Domain;
            hd.OperationRecordType = op;
            hd.VersionNo = versionNo;
            hd.StoreyGuid = StoreyGuid;
            hd.DomainClassName = DomainClassName;
            return hd;
        }
    }
}
