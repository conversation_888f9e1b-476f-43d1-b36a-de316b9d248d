ConnectionService服务接口类
1)  用户登录 
ConnectionService/Login   用于实现用户登录。

2) 网页端用户登录
ConnectionService/WebLogin  用于实现网页端用户登录。

3) 中机国际用户登录
ConnectionService/LoginForZJ  用于实现用户登录（中机国际项目使用）。
4) ~~是否登录~~ **<font color="red">Not implemented </font>**
   ConnectionService/IsCurrentLogin  用于获取用户是否处于登录状态
5)  ~~用户注销~~  **<font color="red">Not implemented </font>**
   ConnectionService/Logout  用于实现用户退出登录
6) ~~是否登录~~ **<font color="red">Not implemented </font>**
   ConnectionService/IsCurrentLogin  用于获取用户是否处于登录状态
7) 判断是否存在同名项目
   ConnectionService/CheckProjectByPName  用于判断服务器是否存在同名项目（中机使用）
8) 获取已有接口服务
   ConnectionService/GetGeneralServicesInfomation 获取服务接口的调用地址
9) 获取所有在线用户列表
   ConnectionService/GetOnlineUserlist  用于实现获取在线用户列表功能
10) 获取所参与的项目列表
   ConnectionService/GetProjectList  用于实现获取项目列表功能
11) 打开项目连接
ConnectionService/OpenProject   用于实现打开项目并获取相应服务接口功能
12) 打开项目连接（中机项目使用）
ConnectionService/OpenProjectByProjectName  用于实现打开项目并获取相应服务接口功能
TeamQueryService服务接口类
1)	获取登录用户信息
TeamQueryService/GetMemberInfo  用于实现获取登录用户信息功能，客户端使用
2)	获取登录用户信息(NEW)
TeamQueryService/GetMemberInfoNew  用于实现获取登录用户信息功能，客户端使用
3)	获取团队成员列表
		TeamQueryService/GetMembers  用于实现获取团队内所有成员列表功能。
4)	获取团队管理员列表
		TeamQueryService/GetTeamAdministrators  用于实现获取团队管理员列表功能。
5)	获取团队模板列表
TeamQueryService/GetTeamTemplateList 用于实现获取团队模板列表功能
6)	获取项目模板列表
TeamQueryService/GetProjectTemplateList 用于实现获取团队模板列表功能。
7)	获取项目成员列表
TeamQueryService/GetProjectMembers 用于实现获取项目成员列表功能
8)	获取提交历史版本
		TeamQueryService/GetVersionDatas 用于实现获取项目成员列表功能。（网页端统计图使用）
TeamManagementService服务接口类
1)	创建项目
TeamManagementService/CreateProject 用于实现创建项目功能
2)	创建项目（带版本信息）
TeamManagementService/CreateProjectWithSchemaVersion 用于实现创建项目功能（增加客户端schema版本号）
3)	删除项目
		TeamManagementService/DeleteProject 用于实现删除项目功能。
4)	修改项目信息
		TeamManagementService/ModifyProjectInfo 用于实现修改项目信息功能。
5)	获取用户所有参与的项目及在该项目中的角色及权限信息
TeamManagementService/GetAllProjectAndRoleAndPermissionByLogname
		用于实现获取用户所有参与的项目及在该项目中的角色及权限信息功能。
6)	添加团队成员
		TeamManagementService/AddMember 用于实现增加团队成员功能。
7)	删除团队成员
		TeamManagementService/DeleteMember 用于实现删除团队成员功能
8)	修改团队成员信息
		TeamManagementService/ModifyMember 用于实现修改团队成员信息功能。
9)	设置团队管理员
TeamManagementService/SetTeamAdministrator 用于实现设定团队管理员的功能
10)	移除团队管理员
		TeamManagementService/RemoveTeamAdministrator 用于实现移除团队管理员的功能。
11)	导出团队成员到模板
TeamManagementService/ExportTeamMembers 用于实现导出团队成员到指定模板的功能。
12)	模板中导入团队成员
TeamManagementService/ImportTeamMembers 用于实现从定模板中导入团队成员的功能。
13)	从已有项目中复制成员及权限
TeamManagementService/CopyMemberAndRoleToProject用于实现从已有项目中复制成员及权限的功能。

ProjectQueryService服务接口类
1)	查看参与项目成员列表
ProjectQueryService/GetProjectMembers 用于实现查看项目参与者列表的功能
2)	查看项目角色列表
		ProjectQueryService/GetProjectRoles 用于实现查看项目角色列表的功能
3)	查看项目描述信息
		ProjectQueryService/GetProjectDescription 用于实现查看项目描述信息的功能
4)	查看当前打开项目名称
		ProjectQueryService/GetProjectName 用于实现查看项目名称的功能
5)	获取项目角色成员列表
		ProjectQueryService/GetProjectRoleMembers 用于实现查看项目角色成员列表的功能
6)	获取项目成员信息及其所属角色
		ProjectQueryService/LoadMemberRoleByMemIDs 用于实现获取项目成员信息及其所属角色功能
7)	查看项目成员拥有权限
		ProjectQueryService/GetUserAuthorities 用于实现查看项目成员在当前项目中所拥有权限功能
8)	获取指定角色权限
		ProjectQueryService/GetRoleAuthorities 用于实现获取指定角色权限功能


ProjectManagementService服务接口类
1)	添加项目成员
		ProjectManagementService/AddMembers 用于实现添加项目参与者的功能
2)	删除项目成员
		ProjectManagementService/DeleteMembers 用于实现删除项目参与者的功能
3)	设置或移除项目管理员
		ProjectManagementService/SetProjectAdmin 用于实现设置项目管理员的功能
4)	添加项目角色
		ProjectManagementService/AddRole 用于实现添加项目角色的功能
5)	删除项目角色
		ProjectManagementService/DeleteRole 用于实现删除项目角色的功能
6)	修改项目角色
		ProjectManagementService/ModifyProjectRole 用于实现修改项目角色的功能
7)	创建项目磁盘映射
		ProjectManagementService/AddRepository|AddRepositoryById 用于实现创建项目磁盘映射的功能
8)	移除项目磁盘映射
		ProjectManagementService/RemoveRepository 用于实现移除项目磁盘映射的功能
9)	项目角色中添加成员
		ProjectManagementService/SetMemberToRole 用于实现项目中添加成员的功能
10)	 移除项目角色中的成员
ProjectManagementService/DeleteTeamMembers 用于实现移除项目角色中的成员的功能
11)	 获取成员所有的角色
ProjectManagementService/LoadMemberRole 用于实现获取成员所有的角色的功能
12)	 获取成员所有的权限
ProjectManagementService/LoadMemberPermission 用于实现获取成员所有权限的功能
13)	 导入项目角色
ProjectManagementService/ImportProjectRoles 用于实现导入项目角色的功能
14)	 导出项目角色
ProjectManagementService/ExportProjectRoles 用于实现导出项目角色的功能
15)	 导入项目成员
ProjectManagementService/ImportProjectMembers用于实现导入项目成员的功能
16)	 导出项目成员
ProjectManagementService/ExportProjectMembers用于实现导出项目成员的功能
17)	 获取成员模版下所有成员信息
ProjectManagementService/GetTemplateProjectMembers用于实现获取成员模版下所有成员信息的功能
18)	 导出项目成员，角色，权限
ProjectManagementService/ExportMembersByProjectID 用于实现导出项目成员，角色，权限的功能
19)	 导入项目成员，角色，权限
ProjectManagementService/ImportMembersAndRoles 用于实现导入项目成员，角色，权限的功能


ModelQueryService服务接口类
1)	根据instanceid和versionNo获取特定的model数据
ModelQueryService/DownloadModeldataByInstanceIDs 用于实现根据instanceid和versionNo获取特定的model数据的功能
2)	按专业获取特定版本数据
ModelQueryService/DownloadVesionByDomain 用于实现按专业获取特定版本数据的功能
3)	下载全部楼层数据
ModelQueryService/DownloadStoreyDataByVersionNo 用于实现下载全部楼层数据的功能
4)	获取项目当前版本号
ModelQueryService/GetCurrentVersionNo 用于实现获取项目当前版本号的功能
5)	获取专业初始化标记（该专业是否曾上传过数据）
ModelQueryService/GetInitialMark 用于实现获取该专业是否曾上传过数据的功能
6)	获取专业未下载的版本的版本信息
ModelQueryService/GetUndownloadVersionInfomation 用于实现获取该专业未下载的版本的版本信息的功能
7)	查看版本信息
ModelQueryService/GetVersionInfomation 用于实现查看版本信息的功能
8)	查看提交历史版本信息
ModelQueryService/GetVersionDatas 用于实现查看提交历史版本信息的功能
9)	获取项目所有的提交记录
ModelQueryService/GetVersionInfomationHistory 用于实现获取项目所有的提交记录的功能
10)	获取某提交历史版本中关系数据记录
ModelQueryService/GetVersionInformationHistoryRelationship用于实现获取某提交历史版本中关系数据记录的功能
11)	获取某提交历史版本中构件数据记录
ModelQueryService/GetVersionInfomationHistoryData用于实现获取某提交历史版本中构件数据记录的功能
12)	获取锁定资源类列表
ModelQueryService/GetLockedResourceList用于实现获取锁定资源类列表的功能
13)	获取锁定构件列表
ModelQueryService/GetLockUserList用于实现获取锁定构件列表的功能
14)	获取专业最新版本号
ModelQueryService/GetNewestVersionByDomain用于实现获取专业最新版本号的功能
15)	获取楼层列表数据
ModelQueryService/GetStoreyListdDatas 用于实现获取楼层列表数据的功能
16)	获取锁定的构件列表与锁定用户id
ModelQueryService/GetLockIndex  用于实现获取锁定的构件列表与锁定用户id的功能
17)	查询构件锁定状态
ModelQueryService/GetLockModelDatas 用于实现查询构件锁定状态的功能
18)	获取楼层及对应版本号
ModelQueryService/GetStoreyVersionNo用于实现获取楼层及对应版本号的功能
19)	获取专业楼层锁定状态
ModelQueryService/GetStoreyLock 用于实现获取专业楼层锁定状态的功能
20)	根据构件Id ,返回对应的锁定该构件的用户Id
ModelQueryService/GetLockedUserIdByInstanceId用于实现根据构件Id ,返回对应的锁定该构件的用户Id的功能

ModelUploadService服务接口类
1)	检查单例类冲突
ModelQueryService/CheckSingletonClass 用于实现检查单例类冲突的功能
2)	检查资源类冲突
ModelQueryService/CheckResourceClassByResourceName 用于实现检查资源类冲突的功能
3)	客户端新增资源类时检查资源类构件是否存在锁定冲突
ModelQueryService/CheckResourceClassConfilict 用于实现客户端新增资源类时检查资源类构件是否存在锁定冲突的功能
4)	上传数据
ModelQueryService/CheckInWithResourceForWebWithVersion用于实现网页端创建联机项目时上传构件数据的功能
5)	数据签入
ModelQueryService/CheckInWithResourceLock 用于实现上传构件数据的功能

ModelWriteService服务接口类
1)	锁定数据
ModelQueryService/CheckOutAndUpdate用于实现锁定构件数据的功能
2)	构件是否被他人锁定
ModelQueryService/IsLockConflictWithConflictList 用于实现判断构件是否被他人锁定的功能
3)	锁定构件
ModelQueryService/CheckOutData 用于实现构件数据判断是否被锁定及是否最新版本并锁定构件的功能
4)	锁定构件并返回冲突信息
ModelQueryService/CheckOutAndUpdateWithConflictList 用于实现锁定构件并返回冲突信息的功能
5)	按专业锁定构件
ModelQueryService/CheckOutByDomainWithConflictList用于实现按专业锁定构件的功能
6)	按专业id判断是否有构件锁冲突
ModelQueryService/CheckConflictByDomainIds 用于实现按专业id判断是否有构件锁冲突的功能
7)	按用户解锁
ModelQueryService/UnlockByUser用于实现按用户解锁(同时会解锁用户锁定的专业楼层)的功能
8)	按用户及构件类型id解锁
ModelQueryService/UnlockByUserAndDomain 用于实现按用户及构件类型id解锁的功能
9)	获取项目Schema版本号
ModelQueryService/GetProjectSchema 用于实现获取项目Schema版本号的功能
10)	批量锁定楼层
ModelQueryService/LockStoreyMulti 用于实现批量锁定楼层的功能

11)	按专业锁定楼层
ModelQueryService/LockStorey   用于实现按专业锁定楼层的功能
12)	解锁专业楼层
ModelQueryService/UnlockStorey  用于实现解锁专业楼层的功能
13)	解锁专业楼层（网页端）
ModelQueryService/UnlockStoreyByForce 用于网页端实现解锁专业楼层的功能
14)	解除服务端当前用户在domains专业下的楼层锁
ModelQueryService/RevoeDomainStoreyLock用于实现解除服务端当前用户在domains专业下的楼层锁的功能
15)	检查专业楼层锁定状态
ModelQueryService/CheckStoreyOccupation 用于实现检查专业楼层锁定状态，若无冲突则锁定楼层的功能














































