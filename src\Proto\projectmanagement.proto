syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";

package bimbase.api;


message ProjectManagementRequest{
	string sessionId = 1;
	string projectId = 2;
}

message AddMembersRequest{
	string sessionId = 1;
	string projectId = 2;
	repeated string memberNames = 3;
	string roleGuid = 4;
}

message AddMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated int32 projectMemId = 4;
}

message DeleteMembersRequest{
	string sessionId = 1;
	string projectId = 2;
	repeated string memberNames = 3;
}
message SetProjectAdminRequest{
	string sessionId = 1;
	string projectId = 2;
	string memberName = 3;
	bool isProjectAdmin = 4;
}

message AddRepositoryResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	int32 instanceId = 4;
}

message AddRoleRequest{
	string sessionId = 1;
	string projectId = 2;
	RoleDto role = 3;
	repeated AuthInfoDto AuthInfoList = 4;
}

message DeleteRoleRequest{
	string sessionId = 1;
	repeated string roleIdList = 2;
}

message ModifyProjectRoleRequest{
	string sessionId = 1;
	string roleGuid = 2;
	repeated AuthInfoDto AuthInfoList = 3;
}

message RemoveRepositoryRequest{
	string sessionId = 1;
	string projectId = 2;
	int32 repositoryId = 3;
}

message SetMemberToRoleRequest{
	string sessionId = 1;
	string projectId = 2;
	string roleGuid = 3;
	repeated int32 memberIds = 4;
}

message DeleteTeamMembersRequest{
	string sessionId = 1;
	string roleGuid = 2;
	repeated string userIds = 3;
}

message LoadMemberRoleRequest{
	string sessionId = 1;
	string projectId = 2;
	string memberGuid = 3;
}

message LoadMemberRoleResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated RoleDto roles = 4;
}

message LoadMemberPermissionRequest{
	string sessionId = 1;
	string projectId = 2;
	string memberGuid = 3;
}

message LoadMemberPermissionResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated AuthInfoDto authInfos = 4;
}

message ImportProjectRolesRequest{
	string sessionId = 1;
	string projectId = 2;
	string templatePath = 3;
}

message ExportProjectRolesRequest{
	string sessionId = 1;
	string projectId = 2;
	string templatePath = 3;
	repeated string roleGuids = 4;
}

message ImportProjectMembersRequest{
	string sessionId = 1;
	string projectId = 2;
	string templatePath = 3;
}

message ExportProjectMembersRequest{
	string sessionId = 1;
	repeated string loginNames = 2;
	string templatePath = 3;
}

message GetTemplateProjectMembersRequest{
	string sessionId = 1;
	string projectId = 2;
	string templatePath = 3;
}

message GetTemplateProjectMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcTeamMember teamMembers = 4;
}

message GrpcTeamMemberAndRolesTemplate{
	repeated GrpcRoleTemplate allRoles = 1;
	repeated GrpcTeamMemberTemplate membersJoinRoles = 2;
}

message GrpcRoleTemplate{
    string id = 1;
    string name = 2;
    int32 status = 3;
    int32 type = 4;
    repeated AuthInfoDto authInfos = 5;
}

message GrpcTeamMemberTemplate{
	string id =1;
    string loginName = 2;
    string displayName = 3;
    int32 Color = 4;
	string Avatar = 5 ;
    repeated GrpcRoleTemplate RoleTemplates = 6;
}


message ExportMembersByProjectIDResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcTeamMemberAndRolesTemplate teamMemberAndRoles = 4;
}


message ImportMembersAndRolesRequest{
	string sessionId = 1;
	string projectId = 2;
	GrpcTeamMemberAndRolesTemplate teamMemberAndRoles = 3;
}

service GrpcProjectManagement {
	rpc AddMembers(AddMembersRequest) returns (AddMembersResponse) {
		option (google.api.http) = {
			post: "/v1/AddMembers"
			body: "*"
		};
	}

	rpc DeleteMembers(DeleteMembersRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/DeleteMembers"
			body: "*"
		};
	}

	rpc SetProjectAdmin(SetProjectAdminRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/SetProjectAdmin"
			body: "*"
		};
	}

	rpc AddRole(AddRoleRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/AddRole"
			body: "*"
		};
	}

	rpc DeleteRole(DeleteRoleRequest) returns (GrpcResult){
		option (google.api.http) = {
			delete: "/v1/DeleteRole"
			body: "*"
		};
	}

	rpc ModifyProjectRole(ModifyProjectRoleRequest) returns (GrpcResult){
		option (google.api.http) = {
			put: "/v1/ModifyProjectRole"
			body: "*"
		};
	}

	rpc AddRepository(ProjectManagementRequest) returns(AddRepositoryResponse){
		option (google.api.http) = {
			post: "/v1/AddRepository"
			body: "*"
		};
	}

	rpc RemoveRepository(RemoveRepositoryRequest) returns(GrpcResult){
		option (google.api.http) = {
			post: "/v1/RemoveRepository"
			body: "*"
		};
	}

	rpc SetMemberToRole(SetMemberToRoleRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/SetMemberToRole"
			body: "*"
		};
	}


	rpc DeleteTeamMembers(DeleteTeamMembersRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/DeleteTeamMembers"
			body: "*"
		};
	}

	rpc LoadMemberRole(LoadMemberRoleRequest) returns(LoadMemberRoleResponse){
		option (google.api.http) = {
			post: "/v1/LoadMemberRole"
			body: "*"
		};
	}

	rpc LoadMemberPermission(LoadMemberPermissionRequest) returns (LoadMemberPermissionResponse){
		option (google.api.http) = {
			post: "/v1/LoadMemberPermission"
			body: "*"
		};
	}

	rpc ImportProjectRoles(ImportProjectRolesRequest) returns(GrpcResult){
		option (google.api.http) = {
			post: "/v1/ImportProjectRoles"
			body: "*"
		};
	}

	rpc ExportProjectRoles(ExportProjectRolesRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/ExportProjectRoles"
			body: "*"
		};
	}

	rpc ImportProjectMembers(ImportProjectMembersRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/ImportProjectMembers"
			body: "*"
		};
	}

	rpc ExportProjectMembers(ExportProjectMembersRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/ExportProjectMembers"
			body: "*"
		};
	}

	rpc GetTemplateProjectMembers(GetTemplateProjectMembersRequest) returns(GetTemplateProjectMembersResponse){
		option (google.api.http) = {
			post: "/v1/GetTemplateProjectMembers"
			body: "*"
		};
	}

	rpc ExportMembersByProjectID(ProjectManagementRequest) returns (ExportMembersByProjectIDResponse){
		option (google.api.http) = {
			post: "/v1/ExportMembersByProjectID"
			body: "*"
		};
	}

	rpc ImportMembersAndRoles(ImportMembersAndRolesRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/ImportMembersAndRoles"
			body: "*"
		};
	}
	

}