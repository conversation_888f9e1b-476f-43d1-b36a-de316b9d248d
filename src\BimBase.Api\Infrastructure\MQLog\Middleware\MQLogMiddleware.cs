using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using Microsoft.Extensions.Configuration;
using System.Net;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志专用中间件，采集接口请求/响应信息并写入MQ日志，不影响原有日志体系
    /// 可通过配置开关控制是否启用
    /// </summary>
    public class MQLogMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<MQLogMiddleware> _logger;
        private readonly IConfiguration _config;
        private readonly bool _enableMQLogging;

        public MQLogMiddleware(RequestDelegate next, IServiceScopeFactory scopeFactory, ILogger<MQLogMiddleware> logger, IConfiguration config)
        {
            _next = next;
            _scopeFactory = scopeFactory;
            _logger = logger;
            _config = config;
            _enableMQLogging = config.GetValue<bool>("LogSettings:LinShiMQ:Enabled", false);
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (!_enableMQLogging)
            {
                await _next(context);
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            var requestTime = DateTime.Now;
            
            string serverIp = Dns.GetHostAddresses(Dns.GetHostName())
                .FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)?.ToString();
            string serverName = Environment.MachineName;

            string clientIp = context.Request.Headers["X-Forwarded-For"].FirstOrDefault()
                ?? context.Connection.RemoteIpAddress?.ToString();

            var interfaceLog = new MQInterfaceLog
            {
                RequestTime = requestTime,
                InterfaceName = context.GetEndpoint()?.DisplayName ?? context.Request.Path,
                HttpMethod = context.Request.Method,
                LogType = "interface",
                LogLevel = "info",
                ProjectId = context.Request.Headers["project-id"].FirstOrDefault(),
                ProjectName = context.Request.Headers["project-name"].FirstOrDefault(),
                SessionId = context.Request.Headers["session-id"].FirstOrDefault(),
                ServerIp = serverIp,
                ServerName = serverName,
                AppName = _config["AppSettings:AppName"] ?? AppDomain.CurrentDomain.FriendlyName,
                AppVersion = _config["AppSettings:AppVersion"],
                Environment = _config["AppSettings:Environment"] ?? _config["ASPNETCORE_ENVIRONMENT"],
                AddTime = DateTime.Now,
                ClientIp = clientIp
            };

            try
            {
                if (context.Request.ContentLength > 0 && context.Request.Body.CanRead)
                {
                    context.Request.EnableBuffering();
                    using (var reader = new System.IO.StreamReader(context.Request.Body, leaveOpen: true))
                    {
                        var body = await reader.ReadToEndAsync();
                        interfaceLog.RequestParams = body;
                        context.Request.Body.Position = 0;
                    }
                }

                interfaceLog.ClientIp = clientIp;
                if (context.Request.Headers.TryGetValue("x-forwarded-for", out var forwardedFor))
                {
                    interfaceLog.ClientIp = forwardedFor.FirstOrDefault() ?? interfaceLog.ClientIp;
                }
                else if (context.Request.Headers.TryGetValue("grpc-client-ip", out var grpcClientIp))
                {
                    interfaceLog.ClientIp = grpcClientIp.FirstOrDefault() ?? interfaceLog.ClientIp;
                }

                try
                {
                    if (context.Session != null)
                    {
                        if (context.Session.Keys.Contains("userId"))
                        {
                            interfaceLog.UserId = context.Session.GetString("userId");
                        }
                        if (context.Session.Keys.Contains("username"))
                        {
                            interfaceLog.UserName = context.Session.GetString("username");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "[MQLogMiddleware] Session访问异常，继续处理请求");
                }

                if (string.IsNullOrEmpty(interfaceLog.UserId) && context.Request.Headers.TryGetValue("user-id", out var userIdHeader))
                {
                    interfaceLog.UserId = userIdHeader.FirstOrDefault();
                }
                if (string.IsNullOrEmpty(interfaceLog.UserName) && context.Request.Headers.TryGetValue("user-name", out var userNameHeader))
                {
                    interfaceLog.UserName = userNameHeader.FirstOrDefault();
                }

                if (context.Items.ContainsKey("GrpcRequest"))
                {
                    var grpcRequest = context.Items["GrpcRequest"];
                    if (grpcRequest != null)
                    {
                        interfaceLog.RequestParams = System.Text.Json.JsonSerializer.Serialize(grpcRequest);
                    }
                }

                using (var scope = _scopeFactory.CreateScope())
                {
                    var logWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                    await logWriterService.WriteInterfaceLogAsync(interfaceLog);
                }

                await _next(context);

                stopwatch.Stop();
                interfaceLog.ResponseTime = DateTime.Now;
                interfaceLog.TotalMilliseconds = stopwatch.ElapsedMilliseconds;
                interfaceLog.ResponseStatusCode = context.Response.StatusCode;
                interfaceLog.IsSuccess = context.Response.StatusCode >= 200 && context.Response.StatusCode < 300;

                using (var scope = _scopeFactory.CreateScope())
                {
                    var logWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                    await logWriterService.WriteInterfaceLogAsync(interfaceLog);
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                interfaceLog.ResponseTime = DateTime.Now;
                interfaceLog.ResponseStatusCode = 500;
                interfaceLog.IsSuccess = false;
                interfaceLog.ErrorMessage = ex.Message;
                interfaceLog.LogLevel = "error";

                using (var scope = _scopeFactory.CreateScope())
                {
                    var logInterfaceWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                    var logErrorWriterService = scope.ServiceProvider.GetRequiredService<IMQErrorWriterService>();
                    await logInterfaceWriterService.WriteInterfaceLogAsync(interfaceLog);

                    var errorLog = new MQErrorLog
                    {
                        ErrorId = Guid.NewGuid().ToString(),
                        RequestId = context.TraceIdentifier,
                        ProjectId = interfaceLog.ProjectId,
                        ProjectName = interfaceLog.ProjectName,
                        SessionId = interfaceLog.SessionId,
                        ServerIp = serverIp,
                        ServerName = serverName,
                        AppName = interfaceLog.AppName,
                        AppVersion = interfaceLog.AppVersion,
                        Environment = interfaceLog.Environment,
                        LogLevel = "error",
                        LogType = "error",
                        ErrorType = ex.GetType().FullName,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex.HResult.ToString(),
                        ErrorStackTrace = ex.StackTrace,
                        InputParams = interfaceLog.RequestParams,
                        SourceClassName = ex.TargetSite?.DeclaringType?.FullName,
                        SourceMethodName = ex.TargetSite?.Name,
                        AdditionalData = ex.Data != null && ex.Data.Count > 0 ? System.Text.Json.JsonSerializer.Serialize(ex.Data) : null,
                        AddTime = DateTime.Now,
                        UserId = interfaceLog.UserId,
                        UserName = interfaceLog.UserName,
                        ClientIp = clientIp,
                    };
                    await logErrorWriterService.WriteErrorLogAsync(errorLog);
                }

                _logger.LogError(ex, "[MQLogMiddleware] 捕获异常并写入MQ日志");
                throw;
            }
        }
    }
} 