﻿using AutoMapper;
using AutoMapper.Execution;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using static BimBase.Api.Protos.GrpcTeamQuery;
using BimBase.Api.Infrastructure.MQLog;

namespace BimBase.Api.Grpc
{
    public class MainprojectAuthTemplate
    {
        public List<MainProjectTeamGroup> MainProjectTeamGroupList { get; set; }

        public List<TeamGroupAuth> TeamGroupAuthList { get; set; }
    }
    //[Authorize]
    public class TeamQueryService : GrpcTeamQueryBase
    {
        private readonly UrlsConfig _urls;
        private readonly MajorConfig _major;
        private readonly ILogger<TeamQueryService> _logger;
        private readonly ITeamRepository _teamRepository;
        private IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        private readonly TeamDbContext _teamDbContext;
        private readonly Func<DbConnection, Guid, IProjectRepository> _projectRepositoryFactory;
        private IProjectRepository _projectRepository;
        private readonly IMQRabbitMQService _rabbitMQService;

        public TeamQueryService(IOptions<UrlsConfig> config, IOptions<MajorConfig> major,ITeamRepository teamRepository
            , ILogger<TeamQueryService> logger
            , IHttpContextAccessor httpContextAccessor
            , IMapper mapper
            , TeamDbContext teamDbContext
            , Func<DbConnection, Guid, IProjectRepository> projectRepositoryFactory
            , IAuthorityManager authorityManager
            , IMQRabbitMQService rabbitMQService)
        {
            _urls = config.Value;
            _major = major.Value;
            _teamRepository = teamRepository;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _teamDbContext = teamDbContext;
            _projectRepositoryFactory = projectRepositoryFactory;
            _authorityManager = authorityManager;
            _rabbitMQService = rabbitMQService;
        }

        /// <summary>
        /// 客户端使用，获取登录用户信息（账户、姓名、颜色、身份（是否管理员）、角色、权限、锁定构件数）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMemberInfoResponse> GetMemberInfo(GetMemberInfoRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var userName = _httpContextAccessor.HttpContext.Session.GetString("username");


            var x = new GetMemberInfoResponse();
            x.IsAdmin = false;
            x.LockCount = 0;
            x.Message = string.Empty;

            var adminMembers = new List<TeamMember>();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }


            if (!Guid.TryParse(request.ProjectId, out var projectId) || projectId == Guid.Empty)
            {
                x.Message = $"参数{request.ProjectId}有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser.AvatarData == null)
            {
                Assembly assembly = Assembly.GetExecutingAssembly();
                string resourceName = "BimBase.Api.Resources.defautAvatar.png";
                //string resourceName = "defautAvatar.png";
                Stream istr = assembly.GetManifestResourceStream(resourceName);
                if (istr != null)
                {
                    byte[] bytes = new byte[istr.Length];
                    istr.Read(bytes, 0, bytes.Length);
                    // 设置当前流的位置为流的开始  
                    istr.Seek(0, SeekOrigin.Begin);
                    currentUser.AvatarData = bytes;
                    currentUser.AvatarExtType = "png";
                }
            }

            x.TeamMemberDto = new GrpcTeamMember
            {
                Id = currentUser?.ID.ToString() ?? string.Empty,
                Avatar = currentUser?.Avatar ?? string.Empty,
                AvatarData = ByteString.CopyFrom(currentUser.AvatarData),
                AvatarExtType = currentUser?.AvatarExtType ?? string.Empty,
                Color = currentUser.Color,
                //CreateId = currentUser?.CreateId.ToString(),
                Department = currentUser.Department ?? string.Empty,
                DisplayName = currentUser?.DisplayName,
                Email = currentUser.Email ?? string.Empty,
                LoginName = currentUser?.LoginName ?? string.Empty,
                PasswordMD5 = currentUser?.PasswordMD5 ?? string.Empty,
            };

            var roleResult = _authorityManager.GetOwnRoleList(BuildinAdministrators.BuildInSystemAdminGuid.ToString(), out var roles, out var message);

            if (roleResult)
            {
                foreach (var role in roles)
                {
                    if (role.Type == 1) //团队管理员
                    {
                        List<Guid> memberGuids;
                        var ret = _authorityManager.GetRoleUsers(role.Id, out memberGuids, out message);

                        if (ret)
                        {
                            foreach (var memberGuid in memberGuids)
                            {
                                var teamMemberInfo = _teamRepository.Members.SingleOrDefault(m => m.ID == memberGuid);
                                if (null != teamMemberInfo)
                                    adminMembers.Add(teamMemberInfo);
                            }
                        }

                    }
                }
            }

            x.IsAdmin = adminMembers.Exists(member => member.LoginName == currentUser.LoginName);
            //获取用户的角色列表
            var roleRet = _authorityManager.GetRoleInfoByUserIdAndProjectId(currentUser.ID, projectId, out var memRoles, out message);

            if (roleRet)
            {
                x.RoleDtoList.AddRange(memRoles);
            }

            var authinfoResult = _authorityManager.GetProjectUserAuth(currentUser.ID, projectId, out var authInfos, out message);

            if (authinfoResult)
            {
                var a = _mapper.Map<RepeatedField<AuthInfoDto>>(authInfos);
                x.AuthInfoList.AddRange(a);
            }
            //获取用户锁定构件数量
            _projectRepository = _projectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), projectId);

            var proMem = _projectRepository.Members.SingleOrDefault(s => s.TeamMemberID == currentUser.ID);
            if (proMem != null)
            {
                var lockCount = _projectRepository.LockedComponents.Count(dat => dat.LockUserId == proMem.ID);
                x.LockCount = lockCount;
            }


            return x;
        }


        /// <summary>
        /// 客户端使用，获取登录用户信息（账户、姓名、颜色、身份（是否管理员）、角色、权限、锁定构件数）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMemberInfoNewResponse> GetMemberInfoNew(GetMemberInfoRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var userName = _httpContextAccessor.HttpContext.Session.GetString("username");


            var x = new GetMemberInfoNewResponse();
            x.IsAdmin = false;
            x.LockCount = 0;
            x.Message = string.Empty;

            var adminMembers = new List<TeamMember>();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }


            if (!Guid.TryParse(request.ProjectId, out var projectId) || projectId == Guid.Empty)
            {
                x.Message = $"参数{request.ProjectId}有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser.AvatarData == null)
            {
                Assembly assembly = Assembly.GetExecutingAssembly();
                string resourceName = "BimBase.Api.Resources.defautAvatar.png";
                //string resourceName = "defautAvatar.png";
                Stream istr = assembly.GetManifestResourceStream(resourceName);
                if (istr != null)
                {
                    byte[] bytes = new byte[istr.Length];
                    istr.Read(bytes, 0, bytes.Length);
                    // 设置当前流的位置为流的开始  
                    istr.Seek(0, SeekOrigin.Begin);
                    currentUser.AvatarData = bytes;
                    currentUser.AvatarExtType = "png";
                }
            }

            x.TeamMemberDto = new GrpcTeamMember
            {
                Id = currentUser?.ID.ToString() ?? string.Empty,
                Avatar = currentUser?.Avatar ?? string.Empty,
                AvatarData = ByteString.CopyFrom(currentUser.AvatarData),
                AvatarExtType = currentUser?.AvatarExtType ?? string.Empty,
                Color = currentUser.Color,
                //CreateId = currentUser?.CreateId.ToString(),
                Department = currentUser.Department ?? string.Empty,
                DisplayName = currentUser?.DisplayName,
                Email = currentUser.Email ?? string.Empty,
                LoginName = currentUser?.LoginName ?? string.Empty,
                PasswordMD5 = currentUser?.PasswordMD5 ?? string.Empty,
            };

            ////获取 是否管理员 信息
            //var roleResult = await GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            //{
            //    var client = new AuthGrpc.AuthGrpcClient(channel);

            //    _logger.LogDebug(" grpc client created, request={@request}", request);

            //    var authGrpcRequest = new GetOwnRoleListRequest { UserId = BuildinAdministrators.BuildInSystemAdminGuid.ToString() };

            //    var response = await client.GetOwnRoleListAsync(authGrpcRequest);

            //    _logger.LogDebug(" grpc response: {@response}", response);

            //    return response;
            //});

            //if (roleResult.IsSuccess)
            //{
            //    foreach (var role in roleResult.RoleList)
            //    {
            //        if (role.Type == 1) //团队管理员
            //        {
            //            List<Guid> memberGuids;

            //            var ret = await GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            //            {
            //                var client = new AuthGrpc.AuthGrpcClient(channel);
            //                var response = await client.GetUsersFromRoleIdAsync(new GetUsersFromRoleIdRequest { RoleId = BuildinAdministrators.BuildInSystemAdminGuid.ToString() });
            //                return response;
            //            });

            //            if (ret.IsSuccess)
            //            {
            //                var p = ret.Result.Unpack<Value>();

            //                memberGuids = System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(p.StringValue);

            //                foreach (var memberGuid in memberGuids)
            //                {
            //                    var teamMemberInfo = _teamRepository.Members.SingleOrDefault(m => m.ID == memberGuid);
            //                    if (null != teamMemberInfo)
            //                        adminMembers.Add(teamMemberInfo);
            //                }
            //            }

            //        }
            //    }
            //}

            //x.IsAdmin = adminMembers.Exists(member => member.LoginName == currentUser.LoginName);
            ////获取用户的角色列表
            //var authListResult = await GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            //{
            //    var client = new AuthGrpc.AuthGrpcClient(channel);
            //    var response = await client.GetRoleInfoByUserIdAndProjectIdAsync(new GetRoleInfoByUserIdAndProjectIdRequest { UserId = currentUser.ID.ToString() });
            //    return response;
            //});

            //if (authListResult.IsSuccess)
            //{
            //    x.RoleDtoList.AddRange(authListResult.RoleList);
            //}

            ////获取用户的权限列表
            //var authinfoResult = await GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            //{
            //    var client = new AuthGrpc.AuthGrpcClient(channel);
            //    var response = await client.GetProjectAuthByUserIdAsync(new GetProjectAuthByUserIdRequest
            //    {
            //        UserId = currentUser.ID.ToString(),
            //        ProjectId = request.ProjectId
            //    });
            //    return response;
            //});

            //if (authinfoResult.IsSuccess)
            //{
            //    var a = _mapper.Map<RepeatedField<AuthInfoDto>>(authinfoResult.AuthInfoList);
            //    x.AuthInfoList.AddRange(a);
            //}


            ////获取用户锁定构件数量
            //_projectRepository = _projectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), Guid.Parse(request.ProjectId));

            //var proMem = _projectRepository.Members.SingleOrDefault(s => s.TeamMemberID == currentUser.ID);
            //if (proMem != null)
            //{
            //    var lockCount = _projectRepository.LockedComponents.Count(dat => dat.LockUserId == proMem.ID);
            //    x.LockCount = lockCount;
            //}


            var roleResult = _authorityManager.GetOwnRoleList(BuildinAdministrators.BuildInSystemAdminGuid.ToString(), out var roles, out var message);

            if (roleResult)
            {
                foreach (var role in roles)
                {
                    if (role.Type == 1) //团队管理员
                    {
                        List<Guid> memberGuids;
                        var ret = _authorityManager.GetRoleUsers(role.Id, out memberGuids, out message);

                        if (ret)
                        {
                            foreach (var memberGuid in memberGuids)
                            {
                                var teamMemberInfo = _teamRepository.Members.SingleOrDefault(m => m.ID == memberGuid);
                                if (null != teamMemberInfo)
                                    adminMembers.Add(teamMemberInfo);
                            }
                        }

                    }
                }
            }

            x.IsAdmin = adminMembers.Exists(member => member.LoginName == currentUser.LoginName);
            //获取用户的角色列表
            var roleRet = _authorityManager.GetRoleInfoByUserIdAndProjectId(currentUser.ID, projectId, out var memRoles, out message);

            if (roleRet)
            {
                x.RoleDtoList.AddRange(memRoles);
            }

            var authinfoResult = _authorityManager.GetProjectUserAuth(currentUser.ID, projectId, out var authInfos, out message);

            if (authinfoResult)
            {
                var a = _mapper.Map<RepeatedField<AuthInfoDto>>(authInfos);
                x.AuthInfoList.AddRange(a);
            }
            //获取用户锁定构件数量
            _projectRepository = _projectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), projectId);

            var proMem = _projectRepository.Members.SingleOrDefault(s => s.TeamMemberID == currentUser.ID);
            if (proMem != null)
            {
                var lockCount = _projectRepository.LockedComponents.Count(dat => dat.LockUserId == proMem.ID);
                x.LockCount = lockCount;
            }

            //获取锁定构件信息
            string domainClassDisplayName = "";
            var query = (from lc in _projectRepository.LockedComponents where lc.LockUserId == proMem.ID select lc).ToList();
            var lcInstanceID = query.Select(t => t.InstanceId).ToList();
            var modelLocked = (from ml in _projectRepository.Datas where lcInstanceID.Contains(ml.InstanceId) select ml).ToList();
            var domainIds = new HashSet<int>(modelLocked.Select(m => m.Domain).ToList());


            foreach (var did in domainIds)
            {
                GrpcLockInfo li = new GrpcLockInfo();
                //li.LockDataList = new List<GrpcLockDataInfo>();
                switch (did)
                {
                    case -1: domainClassDisplayName = ""; break;
                    case 0: domainClassDisplayName = "通用"; break;
                    case 1: domainClassDisplayName = "建筑"; break;
                    case 2: domainClassDisplayName = "结构"; break;
                    case 3: domainClassDisplayName = "暖通"; break;
                    case 4: domainClassDisplayName = "给排水"; break;
                    case 5: domainClassDisplayName = "电气"; break;
                    case 6: domainClassDisplayName = "装配式"; break;
                    case 11: domainClassDisplayName = "机电通用"; break;
                    case 16: domainClassDisplayName = "建筑机电通用"; break;
                    case 17: domainClassDisplayName = "结构设计"; break;
                    default: domainClassDisplayName = "其他"; break;
                }
                var clsID = new HashSet<string>(modelLocked.Where(d => d.Domain == did).Select(t => t.DomainClassName).ToList());
                int realCount = 0;
                foreach (var item in clsID)
                {
                    int clsCount = modelLocked.Count(dat => dat.DomainClassName == item && dat.Domain == did);
                    realCount += clsCount;
                    var schemaName = modelLocked.FirstOrDefault(m => m.DomainClassName == item).ECSchemaName;
                    if (clsCount > 0)
                    {
                        GrpcLockDataInfo lockDataInfo = new GrpcLockDataInfo
                        {
                            EcSchemaName = schemaName,
                            DomainClassName = item,
                            LockCount = clsCount
                        };
                        li.LockDataList.Add(lockDataInfo);
                    }

                }
                li.DomainId = did;
                li.DomainDisplayname = domainClassDisplayName;
                x.LockInfos.Add(li);
            }


            return x;
        }

        /// <summary>
        /// 获取团队成员列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMembersResponse> GetMembers(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetMembersResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var teamMember = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (teamMember is null)
            {
                x.Message = "获取当前用户信息失败！";
                return x;
            }
            //var teamId = await (from m in _teamRepository.Members where m.LoginName == userName select m.ID).FirstOrDefaultAsync();
            List<TeamMember> members = _teamRepository.Members.ToList();
            //if (userName != BuildinAdministrators.BuildInTeamAdministratorLoginName)
            //{
            //    members = _teamRepository.Members.Where(tm => tm.CreateId == teamId || tm.LoginName == userName).ToList();
            //}
            members.RemoveAll(
                mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));


            x.Issuccess = true;
            x.TeamMemberDtoList.AddRange(_mapper.Map<RepeatedField<GrpcTeamMember>>(members));
            return x;
        }

        public override async Task<GetProjectMembersByProjectIdResponse> GetProjectMembersByProjectId(GetProjectMembersByProjectIdRequest request, ServerCallContext context)
        {
            var x = new GetProjectMembersByProjectIdResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "sessinId 不存在或过期！";
                return x;
            }
            var pMembers = _teamRepository.GetProjectRepository(projectId).Members.ToList();
            foreach (var mem in pMembers)
            {
                var teamMember = _teamRepository.Members.SingleOrDefault(memT => memT.ID == mem.TeamMemberID);
                if (teamMember != null)
                {
                    var member = new GrpcProjectMember();
                    member.Id = mem.ID;
                    member.TeamMemberGuid = teamMember.ID.ToString();
                    member.LoginName = teamMember.LoginName;
                    member.DisplayName = teamMember.DisplayName;
                    member.Color = teamMember.Color;
                    member.Avatar = teamMember.Avatar;
                    member.IsProjectAdmin = mem.IsProjectAdmin;
                    x.TeamMemberDtoList.Add(member);
                }
            }

            x.Issuccess = true;
            return x;
        }

        /// <summary>
        /// 查看团队管理员列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMembersResponse> GetTeamAdministrators(GetTeamAdministratorsRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            //    var userName = _httpContextAccessor.HttpContext.Session.GetString("username");

            var x = new GetMembersResponse();
            x.Message = string.Empty;
            var adminMembers = new List<TeamMember>();

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            //if (!Guid.TryParse(request.ProjectId, out var projectId) || projectId == Guid.Empty)
            //{
            //    x.Message = $"参数{request.ProjectId}有误！";
            //    return x;
            //}


            //获取 是否管理员 信息
            var roleResult = GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
            {
                var client = new AuthGrpc.AuthGrpcClient(channel);

                _logger.LogDebug(" grpc client created, request={@request}", request);

                var authGrpcRequest = new GetOwnRoleListRequest { UserId = BuildinAdministrators.BuildInSystemAdminGuid.ToString() };

                var response = client.GetOwnRoleList(authGrpcRequest);

                _logger.LogDebug(" grpc response: {@response}", response);

                return response;
            });

            if (roleResult.Result.IsSuccess)
            {
                foreach (var role in roleResult.Result.RoleList)
                {
                    if (role.Type == 1) //团队管理员
                    {
                        List<Guid> memberGuids;

                        var ret = await GrpcCallerService.CallService(_urls.GrpcBimBaseAuth, async channel =>
                        {
                            var client = new AuthGrpc.AuthGrpcClient(channel);
                            var response = await client.GetUsersFromRoleIdAsync(new GetUsersFromRoleIdRequest { RoleId = role.Id });
                            return response;
                        });

                        if (ret.IsSuccess)
                        {
                            var p = ret.Result.Unpack<Value>();

                            memberGuids = System.Text.Json.JsonSerializer.Deserialize<List<Guid>>(p.StringValue);

                            foreach (var memberGuid in memberGuids)
                            {
                                var teamMemberInfo = _teamRepository.Members.SingleOrDefault(m => m.ID == memberGuid);
                                if (null != teamMemberInfo)
                                    adminMembers.Add(teamMemberInfo);
                            }
                        }


                    }
                }
            }

            x.Issuccess = true;
            x.TeamMemberDtoList.AddRange(_mapper.Map<RepeatedField<GrpcTeamMember>>(adminMembers));
            return x;
        }

        /// <summary>
        /// 获取团队模板列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override Task<GetTemplateResponse> GetTeamTemplateList(Empty request, ServerCallContext context)
        {
            var info = new GetTemplateResponse();
            TemplateCacheList templateCacheList = new TemplateCacheList();
            //templateCacheList.TeamRoleNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.TeamRoleRoot));
            //templateCacheList.TeamMemberNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.TeamMemberRoot));
            templateCacheList.MainProjectMemberAndPermissionNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.MainProjectMemberAndPermissionRoot));
            info.TemplateCacheList = templateCacheList;
            info.Issuccess = true;
            return Task.FromResult(info);
        }

        /// <summary>
        /// 获取项目模板列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override Task<GetTemplateResponse> GetProjectTemplateList(Empty request, ServerCallContext context)
        {
            var info = new GetTemplateResponse();
            info.TemplateCacheList.ProjectRoleNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.ProjectRoleRoot));
            info.TemplateCacheList.ProjectMemberNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.ProjectMemberRoot));
            info.TemplateCacheList.DomainClassNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.DomainClassRoot));
            info.TemplateCacheList.ReleaseNames.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.ReleaseRoot));
            info.TemplateCacheList.WorkGroups.AddRange(BimBaseServerTemplates.GetTemplateNameList(BimBaseServerTemplates.WGRoot));
            info.Issuccess = true;
            return Task.FromResult(info);
        }



        public override Task<GetVersionDatasResponse> GetVersionDatas(GetVersionDatasRequest request, ServerCallContext context)
        {
            var x = new GetVersionDatasResponse();
            if (request.EndTime.CompareTo(request.StartTime) < 0)
            {
                var tmp = request.StartTime;
                request.StartTime = request.EndTime;
                request.EndTime = tmp;
            }
            _projectRepository = _projectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), Guid.Parse(request.ProjectId));
            //找到项目下所有的版本数据
            var rtVersions
                    = (from ver in _projectRepository.Versions
                       where ver.Time > request.StartTime.ToDateTime()
                       && ver.Time < request.EndTime.ToDateTime()
                       select ver).ToList();

            x.Issuccess = true;
            x.VersionDataList.AddRange(_mapper.Map<RepeatedField<GrpcVersionData>>(rtVersions));
            return Task.FromResult(x);
        }


        public override async Task<GetUserGuidByLoginNameResponse> GetUserGuidByLoginName(GetUserGuidByLoginNameRequest request, ServerCallContext context)
        {
            var x = new GetUserGuidByLoginNameResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.Loginname))
            {
                x.Message = $"参数{request.Loginname}有误！";
                return x;
            }
            var member = await _teamRepository.Members.FirstOrDefaultAsync(u => u.LoginName == request.Loginname);
            if (member != null)
            {
                x.Issuccess = true;
                x.Memberid = member.ID.ToString();
            }
            return x;
        }

        public override async Task<GetTeamGroupAndMemberAuthByObjectIdResponse> GetTeamGroupAndMemberAuthByObjectId(GetTeamGroupAndMemberAuthByObjectIdRequest request, ServerCallContext context)
        {
            var x = new GetTeamGroupAndMemberAuthByObjectIdResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.Objectid))
            {
                x.Message = $"参数{request.Objectid}有误！";
                return x;
            }
            var objectid = Guid.Empty;
            if (!Guid.TryParse(request.Objectid, out objectid))
            {
                x.Message = $"参数{request.Objectid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            List<TeamGroupMemberAndAuth> teamGroupMemberAndAuths = new List<TeamGroupMemberAndAuth>();
            var teamGroupAuths = _teamRepository.TeamGroupAuths.ToList();

            var groups = _teamRepository.TeamGroups.ToList();
            var memlist = _teamRepository.Members.ToList();
            var objectGroupAuth = teamGroupAuths.Where(a => a.ObjectId == objectid).ToList();
            foreach (var item in objectGroupAuth)
            {
                TeamGroupMemberAndAuth teamGroupMemberAndAuth = new TeamGroupMemberAndAuth();
                List<TeamAuth> teamAuths = JsonSerializer.Deserialize<List<TeamAuth>>(item.AuthInfo);
                List<GrpcTeamAuth> retList = _mapper.Map<List<GrpcTeamAuth>>(teamAuths);
                if (item.IsGroupOrTeamMember == 0)
                {
                    var gp = groups.FirstOrDefault(g => g.Id == int.Parse(item.GroupOrMemberId));
                    teamGroupMemberAndAuth.GroupName = gp.GroupName;
                    teamGroupMemberAndAuth.TeamAuthList.Add(retList);
                    teamGroupMemberAndAuths.Add(teamGroupMemberAndAuth);
                }
                if (item.IsGroupOrTeamMember == 1)
                {
                    var mem = memlist.FirstOrDefault(m => m.ID == Guid.Parse(item.GroupOrMemberId));
                    teamGroupMemberAndAuth.GroupName = mem.LoginName;
                    teamGroupMemberAndAuth.TeamAuthList.Add(retList);
                    teamGroupMemberAndAuths.Add(teamGroupMemberAndAuth);
                }
            }
            x.IsSuccess = true;
            x.Memberauth.Add(teamGroupMemberAndAuths);
            return x;
        }

        public override async Task<GetAllTeammembersByMainProjectIdResponse> GetAllTeammembersByMainProjectId(GetAllTeammembersByMainProjectIdRequest request, ServerCallContext context)
        {
            var x = new GetAllTeammembersByMainProjectIdResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.MGuid))
            {
                x.Message = $"参数{request.MGuid}有误！";
                return x;
            }
            var mGuid = Guid.Empty;
            if (!Guid.TryParse(request.MGuid, out mGuid))
            {
                x.Message = $"参数{request.MGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            List<TeamMember> memberTmp = new List<TeamMember>();
            var mainproject = _teamRepository.MainProjects.FirstOrDefault(s => s.ID == mGuid);
            if (mainproject != null)
            {
                memberTmp = _teamRepository.GetTeamMembersByObjectId(mGuid, mGuid, 1); ;//_teamRepository.Members.ToList();

            }
            x.IsSuccess = true;
            x.Member.AddRange(_mapper.Map<List<GrpcTeamMember>>(memberTmp));
            return x;
        }

        public override async Task<GetAllTeammembersByMainProjectIdResponse> GetAllMembersByProjectGuid(GetAllTeammembersByMainProjectIdRequest request, ServerCallContext context)
        {
            var x = new GetAllTeammembersByMainProjectIdResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.MGuid))
            {
                x.Message = $"参数{request.MGuid}有误！";
                return x;
            }
            var mGuid = Guid.Empty;
            if (!Guid.TryParse(request.MGuid, out mGuid))
            {
                x.Message = $"参数{request.MGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            List<TeamMember> memberTmp = new List<TeamMember>();
            var mainproject = _teamRepository.MainProjects.FirstOrDefault(s => s.ID == mGuid);
            if (mainproject != null)
            {
                memberTmp = _teamRepository.GetTeamMembersByObjectId(mGuid, mGuid, 3); ;//_teamRepository.Members.ToList();

            }
            x.IsSuccess = true;
            x.Member.AddRange(_mapper.Map<List<GrpcTeamMember>>(memberTmp));
            return x;
        }

        public override async Task<GetTeamAuthResponse> GetCurrentObjectAuth(GetCurrentObjectAuthRequest request, ServerCallContext context)
        {
            var x = new GetTeamAuthResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.Objectid))
            {
                x.Message = $"参数{request.Objectid}有误！";
                return x;
            }
            var objectid = Guid.Empty;
            if (!Guid.TryParse(request.Objectid, out objectid))
            {
                x.Message = $"参数{request.Objectid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamGroupAuths = _teamRepository.TeamGroupAuths.ToList();
            List<GrpcTeamAuth> retList = new List<GrpcTeamAuth>();

            var objectGroupAuth = teamGroupAuths.Where(a => a.ObjectId == objectid && a.GroupOrMemberId == request.Groupormemberid).FirstOrDefault();
            if (objectGroupAuth != null)
            {
                List<TeamAuth> teamAuths = JsonSerializer.Deserialize<List<TeamAuth>>(objectGroupAuth.AuthInfo);
                retList = _mapper.Map<List<GrpcTeamAuth>>(teamAuths);
            }
            x.IsSuccess = true;
            x.AuthInfoList.AddRange(retList);
            return x;
        }

        public override async Task<GetAllMemberAuthListResponse> GetAllMemberAuthList(GetAllMemberAuthListRequest request, ServerCallContext context)
        {
            var x = new GetAllMemberAuthListResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }

            var teamGroupAuths = _teamRepository.TeamGroupAuths.Where(s => s.ObjectId == Guid.Empty && s.MainProjectId == Guid.Empty).ToList();
            x.IsSuccess = true;
            x.TeamgroupAuthList.AddRange(_mapper.Map<List<GrpcTeamGroupAuth>>(teamGroupAuths));
            return x;
        }

        public override async Task<GetTeamAuthResponse> GetAuthByObjectId(GetAuthByObjectIdRequest request, ServerCallContext context)
        {
            var x = new GetTeamAuthResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.Objectid) || string.IsNullOrEmpty(request.MainProjectId))
            {
                return x;
            }
            var objectid = Guid.Empty;
            if (!Guid.TryParse(request.Objectid, out objectid))
            {
                x.Message = $"参数{request.Objectid}格式有误！";
                return x;
            }
            var mainProjectId = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectId, out mainProjectId))
            {
                x.Message = $"参数{request.MainProjectId}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamGroupAuths = _teamRepository.GetAuthByObjectId(objectid, mainProjectId, request.Objecttype, currentUser.LoginName);
            List<GrpcTeamAuth> retList = new List<GrpcTeamAuth>();

            if (teamGroupAuths != null)
            {
                retList = _mapper.Map<List<GrpcTeamAuth>>(teamGroupAuths);
            }
            x.IsSuccess = true;
            x.AuthInfoList.AddRange(retList);
            return x;
        }

        public override async Task<GetTeamAuthResponse> GetTeamGroupAuth(GetTeamGroupAuthRequest request, ServerCallContext context)
        {
            var x = new GetTeamAuthResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            List<GrpcTeamAuth> retList = new List<GrpcTeamAuth>();
            var adminauth = _teamRepository.TeamGroupAuths.Where(s => s.GroupOrMemberId == request.Teamgroupid.ToString() && s.ObjectId == Guid.Empty).FirstOrDefault();
            if (adminauth != null)
            {
                var authinfo = adminauth.AuthInfo;
                if (!string.IsNullOrEmpty(authinfo))
                {
                    var teamAuths = JsonSerializer.Deserialize<List<TeamAuth>>(authinfo);
                    retList = _mapper.Map<List<GrpcTeamAuth>>(teamAuths);
                }
            }
            x.IsSuccess = true;
            x.AuthInfoList.AddRange(retList);
            return x;
        }

        public override async Task<GetModelLockUserByProjectGuidResponse> GetModelLockUserByProjectGuid(GetModelLockUserByProjectGuidRequest request, ServerCallContext context)
        {
            var x = new GetModelLockUserByProjectGuidResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var projectGuid = Guid.Empty;
            if (!Guid.TryParse(request.ProjectGuid, out projectGuid))
            {
                x.Message = $"参数{request.ProjectGuid}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var lockmodelList = _teamRepository.TeamProjectModelLocks.ToList();
            //查询模型是否被其他人锁定
            var lockmodel = lockmodelList.FirstOrDefault(s => s.ProjectGuid == projectGuid);
            var modellockuser = "";

            if (lockmodel != null)
            {
                modellockuser = lockmodel.ModelLockUser;
            }
            x.IsSuccess = true;
            x.Modellockuser = modellockuser;
            return x;
        }

        public override async Task<GetAllTeamUserGroupsResponse> GetAllTeamUserGroups(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var userName = _httpContextAccessor.HttpContext.Session.GetString("username");

            var x = new GetAllTeamUserGroupsResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamUserGroups = _teamRepository.TeamUserGroups.ToList();
            x.IsSuccess = true;
            x.TeamuserGroups.AddRange(_mapper.Map<List<GrpcTeamUserGroup>>(teamUserGroups));
            return x;
        }

        public override async Task<GetTeamGroupMembersByMainProjectIDsResponse> GetTeamGroupMembersByMainProjectIDs(GetTeamGroupMembersByMainProjectIDsRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var userName = _httpContextAccessor.HttpContext.Session.GetString("username");

            var x = new GetTeamGroupMembersByMainProjectIDsResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var objectGuid = Guid.Empty;
            if (!Guid.TryParse(request.ObjectGuid, out objectGuid))
            {
                x.Message = $"参数{request.ObjectGuid}格式有误！";
                return x;
            }
            var objecttype = request.Objecttype;
            List<TeamMember> retTeamGroups = new List<TeamMember>();
            //获取当前项目的用户组列表 MemberType = 1为用户
            var currentMainProjectTeamGroups = _teamRepository.MainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 1).ToList();
            if (currentMainProjectTeamGroups != null && currentMainProjectTeamGroups.Any())
            {
                if (objecttype == 1)
                {
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => t.ObjectId == objectGuid || t.ObjectId == Guid.Empty).ToList();
                }
                else if (objecttype == 2)
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectGuid);
                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                else
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    //_logger.LogInformation("GetAuthByObectId=======查询上级文件夹id:");
                    Guid fileGuid = Guid.Empty;
                    if (objecttype == 3)
                    {
                        var teamproject = _teamRepository.Projects.FirstOrDefault(t => t.ID == objectGuid);

                        if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
                        {
                            fileGuid = Guid.Parse(teamproject.FileDirectoryID);
                        }
                    }
                    else if (objecttype == 4)
                    {
                        var volume = _teamRepository.Volumes.FirstOrDefault(t => t.VolumeId == objectGuid);
                        if (volume != null)
                        {
                            fileGuid = volume.FileDirectoryId;
                        }
                    }
                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(objectGuid);
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);


                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                var currentTeamGroupIds = currentMainProjectTeamGroups.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamGroupIds != null && currentTeamGroupIds.Any())
                {
                    var intCurrentTeamGroupIds = currentTeamGroupIds.Select<string, Guid>(x => Guid.Parse(x)).ToList();
                    retTeamGroups = _teamRepository.Members.Where(t => intCurrentTeamGroupIds.Contains(t.ID)).ToList();
                    retTeamGroups.RemoveAll(mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));

                }

            }

            x.IsSuccess = true;
            x.Teammembers.AddRange(_mapper.Map<List<GrpcTeamMember>>(retTeamGroups));
            return x;

        }

        public override async Task<GetTeamGroupsByMainProjectIdResponse> GetTeamGroupsByMainProjectId(GetTeamGroupsByMainProjectIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamGroupsByMainProjectIdResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var mainProjectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectGuid, out mainProjectGuid))
            {
                x.Message = $"参数{request.MainProjectGuid}格式有误！";
                return x;
            }
            var objectGuid = Guid.Empty;
            if (!Guid.TryParse(request.ObjectGuid, out objectGuid))
            {
                x.Message = $"参数{request.ObjectGuid}格式有误！";
                return x;
            }
            var objecttype = request.Objecttype;
            List<TeamGroup> retTeamGroups = new List<TeamGroup>();
            //获取当前项目的用户组列表 MemberType = 0为用户组
            var currentMainProjectTeamGroups = _teamRepository.MainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 0).ToList();
            if (currentMainProjectTeamGroups != null && currentMainProjectTeamGroups.Any())
            {
                if (objecttype == 1)
                {
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => t.ObjectId == objectGuid || t.ObjectId == Guid.Empty).ToList();
                }
                else if (objecttype == 2)
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectGuid);
                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                else
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    //_logger.LogInformation("GetAuthByObectId=======查询上级文件夹id:");
                    Guid fileGuid = Guid.Empty;
                    if (objecttype == 3)
                    {
                        var teamproject = _teamRepository.Projects.FirstOrDefault(t => t.ID == objectGuid);

                        if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
                        {
                            fileGuid = Guid.Parse(teamproject.FileDirectoryID);
                        }
                    }
                    else if (objecttype == 4)
                    {
                        var volume = _teamRepository.Volumes.FirstOrDefault(t => t.VolumeId == objectGuid);
                        if (volume != null)
                        {
                            fileGuid = volume.FileDirectoryId;
                        }
                    }
                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(objectGuid);
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);


                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                //获取用户组id，去重
                var currentTeamGroupIds = currentMainProjectTeamGroups.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamGroupIds != null && currentTeamGroupIds.Any())
                {
                    var intCurrentTeamGroupIds = currentTeamGroupIds.Select<string, int>(x => Convert.ToInt32(x)).ToList();
                    retTeamGroups = _teamRepository.TeamGroups.Where(t => intCurrentTeamGroupIds.Contains(t.Id)).ToList();

                }
            }

            x.IsSuccess = true;
            x.Teamgroups.AddRange(_mapper.Map<List<GrpcTeamGroup>>(retTeamGroups));
            return x;
        }


        public override async Task<GetTeamRoleMembersResponse> GetTeamRoleMembers(GetTeamRoleMembersRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamRoleMembersResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamUserRoles = _teamRepository.TeamUserGroups.Where(t => t.GroupId == request.TeamRoleId).ToList();
            List<TeamMember> memberTmp = new List<TeamMember>();
            if (teamUserRoles.Any())
            {

                foreach (var t in teamUserRoles)
                {
                    Guid memGuid = t.TeamMemberId;
                    TeamMember mem = _teamRepository.Members.FirstOrDefault(mem => mem.ID == memGuid);
                    if (mem != null)
                    {
                        memberTmp.Add(mem);
                    }

                }
            }
            x.IsSuccess = true;
            x.Teammembers.AddRange(_mapper.Map<List<GrpcTeamMember>>(memberTmp));
            return x;
        }

        public override async Task<GetUserRolesAndSubLibAuthResponse> GetUserRolesAndSubLibAuth(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            GetUserRolesAndSubLibAuthResponse x = new GetUserRolesAndSubLibAuthResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            _logger.LogInformation("GetUserRolesAndSubLibAuth begin");
            Stopwatch sw = new Stopwatch();
            sw.Start();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            GrpcUserRoleLibAuthInfo userRoleLibAuthInfo = new GrpcUserRoleLibAuthInfo();
            var username = currentUser.LoginName;
            var teamMember = _teamRepository.Members.SingleOrDefault(m => m.LoginName == username);

            userRoleLibAuthInfo.TeamMemberGuid = teamMember.ID.ToString();
            userRoleLibAuthInfo.LoginName = username;
            List<GrpcMPUserGroupLibAuthWithMPGuid> dicLibAuthlist = new List<GrpcMPUserGroupLibAuthWithMPGuid>();
            List<GrpcMPUserGroupAuthWithMPGuid> dicTeamGroupAuthList = new List<GrpcMPUserGroupAuthWithMPGuid>();

            var teamgroups = _teamRepository.TeamUserGroups.Where(s => s.TeamMemberId == teamMember.ID).Select(s => s.GroupId.ToString()).ToList();
            //用户所在的企业级用户组及外层权限
            var userTeamgroupauths = _teamRepository.TeamGroupAuths.Where(s => teamgroups.Contains(s.GroupOrMemberId) && s.MainProjectId == Guid.Empty && s.ObjectId == Guid.Empty).ToList();
            userRoleLibAuthInfo.TeamGroupAuths.AddRange(_mapper.Map<List<GrpcTeamGroupAuth>>(userTeamgroupauths));// = userTeamgroupauths;

            //查询用户的元件库等级库权限
            //查询用户参与的项目
            var userMPList = _teamRepository.GetMainProjectListByMember(teamMember.ID);
            stopwatch.Stop();
            _logger.LogInformation(username + "GetUserRolesAndSubLibAuth 获取部门权限耗时：" + stopwatch.ElapsedMilliseconds);
            var mpids = userMPList.Select(s => s.ID).ToList();
            HashSet<Guid> hashsetMpids = new HashSet<Guid>(mpids);
            _logger.LogInformation(username + "GetUserRolesAndSubLibAuth mpids.count：" + mpids.Count);
            var allmainprojectgroupauths = _teamRepository.mainProjectUserGroupAuths.InRange(s => s.MainProjectID, 10000, hashsetMpids).ToList();
            var allmainprojectgroupusers = _teamRepository.mainProjectUserGroupMembers.InRange(s => s.MainProjectId, 10000, hashsetMpids).ToList();
            var allmainprojectgrouplibauths = _teamRepository.mainProjectUserGroupLibAuths.InRange(s => s.MainProjectID, 10000, hashsetMpids).ToList();
            var dicgroupuser = allmainprojectgroupusers
                        .Where(s => s.TeamMemberId == teamMember.ID)
                        .GroupBy(s => s.MainProjectId)
                        .ToDictionary(g => g.Key, g => g.ToList());
            var realmpIds = dicgroupuser.Keys.ToList();

            var hasLibAuths = allmainprojectgrouplibauths
                        .Where(s => realmpIds.Contains(s.MainProjectID) && s.Permission == 1).ToList();

            //dicLibAuthlist 
            var libauthlist = hasLibAuths
                        .Where(s => mpids.Contains(s.MainProjectID) && s.Permission == 1 && dicgroupuser[s.MainProjectID].Select(u => u.UserGroupId).Contains(s.MPUserGroupId))
                        .GroupBy(s => s.MainProjectID)
                        .ToDictionary(g => g.Key, g => g.Select(s =>
                            new MPUserGroupLibAuth
                            {
                                ID = s.ID,
                                MPUserGroupId = s.MPUserGroupId,
                                LibType = s.LibType,
                                Permission = s.Permission,
                                AuthInfo = s.AuthInfo,
                                ExtendStr = s.ExtendStr
                            }).ToList());

            foreach (var libauth in libauthlist)
            {
                GrpcMPUserGroupLibAuthWithMPGuid grpcMPUserGroupLibAuthWithMPGuid = new GrpcMPUserGroupLibAuthWithMPGuid();
                grpcMPUserGroupLibAuthWithMPGuid.MpGuid = libauth.Key.ToString();
                libauthlist.TryGetValue(libauth.Key, out var mPUserGroupLibAuths);
                if (mPUserGroupLibAuths != null && mPUserGroupLibAuths.Any())
                {
                    grpcMPUserGroupLibAuthWithMPGuid.MpUserGroupLibAuths.AddRange(_mapper.Map<List<GrpcMPUserGroupLibAuth>>(mPUserGroupLibAuths));
                }
                dicLibAuthlist.Add(grpcMPUserGroupLibAuthWithMPGuid);
            }
            //dicLibAuthlist = _mapper.Map<List<GrpcMPUserGroupLibAuthWithMPGuid>>(libauthlist);
            var hasgroupauths = allmainprojectgroupauths.Where(s => realmpIds.Contains(s.MainProjectID)).ToList();
            var teamGroupAuthList = hasgroupauths
                .Where(s => mpids.Contains(s.MainProjectID) && dicgroupuser[s.MainProjectID].Select(u => u.UserGroupId.ToString()).Contains(s.GroupOrMemberId))
                .GroupBy(s => s.MainProjectID)
                .ToDictionary(g => g.Key, g => g.Select(s =>
                    new MPUserGroupAuth
                    {
                        Id = s.Id,
                        GroupOrMemberId = s.GroupOrMemberId,
                        IsGroupOrTeamMember = s.IsGroupOrTeamMember,
                        AuthInfo = s.AuthInfo,
                        ObjectType = s.ObjectType,
                        ObjectId = s.ObjectId,
                        InstanceId = s.InstanceId,
                        TreeId = s.TreeId
                    }).ToList());
            foreach (var item in teamGroupAuthList)
            {
                GrpcMPUserGroupAuthWithMPGuid grpcMPUserGroupAuthWithMPGuid = new GrpcMPUserGroupAuthWithMPGuid();
                grpcMPUserGroupAuthWithMPGuid.MpGuid = item.Key.ToString();
                teamGroupAuthList.TryGetValue(item.Key, out var mPUserGroupAuths);
                if (mPUserGroupAuths != null && mPUserGroupAuths.Any())
                {
                    grpcMPUserGroupAuthWithMPGuid.MpUserGroupAuths.AddRange(_mapper.Map<List<GrpcMPUserGroupAuth>>(mPUserGroupAuths));
                }
                dicTeamGroupAuthList.Add(grpcMPUserGroupAuthWithMPGuid);
            }
            //dicTeamGroupAuthList = _mapper.Map<List<GrpcMPUserGroupAuthWithMPGuid>>(teamGroupAuthList);

            userRoleLibAuthInfo.MpUserGroupLibAuthList.AddRange(dicLibAuthlist);// = dicLibAuthlist;
            userRoleLibAuthInfo.MpUserGroupAuthList.AddRange(dicTeamGroupAuthList);// = dicTeamGroupAuthList;
            allmainprojectgroupusers = allmainprojectgroupusers.Where(s => mpids.Contains(s.MainProjectId)).ToList();
            userRoleLibAuthInfo.MainProjectUserGroupMembers.AddRange(_mapper.Map<List<GrpcMainProjectUserGroupMember>>(allmainprojectgroupusers));
            sw.Stop();
            _logger.LogInformation("GetUserRolesAndSubLibAuth 一共耗时：" + sw.ElapsedMilliseconds);
            _logger.LogInformation("GetUserRolesAndSubLibAuth userRoleLibAuthInfo.mPUserGroupLibAuths count:" + userRoleLibAuthInfo.MpUserGroupLibAuthList.Count);
            _logger.LogInformation("GetUserRolesAndSubLibAuth userRoleLibAuthInfo.mPUserGroupAuths count:" + userRoleLibAuthInfo.MpUserGroupAuthList.Count);

            x.UserRoleLibAuthInfo = userRoleLibAuthInfo;
            x.IsSuccess = true;
            return x;
        }


        /// <summary>
        /// 工艺三维使用，获取用户参与的项目列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetAllMainprojectListByTeammberIdResponse> GetAllMainprojectListByTeammberId(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetAllMainprojectListByTeammberIdResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            List<MainProject> mainProjects = new List<MainProject>();

            string major = _major.Major;
            if (string.Equals(major, "Elec", StringComparison.OrdinalIgnoreCase))
            {
                var name = currentUser.LoginName;
                if ((name == BuildinAdministrators.BuildInTeamAdministratorLoginName) || (name == BuildinAdministrators.BuildInSystemModelAdminLoginName) || (name == BuildinAdministrators.BuildInSystemAdminLoginName))
                    mainProjects = _teamRepository.MainProjects.OrderBy(s => s.CreationTime).ToList();
                else
                    //projects = mem.Projects.OrderBy(s => s.CreationTime).ToList();
                    mainProjects = _teamRepository.GetMainProjectListByMember(currentUser.ID);
            }
            else
            {
                var allmplist = _teamRepository.MainProjects.ToList();
                if (currentUser != null)
                {
                    var usermainproject = _teamRepository.mainProjectUserGroupMembers.Where(s => s.TeamMemberId == currentUser.ID).ToList();
                    if (usermainproject != null && usermainproject.Any())
                    {
                        var mpids = usermainproject.Select(u => u.MainProjectId).Distinct().ToList();
                        mainProjects = allmplist.Where(m => mpids.Contains(m.ID)).ToList();
                    }
                }
            }
            x.IsSuccess = true;
            x.MainProjects.AddRange(_mapper.Map<List<GrpcMainProject>>(mainProjects));
            return x;
        }
        /// <summary>
        /// 根据id获取用户组信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetTeamGroupByIDResponse> GetTeamGroupByID(GetTeamGroupByIDRequest request, ServerCallContext context)
        {

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamGroupByIDResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var groupId = request.GroupId;
            var teamGroup = _teamRepository.TeamGroups.FirstOrDefault(m => m.Id == groupId);
            x.IsSuccess = true;
            x.TeamGroup = _mapper.Map<GrpcTeamGroup>(teamGroup);
            return x;
        }

        /// <summary>
        /// 获取用户组列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetTeamGroupsResponse> GetTeamGroups(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamGroupsResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamGroups = _teamRepository.TeamGroups.ToList();
            x.IsSuccess = true;
            x.TeamGroups.AddRange(_mapper.Map<List<GrpcTeamGroup>>(teamGroups));
            return x;
        }
        /// <summary>
        /// 批量获取用户部门分组，按loginname（账户名）字母升序排列
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetAllTeamUserGroupsWithUserInfoResponse> GetAllTeamUserGroupsWithUserInfo(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetAllTeamUserGroupsWithUserInfoResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamGroups = _teamRepository.TeamGroups.ToDictionary(g => g.Id, g => g.GroupName);
            var allGroupsUsers = _teamRepository.TeamUserGroups.Select(g => g.TeamMemberId).ToList();
            var allteammember = _teamRepository.Members.Where(t => allGroupsUsers.Contains(t.ID)).ToList();
            //从角色用户关联表中获取teamroleid角色的用户id列表
            var userTeamGroups = _teamRepository.TeamUserGroups.GroupBy(g => g.GroupId).ToDictionary(g => g.Key, g => g.ToList());
            List<GrpcTeamUserGroupsWithUserInfo> grpcTeamUserGroupsWithUserInfos = new List<GrpcTeamUserGroupsWithUserInfo>();
            foreach (var gid in userTeamGroups.Keys)
            {
                GrpcTeamUserGroupsWithUserInfo grpcTeamUserGroupsWithUserInfo = new GrpcTeamUserGroupsWithUserInfo();
                string groupname = teamGroups[gid];
                var teammember = userTeamGroups[gid];
                var teammemberIds = teammember.Select(t => t.TeamMemberId).ToList();
                var members = allteammember.Where(t => teammemberIds.Contains(t.ID)).OrderBy(g => g.LoginName).ToList();
                grpcTeamUserGroupsWithUserInfo.GroupId = gid;
                grpcTeamUserGroupsWithUserInfo.GroupMembers.AddRange(_mapper.Map<List<GrpcTeamMember>>(members));
                grpcTeamUserGroupsWithUserInfos.Add(grpcTeamUserGroupsWithUserInfo);
            }
            x.IsSuccess = true;
            x.DicUserGroupMember.AddRange(grpcTeamUserGroupsWithUserInfos);
            return x;
        }

        /// <summary>
        /// 通过用户id获取用户所有的用户组（企业级用户组（部门））
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetTeamGroupListByTeamMemberIdResponse> GetTeamGroupListByTeamMemberId(GetTeamGroupListByTeamMemberIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamGroupListByTeamMemberIdResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var teamMemberGuid = Guid.Empty;
            if (!Guid.TryParse(request.TeamMemberGuid, out teamMemberGuid))
            {
                x.Message = $"参数{request.TeamMemberGuid}格式有误！";
                return x;
            }
            List<GrpcTeamGroup> userTeamGroups = new List<GrpcTeamGroup>();
            List<TeamGroup> teamGroups = _teamRepository.TeamGroups.ToList();
            //从角色用户关联表中获取teamroleid角色的用户id列表
            var teamUserGroups = _teamRepository.TeamUserGroups.ToList();
            if (teamUserGroups != null)
            {
                var userGroupIds = teamUserGroups.Where(s => s.TeamMemberId == teamMemberGuid).Select(t => t.GroupId).ToList();
                if (userGroupIds != null && userGroupIds.Any())
                {
                    var tgs = teamGroups.Where(s => userGroupIds.Contains(s.Id)).ToList();
                    userTeamGroups = _mapper.Map<List<GrpcTeamGroup>>(tgs);
                }
            }
            x.IsSuccess = true;
            x.UserTeamGroups.AddRange(userTeamGroups);
            return x;
        }

        /// <summary>
        /// 通过id获取用户信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetTeamMemberByIDResponse> GetTeamMemberByID(GetTeamMemberByIDRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamMemberByIDResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var memberId = Guid.Empty;
            if (!Guid.TryParse(request.MemberId, out memberId))
            {
                x.Message = $"参数{request.MemberId}格式有误！";
                return x;
            }
            var teamMember = _teamRepository.Members.FirstOrDefault(m => m.ID == memberId);
            if (null == teamMember)
            {
                x.Message = "未找到id为" + memberId + "的用户";
                x.IsSuccess = false;
                return x;
            }
            x.IsSuccess = true;
            x.TeamMember = _mapper.Map<GrpcTeamMember>(teamMember);
            return x;
        }
        /// <summary>
        /// 通过账号loginName获取用户信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetTeamMemberByNameResponse> GetTeamMemberByName(GetTeamMemberByNameRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetTeamMemberByNameResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (string.IsNullOrEmpty(request.MemberName))
            {
                x.Message = $"参数{request.MemberName}格式有误！";
                return x;
            }
            var teamMember = _teamRepository.Members.FirstOrDefault(m => m.LoginName == request.MemberName);
            if (null == teamMember)
            {
                x.Message = "未找到用户" + request.MemberName;
                x.IsSuccess = false;
                return x;
            }
            x.IsSuccess = true;
            x.TeamMember = _mapper.Map<GrpcTeamMember>(teamMember);
            return x;
        }


        /// <summary>
        /// 获取mainproject参与用户数
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMainProjectMembersResponse> GetMainProjectMembers(GetMainProjectMembersRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetMainProjectMembersResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var mainGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainGuid, out mainGuid))
            {
                x.Message = $"参数{request.MainGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            List<TeamMember> mainMembers = new List<TeamMember>();
            MainProject main = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainGuid);
            if (main != null)
            {
                mainMembers = _teamRepository.GetTeamMembersByMainProject(mainGuid);//main.MainProjectTeamMembers;
                mainMembers.RemoveAll(
                mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));
            }
            x.IsSuccess = true;
            x.MainMembers.AddRange(_mapper.Map<List<GrpcTeamMember>>(mainMembers));
            return x;
        }

        public override async Task<GetAllMembersAuthByObectIdResponse> GetAllMembersAuthByObectId(GetAllMembersAuthByObectIdRequest request, ServerCallContext context)
        {
            var x = new GetAllMembersAuthByObectIdResponse();

            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            if (string.IsNullOrEmpty(request.Objectid) || string.IsNullOrEmpty(request.MainProjectId))
            {
                return x;
            }
            var objectid = Guid.Empty;
            if (!Guid.TryParse(request.Objectid, out objectid))
            {
                x.Message = $"参数{request.Objectid}格式有误！";
                return x;
            }
            var mainProjectId = Guid.Empty;
            if (!Guid.TryParse(request.MainProjectId, out mainProjectId))
            {
                x.Message = $"参数{request.MainProjectId}格式有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            //获取文件夹的父辈文件夹节点
            var allFileDirectories = _teamRepository.FileDirectories.Where(f => f.MainProjectId == mainProjectId).ToList();
            var teamproject = _teamRepository.Projects.FirstOrDefault(t => t.ID == objectid);

            var projectManager = _teamRepository.GetProjectRepository(objectid);
            var members = _teamRepository.GetTeamMembersByObjectId(mainProjectId, objectid, 3);//projectManager.Members;

            //log.Info("GetAllMembersAuthByObectId=======查询上级文件夹id:");
            Guid fileGuid = Guid.Empty;
            if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
            {
                fileGuid = Guid.Parse(teamproject.FileDirectoryID);
            }
            List<Guid> objIds = new List<Guid>();
            objIds.Add(objectid);
            var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);

            if (objectFileDir != null)
            {
                objIds.Add(objectFileDir.ID);

                while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                {
                    objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);
                    }

                }
            }

            var teamGroupAuths = _teamRepository.TeamGroupAuths.ToList();
            //log.Info("GetAllMembersAuthByObectId=======objIds个数:" + objIds.Count);
            //定义以成员名字为key，成员权限列表为value的Dictionary
            Dictionary<string, List<GrpcTeamAuth>> allMemberAuthList = new Dictionary<string, List<GrpcTeamAuth>>();

            if (teamGroupAuths == null || (!teamGroupAuths.Any()))
            {
                //log.Info("GetAllMembersAuthByObectId=======teamGroupAuths无数据");
                teamGroupAuths = new List<TeamGroupAuth>();
            }

            foreach (var mem in members)
            {
                var cutUser = _teamRepository.Members.FirstOrDefault(m => m.ID == mem.ID);
                if (cutUser != null)
                {
                    Guid teammemberid = cutUser.ID;

                    List<TeamAuth> retList = new List<TeamAuth>();
                    List<TeamAuth> allAuthList = new List<TeamAuth>();

                    //查询当前用户参与的teamgroup
                    var teamuserGroups = _teamRepository.TeamUserGroups.Where(g => g.TeamMemberId == teammemberid).ToList();
                    //查询当前用户参与的teamgroupid
                    var groupids = new List<int>();
                    if (teamuserGroups != null && teamuserGroups.Any())
                    {
                        groupids = teamuserGroups.Select(s => s.GroupId).ToList();
                    }
                    else
                    {
                        //log.Info("GetAllMembersAuthByObectId=======" + teammemberid + "未加入任何group");
                    }



                    //权限列表，权限对象id为所有父辈节点id
                    var objectGroupAuth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == teammemberid.ToString()).ToList();
                    if (objectGroupAuth != null && objectGroupAuth.Any())
                    {
                        foreach (var ob in objectGroupAuth)
                        {
                            List<TeamAuth> authList = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                            if (authList.Any())
                            {
                                allAuthList.AddRange(authList);
                            }
                        }
                    }
                    else
                    {
                        //log.Info("GetAllMembersAuthByObectId=======" + teammemberid + "未设置权限");
                    }

                    //log.Info("GetAllMembersAuthByObectId=======groupids具有的权限列表");
                    if (groupids.Any())
                    {
                        //groupids具有的权限列表
                        foreach (var gid in groupids)
                        {
                            var groupauth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == gid.ToString()).ToList();
                            if (groupauth != null && groupauth.Any())
                            {
                                foreach (var ob in groupauth)
                                {
                                    List<TeamAuth> gauth = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                                    if (gauth.Any())
                                    {
                                        allAuthList.AddRange(gauth);
                                    }
                                }
                            }

                        }
                    }
                    //log.Info("GetAllMembersAuthByObectId=======按权限名称去重");
                    //按权限名称去重
                    List<string> authNameList = allAuthList.Select(s => s.AuthName).Distinct().ToList();
                    foreach (var authName in authNameList)
                    {
                        TeamAuth ta = new TeamAuth();
                        ta.AuthName = authName;
                        var noAuth = allAuthList.Where(a => a.AuthName == authName && a.Permission == 0).ToList();
                        //相同权限名称中有权限为0，即任意父级节点无该操作权限，则判定为无该操作权限
                        if (noAuth.Any())
                        {
                            ta.Permission = 0;
                        }
                        else
                        {
                            ta.Permission = 1;
                        }
                        retList.Add(ta);
                    }
                    //log.Info("GetAllMembersAuthByObectId=======分组");
                    var nlist = retList.Select(s => s.AuthName).ToList();
                    var alist = retList.Select(s => s.Permission).ToList();
                    if (!allMemberAuthList.ContainsKey(cutUser.LoginName))
                    {
                        var grpcRetList = _mapper.Map<List<GrpcTeamAuth>>(retList);
                        //log.Info("GetAllMembersAuthByObectId=======" + cutUser.LoginName + "|权限:" + string.Join(",", nlist) + "|permission:" + string.Join(",", alist));
                        allMemberAuthList.Add(cutUser.LoginName, grpcRetList);
                    }

                }

            }
            //log.Info("GetAllMembersAuthByObectId=======end");
            //return allMemberAuthList;
            return x;
        }

        public override async Task<GrpcResult> ExportMainprojectAuthInfoByMainprojectGuid(ExportMainprojectAuthInfoByMainprojectGuidRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            string username = currentUser.LoginName;
            if (!Guid.TryParse(request.MainProjectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数MainProjectGuid格式有误！";
                return x;
            }
            var templatePath = request.TemplatePath;
            if (mainProjectGuid != Guid.Empty && !string.IsNullOrEmpty(templatePath))
            {
                var allmainprojectTeamgroup = _teamRepository.MainProjectTeamGroups.ToList();
                var allteamgroupauth = _teamRepository.TeamGroupAuths.ToList();
                //查询项目（mainproject）参与用户
                //mainprojectteamgroups中object为mainprojectguid的数据
                var mainprojectTeamgroupList = allmainprojectTeamgroup.Where(m => m.ObjectId == mainProjectGuid).ToList();
                var teamgroupAuthList = allteamgroupauth.Where(t => t.ObjectId == mainProjectGuid).ToList();
                MainprojectAuthTemplate mpAuth = new MainprojectAuthTemplate();
                mpAuth.MainProjectTeamGroupList = mainprojectTeamgroupList;
                mpAuth.TeamGroupAuthList = teamgroupAuthList;

                BimBaseServerTemplates.SaveTemplateFile(mpAuth, BimBaseServerTemplates.MainProjectMemberAndPermissionRoot + templatePath + ".tplt");
                x.IsSuccess = true;

                return x;
            }
            return x;
        }

        /// <summary>
        /// 导入成员及权限模板（导入权限）
        /// </summary>
        /// <remarks>WEB</remarks>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GrpcResult> ImportMainprojectAuthInfoToMainprojectGuid(ImportMainprojectAuthInfoToMainprojectGuidRequest request, ServerCallContext context)
        {
            var x = new GrpcResult();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }

            string username = currentUser.LoginName;
            if (!Guid.TryParse(request.MainProjectGuid, out var mainProjectGuid))
            {
                x.Message = $"参数MainProjectGuid格式有误！";
                return x;
            }
            var templatePath = request.TemplatePath;
            if (mainProjectGuid != Guid.Empty && !string.IsNullOrEmpty(templatePath))
            {
                using (var scope = new TransactionScope(
                        TransactionScopeOption.Required,
                        new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted },
                        TransactionScopeAsyncFlowOption.Enabled))
                {
                    MainProject mp = _teamRepository.MainProjects.FirstOrDefault(m => m.ID == mainProjectGuid);
                    var members = _teamRepository.Members.ToList();
                    MainprojectAuthTemplate authtemplist = BimBaseServerTemplates.ReadTemplateFile<MainprojectAuthTemplate>(BimBaseServerTemplates.MainProjectMemberAndPermissionRoot + templatePath + ".tplt");
                    List<MainProjectTeamGroup> mptGroupList = authtemplist.MainProjectTeamGroupList;
                    List<TeamGroupAuth> tgAuthList = authtemplist.TeamGroupAuthList;
                    if (mptGroupList != null && mptGroupList.Any())
                    {
                        //MainProjectTeamGroup 项目成员表信息导入
                        foreach (var a in mptGroupList)
                        {
                            if (a.MemberType == 0)
                            {
                                int intgroupid = int.Parse(a.GroupOrTeamMemberId);
                                TeamGroup tg = _teamRepository.TeamGroups.FirstOrDefault(g => g.Id == intgroupid);
                                if (tg != null)
                                {
                                    _teamRepository.AddTeamGroupToMainproject(tg, mp, mp.ID);
                                }

                            }
                            else
                            {
                                Guid memberGuid = Guid.Parse(a.GroupOrTeamMemberId);
                                TeamMember tm = members.FirstOrDefault(mem => mem.ID == memberGuid);
                                if (tm != null)
                                {
                                    _teamRepository.AddTeamGrouMemberToMainProject(tm, mp, mp.ID);
                                }
                            }
                        }
                        //
                    }
                    if (tgAuthList != null && tgAuthList.Any())
                    {
                        foreach (var t in tgAuthList)
                        {
                            TeamGroupAuth newAuth = new TeamGroupAuth
                            {
                                MainProjectId = mp.ID,
                                GroupOrMemberId = t.GroupOrMemberId,
                                IsGroupOrTeamMember = t.IsGroupOrTeamMember,
                                ObjectId = mp.ID,
                                ObjectType = 1,
                                AuthInfo = t.AuthInfo

                            };
                            var ret = _teamRepository.DeleteTeamGroupAuth(newAuth);
                            if (ret)
                            {
                                _teamRepository.GiveAuthToTeamGroup(newAuth);
                            }
                        }
                    }

                    scope.Complete();
                    x.IsSuccess = true;
                    return x;
                }

            }
            return x;
        }


        public override async Task<GrpcResult> TestLogging(TestLoggingRequest request, ServerCallContext context)
        {
            _logger.LogInformation("TestLogging 测试流程开始");


            _logger.LogInformation("SessionId: {SessionId}, MainProjectGuid: {MainProjectGuid}", request.SessionId, request.MainProjectGuid);

            var rand = new Random();
            int mode = rand.Next(0, 6); // 0-2: 正常流程, 3-5: 异常流程

            switch (mode)
            {
                case 0:
                    // 正常流程1：加法
                    int a = 1, b = 2, c = a + b;
                    _logger.LogInformation("正常流程1：加法完成，a={a}, b={b}, c={c}", a, b, c);
                    return new GrpcResult { IsSuccess = true, Message = "正常流程1：加法完成" };
                case 1:
                    // 正常流程2：字符串拼接
                    string s1 = "Hello", s2 = "World", s3 = s1 + " " + s2;
                    _logger.LogInformation("正常流程2：字符串拼接，s1={s1}, s2={s2}, s3={s3}", s1, s2, s3);
                    return new GrpcResult { IsSuccess = true, Message = "正常流程2：字符串拼接完成" };
                case 2:
                    // 正常流程3：模拟数据库查询
                    var list = new List<int> { 1, 2, 3, 4, 5 };
                    int sum = list.Sum();
                    _logger.LogInformation("正常流程3：模拟数据库查询，sum={sum}", sum);
                    return new GrpcResult { IsSuccess = true, Message = "正常流程3：数据查询完成" };
                case 3:
                    // 异常流程1：除零异常
                    try { int m = 1;int n = 0; int d = m / n; } catch (Exception ex) { _logger.LogError(ex, "异常流程1：除零异常"); throw new InvalidOperationException("模拟除零异常", ex); }
                    break;
                case 4:
                    // 异常流程2：业务异常
                    try { throw new ApplicationException("模拟业务异常"); } catch (Exception ex) { _logger.LogError(ex, "异常流程2：业务异常"); throw; }
                    break;
                case 5:
                    // 异常流程3：外部依赖异常
                    try { throw new Exception("模拟外部依赖异常（如数据库、RPC等）"); } catch (Exception ex) { _logger.LogError(ex, "异常流程3：外部依赖异常"); throw; }
                    break;
            }
            return new GrpcResult { IsSuccess = false, Message = "TestLogging 未知分支" };
        }
    }
}