using System;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级接口，定义了升级和回滚操作
    /// </summary>
    public interface IDatabaseUpgrade
    {
        /// <summary>
        /// 获取升级的源版本
        /// </summary>
        string SourceVersion { get; }
        
        /// <summary>
        /// 获取升级的目标版本
        /// </summary>
        string TargetVersion { get; }
        
        /// <summary>
        /// 获取升级描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 执行数据库升级
        /// </summary>
        Task UpgradeAsync();
        
        /// <summary>
        /// 回滚数据库升级
        /// </summary>
        Task RollbackAsync();
        
        /// <summary>
        /// 升级主项目数据库
        /// </summary>
        /// <param name="mainProjectId">主项目ID</param>
        Task UpgradeMainProjectDatabaseAsync(Guid mainProjectId);
        
        /// <summary>
        /// 回滚主项目数据库
        /// </summary>
        /// <param name="mainProjectId">主项目ID</param>
        Task RollbackMainProjectDatabaseAsync(Guid mainProjectId);
        
        /// <summary>
        /// 升级模型数据库
        /// </summary>
        /// <param name="modelId">模型ID</param>
        Task UpgradeModelDatabaseAsync(Guid modelId);
        
        /// <summary>
        /// 回滚模型数据库
        /// </summary>
        /// <param name="modelId">模型ID</param>
        Task RollbackModelDatabaseAsync(Guid modelId);
        
        /// <summary>
        /// 升级库数据库
        /// </summary>
        /// <param name="libraryId">库ID</param>
        Task UpgradeLibraryDatabaseAsync(Guid libraryId);
        
        /// <summary>
        /// 回滚库数据库
        /// </summary>
        /// <param name="libraryId">库ID</param>
        Task RollbackLibraryDatabaseAsync(Guid libraryId);
    }
} 