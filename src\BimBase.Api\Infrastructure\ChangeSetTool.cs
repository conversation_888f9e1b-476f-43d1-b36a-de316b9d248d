﻿using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public static class ChangeSetTool
    {
        private static List<TR> CalculateChanges<T, TR>(List<T> fromDatas, List<T> toDatas) where T : BasicModelDataInfomation where TR : BasicModelDataInfomation
        {
            List<TR> retList = new List<TR>();

            if (null == fromDatas && null == toDatas) return retList;

            List<long> fromDataIdList = null == fromDatas ? new List<long>() : fromDatas.Select(data => data.InstanceId).ToList();
            List<long> toDataIdList = null == toDatas ? new List<long>() : toDatas.Select(data => data.InstanceId).ToList();

            HashSet<long> fromSpecificIdList = new HashSet<long>(fromDataIdList.Except(toDataIdList).ToList());
            HashSet<long> toSpecificIdList = new HashSet<long>(toDataIdList.Except(fromDataIdList).ToList());
            HashSet<long> intersectionIdList = new HashSet<long>(fromDataIdList.Intersect(toDataIdList).ToList());

            List<T> fromSpecificDataList =
                null == fromDatas ? new List<T>() : fromDatas.FindAll(data => fromSpecificIdList.Contains(data.InstanceId)).ToList();
            List<T> toSpecificDataList =
                null == toDatas ? new List<T>() : toDatas.FindAll(data => toSpecificIdList.Contains(data.InstanceId)).ToList();

            List<T> intersectionToDataList =
               null == toDatas ? new List<T>() : toDatas.FindAll(data => intersectionIdList.Contains(data.InstanceId)).OrderBy(d => d.InstanceId).ToList();

            List<T> intersectionFromDataList =
               null == fromDatas ? new List<T>() : fromDatas.FindAll(data => intersectionIdList.Contains(data.InstanceId)).OrderBy(d => d.InstanceId).ToList();

            foreach (var fromData in fromSpecificDataList)
            {
                var modeldata = fromData as ModelData;
                if (modeldata != null)
                {
                    var data = modeldata.CreateHistoryData(OperationRecordType.Delete, modeldata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                var relationship = fromData as Relationship;
                if (relationship != null)
                {
                    var data = relationship.CreateHistoryRelationship(OperationRecordType.Delete, relationship.VersionNo);
                    retList.Add(data as TR);
                }
            }

            foreach (var toData in toSpecificDataList)
            {
                var modeldata = toData as ModelData;
                if (modeldata != null)
                {
                    var data = modeldata.CreateHistoryData(OperationRecordType.Add, modeldata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                var relationship = toData as Relationship;
                if (relationship != null)
                {
                    var data = relationship.CreateHistoryRelationship(OperationRecordType.Add, relationship.VersionNo);
                    retList.Add(data as TR);
                }
            }

            for (int i = 0; i < intersectionIdList.Count; i++)
            {
                var fromdata = intersectionFromDataList[i] as ModelData;
                if (fromdata != null)
                {
                    var todata = intersectionToDataList[i] as ModelData;
                    if (null == todata || todata.VersionNo == fromdata.VersionNo) continue;

                    var data = todata.CreateHistoryData(OperationRecordType.Modify, todata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                //var fromrelationship = intersectionFromDataList[i] as Relationship;
                //if (fromrelationship != null)
                //{
                //    var torelationship = intersectionToDataList[i] as Relationship;
                //    if (null == torelationship || torelationship.VersionNo == fromrelationship.VersionNo) continue;

                //    var data = torelationship.CreateHistoryRelationship(OperationRecordType.Modify,torelationship.VersionNo);
                //    retList.Add(data as TR);
                //}
            }

            return retList;
        }

        private static List<TR> CompareVersions<T, TR>(List<T> fromDatas, List<T> toDatas)
            where T : BasicModelDataInfomation
            where TR : BasicModelDataInfomation
        {
            List<TR> retList = new List<TR>();

            if (null == fromDatas && null == toDatas) return retList;

            List<long> fromDataIdList = null == fromDatas ? new List<long>() : fromDatas.Select(data => data.InstanceId).ToList();
            List<long> toDataIdList = null == toDatas ? new List<long>() : toDatas.Select(data => data.InstanceId).ToList();

            HashSet<long> fromSpecificIdList = new HashSet<long>(fromDataIdList.Except(toDataIdList).ToList());
            HashSet<long> toSpecificIdList = new HashSet<long>(toDataIdList.Except(fromDataIdList).ToList());
            HashSet<long> intersectionIdList = new HashSet<long>(fromDataIdList.Intersect(toDataIdList).ToList());

            List<T> fromSpecificDataList =
                null == fromDatas ? new List<T>() : fromDatas.FindAll(data => fromSpecificIdList.Contains(data.InstanceId)).ToList();
            List<T> toSpecificDataList =
                null == toDatas ? new List<T>() : toDatas.FindAll(data => toSpecificIdList.Contains(data.InstanceId)).ToList();

            List<T> intersectionToDataList =
               null == toDatas ? new List<T>() : toDatas.FindAll(data => intersectionIdList.Contains(data.InstanceId)).OrderBy(d => d.InstanceId).ToList();

            List<T> intersectionFromDataList =
               null == fromDatas ? new List<T>() : fromDatas.FindAll(data => intersectionIdList.Contains(data.InstanceId)).OrderBy(d => d.InstanceId).ToList();

            foreach (var fromData in fromSpecificDataList)
            {
                var modeldata = fromData as ModelData;
                if (modeldata != null)
                {
                    var data = modeldata.CreateHistoryData(OperationRecordType.Delete, modeldata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                var relationship = fromData as Relationship;
                if (relationship != null)
                {
                    var data = relationship.CreateHistoryRelationship(OperationRecordType.Delete, relationship.VersionNo);
                    retList.Add(data as TR);
                }
            }

            foreach (var toData in toSpecificDataList)
            {
                var modeldata = toData as ModelData;
                if (modeldata != null)
                {
                    var data = modeldata.CreateHistoryData(OperationRecordType.Add, modeldata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                var relationship = toData as Relationship;
                if (relationship != null)
                {
                    var data = relationship.CreateHistoryRelationship(OperationRecordType.Add, relationship.VersionNo);
                    retList.Add(data as TR);
                }
            }

            for (int i = 0; i < intersectionIdList.Count; i++)
            {
                var fromdata = intersectionFromDataList[i] as ModelData;
                if (fromdata != null)
                {
                    var todata = intersectionToDataList[i] as ModelData;
                    if (null == todata || todata.VersionNo == fromdata.VersionNo)
                    {
                        if (todata != null)
                        {
                            var unchangedata = todata.CreateHistoryData(OperationRecordType.Unchange, todata.VersionNo);
                            retList.Add(unchangedata as TR);
                        }
                        continue;
                    }

                    var data = todata.CreateHistoryData(OperationRecordType.Modify, todata.VersionNo);
                    retList.Add(data as TR);
                    continue;
                }

                var fromrelationship = intersectionFromDataList[i] as Relationship;
                if (fromrelationship != null)
                {
                    var torelationship = intersectionToDataList[i] as Relationship;
                    if (null == torelationship || torelationship.VersionNo == fromrelationship.VersionNo)
                    {
                        if (torelationship != null)
                        {
                            var unchangedata = torelationship.CreateHistoryRelationship(OperationRecordType.Unchange, torelationship.VersionNo);
                            retList.Add(unchangedata as TR);
                        }
                        continue;
                    }

                    var data = torelationship.CreateHistoryRelationship(OperationRecordType.Modify, torelationship.VersionNo);
                    retList.Add(data as TR);
                }
            }

            return retList;
        }


        public static bool IsModeldataEqual(List<ModelData> fromDatas, List<ModelData> toDatas)
        {
            if (fromDatas.Count != toDatas.Count) return false;

            var todataids = toDatas.Select(data => data.InstanceId).ToList();
            HashSet<long> idHashSet = new HashSet<long>(todataids);

            Dictionary<long, int> toDatasDictionary = new Dictionary<long, int>();
            foreach (var data in toDatas)
            {
                if (!toDatasDictionary.ContainsKey(data.Id))
                    toDatasDictionary.Add(data.Id, data.VersionNo);
            }

            foreach (var data in fromDatas)
            {
                if (!idHashSet.Contains(data.InstanceId)) continue;
                // list 中查询性能慢
                //var todata = toDatas.First(d => d.InstanceId == data.InstanceId);
                //if (todata.VersionNo != data.VersionNo) return false;
                //优化为下面方式，暂时不修改逻辑
                int versionNo;
                toDatasDictionary.TryGetValue(data.Id, out versionNo);
                if (versionNo != data.VersionNo) return false;
            }
            return true;
        }



        public static bool GetChangedSet(List<ModelData> fromDatas, List<Relationship> fromRelationships, List<ModelData> toDatas, List<Relationship> toRelationships, out List<HistoryData> changedDatas, out List<HistoryRelationship> changedRelationships)
        {
            changedRelationships = CalculateChanges<Relationship, HistoryRelationship>(fromRelationships, toRelationships);
            changedDatas = CalculateChanges<ModelData, HistoryData>(fromDatas, toDatas);
            return true;
        }

        public static bool CompareVersion(List<ModelData> fromDatas, List<Relationship> fromRelationships, List<ModelData> toDatas, List<Relationship> toRelationships, out List<HistoryData> changedDatas, out List<HistoryRelationship> changedRelationships)
        {
            changedRelationships = CompareVersions<Relationship, HistoryRelationship>(fromRelationships, toRelationships);
            changedDatas = CompareVersions<ModelData, HistoryData>(fromDatas, toDatas);

            return true;
        }

        public static IEnumerable<HistoryRelationship> GetHistoryRelationships(IProjectRepository modelManager, int verNo, HashSet<long> hids)
        {
            var hRSet = new HashSet<HistoryRelationship>();
            if (modelManager.CurrentVersionNo < verNo || hids.Count == 0)
                return hRSet;

            var relas = modelManager.AllHistoryRelationships.Where(r => r.VersionNo <= verNo).OrderByDescending(r => r.VersionNo).ToArray();

            foreach (var hr in relas)
            {
                if ((hr.Type == RelationshipType.Contain || hr.Type == RelationshipType.Assemble || hr.Type == RelationshipType.Dependence)
                            && (hids.Contains(hr.SourceID) || hids.Contains(hr.TargetID)))
                    hRSet.Add(hr);
            }

            return hRSet;
        }

        public static IEnumerable<HistoryData> GetHistoryDatas(IProjectRepository modelManager, int verBegin, int verEnd, IEnumerable<long> ids)
        {
            if (verBegin == verEnd)
                return null;
            bool isForward = verBegin < verEnd;
            int minVerNo = verEnd, maxVerNo = verBegin;
            if (isForward)
            {
                minVerNo = verBegin;
                maxVerNo = verEnd;
            }
            var datas = new List<HistoryData>();
            if (modelManager.CurrentVersionNo < verEnd)
                return datas;

            List<HistoryData> hds = new List<HistoryData>();
            HashSet<long> idsSet = new HashSet<long>(ids);

            foreach (var hisdata in modelManager.AllHistoryDatas)
            {
                if (idsSet.Contains(hisdata.InstanceId) && hisdata.VersionNo > minVerNo && hisdata.VersionNo <= maxVerNo)
                    hds.Add(hisdata);
            }

            var hdatas = hds.GroupBy(hd => hd.InstanceId);

            foreach (var hdata in hdatas)
            {
                if (isForward)
                {
                    datas.Add(hdata.Last());
                }
                else
                {
                    var hd = hdata.First();
                    var type = hd.OperationRecordType;
                    if (type == OperationRecordType.Delete)
                        hd.OperationRecordType = OperationRecordType.Add;
                    else if (type == OperationRecordType.Add)
                        hd.OperationRecordType = OperationRecordType.Delete;
                    else
                        hd.OperationRecordType = OperationRecordType.Modify;
                    datas.Add(hd);
                }
            }
            return datas;
        }

    }
}
