using System.Threading.Tasks;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.MQLog;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using BimBase.Api.Infrastructure.Services;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ错误日志写入服务实现，负责自动补全错误日志字段并通过RabbitMQ发送日志
    /// </summary>
    public class MQErrorWriterService : IMQErrorWriterService
    {
        private readonly IMQRabbitMQService _rabbitMQService;
        private readonly IConfiguration _config;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public MQErrorWriterService(IMQRabbitMQService rabbitMQService, IConfiguration config, IHttpContextAccessor httpContextAccessor)
        {
            _rabbitMQService = rabbitMQService;
            _config = config;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 写入错误日志，自动补全字段后发送到MQ
        /// </summary>
        public async Task WriteErrorLogAsync(MQErrorLog log)
        {
        //    MQLogEnricher.EnrichErrorLog(log, _httpContextAccessor.HttpContext, _config);
            await _rabbitMQService.PublishErrorLogAsync(log);
        }
    }
} 