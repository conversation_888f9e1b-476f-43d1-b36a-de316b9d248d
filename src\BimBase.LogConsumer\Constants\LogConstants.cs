namespace BimBase.LogConsumer.Constants
{
    /// <summary>
    /// 日志相关常量定义（消费者端）
    /// </summary>
    public static class LogConstants
    {
        // 日志类型
        public const string INTERFACE_LOG_TYPE = "interface";
        public const string PERFORMANCE_LOG_TYPE = "perform";
        public const string INFO_LOG_TYPE = "info";

        // 数据库表名
        public const string INTERFACE_LOG_TABLE = "interfacce_logs";
        public const string ERROR_LOG_TABLE = "error_logs";

        // RabbitMQ相关
        public const string EXCHANGE_NAME = "cooperate-default";
        public const string INTERFACE_QUEUE = "cooperate.interface.base";
        public const string ERROR_QUEUE = "cooperate.error";

        // 路由键
        public const string INTERFACE_REQUEST_ROUTING_KEY = "log.interface.info.request";
        public const string INTERFACE_RESPONSE_ROUTING_KEY = "log.interface.info.response";
        public const string INTERFACE_ERROR_ROUTING_KEY = "log.interface.error.response";
        public const string PERFORMANCE_ERROR_ROUTING_KEY = "log.perform.error.response";
        public const string INFO_ERROR_ROUTING_KEY = "log.info.error.response";

        // 日志属性名（用于JSON序列化）
        public static class PropertyNames
        {
            // 基础字段
            public const string Id = "id";
            public const string RequestId = "requestId";
            public const string ProjectId = "projectId";
            public const string ProjectName = "projectName";
            public const string SessionId = "sessionId";
            public const string ClientIp = "clientIp";
            public const string ServerIp = "serverIp";
            public const string ServerName = "serverName";
            public const string UserId = "userId";
            public const string UserName = "userName";
            public const string AppName = "appName";
            public const string AppVersion = "appVersion";
            public const string Environment = "environment";
            public const string RequestTime = "requestTime";
            public const string LogLevel = "logLevel";
            public const string LogType = "logType";
            public const string InterfaceName = "interfaceName";
            public const string HttpMethod = "httpMethod";
            public const string RequestParams = "requestParams";
            public const string ResponseStatusCode = "responseStatusCode";
            public const string ResponseTime = "responseTime";
            public const string IsSuccess = "isSuccess";
            public const string SourceClassName = "sourceClassName";
            public const string SourceMethodName = "sourceMethodName";
            public const string AdditionalData = "additionalData";
            public const string ErrorMessage = "errorMessage";
            public const string TotalMilliseconds = "totalMilliseconds";
            public const string AddTime = "addTime";
            // 错误日志特有字段
            public const string ErrorId = "errorId";
            public const string ErrorType = "errorType";
            public const string ErrorCode = "errorCode";
            public const string ErrorStackTrace = "errorStackTrace";
            public const string InputParams = "inputParams";
        }
    }
} 