using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb数据库从v9升级到v10
    /// 主要变更：
    /// 1. 添加全局冲突检测索引 {subProjectld, NodeName}
    /// 2. 支持根据NodeNameCheckConfig配置进行不同范围的冲突检测
    /// 3. 优化冲突检测性能
    /// </summary>
    public class MainProjectDbUpgrade_v9_to_v10 : AbstractMainProjectDbUpgrade
    {
        public override string FromVersion => "v9";
        public override string ToVersion => "v10";
        public override string Description => "添加全局冲突检测索引，支持根据配置进行不同范围的节点名称冲突检测";

        public MainProjectDbUpgrade_v9_to_v10(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{FromVersion} -> {ToVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v9 到 v10 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(MainProjectDbContext context)
        {
            // 1. 添加全局冲突检测索引
            await AddGlobalConflictIndexAsync(context);
        }

        /// <summary>
        /// 添加全局冲突检测索引 {subProjectld, NodeName}
        /// </summary>
        private async Task AddGlobalConflictIndexAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD INDEX `idx_subprojectId_nodename` (`subProjectld`, `NodeName`);
                ");
                Logger.LogInformation("成功添加全局冲突检测索引 idx_subprojectId_nodename");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加全局冲突检测索引失败");
                throw;
            }
        }

        protected override async Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {ToVersion} -> {FromVersion} 回滚...");

            try
            {
                // 移除全局冲突检测索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_subprojectId_nodename`;
                ");
                Logger.LogInformation("成功移除全局冲突检测索引");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "回滚操作失败");
                throw;
            }

            Logger.LogInformation($"{ToVersion} -> {FromVersion} 回滚完成");
        }
    }
} 