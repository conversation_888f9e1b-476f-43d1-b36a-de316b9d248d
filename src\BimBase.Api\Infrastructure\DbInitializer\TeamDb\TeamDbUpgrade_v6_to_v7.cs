using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v6升级到v7
    /// 主要变更：
    /// 1. 创建NodeNameCheckConfigs表
    /// </summary>
    public class TeamDbUpgrade_v6_to_v7 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v6";
        public override string TargetVersion => "v7";
        public override string Description => "添加节点名称检查配置表NodeNameCheckConfigs";

        public TeamDbUpgrade_v6_to_v7(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v6 到 v7 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(TeamDbContext context)
        {
            // 创建NodeNameCheckConfigs表
            await CreateNodeNameCheckConfigsTableAsync(context);
        }

        /// <summary>
        /// 创建 NodeNameCheckConfigs 表
        /// </summary>
        private async Task CreateNodeNameCheckConfigsTableAsync(TeamDbContext context)
        {
            if (!await TableExistsAsync(context, "NodeNameCheckConfigs"))
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    CREATE TABLE `NodeNameCheckConfigs` (
                        `Id` BIGINT NOT NULL AUTO_INCREMENT,
                        `NodeTypeName` VARCHAR(255) NULL,
                        `NodeTypeNum` INT NOT NULL,
                        `CheckType` INT NOT NULL,
                        PRIMARY KEY (`Id`),
                        KEY `IX_NodeNameCheckConfig_NodeTypeName` (`NodeTypeName`),
                        KEY `IX_NodeNameCheckConfig_NodeTypeNum` (`NodeTypeNum`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                ");
                Logger.LogInformation("成功创建 NodeNameCheckConfigs 表");
            }
            else
            {
                Logger.LogInformation("NodeNameCheckConfigs 表已存在，跳过创建");
            }
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 删除NodeNameCheckConfigs表
            if (await TableExistsAsync(context, "NodeNameCheckConfigs"))
            {
                await context.Database.ExecuteSqlRawAsync("DROP TABLE `NodeNameCheckConfigs`;");
                Logger.LogInformation("成功删除 NodeNameCheckConfigs 表");
            }
            else
            {
                Logger.LogInformation("NodeNameCheckConfigs 表不存在，跳过删除");
            }

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 