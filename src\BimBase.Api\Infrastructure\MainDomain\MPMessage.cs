﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPMessage
    {
        
        [Key]
        //消息在服务端的ID
        public int MsgServerID { get; set; }
        
        //消息发送者的ID
        public Guid FromUser { get; set; }
        
        //消息接收者的ID
        public Guid ToUser { get; set; }
        //若为用户组消息，此字段需赋值；若为0则该条消息为非用户组消息
        
        public int MPUserGroupId { get; set; }
        
        //消息的文本内容
        public string MsgTextContent { get; set; }
        
        //消息的3种状态，且仅能为其中一种。未读/历史/发送 “这条消息是否已经被消息接收者获取过？” 两种状态：是/否
        public int MsgState { get; set; }
        
        //消息的3种类型，且仅能为其中一种。1文本/2构件/3图片
        public int MsgType { get; set; }
        
        //消息创建的时间，由客户端提供
        public DateTime MsgCreateTime { get; set; }
        
        //消息的扩展信息，如是构件消息，则是构件InstanceID;如是图片消息，则是图片的二进制流。
        public byte[] MsgExData { get; set; }

        //子项目id，如是构件消息，该字段需赋值
        
        public Guid SubProjectId { get; set; }
    }
}
