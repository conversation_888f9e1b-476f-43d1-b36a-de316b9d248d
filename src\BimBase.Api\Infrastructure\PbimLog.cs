﻿using System.ComponentModel.DataAnnotations;

namespace BimBase.Api.Infrastructure
{
    public class PbimLog
    {
        public PbimLog() { }
        [Key]
        public long Id { get; set; }
        public string Timestamp { get; set; }
        public string LogLevel { get; set; }

        public string ProcessId { get; set; }

        public string ThreadId { get; set; }
        public string UserId { get; set; }
        public string Exception {  get; set; }
        public string Message { get; set; }
        public string SourceContext { get; set; }
        public string LogType { get; set; }
        public string Method { get; set; }
        public string FilePath { get; set; }

        public string FuctionName { get; set; }
        public string LineNumber { get; set; }

    }
}
