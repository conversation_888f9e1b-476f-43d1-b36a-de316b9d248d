﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Config
{
    public class SystemConfig
    {
        public String BaseIP = "127.0.0.1";
        public UInt16 Port = 5001;
        public UInt16 LoginTimeOut = 60;
        public UInt16 MaxCPUUsage = 90;
        public UInt16 TaskInterval = 3;
        public bool UseCertificateX509 = false;
        public bool AutoStart = true;
        public String CertThumbprint;
        public String HomePage = "http://localhost:8080";
        public bool Isinited = false;
    }
}
