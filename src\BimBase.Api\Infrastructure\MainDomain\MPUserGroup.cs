﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    /// <summary>
    /// 项目中的用户组
    /// </summary>
    
    public class MPUserGroup
    {
        /// <summary>
        /// 自增id
        /// </summary>
        [Key]
        public int ID { get; set; }

        /// <summary>
        /// 用户组名称
        /// </summary>
        
        public string UserGroupName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        
        public string Description { get; set; }
        /// <summary>
        /// 预留字段，可为空
        /// </summary>
        
        public string ExtendStr { get; set; }
    }

    /// <summary>
    /// 用户组成员
    /// </summary>
    
    public class MPUserGroupMember
    {
        [Key]
        public int ID { get; set; }
        /// <summary>
        /// 用户组ID
        /// </summary>
        
        public int UserGroupId { get; set; }
        /// <summary>
        /// 用户Guid
        /// </summary>
        
        public Guid TeamMemberId { get; set; }
    }


    
    public class MPAuthInfo
    {
        
        public string AuthDisplayName { get; set; }
        
        public string AuthName { get; set; }
        
        public int Permission { get; set; }
    }

    /// <summary>
    /// 等级库和元件库、参数化组件库、图纸和报表模板库、设计配置库权限表
    /// </summary>
    
    public class MPUserGroupLibAuth
    {
        [Key]
        
        public int ID { get; set; }
        /// <summary>
        /// 用户组ID
        /// </summary>
        
        public int MPUserGroupId { get; set; }
        /// <summary>
        /// 库类型
        /// </summary>
        
        [Required, EnumDataType(typeof(LibType))]
        public LibType LibType { get; set; }

        /// <summary>
        /// 是否具有权限
        /// </summary>
        
        public int Permission { set; get; }
        /// <summary>
        /// 自定义权限信息 MPAuthInfo json字符串 
        /// </summary>
        
        public string AuthInfo { get; set; }
        /// <summary>
        /// 扩展字段
        /// </summary>
        
        public string ExtendStr { get; set; }
    }
}
