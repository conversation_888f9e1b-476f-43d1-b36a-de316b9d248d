﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class BasicModelDataInfomation
    {
        [System.ComponentModel.DataAnnotations.Key]
        public Int64 Id { get; set; }

        public Int64 InstanceId { get; set; }//上传一个模型生成的ID

        public byte[] Data { get; set; }   //模型包含所有构件的数据信息

        public string DomainClassName { get; set; }

        public int VersionNo { get; set; }

        public String ECSchemaName { get; set; }


        protected void CopyTo<T>(ref T data) where T : BasicModelDataInfomation
        {
            data.InstanceId = InstanceId;
            data.DomainClassName = DomainClassName;
            data.ECSchemaName = ECSchemaName;
            data.Data = Data;
            data.VersionNo = VersionNo;
        }

        public virtual T ModifyWith<T>(T data)
            where T : BasicModelDataInfomation
        {
            InstanceId = data.InstanceId;
            DomainClassName = data.DomainClassName;
            ECSchemaName = data.ECSchemaName;
            Data = data.Data;
            VersionNo = data.VersionNo;
            return this as T;
        }
    }
}
