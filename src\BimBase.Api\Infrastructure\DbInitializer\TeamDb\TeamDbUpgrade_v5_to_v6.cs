﻿using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    public class TeamDbUpgrade_v5_to_v6 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v5";

        public override string TargetVersion => "v6";

        public override string Description => "TeamDb v5到v6升级（将ClientModuleVersions表的clientip字段长度限制有50变更为254）";
        public TeamDbUpgrade_v5_to_v6(ILogger logger) : base(logger)
        {
        }
        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 回滚：删除mainprojectusergroupmembers表的TeamMemberId索引
            await ModifyColumnIfExistsAsync(context, "clientmoduleversions", "ClientIp", "VARCHAR(50) NULL DEFAULT NULL");

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");

            // 对teamusergroups表增加TeamMemberId索引
            await ModifyColumnIfExistsAsync(context, "clientmoduleversions", "ClientIp", "VARCHAR(254) NULL DEFAULT NULL");

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }
    }
}
