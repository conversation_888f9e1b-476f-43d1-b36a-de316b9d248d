﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Repositories
{
    public interface IRepository
    {
        bool Insert<T>(T t) where T : class;
        bool Insert<T>(IEnumerable<T> tList) where T : class;
        void Update<T>(T t) where T : class;
        void Update<T>(IEnumerable<T> tList) where T : class;
        void Delete<T>(T t) where T : class;
        void Delete<T>(IEnumerable<T> tList) where T : class;
        
        IQueryable<TResult> QueryFrom<TSource, TResult>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TResult>> projection) where TSource : class;
        IQueryable<TSource> QueryFrom<TSource>(Expression<Func<TSource, bool>> predicate = null) where TSource : class;
    }



    public class BaseRepository : IRepository
    {
        private readonly DbContext _dbContext;
        public BaseRepository(DbContext dbContext)
        {
            _dbContext = dbContext;
        }
        public virtual void Delete<T>(T t) where T : class
        {
            _dbContext.Set<T>().Remove(t);
            _dbContext.SaveChanges();
        }

        public virtual void Delete<T>(IEnumerable<T> tList) where T : class
        {
            _dbContext.Set<T>().RemoveRange(tList);
            _dbContext.SaveChanges();
        }

        public virtual bool Insert<T>(T t) where T : class
        {
            _dbContext.Set<T>().Add(t);
            return _dbContext.SaveChanges() > 0;
        }

        public virtual bool Insert<T>(IEnumerable<T> tList) where T : class
        {
            _dbContext.Set<T>().AddRange(tList);
            return _dbContext.SaveChanges() > 0;
        }

        public virtual IQueryable<TResult> QueryFrom<TSource, TResult>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TResult>> projection) where TSource : class
        {
            var x = _dbContext.Set<TSource>().Where(predicate).Select(projection);
            return x;
        }

        public virtual IQueryable<TSource> QueryFrom<TSource>(Expression<Func<TSource, bool>> predicate = null) where TSource : class
        {
            if (predicate is null) return _dbContext.Set<TSource>();
            var x = _dbContext.Set<TSource>().Where(predicate);
            return x;
        }

        public virtual void Update<T>(T t) where T : class
        {
            _dbContext.Set<T>().Update(t);
            _dbContext.SaveChanges();
        }

        public virtual void Update<T>(IEnumerable<T> tList) where T : class
        {
            _dbContext.Set<T>().UpdateRange(tList);
            _dbContext.SaveChanges();
        }
    }
}
