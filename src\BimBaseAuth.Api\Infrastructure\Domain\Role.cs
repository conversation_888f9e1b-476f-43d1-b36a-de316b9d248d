﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Domain
{
	public class Role : Entity
	{
		public string Name
		{
			get;
			set;
		}

		public int Status
		{
			get;
			set;
		}

		public int Type
		{
			get;
			set;
		}

		public DateTime CreateTime
		{
			get;
			set;
		}

		public Role()
		{
			Name = string.Empty;
			Status = 1;
			Type = 0;
			CreateTime = DateTime.Now;
			base.Id = Guid.NewGuid();
		}
	}
}
