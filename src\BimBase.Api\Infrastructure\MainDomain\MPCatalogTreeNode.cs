﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPCatalogTreeNode
    {
        [Key]
        
        public int ID { get; set; }
        
        public long NodeId { get; set; }
        
        public long InstanceId { get; set; }
        /// <summary>
        /// 所属树节点
        /// </summary>
        
        public long TreeId { get; set; }
        /// <summary>
        /// 父节点
        /// </summary>
        
        public long ParentNodeId { get; set; }
        /// <summary>
        /// 节点类型
        /// </summary>
        
        public int NodeType { get; set; }

        
        public string NodeName { get; set; }
        /// <summary>
        /// 节点业务对象
        /// </summary>
        
        public long bPDataKey { get; set; }
        /// <summary>
        /// modelinfo对象
        /// </summary>
        
        public long modelnfoKey { get; set; }
        /// <summary>
        /// 树类型
        /// </summary>
        
        [Required, EnumDataType(typeof(LibType))]
        public LibType TreeType { get; set; }
        /// <summary>
        /// 所属库的id
        /// </summary>
        
        public Guid LibId { get; set; }

        
        public int VersionNo { get; set; }

    }
}
