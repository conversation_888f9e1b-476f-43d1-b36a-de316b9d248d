syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos.ProjectFile";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";


package bimbase.api.projectfile;

message GetNewestVNoRequest{
	string sessionId = 1;
	string keytoken = 2;
	string projectGuid = 3;
}
message GetNewestVNoResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	int32 maxVNo = 4;
}
message DownloadNewestFileRequest{
	string sessionId = 1;
	string projectGuid = 2;
	string keytoken = 3;
	int64 requestId = 4;
	int64 FilePosition = 5;
}

message DownloadNewestFileResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	int64 requestId = 4;
	int32 VersionNo = 5;
	ModelFileTransfer fileInfo = 6;
	int64 currentFilesize = 7;
}
service GrpcProjectFileQuery {
	rpc GetNewestVNo(GetNewestVNoRequest) returns (GetNewestVNoResponse){
		option (google.api.http) = {
			post: "/v1/projectfile/GetNewestVNo"
			body: "*"
		};
	}

	rpc DownloadNewestFile(DownloadNewestFileRequest) returns (DownloadNewestFileResponse){
		option (google.api.http) = {
			post: "/v1/projectfile/DownloadNewestFile"
			body: "*"
		};
	}
}
