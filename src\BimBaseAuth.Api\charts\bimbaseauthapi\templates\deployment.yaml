apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "bimbaseauthapi.fullname" . }}
  labels:
    app: {{ template "bimbaseauthapi.name" . }}
    chart: {{ template "bimbaseauthapi.chart" . }}
    draft: {{ .Values.draft | default "draft-app" }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  revisionHistoryLimit: 0
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "bimbaseauthapi.name" . }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ template "bimbaseauthapi.name" . }}
        draft: {{ .Values.draft | default "draft-app" }}
        release: {{ .Release.Name }}
      annotations:
        buildID: {{ .Values.buildID | default "" | quote }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.deployment.containerPort }}
              protocol: TCP
          {{- if .Values.probes.enabled }}
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          {{- end }}
          env:
            {{- $root := . }}
            {{- range $ref, $values := .Values.secrets }}
            {{- range $key, $value := $values }}
            - name: {{ $ref }}_{{ $key }}
              valueFrom:
                secretKeyRef:
                  name: {{ template "bimbaseauthapi.fullname" $root }}-{{ $ref | lower }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
