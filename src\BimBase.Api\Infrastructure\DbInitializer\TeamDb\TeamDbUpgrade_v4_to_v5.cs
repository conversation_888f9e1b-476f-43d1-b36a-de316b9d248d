using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v4升级到v5
    /// 对teamusergroups表和mainprojectusergroupmembers表增加TeamMemberId索引
    /// </summary>
    public class TeamDbUpgrade_v4_to_v5 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v4";
        public override string TargetVersion => "v5";
        public override string Description => "TeamDb v4到v5升级（对teamusergroups表和mainprojectusergroupmembers表增加TeamMemberId索引）";

        public TeamDbUpgrade_v4_to_v5(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");
            
            // 对teamusergroups表增加TeamMemberId索引
            await CreateIndexIfNotExistsAsync(context, "TeamUserGroups", "IX_TeamMemberId", "TeamMemberId");
            
            // 对mainprojectusergroupmembers表增加TeamMemberId索引
            await CreateIndexIfNotExistsAsync(context, "MainProjectUserGroupMembers", "IX_TeamMemberId", "TeamMemberId");

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 回滚：删除mainprojectusergroupmembers表的TeamMemberId索引
            await DropIndexIfExistsAsync(context, "MainProjectUserGroupMembers", "IX_TeamMemberId");
            
            // 回滚：删除teamusergroups表的TeamMemberId索引
            await DropIndexIfExistsAsync(context, "TeamUserGroups", "IX_TeamMemberId");

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 