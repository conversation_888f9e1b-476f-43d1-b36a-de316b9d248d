﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class ProjectImodel
    {
        
        public int ID { get; set; }

        
        public Guid ProjectGuid { get; set; }

        
        public int Domain { get; set; }

        
        public int VersionNo { get; set; }

        
        public DateTime PublishTime { get; set; }

        
        public string Publisher { get; set; }

        
        public string Uri { get; set; }

        
        public string JobId { get; set; }

        
        [NotMapped]
        public string JobStatus { get; set; }

    }
}
