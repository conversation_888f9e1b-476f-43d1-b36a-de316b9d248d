﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
   
    public class HistoryRelationship : BasicModelDataInfomation
    {
        [Required]
        public Int64 SourceID { get; set; }

        public String SourceDomainClassName { get; set; }

        [Required]
        public String SourceECSchemaName { get; set; }

        [Required]
        public Int64 TargetID { get; set; }

        public String TargetDomainClassName { get; set; }

        [Required]
        public String TargetECSchemaName { get; set; }

        public bool IsForward { get; set; }

        [Required, EnumDataType(typeof(RelationshipType))]
        public RelationshipType Type { get; set; }

        [Required, EnumDataType(typeof(OperationRecordType))]
        public OperationRecordType OperationRecordType { get; set; }

        public HistoryRelationship Clone()
        {
            HistoryRelationship historyRelationship = new HistoryRelationship()
            {
                Data = this.Data,
                //DomainClassID = this.DomainClassID,
                DomainClassName = this.DomainClassName,
                ECSchemaName = this.ECSchemaName,
                OperationRecordType = this.OperationRecordType,
                InstanceId = this.InstanceId,
                SourceID = this.SourceID,
                SourceDomainClassName = this.SourceDomainClassName,
                //SourceDomainClassID = this.SourceDomainClassID,
                SourceECSchemaName = this.SourceECSchemaName,
                TargetID = this.TargetID,
                //TargetDomainClassID = this.TargetDomainClassID,
                TargetDomainClassName = this.TargetDomainClassName,
                TargetECSchemaName = this.TargetECSchemaName,
                VersionNo = this.VersionNo,
                Type = this.Type,
                IsForward = this.IsForward
            };
            return historyRelationship;
        }
    }
}
