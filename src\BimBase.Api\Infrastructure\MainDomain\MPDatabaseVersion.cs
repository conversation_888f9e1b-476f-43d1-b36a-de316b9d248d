using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.MainDomain
{
    /// <summary>
    /// MainProjectDb数据库版本管理表
    /// </summary>
    [Table("databaseversion")]
    public class MPDatabaseVersion
    {
        /// <summary>
        /// 版本号（主键）
        /// </summary>
        [Key]
        [Column("Version")]
        [StringLength(50)]
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 升级时间
        /// </summary>
        [Column("UpgradeTime")]
        public DateTime UpgradeTime { get; set; }

        /// <summary>
        /// 升级描述
        /// </summary>
        [Column("Description")]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 升级者
        /// </summary>
        [Column("UpgradedBy")]
        [StringLength(100)]
        public string UpgradedBy { get; set; } = "System";
    }
} 