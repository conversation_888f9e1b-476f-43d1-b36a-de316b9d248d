using System;
using System.Text.Json.Serialization;

namespace BimBase.LogConsumer.Models
{
    /// <summary>
    /// MQ接口调用信息日志模型
    /// </summary>
    public class MQInterfaceLog
    {
        public int Id { get; set; }
        /// <summary>
        /// 请求ID
        /// </summary>
        [JsonPropertyName("requestId")]
        public string RequestId { get; set; }
        /// <summary>
        /// 项目id，程序获取
        /// </summary>
        [JsonPropertyName("projectId")]
        public string ProjectId { get; set; }
        /// <summary>
        /// 项目名称，程序获取
        /// </summary>
        [JsonPropertyName("projectName")]
        public string ProjectName { get; set; }
        /// <summary>
        /// sessid，程序获取
        /// </summary>
        [JsonPropertyName("sessionId")]
        public string SessionId { get; set; }
        /// <summary>
        /// 客户端IP，从HttpContext.Connection.RemoteIpAddress获取，注意长度255，入库要截断
        /// </summary>
        [JsonPropertyName("clientIp")]
        public string ClientIp { get; set; }
        /// <summary>
        /// 服务端IP
        /// </summary>
        [JsonPropertyName("serverIp")]
        public string ServerIp { get; set; }
        /// <summary>
        /// 服务端名称
        /// </summary>
        [JsonPropertyName("serverName")]
        public string ServerName { get; set; }
        /// <summary>
        /// 用户ID，程序获取
        /// </summary>
        [JsonPropertyName("userId")]
        public string UserId { get; set; }
        /// <summary>
        /// 用户名，程序获取
        /// </summary>
        [JsonPropertyName("userName")]
        public string UserName { get; set; }
        /// <summary>
        /// 应用名称,从配置文件中获取
        /// </summary>
        [JsonPropertyName("appName")]
        public string AppName { get; set; }
        /// <summary>
        /// 应用版本,从配置文件中获取
        /// </summary>
        [JsonPropertyName("appVersion")]
        public string AppVersion { get; set; }
        /// <summary>
        /// 环境,从配置文件中获取
        /// </summary>
        [JsonPropertyName("environment")]
        public string Environment { get; set; }
        /// <summary>
        /// 请求时间，请求接口的时间
        /// </summary>
        [JsonPropertyName("requestTime")]
        public DateTime? RequestTime { get; set; }
        /// <summary>
        /// 日志级别，程序获取
        /// </summary>
        [JsonPropertyName("logLevel")]
        public string LogLevel { get; set; }
        /// <summary>
        /// 日志类型，程序获取
        /// </summary>
        [JsonPropertyName("logType")]
        public string LogType { get; set; }
        /// <summary>
        /// 接口名称，从HttpContext.Request.Path获取
        /// </summary>
        [JsonPropertyName("interfaceName")]
        public string InterfaceName { get; set; }
        /// <summary>
        /// HTTP方法，从HttpContext.Request.Method获取
        /// </summary>
        [JsonPropertyName("httpMethod")]
        public string HttpMethod { get; set; }
        /// <summary>
        /// 请求参数，读取请求体
        /// </summary>
        [JsonPropertyName("requestParams")]
        public string RequestParams { get; set; }
        /// <summary>
        /// 响应状态码，从HttpContext.Response.StatusCode获取
        /// </summary>
        [JsonPropertyName("responseStatusCode")]
        public int? ResponseStatusCode { get; set; }
        /// <summary>
        /// 响应时间，相应完成的时间
        /// </summary>
        [JsonPropertyName("responseTime")]
        public DateTime? ResponseTime { get; set; }
        /// <summary>
        /// 是否成功：根据ResponseStatusCode判断（2xx 为成功）
        /// </summary>
        [JsonPropertyName("isSuccess")]
        public bool? IsSuccess { get; set; }
        /// <summary>
        /// 源类名，通过反射获取控制器类名
        /// </summary>
        [JsonPropertyName("sourceClassName")]
        public string SourceClassName { get; set; }
        /// <summary>
        /// 源方法名，通过反射获取控制器方法名
        /// </summary>
        [JsonPropertyName("sourceMethodName")]
        public string SourceMethodName { get; set; }
        /// <summary>
        /// 附加数据
        /// </summary>
        [JsonPropertyName("additionalData")]
        public string AdditionalData { get; set; }
        /// <summary>
        /// 错误消息，捕获异常的Message属性
        /// </summary>
        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 响应时间，记录请求开始与结束时间差
        /// </summary>
        [JsonPropertyName("totalMilliseconds")]
        public double? TotalMilliseconds { get; set; }
        /// <summary>
        /// 添加时间
        /// </summary>
        [JsonPropertyName("addTime")]
        public DateTime? AddTime { get; set; }
        /// <summary>
        /// 日志阶段，标识是请求还是响应（如'request'或'response'）
        /// </summary>
        [JsonPropertyName("logStage")]
        public string LogStage { get; set; }
        /// <summary>
        /// 中间件耗时间
        /// </summary>
        [JsonPropertyName("middleTotalMilliseconds")]
        public double? MiddleTotalMilliseconds { get; set; }
        /// <summary>
        /// 中间件响应时间
        /// </summary>
        [JsonPropertyName("middleResponseTime")]
        public DateTime? MiddleResponseTime { get; set; }
    }
}
