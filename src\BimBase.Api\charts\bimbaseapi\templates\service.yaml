apiVersion: v1
kind: Service
metadata:
  name: {{ template "bimbaseapi.fullname" . }}
  labels:
    app: {{ template "bimbaseapi.name" . }}
    chart: {{ template "bimbaseapi.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ template "bimbaseapi.name" . }}
    release: {{ .Release.Name }}
