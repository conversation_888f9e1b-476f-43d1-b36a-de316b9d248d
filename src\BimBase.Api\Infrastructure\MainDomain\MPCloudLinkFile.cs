﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPCloudLinkFile
    {
        
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 子项目id
        /// </summary>
        
        public string ProjectId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        
        public string ProjectName { get; set; }

        
        public string FileName { get; set; }

        /// <summary>
        /// 存放路径
        /// </summary>
        
        public string SavePath { get; set; }

        
        public int VersionNo { get; set; }

        
        public long FileSize { get; set; }

        
        public DateTime UploadTime { get; set; }

        
        public string Description { get; set; }


        /// <summary>
        /// 
        /// </summary>
        
        public string FileType { get; set; }

        
        public string UserName { get; set; }
    }
}
