using BimBase.Api.Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    /// <summary>
    /// 数据库版本配置提供者 - 全局访问点
    /// </summary>
    public static class DatabaseVersioningOptionsProvider
    {
        private static IServiceProvider _serviceProvider;
        private static DatabaseVersioningOptions _cachedOptions;
        private static readonly object _lock = new object();

        /// <summary>
        /// 初始化服务提供者
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _cachedOptions = null; // 重置缓存
        }

        /// <summary>
        /// 获取当前的数据库版本配置选项
        /// </summary>
        /// <returns>数据库版本配置选项</returns>
        public static DatabaseVersioningOptions GetOptions()
        {
            if (_cachedOptions != null)
                return _cachedOptions;

            lock (_lock)
            {
                if (_cachedOptions != null)
                    return _cachedOptions;

                if (_serviceProvider == null)
                {
                    // 如果服务提供者未初始化，返回默认配置
                    _cachedOptions = GetDefaultOptions();
                }
                else
                {
                    try
                    {
                        // 尝试从服务容器获取配置
                        var optionsMonitor = _serviceProvider.GetService<IOptions<DatabaseVersioningOptions>>();
                        _cachedOptions = optionsMonitor?.Value ?? GetDefaultOptions();
                    }
                    catch
                    {
                        // 如果获取失败，使用默认配置
                        _cachedOptions = GetDefaultOptions();
                    }
                }
            }

            return _cachedOptions;
        }

        /// <summary>
        /// 强制刷新配置缓存
        /// </summary>
        public static void RefreshOptions()
        {
            lock (_lock)
            {
                _cachedOptions = null;
            }
        }

        /// <summary>
        /// 获取默认配置选项
        /// </summary>
        /// <returns>默认的数据库版本配置</returns>
        private static DatabaseVersioningOptions GetDefaultOptions()
        {
            return new DatabaseVersioningOptions
            {
                AutoExecute = true,
                TargetVersion = "v2",
                Action = "Upgrade"
            };
        }

        /// <summary>
        /// 检查是否应该自动执行版本管理
        /// </summary>
        /// <returns>是否自动执行</returns>
        public static bool ShouldAutoExecute()
        {
            return GetOptions().AutoExecute;
        }

        /// <summary>
        /// 获取目标版本
        /// </summary>
        /// <returns>目标版本字符串</returns>
        public static string GetTargetVersion()
        {
            var options = GetOptions();
            return !string.IsNullOrEmpty(options.TargetVersion) ? options.TargetVersion : "v2";
        }

        /// <summary>
        /// 获取操作类型
        /// </summary>
        /// <returns>操作类型字符串</returns>
        public static string GetAction()
        {
            var options = GetOptions();
            return !string.IsNullOrEmpty(options.Action) ? options.Action : "Upgrade";
        }
    }
} 