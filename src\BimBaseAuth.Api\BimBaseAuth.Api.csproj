﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
    <Platforms>AnyCPU;x64;ARM64</Platforms>
    <Configurations>Debug;Release;ElecRelease</Configurations>
  </PropertyGroup>


  <ItemGroup>
	  <PackageReference Include="AutoMapper" Version="12.0.1" />
	  <PackageReference Include="AutoMapper.Extensions.EnumMapping" Version="3.2.0" />
	  <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
	  <PackageReference Include="Grpc.AspNetCore" Version="2.63.0" />
	  <PackageReference Include="Grpc.AspNetCore.Server.Reflection" Version="2.63.0" />
	  <PackageReference Include="Grpc.AspNetCore.Web" Version="2.63.0" />
	  <PackageReference Include="Grpc.HealthCheck" Version="2.63.0" />
	  <PackageReference Include="Grpc.Tools" Version="2.63.0">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.7" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.7">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.9" />
	  <PackageReference Include="Microsoft.VisualStudio.Azure.Kubernetes.Tools.Targets" Version="1.1.0" />
	  <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\auth.proto" GrpcServices="Server" />
    <None Remove="@(Protobuf)" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Infrastructure\Extensions\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="ConfigTemplate\appsettings.temp.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
