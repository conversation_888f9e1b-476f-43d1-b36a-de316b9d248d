﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// add by asdf 2018-06-05 
    /// 数据持久化到硬盘，插入一条记录
    /// 同步到数据库后，从表中删除
    /// 数据丢失从表中找对对应版本的记录，进行恢复
    /// </summary>
    public class DiskDataRecord
    {
        [System.ComponentModel.DataAnnotations.Key]
        public Int64 Id { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary>
        public string ProjectId { get; set; }
        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ProjectMemberId { get; set; }
        /// <summary>
        /// 是否锁定状态 ，数据恢复时用到,解析参数符合CheckInWithResourceLock 函数的结构
        /// </summary>
        public bool UnLock { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 保存到磁盘的文件名
        /// </summary>
        public string DataFileName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreateAt { get; set; }
        /// <summary>
        /// 描述 ， 数据恢复时用到,解析参数符合CheckInWithResourceLock 函数的结构
        /// </summary>
        public string Desc { get; set; }
    }
}
