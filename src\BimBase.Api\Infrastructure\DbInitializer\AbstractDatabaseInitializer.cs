using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using BimBase.Api.Config;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    /// <summary>
    /// 抽象数据库初始化器基类
    /// 提供所有数据库初始化器的通用功能
    /// </summary>
    public abstract class AbstractDatabaseInitializer<TContext> where TContext : DbContext
    {
        protected const string InitialVersion = "v0";
        
        /// <summary>
        /// 初始化数据库并执行版本管理
        /// </summary>
        public static void Initialize<TInitializer>(TContext context, IServiceProvider serviceProvider) 
            where TInitializer : AbstractDatabaseInitializer<TContext>, new()
        {
            try
            {
                var initializer = new TInitializer();
                // 同步调用异步方法（在应用启动时可以接受）
                initializer.InitializeAsync(context, serviceProvider).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                // 记录错误并重新抛出，让应用启动失败
                Console.WriteLine($"数据库初始化失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 异步初始化数据库并执行版本管理
        /// </summary>
        public async Task InitializeAsync(TContext context, IServiceProvider serviceProvider)
        {
            var logger = GetLogger(serviceProvider);
            
            try
            {
                logger.LogInformation($"开始{GetDatabaseName()}数据库初始化和版本管理...");
                
                // 1. 确保数据库存在，并根据是否新创建决定是否需要初始数据
                var databaseCreated = await EnsureDatabaseExistsAsync(context, logger);
                
                // 2. 只有在数据库新创建时才创建初始数据
                if (databaseCreated)
                {
                    await EnsureInitialDataAsync(context, logger);
                    
                    // 3. 确保在创建触发器前提交所有事务
                    await EnsureNoActiveTransactionAsync(context, logger);
                    
                    // 4. 创建触发器（仅在数据库新创建时）
                    await CreateTriggersInternalAsync(context, logger);
                }
                
                // 4. 执行版本管理
                await ExecuteVersionManagementAsync(context, logger);
                
                logger.LogInformation($"{GetDatabaseName()}数据库初始化和版本管理完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"{GetDatabaseName()}数据库初始化过程中发生错误");
                await HandleRollbackAsync(context, logger, ex);
                throw;
            }
        }
        
        #region 抽象方法 - 子类必须实现
        
        /// <summary>
        /// 获取数据库名称（用于日志输出）
        /// </summary>
        protected abstract string GetDatabaseName();
        
        /// <summary>
        /// 创建初始数据
        /// </summary>
        protected abstract Task CreateInitialDataAsync(TContext context, ILogger logger);
        
        /// <summary>
        /// 检查DatabaseVersion表是否存在
        /// </summary>
        protected abstract Task<bool> DatabaseVersionTableExistsAsync(TContext context);
        
        /// <summary>
        /// 获取当前数据库版本
        /// </summary>
        protected abstract Task<string> GetCurrentVersionAsync(TContext context, ILogger logger);
        
        /// <summary>
        /// 执行升级路径
        /// </summary>
        protected abstract Task ExecuteUpgradePathWithManagerAsync(TContext context, ILogger logger, string currentVersion, string targetVersion);
        
        /// <summary>
        /// 创建数据库触发器（仅在数据库新创建时调用）
        /// </summary>
        protected abstract Task CreateTriggersAsync(TContext context, ILogger logger);
        
        #endregion
        
        #region 虚方法 - 子类可以重写
        
        /// <summary>
        /// 检查是否需要初始数据（默认检查是否有版本数据）
        /// </summary>
        protected virtual async Task<bool> ShouldCreateInitialDataAsync(TContext context, ILogger logger)
        {
            if (!await DatabaseVersionTableExistsAsync(context))
            {
                return true;
            }
            
            // 默认逻辑：检查是否有任何版本记录
            try
            {
                var hasAnyVersion = await HasAnyVersionRecordsAsync(context);
                return !hasAnyVersion;
            }
            catch
            {
                return true;
            }
        }
        
        /// <summary>
        /// 检查是否有任何版本记录（子类可重写）
        /// </summary>
        protected virtual async Task<bool> HasAnyVersionRecordsAsync(TContext context)
        {
            // 默认返回false，子类应该重写这个方法
            return false;
        }
        
        #endregion
        
        #region 触发器创建相关方法
        
        /// <summary>
        /// 确保没有活跃的事务
        /// </summary>
        private async Task EnsureNoActiveTransactionAsync(TContext context, ILogger logger)
        {
            try
            {
                var currentTransaction = context.Database.CurrentTransaction;
                if (currentTransaction != null)
                {
                    logger.LogInformation($"检测到{GetDatabaseName()}存在活跃事务，先提交以避免DDL冲突");
                    await currentTransaction.CommitAsync();
                    logger.LogInformation($"{GetDatabaseName()}事务已提交");
                    
                    // 添加延迟确保事务完全提交
                    await Task.Delay(500);
                }
                else
                {
                    logger.LogInformation($"{GetDatabaseName()}当前无活跃事务，可以安全执行DDL操作");
                }
                
                // 强制保存所有更改并清理上下文状态
                await context.SaveChangesAsync();
                
                // 清理更改跟踪器
                context.ChangeTracker.Clear();
                
                // 添加额外延迟确保数据库状态稳定
                await Task.Delay(300);
                
                logger.LogInformation($"{GetDatabaseName()}数据上下文状态已清理，准备执行DDL操作");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"处理{GetDatabaseName()}事务状态时发生错误");
                throw;
            }
        }
        
        /// <summary>
        /// 创建触发器（前置条件：已确保无活跃事务）
        /// </summary>
        private async Task CreateTriggersInternalAsync(TContext context, ILogger logger)
        {
            logger.LogInformation($"开始为{GetDatabaseName()}创建触发器...");
            
            try
            {
                // 额外的事务和连接状态清理
                await ClearDatabaseConnectionStateAsync(context, logger);
                
                // 直接创建触发器（前面已经确保了没有活跃事务）
                await CreateTriggersAsync(context, logger);
                
                logger.LogInformation($"{GetDatabaseName()}触发器创建完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"创建{GetDatabaseName()}触发器时发生错误");
                throw;
            }
        }
        
        /// <summary>
        /// 清理数据库连接状态，确保没有残留的事务或连接池污染
        /// </summary>
        private async Task ClearDatabaseConnectionStateAsync(TContext context, ILogger logger)
        {
            try
            {
                // 获取连接字符串并尝试刷新连接池状态
                var connectionString = context.Database.GetConnectionString();
                
                // 创建一个临时连接来"预热"和清理连接状态
                var connectionStringBuilder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString)
                {
                    Pooling = false,
                    AutoEnlist = false,
                    ConnectionTimeout = 30
                };
                
                using var testConnection = new MySqlConnector.MySqlConnection(connectionStringBuilder.ConnectionString);
                await testConnection.OpenAsync();
                
                // 执行一个简单的查询确保连接正常
                using var testCommand = testConnection.CreateCommand();
                testCommand.CommandText = "SELECT 1";
                await testCommand.ExecuteScalarAsync();
                
                logger.LogInformation($"{GetDatabaseName()}数据库连接状态已清理和验证");
                
                // 再次延迟确保所有操作完成
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, $"清理{GetDatabaseName()}数据库连接状态时发生警告，将继续执行");
            }
        }
        
        #endregion
        
        #region 通用方法
        
        /// <summary>
        /// 确保数据库存在
        /// </summary>
        /// <returns>如果数据库是新创建的返回true，已存在返回false</returns>
        private async Task<bool> EnsureDatabaseExistsAsync(TContext context, ILogger logger)
        {
            logger.LogInformation($"检查{GetDatabaseName()}数据库是否存在...");
            
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                logger.LogInformation($"{GetDatabaseName()}数据库已创建");
            }
            else
            {
                logger.LogInformation($"{GetDatabaseName()}数据库已存在");
            }
            
            return created;
        }
        
        /// <summary>
        /// 确保初始数据存在
        /// </summary>
        private async Task EnsureInitialDataAsync(TContext context, ILogger logger)
        {
            if (!await ShouldCreateInitialDataAsync(context, logger))
            {
                logger.LogInformation("检测到已有数据，跳过初始数据创建");
                return;
            }
            
            logger.LogInformation($"创建{GetDatabaseName()}初始数据...");
            await CreateInitialDataAsync(context, logger);
        }
        
        /// <summary>
        /// 获取日志记录器
        /// </summary>
        private ILogger GetLogger(IServiceProvider serviceProvider)
        {
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            // 使用具体的类别名称，确保与配置匹配
            var categoryName = $"BimBase.Api.Infrastructure.DbInitializer.{GetDatabaseName()}Initializer";
            var logger = loggerFactory.CreateLogger(categoryName);
            
            // 测试日志系统
            logger.LogInformation($"=== {GetDatabaseName()} 数据库初始化器日志系统已启动 ===");
            
            return logger;
        }
        
        /// <summary>
        /// 执行版本管理
        /// </summary>
        private async Task ExecuteVersionManagementAsync(TContext context, ILogger logger)
        {
            logger.LogInformation($"开始执行{GetDatabaseName()}数据库版本管理...");
            
            // 使用全局配置提供者获取配置
            if (!DatabaseVersioningOptionsProvider.ShouldAutoExecute())
            {
                logger.LogInformation("数据库自动版本控制已禁用，跳过执行");
                return;
            }
            
            // 确保版本表存在
            await EnsureVersionTableAsync(context, logger);
            
            // 获取当前版本
            var currentVersion = await GetCurrentVersionAsync(context, logger);
            var targetVersion = DatabaseVersioningOptionsProvider.GetTargetVersion();
            
            logger.LogInformation($"{GetDatabaseName()}当前版本: {currentVersion}, 目标版本: {targetVersion}");
            
            if (currentVersion == targetVersion)
            {
                logger.LogInformation($"{GetDatabaseName()}数据库已经是目标版本，无需升级");
                return;
            }
            
            // 使用升级管理器执行升级
            await ExecuteUpgradePathWithManagerAsync(context, logger, currentVersion, targetVersion);
        }
        
        /// <summary>
        /// 确保版本表存在
        /// </summary>
        private async Task EnsureVersionTableAsync(TContext context, ILogger logger)
        {
            if (await DatabaseVersionTableExistsAsync(context))
            {
                logger.LogInformation($"{GetDatabaseName()}版本管理相关表已存在");
            }
            else
            {
                logger.LogInformation($"{GetDatabaseName()}版本管理相关表不存在，将在升级过程中创建");
            }
        }
        
        /// <summary>
        /// 处理回滚
        /// </summary>
        private async Task HandleRollbackAsync(TContext context, ILogger logger, Exception originalException)
        {
            try
            {
                logger.LogWarning($"尝试执行{GetDatabaseName()}数据库回滚操作...");
                // 这里可以添加具体的回滚逻辑
                logger.LogInformation($"{GetDatabaseName()}回滚操作完成");
            }
            catch (Exception rollbackException)
            {
                logger.LogError(rollbackException, $"{GetDatabaseName()}回滚操作也失败了");
            }
        }
        
        #endregion
    }
} 