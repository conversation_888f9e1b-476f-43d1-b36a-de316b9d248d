{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Error",
      "Microsoft.Hosting.Lifetime": "Error",
      "Grpc": "Information",
      "Microsoft.AspNetCore": "Error",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
      "Grpc.AspNetCore.Server": "Error",
      "Microsoft.EntityFrameworkCore.Database.Command": "Error",
      "Microsoft.EntityFrameworkCore.Database": "Error"
    },
    "EnableRequestParameters": true,
    "ExcludeRequestParameterMethods": [
      "Upload",
      "Download",
      "GetLargeData",
      "Heartbeat",
      "Ping"
    ]
  },
  "Console": {
    "LogLevel": {
      "Microsoft.EntityFrameworkCore.Database.Command": "Error",
      "Microsoft.EntityFrameworkCore.Database": "Error"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Grpc": "Information",
        "Microsoft.AspNetCore.Routing": "Error",
        "Microsoft": "Error",
        "Microsoft.Hosting.Lifetime": "Error",
        "Microsoft.AspNetCore": "Error",
        "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
        "Grpc.AspNetCore.Server": "Error",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error",
        "Microsoft.EntityFrameworkCore.Database": "Error"
      }
    },
    "Enrich": [
      "FromLogContext",
      "WithSpan",
      {
        "Name": "With",
        "Args": {
          "enricher": "BimBase.Api.BimLoggerEnricher, BimBase.Api"
        }
      },
      {
        "Name": "With",
        "Args": {
          "enricher": "BimBase.Api.Infrastructure.Logging.RequestContextEnricher, BimBase.Api"
        }
      }
    ],
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Console"
            }
          ],
          "bufferSize": 10000,
          "blockWhenFull": true
        }
      },
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Logger",
              "Args": {
                "configureLogger": {
                  "Enrich": [
                    "FromLogContext",
                    "WithSpan",
                    {
                      "Name": "With",
                      "Args": {
                        "enricher": "BimBase.Api.BimLoggerEnricher, BimBase.Api"
                      }
                    },
                    {
                      "Name": "With",
                      "Args": {
                        "enricher": "BimBase.Api.Infrastructure.Logging.RequestContextEnricher, BimBase.Api"
                      }
                    }
                  ],
                  "WriteTo": [
                    {
                      "Name": "File",
                      "Args": {
                        "path": "logs/grpc_performance/grpc_performance-.txt",
                        "rollingInterval": "Day",
                        "fileSizeLimitBytes": 10485760,
                        "retainedFileTimeLimit": "30.00:00:00",
                        "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] [{RequestId}] [{ClientIp}] {Message}{NewLine}{Exception}",
                        "rollOnFileSizeLimit": true
                      }
                    }
                  ]
                }
              }
            }
          ],
          "bufferSize": 10000,
          "blockWhenFull": true
        }
      },
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Logger",
              "Args": {
                "configureLogger": {
                  "Enrich": [
                    "FromLogContext",
                    "WithSpan",
                    {
                      "Name": "With",
                      "Args": {
                        "enricher": "BimBase.Api.BimLoggerEnricher, BimBase.Api"
                      }
                    },
                    {
                      "Name": "With",
                      "Args": {
                        "enricher": "BimBase.Api.Infrastructure.Logging.RequestContextEnricher, BimBase.Api"
                      }
                    }
                  ],
                  "WriteTo": [
                    {
                      "Name": "File",
                      "Args": {
                        "path": "logs/app/app-.txt",
                        "rollingInterval": "Day",
                        "fileSizeLimitBytes": 10485760,
                        "retainedFileTimeLimit": "30.00:00:00",
                        "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] [{RequestId}] [{ClientIp}] {Message}{NewLine}{Exception}",
                        "rollOnFileSizeLimit": true
                      }
                    }
                  ],
                  "Filter": [
                    {
                      "Name": "ByExcluding",
                      "Args": {
                        "expression": "SourceContext = 'BimBase.Api.Infrastructure.Grpc.GrpcPerformanceInterceptor'"
                      }
                    },
                    {
                      "Name": "ByExcluding",
                      "Args": {
                        "expression": "SourceContext = 'BimBase.Api.Infrastructure.Middleware.ResponseCompletionMiddleware'"
                      }
                    }
                  ]
                }
              }
            }
          ],
          "bufferSize": 10000,
          "blockWhenFull": true
        }
      }
    ]
  },
  "LogSettings": {
    "AppName": "本地",
    "AppVersion": "1.0.0",
    "Environment": "测试",
    "ServerIp": "unuse",
    "ServerName": "unuse",
    "LinShiMQ": {
      "Enabled": false
    },
    "MQLogging": {
      "Enabled": true
    },
    "RabbitMQ": {
      "HostName": "***************",
      "UserName": "guest",
      "Password": "guest",
      "VirtualHost": "/",
      "Port": 5672,
      "ExchangeName": "cooperate-default",
      "InterfaceLogQueue": "cooperate.interface.base",
      "ErrorLogQueue": "cooperate.error",
      "InterfaceLogRoutingKey": "log.interface.#",
      "ErrorLogRoutingKey": "log.#.error"
    }
  },
  "DatabaseVersioning": {
    "Action": "Upgrade",
    "TargetVersion": "v6"
  },
  "urls": {
    "GrpcBimBaseAuth": "http://localhost:9001",
    "UploadRootPath": "F:\\GrpcDocumentTemp",
    "LoadDataSavePath": "/data/load_data",
    "MysqLoadDataPath": "/data/load_data"
  },
  "MajorConfig": {
    "Major":"Elec"
  },
  "ConnectionStrings": {
    "DefaultConnection": "server=localhost; port=3306; database=pkpm-pbimserver-teamdb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=200;Min Pool Size=50;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LoggerConnection": "server=localhost; port=3306; database=bimbase-logdb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=100;Min Pool Size=15;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LibraryConnection": "server=localhost; port=3306; database=pkpm-pbimserver-librarydb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=100;Min Pool Size=40;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "TemplateConnection": "server=localhost; port=3306; database={DatabaseName}; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=150;Min Pool Size=30;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "RedisConnection": "localhost:6379"
    //"DefaultConnection": "server=***************; port=6330; database=pkpm-pbimserver-teamdb; user=root; password=*******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=80;Min Pool Size=10;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"LoggerConnection": "server=***************; port=6330; database=bimbase-logdb; user=root; password=*******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=40;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"LibraryConnection": "server=***************; port=6330; database=pkpm-pbimserver-librarydb; user=root; password=*******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=60;Min Pool Size=8;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"TemplateConnection": "server=***************; port=6330; database={DatabaseName}; user=root; password=*******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=150;Min Pool Size=30;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"RedisConnection": "localhost:6379"
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:8001",
        "Protocols": "Http1AndHttp2"
      },
      "Grpc": {
        "Url": "http://0.0.0.0:8002",
        "Protocols": "Http2"
      }
    }
  },
  "AllowedHosts": "*"
}
