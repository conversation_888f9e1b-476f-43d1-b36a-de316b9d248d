﻿using AutoMapper.Execution;
using BimBase.Api.Config;
using MySqlConnector;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Reflection.PortableExecutable;
using System.Runtime.Serialization;
using System.Security.Cryptography;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace BimBase.Api.Infrastructure.Common
{
    public static class UtilityHelper
    {
        private static ConcurrentDictionary<Type, IEnumerable<PropertyInfo>> proInfoCache;
        public static void ReleaseCache()
        {
            if (proInfoCache != null)
            {
                proInfoCache.Clear();
                proInfoCache = null;
            }
        }
        static public List<T> CopyToObject<T>(IEnumerable<T> proxies) where T : class
        {
            Type type = typeof(T);
            IEnumerable<PropertyInfo> propertyInfos = null;

            if (proInfoCache != null)
            {
                proInfoCache.TryGetValue(type, out propertyInfos);
            }
            else
            {
                proInfoCache = new ConcurrentDictionary<Type, IEnumerable<PropertyInfo>>();
            }

            if (propertyInfos == null)
            {
                propertyInfos = from p in type.GetProperties()
                                let attr = p.GetCustomAttributes(typeof(DataMemberAttribute), false)
                                where attr.Length > 0
                                select p;
                proInfoCache.TryAdd(type, propertyInfos);
            }
            List<T> rtObject = new List<T>();
            if (!propertyInfos.Any())
            {
                return rtObject;
            }
            foreach (T proxy in proxies)
            {
                object obj = type.InvokeMember(null, BindingFlags.CreateInstance, null, null, null);
                if (obj != null)
                {
                    foreach (PropertyInfo info in propertyInfos)
                    {
                        object value = info.GetValue(proxy);
                        if (value == null)
                            continue;
                        type.InvokeMember(info.Name, BindingFlags.SetProperty, null, obj, new object[] { value });
                    }
                    rtObject.Add(obj as T);
                }
            }
            List<Type> interfaces = type.GetInterfaces().ToList();
            if (interfaces.Exists(t => t == typeof(IComparable)))
                rtObject.Sort();
            return rtObject;
        }
        internal static string CreatePasswordMD5(string password)
        {
            MD5 md5Hash = MD5.Create();
            byte[] data = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(password));
            StringBuilder sBuilder = new StringBuilder();
            foreach (byte t in data)
            {
                sBuilder.Append(t.ToString("x2"));
            }
            return sBuilder.ToString();
        }

        public static string SHA256EncryptString(string sha256eninstr, bool isupper = false)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(sha256eninstr);
            using (var mySHA256 = SHA256Managed.Create())
            {
                byte[] hash = mySHA256.ComputeHash(bytes);
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < hash.Length; i++)
                {
                    builder.Append(hash[i].ToString(isupper ? "X2" : "x2"));
                }
                return builder.ToString();
            }
        }
        /// <summary>
        /// 字符串DES解密
        /// </summary>
        /// <param name="stringToDecrypt"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        internal static string Decrypt(string stringToDecrypt, string key)
        {
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                byte[] inputByteArray = Convert.FromBase64String(stringToDecrypt);
                des.Key = ASCIIEncoding.ASCII.GetBytes(key);
                des.Mode = CipherMode.ECB;
                des.Padding = PaddingMode.PKCS7;
                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                    {
                        cs.Write(inputByteArray, 0, inputByteArray.Length);
                        cs.FlushFinalBlock();
                        StringBuilder ret = new StringBuilder();
                        return System.Text.Encoding.Default.GetString(ms.ToArray());
                    }
                }
            }
        }

        internal static MysqlConnectionConfig GetMysqlConnectionConfig()
        {
            string DefaultConnection = SettingsConfigHelper.CustomSetting("ConnectionStrings", "DefaultConnection");
            // 解析出用户名和密码的尝试（不推荐，因为可能包含加密的部分）
            var connStrList = DefaultConnection.Split(';');
            
            var databaseIp = connStrList[0].Split('=')[1];
            var databasePort = connStrList[1].Split('=')[1];
            var dbusername = connStrList[3].Split('=')[1];
            var pass = connStrList[4];
            var dbpassword = pass.Substring(pass.IndexOf("password=") + "password=".Length);//.Split('=')[1];

            MysqlConnectionConfig mysqlConnectionConfig = new MysqlConnectionConfig();

            mysqlConnectionConfig.IP = databaseIp;
            mysqlConnectionConfig.Port = databasePort;  
            mysqlConnectionConfig.User = dbusername;    
            mysqlConnectionConfig.Password = dbpassword;

            return mysqlConnectionConfig;

        }

        public static void CopyEntireDir(string sourcePath, string destPath)
        {
            //Now Create all of the directories
            foreach (string dirPath in Directory.GetDirectories(sourcePath, "*",
               SearchOption.AllDirectories))
                Directory.CreateDirectory(dirPath.Replace(sourcePath, destPath));

            //Copy all the files & Replaces any files with the same name
            foreach (string newPath in Directory.GetFiles(sourcePath, "*.*",
               SearchOption.AllDirectories))
                File.Copy(newPath, newPath.Replace(sourcePath, destPath), true);
        }

        public static  string getmysqlpath()   //获取mysql安装路径
        {
            string DefaultConnection = SettingsConfigHelper.CustomSetting("ConnectionStrings", "DefaultConnection");
            MySqlConnection connection = new MySqlConnection(DefaultConnection);
            MySqlCommand command;
            string strsql = "select @@basedir as basePath from dual ";
            try
            {
                connection.Open();
                //Console.WriteLine(connection.State.ToString());
                command = new MySqlCommand(strsql, connection);
                string strPath = string.Empty;
                strPath = (string)command.ExecuteScalar();
                strPath = strPath + "bin";
                return strPath;
            }
            catch (Exception)
            {
                return "";
            }
        }

        
    }
}
