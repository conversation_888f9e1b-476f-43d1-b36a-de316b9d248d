﻿using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class ModelFile

    {
        
        [Key]
        public Int64 Id { get; set; }

        
        public string FileName { get; set; }

        
        public string FilePath { get; set; }
        
        public string Description { get; set; }
        //文件大小
        
        public long FileSize { get; set; }

        
        public int VersionNo { get; set; }

        
        public string UserName { get; set; }

        //上传时间
        
        public DateTime UploadTime { get; set; }

        //所属文件目录结构id
        //
        //public int DomainTypeId { get; set; }

        
        public string FileType { get; set; }


    }
}
