using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.LibDomain
{
    /// <summary>
    /// 客户端版本兼容性配置表
    /// </summary>
    [Table("clientversioncompatibility")]
    public class ClientVersionCompatibility
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        [Column("ID")]
        public int ID { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [Column("ClientId")]
        [StringLength(40)]
        public string? ClientId { get; set; }

        /// <summary>
        /// 客户端版本
        /// </summary>
        [Column("ClientVersion")]
        [StringLength(40)]
        public string? ClientVersion { get; set; }

        /// <summary>
        /// 可兼容的最小版本
        /// </summary>
        [Column("MinCompatibleVersion")]
        [StringLength(40)]
        public string? MinCompatibleVersion { get; set; }
    }
} 