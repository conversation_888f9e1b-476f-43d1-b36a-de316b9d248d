using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BimBase.Api.Infrastructure.Domain
{
    /// <summary>
    /// 客户端模块版本记录实体
    /// </summary>
    [Table("ClientModuleVersions")]
    public class ClientModuleVersion
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; }
        
        /// <summary>
        /// 模块ID
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ModuleId { get; set; }
        
        /// <summary>
        /// 版本号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Version { get; set; }
        
        /// <summary>
        /// 项目ID，用于关联主项目
        /// </summary>
        [StringLength(50)]
        public string ProjectId { get; set; }

        private string _clientIp;
        [StringLength(254)]
        public string ClientIp
        {
            get => _clientIp;
            set => _clientIp = value?.Length > 254 ? value.Substring(0, 254) : value;
        }

        /// <summary>
        /// 用户ID
        /// </summary>
        [StringLength(50)]
        public string UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        [StringLength(100)]
        public string UserName { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Required]
        public DateTime UpdatedAt { get; set; }
    }
} 