﻿using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Protos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Repositories
{
    public interface ITeamRepository:IRepository
    {
        Task<TeamMember> GetCurrentUserInfoAsync(string sessionId);

        Task<List<GrpcOpenningSessionUser>> GetOpenningUserListAsync(Guid projectGuid); 
        Task<Guid> GetCurrentUserProjectIdAsync(string sessionId);
        bool CheckUserPassword(string username, string passMD5, out TeamMember userInfo);
        bool CheckUserLogname(string Logname);
        string CreateSession(Guid userId);
        bool UpdateSession(string sessionId, Guid projectGuid);
        bool DeleteSession(string sessionId);
        IQueryable<string> GetOnlineUserlist();
        bool IsCurrentLogin(string sessionId);
        bool CheckProjectByPName(string projectName);

        string GetProjectIdByName(string projectName);

        IList<TeamProject> GetProjectList(string sessionId);

        System.Linq.IQueryable<TeamMember> Members { get; }
        System.Linq.IQueryable<TeamProject> Projects { get; }
        System.Linq.IQueryable<SchemaVersion> Versions { get; }
        System.Linq.IQueryable<DbVersion> DbVersions { get; }

        System.Linq.IQueryable<FileDirectory> FileDirectories { get; }
        System.Linq.IQueryable<MainProject> MainProjects { get; }
        System.Linq.IQueryable<MainProjectUserRole> MainProjectUserRoles { get; }

        //ModelDbContext GetModelDbContext(Guid projectId);
        bool AddMemberToMainProject(TeamMember mbr, MainProject mainProject);
        ModelDomain.ProjectMember AddMemberToProject(TeamMember mbr, TeamProject proj, bool asAdmin = false);
        bool RemoveMemberFromProject(TeamMember mbr, TeamProject proj);
        MainProject AddMainProject(MainProject mainProject);
        TeamProject AddProject(TeamProject prj);
        IProjectRepository GetProjectRepository(Guid projectId);
        IMainProjectRepository GetMainProjectRepository(Guid mainProjectId);
        TeamProject DeleteProject(Guid id);

        bool CheckProjectName(string pName);

        bool ModifyProjectInfo(TeamProject teamProject);

        TeamMember AddMember(TeamMember mbr);
        bool DeleteMember(string loginName, out string message);
        bool AddMemberListToMainProject(List<TeamMember> mbrList, MainProject mainProject);


        bool DeleteMainProjectFiles(Guid id);
        bool DeleteMainProject(Guid id);

        //bool DeleteMainProjectByName(string mpName);


        FileDirectory DeleteFileDirectory(Guid id);

        bool UpdateFileDirectoryOrderNo(Guid dirGuid, string upOrDown);
        //MainProjectUserRole AddMainProjectUserRole(MainProjectUserRole mainUserRole);
        bool AddFileDirectoreyList(List<FileDirectory> fileDicList);

        FileDirectory AddFileDirectorey(FileDirectory filedirectory);

        bool UpdateMemberInfo(TeamMember member);
        bool CheckFileDirectoryName(FileDirectory filedir);

        bool UpdateFileDirectoryInfo(FileDirectory directory);

        bool UpdateMainprojectInfo(MainProject mainproject);
        bool CheckMainProjectName(String mainPName);
        bool CheckProjectName(String pName, string mainProjectId);
        bool UpdateMainProjectLastUpdateTime(Guid mGuid);
        //bool Addcommonfiles(commonfiles cf);
        //bool Addcloudlinkfiles(cloudlinkfiles clf);

        //IQueryable<commonfiles> commonfiles { get; }

        //IQueryable<cloudlinkfiles> cloudlinkfiles { get; }
        IpMap AddIpMap(IpMap ipMap);

        IQueryable<IpMap> IpMaps { get; }

        bool CheckIpAddress(string address);
        bool UpdateIpMap(IpMap server);

        bool RemoveMemberFromTeamGroup(TeamMember mbr, TeamGroup teamGroup);
        bool AddMemberToTeamGroup(TeamMember mbr, TeamGroup teamGroup);


        bool AddTeamGroupToMainproject(TeamGroup tg, MainProject mainProject, Guid objectId);


        bool AddTeamGrouMemberToMainProject(TeamMember meb, MainProject mainProject, Guid objectId);
        bool DeleteTeamGroupAuth(TeamGroupAuth teamGroupAuth);
        bool DeleteTeamGroupAuth(Guid objectGuid, string groupormemberid);
        bool GiveAuthToTeamGroup(TeamGroupAuth teamGroupAuth);
        bool GiveAuthToTeamGroup(TeamGroupAuth teamGroupAuth, List<TeamAuth> authList);

        bool DeleteGroupOrMemberFromMainProject(Guid mainprojectGuid, Guid objectGuid, string groupormemberid);

        bool DeleteGroupOrMemberListFromMainProject(Guid mainprojectGuid, Guid objectGuid, List<string> groupormemberids);
        IQueryable<TeamUserGroup> TeamUserGroups { get; }
        IQueryable<TeamGroup> TeamGroups { get; }
        TeamGroup AddTeamGroup(TeamGroup teamGroup);

        bool UpdateTeamGroup(TeamGroup teamGroup);
        bool DeleteTeamGroup(int id);

        

        /// <summary>
        /// 获取所有的权限的列表
        /// </summary>
        IQueryable<TeamAuth> TeamAuths { get; }

        /// <summary>
        /// 获取所有项目的项目与用户组关联数据
        /// </summary>
        IQueryable<MainProjectTeamGroup> MainProjectTeamGroups { get; }
        /// <summary>
        /// TeamGroupAuth列表
        /// </summary>
        IQueryable<TeamGroupAuth> TeamGroupAuths { get; }

        /// <summary>
        /// 卷册图档 列表
        /// </summary>
        IQueryable<Volume> Volumes { get; }
        /// <summary>
        /// 还原模型数据库后，更新modelfiles表里的filepath字段信息
        /// </summary>
        /// <param name="projectGuid">原projectid</param>
        /// <param name="newProjectGuid">新projectid</param>
        /// <param name="newPath">新的filepath</param>
        /// <returns></returns>
        bool UpdateModelFileInfo(Guid projectGuid, Guid newProjectGuid, string newPath);
        /// <summary>
        /// 创建PKPM-PBIMServer-MPDB- + mainProjectGuid 数据库
        /// </summary>
        /// <param name="mainProjectGuid"></param>
        /// <returns></returns>
        bool CreateMPdataBase(Guid mainProjectGuid);
        /// <summary>
        /// 创建`PKPM-PBIMServer-ModelDB-" + projectGuid + "` 数据库
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <returns></returns>
        bool CreateModeldataBase(Guid projectGuid);
        /// <summary>
        /// 清除锁定构件数据
        /// </summary>
        /// <param name="projectGuid"></param>
        /// <param name="newProjectGuid"></param>
        /// <returns></returns>
        bool ClearLockedComponents(Guid newProjectGuid);
        /// <summary>
        /// 清除文件级协同锁定
        /// </summary>
        /// <param name="newProjectGuid"></param>
        /// <returns></returns>
        bool ClearLockModelfiles(Guid newProjectGuid);
        bool UpdateMileStoneFileInfo(Guid projectGuid, string oldMainprojectName, Guid newProjectGuid, string newPath);
        IQueryable<TeamProjectModelLock> TeamProjectModelLocks { get; }

        Volume AddVolume(Volume vol);

       bool AddVolumeList(List<Volume> volList);

         bool DeleteVolume(Guid volGuid);

        IQueryable<VolumeVersion> VolumeVersions { get; }

        bool AddVolumeVersion(VolumeVersion volVer);
        bool AddVolumeVersionList(List<VolumeVersion> volVerList);
        bool AddTeamProjectModelLock(TeamProjectModelLock teamProjectModelLock);

        bool DeleteTeamProjectModelLock(string lockuser, Guid projectGuid);

        List<MainProject> GetMainProjectListByMember(Guid memberGuid);

        List<TeamProject> GetTeamProjectListByMember(Guid memberGuid);

        List<TeamProject> GetTeamProjectListByMemberAndMainprojectId(Guid memberGuid, Guid mainprojectId);

        List<TeamProject> GetTeamProjectListByMemberForPDMS(Guid memberGuid);
        /// <summary>
        /// 获取项目下所有模型，文件夹参与用户
        /// </summary>
        /// <param name="mainProjectGuid"></param>
        /// <returns></returns>
        List<TeamMember> GetTeamMembersByMainProject(Guid mainProjectGuid);
        /// <summary>
        /// 判断权限
        /// </summary>
        /// <param name="objectid">操作对象id：项目guid\文件夹guid\模型guid</param>
        /// <param name="mainProjectId">项目Guid</param>
        /// <param name="objecttype">权限对象类型 1：项目 2：文件夹 3：模型 4：卷册图档</param>
        /// <returns></returns>
        List<TeamAuth> GetAuthByObjectId(Guid objectid, Guid mainProjectId, int objecttype, string username);

       bool GetEditModelAuth(Guid objectid, Guid mainProjectId, string username);

         /// <summary>
        /// 获取节点的参与用户
        /// </summary>
        /// <param name="mainProjectGuid"></param>
        /// <param name="objectGuid"></param>
        /// <param name="objecttype"></param>
        /// <returns></returns>
        List<TeamMember> GetTeamMembersByObjectId(Guid mainProjectGuid, Guid objectGuid, int objecttype);

        

        long CreateRepositoryId();

        RepositoryInformation GetRepository();

        bool UpdateRepository(long rep);

        IQueryable<MainProjectUserGroupMember> mainProjectUserGroupMembers { get; }

        /// <summary>
        /// 添加用户到用户组
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        bool AddTeamMemberToMPUsergroup(Guid memberid, int usergroupid, string usergroupname, Guid mainprojectGuid);
        bool AddTeamMemberListToMPUsergroup(List<Guid> memberid, int usergroupid, string usergroupname, Guid mainprojectGuid);

        bool AddMainProjectUserGroupMemberList(List<MainProjectUserGroupMember> members);

        bool AddMainProjectUserGroupAuthList(List<MainProjectUserGroupAuth> members);

        bool AddMainProjectTeamGroupList(List<MainProjectTeamGroup> members);
        bool AddTeamUserGroupList(List<TeamUserGroup> teamUserGroups);
        /// <summary>
        /// 从用户组中移除用户
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        bool RemoveTeamMemberFromMPUsergroup(Guid memberid, int usergroupid, Guid mainprojectGuid);

        bool RemoveTeamMemberListFromMPUsergroup(List<Guid> memberidList, int usergroupid, Guid mainprojectGuid);
        bool RemoveTeamMemberFromMPUsergroupList(Guid memberid, List<int> usergroupidList, Guid mainprojectGuid);
        bool RemoveTeamMemberListFromMPUsergroupList(List<Guid> memberids, List<int> usergroupidList, Guid mainprojectGuid);

        IQueryable<MainProjectUserGroupAuth> mainProjectUserGroupAuths { get; }

        bool GiveAuthToMPUserGroupMultiple(Guid mainprojectGuid, List<MPUserGroupAuth> userGroupAuthList);

        bool GiveAuthToMPUserGroup(Guid mainprojectGuid, MPUserGroupAuth userGroupAuth, List<MPAuthInfo> authList);

        IQueryable<MainProjectUserGroupLibAuth> mainProjectUserGroupLibAuths { get; }

        bool SetMPUserGroupLibAuth(Guid mainprojectGuid, MPUserGroupLibAuth mpUserGroupLibAuth);

        bool AddMainProjectUserGroupLibAuthList(List<MainProjectUserGroupLibAuth> mpUserGroupLibAuth);

        bool UpdataProjectFileDirId(Guid mpGuid, List<Guid> prjGuidList, Guid newDirId);
        bool UpdateVolumeFileDirId(Guid mpGuid, List<Guid> volGuidList, Guid newDirId);
        bool UpdateMainprojectDeleteState(MainProject mainproject);

        /*        IQueryable<CacheMainproject> cacheMainprojects { get; }

                bool AddCacheMainprojects(Guid mpGuid);

                bool RemoveCacheMainprojects(Guid mpGuid);

                **/

        /// <summary>
        /// 获取所有MainProject，忽略ClientVersion过滤条件
        /// </summary>
        IQueryable<MainProject> MainProjectsIgnoreClientVersion { get; }

        IQueryable<ClientModuleVersion> ClientModuleVersions { get; }

        bool AddClientModuleVersions(List<ClientModuleVersion> clientModuleVersion);

        IQueryable<ClientVersionCompatibility> ClientVersionCompatibilities { get; }

        /// <summary>
        /// 添加节点名称检查配置列表
        /// </summary>
        /// <param name="nodeNameCheckConfigs">节点名称检查配置列表</param>
        /// <returns>是否添加成功</returns>
        bool AddNodeNameCheckConfigs(List<NodeNameCheckConfig> nodeNameCheckConfigs);

        /// <summary>
        /// 获取节点名称检查配置列表
        /// </summary>
        /// <returns>节点名称检查配置查询</returns>
        IQueryable<NodeNameCheckConfig> NodeNameCheckConfigs { get; }
    }
}
