using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb升级接口
    /// </summary>
    public interface IMainProjectDbUpgrade
    {
        /// <summary>
        /// 源版本号
        /// </summary>
        string FromVersion { get; }

        /// <summary>
        /// 目标版本号
        /// </summary>
        string ToVersion { get; }

        /// <summary>
        /// 执行升级
        /// </summary>
        /// <returns></returns>
        Task ExecuteAsync();

        /// <summary>
        /// 回滚升级
        /// </summary>
        /// <returns></returns>
        Task RollbackAsync();

        /// <summary>
        /// 获取升级描述
        /// </summary>
        /// <returns></returns>
        string GetDescription();

        /// <summary>
        /// 验证升级前提条件
        /// </summary>
        /// <returns></returns>
        Task<bool> ValidateAsync();
    }
} 