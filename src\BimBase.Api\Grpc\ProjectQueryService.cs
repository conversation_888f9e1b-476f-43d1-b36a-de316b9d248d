﻿using AutoMapper;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Protos;
using Grpc.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static BimBase.Api.Protos.GrpcProjectQuery;

namespace BimBase.Api.Grpc
{
    public class ProjectQueryService: GrpcProjectQueryBase
    {
        private readonly ITeamRepository _teamRepository;
        private readonly IMapper _mapper;
        private readonly IAuthorityManager _authorityManager;
        public ProjectQueryService(ITeamRepository teamRepository
            , IMapper mapper
            , IAuthorityManager authorityManager)
        {
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _authorityManager = authorityManager ?? throw new ArgumentNullException(nameof(authorityManager));
        }


        public override async Task<GetProjectMembersResponse> GetProjectMembers(ProjectQueryRequest request, ServerCallContext context)
        {
            var x = new GetProjectMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if(currentUser is null)
            {
                x.Message = "sessinId 不存在或过期！";
                return x;
            }
            var pMembers = _teamRepository.GetProjectRepository(projectId).Members.ToList();
            foreach (var mem in pMembers)
            {
                var teamMember = _teamRepository.Members.SingleOrDefault(memT => memT.ID == mem.TeamMemberID);
                if (teamMember != null)
                {
                    var member = new GrpcProjectMember();
                    member.Id = mem.ID;
                    member.TeamMemberGuid = teamMember.ID.ToString();
                    member.LoginName = teamMember.LoginName;
                    member.DisplayName = teamMember.DisplayName;
                    member.Color = teamMember.Color;
                    member.Avatar = teamMember.Avatar;
                    member.IsProjectAdmin = mem.IsProjectAdmin;
                    x.ProjectMembers.Add(member);
                }
            }

            var ret = _authorityManager.GetProjectUserAuth(currentUser.ID, projectId, out var authInfos, out var message);
            if (ret)
            {
                x.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));
            }
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetProjectRolesResponse> GetProjectRoles(ProjectQueryRequest request, ServerCallContext context)
        {
            var x = new GetProjectRolesResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "sessinId 不存在或过期！";
                return x;
            }

            var ret = _authorityManager.GetProjectRole(request.ProjectId, out var roles, out var message);
            if (ret)
            {
                x.Roles.AddRange(_mapper.Map<List<RoleDto>>(roles));
            }
            else
            {
                x.Message = message;
            }
            x.IsSuccess = ret;
            return x;
        }

        public override async Task<GetProjectDescriptionResponse> GetProjectDescription(ProjectQueryRequest request, ServerCallContext context)
        {
            var x = new GetProjectDescriptionResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var teamProject = _teamRepository.Projects.SingleOrDefault(p => p.ID.Equals(projectId));
            if (teamProject != null)
            {
                x.IsSuccess = true;
                x.ProjectDescription = teamProject.Description;
            }

            return x;
        }

        public override async Task<GetProjectNameResponse> GetProjectName(ProjectQueryRequest request, ServerCallContext context)
        {
            var x = new GetProjectNameResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var teamProject = _teamRepository.Projects.SingleOrDefault(p => p.ID.Equals(projectId));
            if (teamProject != null)
            {
                x.IsSuccess = true;
                x.ProjectName = teamProject.Name;
            }

            return x;
        }

        public override async Task<GetProjectRoleMembersResponse> GetProjectRoleMembers(GetProjectRoleMembersRequest request, ServerCallContext context)
        {
            var x = new GetProjectRoleMembersResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var ret = _authorityManager.GetRoleUsers(request.RoleGuid, out var memberGuids, out var message);
            if (!ret)
            {
                x.Message = message;
                return x;
            }

            var projectRepo = _teamRepository.GetProjectRepository(projectId);
            var members = projectRepo.Members.Where(m => memberGuids.Contains(m.TeamMemberID)).ToList();

            foreach (var member in members)
            {
                var teamMember = _teamRepository.Members.SingleOrDefault(m => m.ID == member.TeamMemberID);
                if (null == teamMember)
                {
                    x.Message = "编号[" + member.TeamMemberID + "]的用户无法找到";
                    return x;
                }

                x.ProjectMembers.Add(new GrpcProjectMember { Color = member.Color, DisplayName = teamMember.DisplayName, Id = member.ID, LoginName = teamMember.LoginName, TeamMemberGuid = teamMember.ID.ToString(), Avatar = teamMember.Avatar });
            }

            return x;
        }


        public override async Task<LoadMemberRoleByMemIDsResponse> LoadMemberRoleByMemIDs(LoadMemberRoleByMemIDsRequest request, ServerCallContext context)
        {
            var x = new LoadMemberRoleByMemIDsResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            foreach (var member in request.ProjectMembers)
            {
                var ret = _authorityManager.GetRoleInfoByUserIdAndProjectId(Guid.Parse(member.TeamMemberGuid), projectId, out var roles, out var message);
                if (ret)
                {
                    var mrinfo = new GrpcMemberRoleInfo();
                    var r = roles.FirstOrDefault();
                    mrinfo.Id = member.Id;
                    mrinfo.Avatar = member.Avatar;
                    mrinfo.LoginName = member.LoginName;
                    mrinfo.DisplayName = member.DisplayName;
                    mrinfo.GuidID = member.TeamMemberGuid;
                    mrinfo.RoleName = r.Name;
                    x.MemRoleInfos.Add(mrinfo);
                }
                else
                {
                    x.Message = message;
                    return x;
                }
                
            }
            x.IsSuccess = true;
            return x;
        }


        public override async Task<GetUserAuthoritiesResponse> GetUserAuthorities(ProjectQueryRequest request, ServerCallContext context)
        {
            var x = new GetUserAuthoritiesResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            if (string.IsNullOrWhiteSpace(request.ProjectId) || !Guid.TryParse(request.ProjectId, out var projectId))
            {
                x.Message = $"参数 ProjectId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var ret = _authorityManager.GetProjectUserAuth(currentUser.ID, projectId, out var authInfos, out var message);
            if (ret)
            {
                x.IsSuccess = true;
                x.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));
            }
            x.Message = message;
            return x;
        }

        public override async Task<GetRoleAuthoritiesResponse> GetRoleAuthorities(GetRoleAuthoritiesRequest request, ServerCallContext context)
        {
            var x = new GetRoleAuthoritiesResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.IsSuccess = false;
                x.Message = "获取当前用户信息失败！";
            }

            var ret = _authorityManager.GetRoleAuthorities(request.RoleId, out var authInfos);
            if (ret)
            {
                x.IsSuccess = true;
                x.AuthInfos.AddRange(_mapper.Map<List<AuthInfoDto>>(authInfos));
            }
            return x;
        }

    }
}
