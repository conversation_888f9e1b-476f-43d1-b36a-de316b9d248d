using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v3升级到v4
    /// 增加索引
    /// </summary>
    public class TeamDbUpgrade_v3_to_v4 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v3";
        public override string TargetVersion => "v4";
        public override string Description => "TeamDb v3到v4升级（增加索引）";

        public TeamDbUpgrade_v3_to_v4(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");
            
            // 空升级，无需执行任何操作
            Logger.LogInformation("这是一个空升级，无需执行任何数据库更改");            
            // 创建索引
            await CreateIndexIfNotExistsAsync(context, "MainProjectTeamGroups", "IX_MainProjectId", "MainProjectId");
            await CreateIndexIfNotExistsAsync(context, "MainProjectTeamGroups", "IX_MemberType", "MemberType");
            await CreateIndexIfNotExistsAsync(context, "MainProjectUserGroupMembers", "IX_MainProjectId", "MainProjectId");

            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");

            // 回滚
            await DropIndexIfExistsAsync(context, "MainProjectUserGroupMembers", "IX_MainProjectId");
            await DropIndexIfExistsAsync(context, "MainProjectTeamGroups", "IX_MemberType");
            await DropIndexIfExistsAsync(context, "MainProjectTeamGroups", "IX_MainProjectId");

            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 