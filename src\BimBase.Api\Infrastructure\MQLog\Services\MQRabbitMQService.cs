using System.Text.Json;
using System.Text;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using Microsoft.Extensions.Options;
using System.Threading.Tasks;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using BimBase.Api.Infrastructure.MQLog.Services;
using System.Threading;

namespace BimBase.Api.Infrastructure.MQLog
{
    public class LogPublishItem
    {
        public object LogData { get; set; }
        public string RoutingKey { get; set; }
        public string QueueName { get; set; }
    }

    /// <summary>
    /// MQ日志消息发送服务，负责将日志对象序列化并发送到RabbitMQ
    /// </summary>
    public class MQRabbitMQService : IMQRabbitMQService, IDisposable
    {
        private readonly ILogger<MQRabbitMQService> _logger;
        private readonly MQLogSettings _settings;
        private readonly IRabbitMQConnectionProvider _connectionProvider;
        private readonly IChannelPool _channelPool;
        private readonly BlockingCollection<LogPublishItem> _logQueue;
        private readonly Task _consumerTask;
        private bool _disposed;

        public MQRabbitMQService(
            ILogger<MQRabbitMQService> logger,
            IOptions<MQLogSettings> settings,
            IRabbitMQConnectionProvider connectionProvider,
            IChannelPool channelPool)
        {
            _logger = logger;
            _settings = settings.Value;
            _connectionProvider = connectionProvider;
            _channelPool = channelPool;
            _logQueue = new BlockingCollection<LogPublishItem>(boundedCapacity: 10000);
            // 添加异常处理，避免内存不足时导致服务崩溃
            try
            {
                _consumerTask = Task.Factory.StartNew(ConsumeLogQueue, TaskCreationOptions.LongRunning);
                _logger.LogInformation("[MQ日志] 后台消费线程启动成功");
            }
            catch (OutOfMemoryException ex)
            {
                _logger.LogError(ex, "[MQ日志] 系统内存不足，无法启动后台消费线程，MQ日志功能将不可用");
                _consumerTask = Task.CompletedTask; // 使用已完成的任务作为占位符
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[MQ日志] 启动后台消费线程失败，MQ日志功能将不可用");
                _consumerTask = Task.CompletedTask; // 使用已完成的任务作为占位符
            }
        }

        public async Task InitializeExchangeAndQueuesAsync(int retryCount = 3)
        {
            for (int i = 0; i < retryCount; i++)
            {
                try
                {
                    using var channel = _connectionProvider.GetConnection().CreateModel();
                    channel.ExchangeDeclare(
                        exchange: _settings.RabbitMQ.ExchangeName,
                        type: ExchangeType.Topic,
                        durable: true,
                        autoDelete: false);
                    channel.QueueDeclare(
                        queue: _settings.RabbitMQ.InterfaceLogQueue,
                        durable: true,
                        exclusive: false,
                        autoDelete: false);
                    channel.QueueBind(
                        queue: _settings.RabbitMQ.InterfaceLogQueue,
                        exchange: _settings.RabbitMQ.ExchangeName,
                        routingKey: _settings.RabbitMQ.InterfaceLogRoutingKey);
                    channel.QueueDeclare(
                        queue: _settings.RabbitMQ.ErrorLogQueue,
                        durable: true,
                        exclusive: false,
                        autoDelete: false);
                    channel.QueueBind(
                        queue: _settings.RabbitMQ.ErrorLogQueue,
                        exchange: _settings.RabbitMQ.ExchangeName,
                        routingKey: _settings.RabbitMQ.ErrorLogRoutingKey);
                    _logger.LogInformation("[MQ日志] RabbitMQ exchange/queues initialized successfully.");
                    return;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"[MQ日志] RabbitMQ初始化失败，第{i+1}次重试");
                    if (i == retryCount - 1) throw;
                    await Task.Delay(2000);
                }
            }
        }

        public Task PublishInterfaceLogAsync(object logData)
        {
            try
            {
                var item = new LogPublishItem
                {
                    LogData = logData,
                    RoutingKey = _settings.RabbitMQ.InterfaceLogRoutingKey,
                    QueueName = _settings.RabbitMQ.InterfaceLogQueue
                };
                _logQueue.Add(item);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[MQ日志] 入队接口日志异常，已忽略");
            }
            return Task.CompletedTask;
        }

        public Task PublishErrorLogAsync(object logData)
        {
            try
            {
                var item = new LogPublishItem
                {
                    LogData = logData,
                    RoutingKey = _settings.RabbitMQ.ErrorLogRoutingKey,
                    QueueName = _settings.RabbitMQ.ErrorLogQueue
                };
                _logQueue.Add(item);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[MQ日志] 入队错误日志异常，已忽略");
            }
            return Task.CompletedTask;
        }

        private void ConsumeLogQueue()
        {
            foreach (var item in _logQueue.GetConsumingEnumerable())
            {
                try
                {
                    PublishMessage(item.LogData, item.RoutingKey, item.QueueName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[MQ日志] 后台线程发布日志到MQ失败，RoutingKey: {RoutingKey}", item.RoutingKey);
                }
            }
        }

        private void PublishMessage(object logData, string routingKey, string queueName)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                IModel channel = null;
                try
                {
                    // 每次新建 Channel
                    channel = _connectionProvider.GetConnection().CreateModel();
                    var message = JsonSerializer.Serialize(logData);
                    var body = Encoding.UTF8.GetBytes(message);
                    var properties = channel.CreateBasicProperties();
                    properties.Persistent = true;
                    properties.MessageId = Guid.NewGuid().ToString();
                    string requestId = ExtractRequestId(logData);
                    string stage = ExtractStage(logData);
                    _logger.LogDebug("[MQ发布] 开始发布消息, RequestId: {RequestId}, 阶段: {Stage}, 尝试次数: {Attempt}", requestId, stage, attempt);
                    channel.ConfirmSelect();
                    channel.BasicPublish(
                        exchange: _settings.RabbitMQ.ExchangeName,
                        routingKey: routingKey,
                        mandatory: true,
                        basicProperties: properties,
                        body: body);
                    if (channel.WaitForConfirms(TimeSpan.FromSeconds(5)))
                    {
                        _logger.LogDebug("[MQ发布] 消息发布确认成功, RequestId: {RequestId}, 阶段: {Stage}, 尝试次数: {Attempt}", requestId, stage, attempt);
                        return;
                    }
                    else
                    {
                        _logger.LogWarning("[MQ发布] 消息发布确认失败, RequestId: {RequestId}, 阶段: {Stage}, 尝试次数: {Attempt}", requestId, stage, attempt);
                        if (attempt == maxRetries)
                        {
                            _logger.LogError("[MQ发布] 消息发布最终失败, RequestId: {RequestId}, 阶段: {Stage}, 已重试 {MaxRetries} 次", requestId, stage, maxRetries);
                            return; // 只记录日志，不抛异常
                        }
                        Thread.Sleep(retryDelayMs * attempt);
                    }
                }
                catch (Exception ex)
                {
                    string requestId = ExtractRequestId(logData);
                    string stage = ExtractStage(logData);
                    _logger.LogWarning(ex, "[MQ发布] 发布消息异常, RequestId: {RequestId}, 阶段: {Stage}, 尝试次数: {Attempt}, 异常: {ErrorMessage}", requestId, stage, attempt, ex.Message);
                    if (attempt == maxRetries)
                    {
                        _logger.LogError(ex, "[MQ发布] 发布消息最终失败, RequestId: {RequestId}, 阶段: {Stage}, 已重试 {MaxRetries} 次", requestId, stage, maxRetries);
                        return; // 只记录日志，不抛异常
                    }
                    Thread.Sleep(retryDelayMs * attempt);
                }
                finally
                {
                    if (channel != null)
                        channel.Dispose(); // 释放 Channel
                }
            }
        }

        /// <summary>
        /// 从日志数据中提取RequestId
        /// </summary>
        private string ExtractRequestId(object logData)
        {
            try
            {
                if (logData is System.Dynamic.ExpandoObject expando)
                {
                    var dict = (IDictionary<string, object>)expando;
                    return dict.TryGetValue("RequestId", out var value) ? value?.ToString() : "Unknown";
                }
                
                // 使用反射获取RequestId属性
                var requestIdProperty = logData.GetType().GetProperty("RequestId");
                return requestIdProperty?.GetValue(logData)?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 从日志数据中提取阶段信息
        /// </summary>
        private string ExtractStage(object logData)
        {
            try
            {
                if (logData is System.Dynamic.ExpandoObject expando)
                {
                    var dict = (IDictionary<string, object>)expando;
                    return dict.TryGetValue("LogStage", out var value) ? value?.ToString() : "Unknown";
                }
                
                // 使用反射获取LogStage属性
                var stageProperty = logData.GetType().GetProperty("LogStage");
                return stageProperty?.GetValue(logData)?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _logQueue.CompleteAdding();
                try { _consumerTask.Wait(); } catch { }
                _logQueue.Dispose();
                _disposed = true;
            }
        }
    }
} 