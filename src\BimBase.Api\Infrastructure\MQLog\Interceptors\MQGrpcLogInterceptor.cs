using Grpc.Core;
using Grpc.Core.Interceptors;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.Text.Json;
using System.Net;
using Microsoft.AspNetCore.Http;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志专用gRPC拦截器，采集gRPC接口日志并写入MQ日志，不影响原有拦截器
    /// 可通过配置开关控制是否启用
    /// </summary>
    public class MQGrpcLogInterceptor : Interceptor
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<MQGrpcLogInterceptor> _logger;
        private readonly IConfiguration _config;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly bool _enableMQLogging;

        public MQGrpcLogInterceptor(IServiceScopeFactory scopeFactory, ILogger<MQGrpcLogInterceptor> logger, IConfiguration config, IHttpContextAccessor httpContextAccessor)
        {
            _scopeFactory = scopeFactory;
            _logger = logger;
            _config = config;
            _httpContextAccessor = httpContextAccessor;
            _enableMQLogging = config.GetValue<bool>("LogSettings:LinShiMQ:Enabled", false);
        }

        public override async Task<TResponse> UnaryServerHandler<TRequest, TResponse>(TRequest request, ServerCallContext context, UnaryServerMethod<TRequest, TResponse> continuation)
        {
            if (!_enableMQLogging)
            {
                return await continuation(request, context);
            }

            var stopwatch = Stopwatch.StartNew();
            var requestTime = DateTime.Now;

            string requestId = _httpContextAccessor.HttpContext?.TraceIdentifier;
            if (string.IsNullOrEmpty(requestId))
            {
                requestId = Guid.NewGuid().ToString();
            }
            context.UserState["RequestId"] = requestId;

            string userId = null, userName = null;
            if (context.RequestHeaders != null)
            {
                var userIdEntry = context.RequestHeaders.FirstOrDefault(h => h.Key == "user-id");
                if (userIdEntry != null) userId = userIdEntry.Value;
                var userNameEntry = context.RequestHeaders.FirstOrDefault(h => h.Key == "user-name");
                if (userNameEntry != null) userName = userNameEntry.Value;
            }

            string requestParams = JsonSerializer.Serialize(request);

            string clientIp =
                context.RequestHeaders.FirstOrDefault(h => h.Key == "x-forwarded-for")?.Value
                ?? _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString()
                ?? context.Peer;




            var interfaceLog = new MQInterfaceLog
            {
                RequestId = requestId,
                RequestTime = requestTime,
                InterfaceName = context.Method,
                HttpMethod = "gRPC",
                RequestParams = requestParams,
                ClientIp = clientIp,
                UserId = userId,
                UserName = userName,
                LogType = "interface",
                LogLevel = "info",
                ProjectId = context.RequestHeaders.FirstOrDefault(h => h.Key == "project-id")?.Value,
                ProjectName = context.RequestHeaders.FirstOrDefault(h => h.Key == "project-name")?.Value,
                SessionId = context.RequestHeaders.FirstOrDefault(h => h.Key == "session-id")?.Value,
                ServerIp = _config["LogSettings:ServerIp"],
                ServerName = _config["LogSettings:ServerName"],
                AppName = _config["LogSettings:AppName"] ?? AppDomain.CurrentDomain.FriendlyName,
                AppVersion = _config["LogSettings:AppVersion"],
                Environment = _config["LogSettings:Environment"],
                AddTime = DateTime.Now,
                ResponseTime = null,
                IsSuccess = null,
                ResponseStatusCode = null,
                TotalMilliseconds = null,
                LogStage = "request"
            };
            using (var scope = _scopeFactory.CreateScope())
            {
                try
                {
                    var logWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                    await logWriterService.WriteInterfaceLogAsync(interfaceLog);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "[MQGrpcLogInterceptor] MQ 日志写入失败，不影响主流程");
                }
            }
            using (var scope = _scopeFactory.CreateScope())
            {
                var logInterfaceWriterService = scope.ServiceProvider.GetRequiredService<IMQInterfaceWriterService>();
                var logErrorWriterService = scope.ServiceProvider.GetRequiredService<IMQErrorWriterService>();
                try
                {
                    var response = await continuation(request, context);
                    stopwatch.Stop();
                    interfaceLog.ResponseTime = DateTime.Now;
                    interfaceLog.TotalMilliseconds = stopwatch.ElapsedMilliseconds;
                    interfaceLog.ResponseStatusCode = 200;
                    interfaceLog.IsSuccess = true;
                    interfaceLog.LogStage = "response";
                    try
                    {
                        await logInterfaceWriterService.WriteInterfaceLogAsync(interfaceLog);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[MQGrpcLogInterceptor] 响应日志写入失败，不影响主流程");
                    }
                    return response;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    interfaceLog.ResponseTime = DateTime.Now;
                    interfaceLog.TotalMilliseconds = stopwatch.ElapsedMilliseconds;
                    interfaceLog.ResponseStatusCode = 500;
                    interfaceLog.IsSuccess = false;
                    interfaceLog.ErrorMessage = ex.Message;
                    interfaceLog.LogLevel = "error";
                    interfaceLog.LogStage = "response";
                    interfaceLog.SourceClassName = ex.TargetSite?.DeclaringType?.FullName;
                    interfaceLog.SourceMethodName = ex.TargetSite?.Name;
                    try
                    {
                        await logInterfaceWriterService.WriteInterfaceLogAsync(interfaceLog);
                    }
                    catch (Exception mqEx)
                    {
                        _logger.LogWarning(mqEx, "[MQGrpcLogInterceptor] 错误响应日志写入失败，不影响主流程");
                    }
                    var errorLog = new MQErrorLog
                    {
                        ErrorId = Guid.NewGuid().ToString(),
                        RequestId = requestId,
                        ProjectId = interfaceLog.ProjectId,
                        ProjectName = interfaceLog.ProjectName,
                        SessionId = interfaceLog.SessionId,
                        ServerIp = interfaceLog.ServerIp,
                        ServerName = interfaceLog.ServerName,
                        AppName = interfaceLog.AppName,
                        AppVersion = interfaceLog.AppVersion,
                        Environment = interfaceLog.Environment,
                        LogLevel = "error",
                        LogType = "error",
                        ErrorType = ex.GetType().FullName,
                        ErrorMessage = ex.Message,
                        ErrorCode = ex.HResult.ToString(),
                        ErrorStackTrace = ex.StackTrace,
                        InputParams = interfaceLog.RequestParams,
                        SourceClassName = ex.TargetSite?.DeclaringType?.FullName,
                        SourceMethodName = ex.TargetSite?.Name,
                        AdditionalData = ex.Data != null && ex.Data.Count > 0 ? JsonSerializer.Serialize(ex.Data) : null,
                        AddTime = DateTime.Now,
                        UserId = userId,
                        UserName = userName,
                        ClientIp = clientIp,
                    };
                    try
                    {
                        await logErrorWriterService.WriteErrorLogAsync(errorLog);
                    }
                    catch (Exception mqEx)
                    {
                        _logger.LogWarning(mqEx, "[MQGrpcLogInterceptor] 错误日志写入失败，不影响主流程");
                    }
                    _logger.LogError(ex, "[MQGrpcLogInterceptor] 捕获异常并写入MQ日志");
                    throw;
                }
            }
        }
    }
}