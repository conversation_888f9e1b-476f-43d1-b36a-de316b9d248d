# 数据库层面节点名称冲突处理实现

## 实现概述

根据用户需求，我们实现了在数据库层面直接处理节点名称冲突的方案，无需前置判断，通过 ON DUPLICATE KEY UPDATE 语句在保存时自动检测和记录冲突信息。同时支持根据 `NodeNameCheckConfig` 配置进行不同范围的冲突检测。

## 核心设计理念

### 1. 数据库层面动态冲突检测
- 添加 `ConflictScope` 字段，用于动态标识冲突检测范围
- 根据配置动态设置 `ConflictScope` 的值
- 使用 `{subProjectld, ConflictScope, NodeName}` 作为唯一索引

### 2. 配置化冲突检测
- 根据 `NodeNameCheckConfig` 配置决定冲突检测范围
- `CheckType = 0`：全局检测，`ConflictScope = "GLOBAL"`
- `CheckType = 1`：同级检测，`ConflictScope = ParentNodeId.ToString()`
- 支持不同节点类型使用不同的冲突检测策略

### 3. 数据库层面处理
- 使用唯一索引 `{subProjectld, ConflictScope, NodeName}` 进行冲突检测
- 通过 ON DUPLICATE KEY UPDATE 自动处理冲突
- 在数据库中记录冲突信息

### 4. 冲突信息记录
- 添加 `OriginalNodeName` 字段记录冲突前的原始名称
- 添加 `HasConflict` 字段标记是否存在冲突
- 添加 `ConflictScope` 字段标识冲突检测范围
- 保存后查询冲突信息返回给客户端

## 数据库结构变更

### 新增字段

```sql
-- 添加原始节点名称字段
ALTER TABLE `mpprojecttreenodes` 
ADD COLUMN `OriginalNodeName` VARCHAR(190) NULL 
COMMENT '原始节点名称（用于记录冲突前的名称）';

-- 添加冲突标记字段
ALTER TABLE `mpprojecttreenodes` 
ADD COLUMN `HasConflict` INT NOT NULL DEFAULT 0 
COMMENT '冲突标记（1=存在冲突，0=无冲突）';

-- 添加冲突查询索引
ALTER TABLE `mpprojecttreenodes` 
ADD INDEX `idx_hasConflict` (`HasConflict`);
```

### 配置表结构

```sql
-- NodeNameCheckConfig 表结构
CREATE TABLE `nodenamecheckconfigs` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NodeTypeName` varchar(50) NOT NULL COMMENT '节点类型名称',
  `NodeTypeNum` int NOT NULL COMMENT '节点类型编号',
  `CheckType` int NOT NULL DEFAULT 1 COMMENT '冲突判断范围（0=全局，1=同级节点）',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_nodetypenum` (`NodeTypeNum`)
) COMMENT='节点名称冲突检查配置表';

-- 新增字段
ALTER TABLE `mpprojecttreenodes` 
ADD COLUMN `ConflictScope` VARCHAR(190) NULL 
COMMENT '冲突检测范围标识（用于动态唯一索引）';
```

### 索引结构

```sql
-- 动态冲突检测索引（唯一索引）
CREATE UNIQUE INDEX `idx_subprojectId_conflictScope_nodename` 
ON `mpprojecttreenodes` (`subProjectld`, `ConflictScope`, `NodeName`);

-- 普通索引，用于快速查询
CREATE INDEX `IX_MPProjectTreeNodes_InstanceId_subProjectld` 
ON `mpprojecttreenodes` (`InstanceId`, `subProjectld`);

-- 冲突查询索引
CREATE INDEX `idx_hasConflict` 
ON `mpprojecttreenodes` (`HasConflict`);
```

## 核心实现

### 1. 实体类更新

```csharp
public class MPProjectTreeNode
{
    // ... 现有字段 ...

    /// <summary>
    /// 原始节点名称（用于记录冲突前的名称）
    /// </summary>
    [Column("OriginalNodeName")]
    [StringLength(190)]
    public string OriginalNodeName { get; set; }

    /// <summary>
    /// 冲突标记（1=存在冲突，0=无冲突）
    /// </summary>
    [Column("HasConflict")]
    public int HasConflict { get; set; }
}
```

### 2. 动态设置ConflictScope

```csharp
private async Task<List<GrpcMPProjectTreeNode>> SetConflictScopeForTreeNodes(
    List<GrpcMPProjectTreeNode> addTreeDatas, Guid subProjectGuid)
{
    // 获取节点名称检查配置
    var nodeNameCheckConfigs = await GetNodeNameCheckConfigs();
    
    // 按节点类型分组处理
    var nodesByType = addTreeDatas.GroupBy(n => n.NodeType).ToList();

    foreach (var typeGroup in nodesByType)
    {
        var nodeType = typeGroup.Key;
        var nodesInType = typeGroup.ToList();
        
        // 获取该节点类型的检查配置
        var checkType = GetCheckTypeForNodeType(nodeType, nodeNameCheckConfigs);
        
        foreach (var node in nodesInType)
        {
            // 根据配置设置ConflictScope
            string conflictScope;
            if (checkType == 0) // 全局检测
            {
                conflictScope = "GLOBAL";
            }
            else // 同级检测
            {
                conflictScope = node.ParentNodeId.ToString();
            }
            
            // 设置ConflictScope字段
            node.ConflictScope = conflictScope;
        }
    }
}
```

### 3. 数据库批量操作

```csharp
public void SaveMPProjectTreeNodes(DbContext context, List<MPProjectTreeNode> recordDatas)
{
    const string command = @"' into table mpprojecttreenodes fields terminated by '^^' lines terminated by '\n' " +
        "(NodeId, InstanceId, TreeId, ParentNodeId, NodeType, NodeName, bPDataKey, modelnfoKey, subProjectld, level, indexCode, OriginalNodeName, HasConflict, ConflictScope) " +
        "ON DUPLICATE KEY UPDATE " +
        "OriginalNodeName = CASE WHEN OriginalNodeName IS NULL THEN VALUES(NodeName) ELSE OriginalNodeName END, " +
        "HasConflict = 1, " +
        "NodeName = CONCAT(VALUES(NodeName), '_', (SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(NodeName, '_', -1) AS UNSIGNED)), 0) + 1 FROM mpprojecttreenodes WHERE subProjectld = VALUES(subProjectld) AND ConflictScope = VALUES(ConflictScope) AND NodeName LIKE CONCAT(VALUES(NodeName), '_%'))), " +
        "TreeId = VALUES(TreeId), " +
        "NodeType = VALUES(NodeType), " +
        "bPDataKey = VALUES(bPDataKey), " +
        "modelnfoKey = VALUES(modelnfoKey), " +
        "level = VALUES(level), " +
        "indexCode = VALUES(indexCode) ";
}
```

### 3. 冲突信息查询

```csharp
private async Task<List<GrcpConflictNodeList>> GetConflictNodesAfterSave(
    IMainProjectRepository mainProjectManager, 
    List<GrpcMPProjectTreeNode> addTreeDatas, 
    Guid subProjectGuid)
{
    var conflictNodeList = new List<GrcpConflictNodeList>();
    
    try
    {
        // 查询保存后有冲突标记的节点
        var conflictNodes = mainProjectManager.MPProjectTreeNodes
            .Where(n => n.subProjectld == subProjectGuid && n.HasConflict == 1)
            .ToList();

        foreach (var conflictNode in conflictNodes)
        {
            // 根据节点类型确定冲突检查类型
            var checkType = GetCheckTypeForNodeType(conflictNode.NodeType, nodeNameCheckConfigs);
            
            var conflictRecord = new GrcpConflictNodeList
            {
                CheckType = checkType, // 根据配置确定检查类型
                NodeId = conflictNode.NodeId,
                OldNodeName = conflictNode.OriginalNodeName ?? conflictNode.NodeName,
                NewNodeName = conflictNode.NodeName,
                ParentNodeId = checkType == 1 ? conflictNode.ParentNodeId : 0, // 全局检查时父节点ID为空
                ConflitNodeIdList = { conflictNode.NodeId }
            };

            conflictNodeList.Add(conflictRecord);
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "查询保存后的冲突节点信息时发生异常");
    }

    return conflictNodeList;
}
```

## 工作流程

### 1. 数据保存流程
```
1. 接收上传的树节点数据
2. 根据配置设置ConflictScope字段
3. 调用数据库批量保存
4. 数据库根据ConflictScope进行动态冲突检测
5. 保存后查询冲突信息
6. 返回冲突报告给客户端
```

### 2. 冲突处理流程
```
1. 应用层根据节点类型查询 NodeNameCheckConfig 配置
2. 根据 CheckType 设置ConflictScope：
   - CheckType = 0：ConflictScope = "GLOBAL"（全局检测）
   - CheckType = 1：ConflictScope = ParentNodeId.ToString()（同级检测）
3. 执行数据库批量保存
4. 数据库根据唯一索引 {subProjectld, ConflictScope, NodeName} 检测冲突
5. 记录原始节点名称到 OriginalNodeName
6. 设置 HasConflict = 1
7. 自动生成新的节点名称（添加数字后缀）
```

### 3. 配置使用示例

```sql
-- 配置管道节点类型使用全局冲突检测
INSERT INTO nodenamecheckconfigs (NodeTypeName, NodeTypeNum, CheckType) 
VALUES ('管道', 1, 0);

-- 配置阀门节点类型使用同级节点冲突检测
INSERT INTO nodenamecheckconfigs (NodeTypeName, NodeTypeNum, CheckType) 
VALUES ('阀门', 2, 1);

-- 配置设备节点类型使用同级节点冲突检测（默认）
INSERT INTO nodenamecheckconfigs (NodeTypeName, NodeTypeNum, CheckType) 
VALUES ('设备', 3, 1);
```

## 性能优势

### 1. 无前置检测开销
- 移除所有应用层的冲突检查逻辑
- 避免双重循环和复杂查询
- 减少内存使用和CPU计算

### 2. 数据库层面处理
- 利用数据库的索引和约束机制
- 减少应用层和数据库之间的网络往返
- 提高并发处理能力

### 3. 批量操作优化
- 支持大量数据的批量插入
- 数据库层面的原子性操作
- 减少事务开销

## 数据库升级

### 升级脚本
创建了 `MainProjectDbUpgrade_v8_to_v9.cs` 升级脚本：
- 添加 `OriginalNodeName` 字段
- 添加 `HasConflict` 字段
- 添加相关索引
- 支持回滚操作

### 升级步骤
```bash
# 执行数据库升级
# 系统会自动执行 v8 -> v9 升级脚本
# 添加冲突记录字段和索引
```

## 测试建议

### 1. 功能测试
- 测试节点名称冲突的自动检测
- 验证原始名称和新名称的记录
- 检查冲突标记的正确性

### 2. 性能测试
- 测试大量数据时的冲突处理性能
- 对比优化前后的响应时间
- 监控数据库层面的处理效率

### 3. 并发测试
- 测试多用户同时上传时的冲突处理
- 验证数据库锁的正确性
- 检查数据一致性

## 注意事项

1. **数据库升级**：需要执行 v8->v9 升级脚本来添加新字段
2. **向后兼容**：保持了原有的 API 接口不变
3. **错误处理**：添加了完善的异常处理和日志记录
4. **监控建议**：建议监控冲突检测的频率和处理时间

## 总结

这个实现方案完全满足了用户的需求：
- ✅ 无前置判断重名
- ✅ 在 SaveMPProjectTreeNodes 方法中处理冲突
- ✅ 使用 ON DUPLICATE KEY UPDATE 方式
- ✅ 记录冲突信息（OriginalNodeName 字段）
- ✅ 记录修改后的节点名（NodeName 字段）
- ✅ 支持大数据量的高效处理
- ✅ 支持配置化的冲突检测范围（全局/同级节点）
- ✅ 根据 NodeNameCheckConfig 配置决定冲突检测策略

通过数据库层面的直接处理，我们实现了高效、可靠、灵活的节点名称冲突处理机制，支持不同节点类型使用不同的冲突检测策略。 