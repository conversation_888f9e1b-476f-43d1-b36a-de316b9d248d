﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class MainProjectDbContextFactory : IDesignTimeDbContextFactory<MainProjectDbContext>
    {
        public MainProjectDbContext CreateDbContext(string[] args)
        {
            var connectionString = args[0];
            var optionsBuilder = new DbContextOptionsBuilder<MainProjectDbContext>()
                .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

            return new MainProjectDbContext(optionsBuilder.Options);
        }
    }
}
