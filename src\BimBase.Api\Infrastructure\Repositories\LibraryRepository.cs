﻿using BimBase.Api.Infrastructure.DbInitializer;
using BimBase.Api.Infrastructure.Grpc;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.MainDomain;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace BimBase.Api.Infrastructure.Repositories
{
    public class LibraryRepository : ILibraryRepository
    {
        private readonly LibraryDbContext _libDbContext;
        private volatile bool disposedValue;
        public LibraryRepository(LibraryDbContext libDbContext)
        {
            _libDbContext = libDbContext ?? throw new ArgumentNullException(nameof(libDbContext));
            //LibraryDbInitializer.Initialize(_libDbContext);
        }
        public IQueryable<CLCatalogTreeNode> CLCatalogTreeNodes
        {
            get
            {
                return _libDbContext.CLCatalogTreeNodes.AsNoTracking();
            }
        }
        //public IQueryable<CLCatalogTreeNodeHistory> CLCatalogTreeNodeHistories
        //{
        //    get
        //    {
        //        return _libDbContext.CLCatalogTreeNodeHistories.AsNoTracking();
        //    }
        //}
        public bool RemoveCatalogTreeNodesByLibIds(List<Guid> delLibGuidList)
        {
            var del = _libDbContext.CLCatalogTreeNodes.Where(c => delLibGuidList.Contains(c.LibId)).ToList();
            if (del.Any())
            {
                _libDbContext.CLCatalogTreeNodes.RemoveRange(del);
                _libDbContext.SaveChanges();
            }
            return true;
        }
        public bool RemoveCLLibraryDatasByLibIds(List<Guid> delLibGuidList)
        {
            var del = _libDbContext.CLLibraryDatas.Where(c => delLibGuidList.Contains(c.LibId)).ToList();
            if (del.Any())
            {
                _libDbContext.CLLibraryDatas.RemoveRange(del);
                _libDbContext.SaveChanges();
            }
            return true;
        }

        public bool RemoveCLMainProjectLibsInfo(Guid mainprojectGuid)
        {
            var del = _libDbContext.CLMainprojectLibs.Where(c => c.MainProjectGuid == mainprojectGuid).ToList();
            if (del.Any())
            {
                _libDbContext.CLMainprojectLibs.RemoveRange(del);
                _libDbContext.SaveChanges();
            }
            return true;
        }

        public IQueryable<CLTreeNodeLock> CLTreeNodesLocks
        {
            get
            {
                return _libDbContext.CLTreeNodeLocks.AsNoTracking();
            }
        }
        public IQueryable<CLTreeNodeVersion> CLTreeNodesVersions
        {
            get
            {
                return _libDbContext.CLTreeNodeVersions.AsNoTracking();
            }
        }


        public bool CheckInCLTreeNode(List<CLCatalogTreeNode> addDatas,
            List<CLCatalogTreeNode> modifyDatas,
            List<CLCatalogTreeNode> delDatas, Guid libGuid, int version)
        {

            ILibraryBulkOperation bulkOperation = new LibraryBulkOperation(); 

            if (bulkOperation != null)
            {
                bulkOperation.SaveTreeNodes(_libDbContext, addDatas, version);
                bulkOperation.ModifyTreeNodes(_libDbContext, modifyDatas, version);
                bulkOperation.DeleteTreeNodes(_libDbContext, delDatas, libGuid);
                //bulkOperation.SaveHistoryTreeNodes(_libDbContext, addDatas, modifyDatas, delDatas, version);


                //PbimLog //PbimLog = new //PbimLog("Info");
                //PbimLog.Info(" ==> CheckInWithDatas  diskData.Version==> end");
                return true;
            }

            return false;
        }
        public bool CheckInCLLibData(List<CLLibraryData> addDatas,
            List<CLLibraryData> modifyDatas,
            List<CLLibraryData> delDatas,
            Guid libGuid, int version)
        {
            ILibraryBulkOperation bulkOperation = new LibraryBulkOperation();


            if (bulkOperation != null)
            {
                bulkOperation.SaveLibDatas(_libDbContext, addDatas, version);
                bulkOperation.ModifyLibDatas(_libDbContext, modifyDatas, version);
                bulkOperation.DeleteLibDatas(_libDbContext, delDatas, libGuid);
                bulkOperation.SaveHistoryLibData(_libDbContext, addDatas, modifyDatas, delDatas, version);

                //PbimLog //PbimLog = new //PbimLog("Info");
                //PbimLog.Info(" ==> CheckInCLLibData  diskData.Version==> end");
                return true;
            }

            return false;
        }
        /// <summary>
        /// 批量锁定
        /// </summary>
        /// <param name="memberGuid"></param>
        /// <param name="locklibList"></param>
        /// <returns></returns>
        public bool LockCLLibTreeNode(List<CLTreeNodeLock> locklibList)
        {
            var sw = new Stopwatch();
            sw.Start();
            if (locklibList == null || !locklibList.Any())
                return false;

            ILibraryBulkOperation bulkOperation = new LibraryBulkOperation(); 

            if (bulkOperation != null)
            {
                bulkOperation.saveLockLibDatas(_libDbContext, locklibList);

                return true;
            }

            return false;
            
        }
        public bool UnlockCLLibTreeNodeByUser(string memberName)
        {
            var sw = new Stopwatch();
            sw.Start();
            //优化第一次创建联机项目，无需走下面的代码
            var isExist = _libDbContext.CLTreeNodeLocks.AsNoTracking().Any();
            if (!isExist) return true;

            var sb = new StringBuilder();

            sb.Append("DELETE FROM `cltreenodelocks` WHERE LockUserName = {0} ");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _libDbContext.Database.BeginTransaction())
            {
                try
                {
                    _libDbContext.Database.ExecuteSqlRaw(deleteStr, memberName);
                    dbContextTransaction.Commit();
                    sw.Stop();
                    //log.InfoFormat("UnlockCLLibTreeNodeByUser==>耗时{0}毫秒。", sw.ElapsedMilliseconds);
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    //log.Error("UnlockMPLib==>" + ex.Message);
                    throw ex;
                }
            }
        }
        /// <summary>
        /// 批量解锁
        /// </summary>
        /// <returns></returns>
        public bool UnlockCLLibTreeNode(string memberName, List<long> unlockLibList, Guid libGuid)
        {
            var sw = new Stopwatch();
            sw.Start();
            //没有数据会造成后面的sql语句异常
            if (unlockLibList.Count <= 0) return true;
            HashSet<long> dataIdSet = new HashSet<long>(unlockLibList);
            //优化第一次创建联机项目，无需走下面的代码
            var isExist = _libDbContext.CLTreeNodeLocks.AsNoTracking().Any();
            if (!isExist) return true;

            var sb = new StringBuilder();
            var values = string.Join(",", dataIdSet);

            sb.Append("DELETE FROM `cltreenodelocks` WHERE LockUserName = {0} AND libid = '" + libGuid + "' AND NodeId in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _libDbContext.Database.BeginTransaction())
            {
                try
                {
                    _libDbContext.Database.ExecuteSqlRaw(deleteStr, memberName);
                    dbContextTransaction.Commit();
                    sw.Stop();
                    //log.InfoFormat("UnlockMPLib==>删除{0}条数据，耗时{1}毫秒。", dataIdSet.Count, sw.ElapsedMilliseconds);
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    //log.Error("UnlockMPLib==>" + ex.Message);
                    throw ex;
                }
            }
        }

        public int SaveLibVersion(CLTreeNodeVersion versionData)
        {
            if (null == versionData)
                return -1;

            _libDbContext.CLTreeNodeVersions.Add(versionData);
            _libDbContext.SaveChanges();

            var addedversion =
                _libDbContext.ChangeTracker.Entries<CLTreeNodeVersion>()
                    .FirstOrDefault(entry => entry.State == EntityState.Unchanged);

            //if (addedversion != null) return addedversion.Entity.VersionNo;
            return versionData.VersionNo;
        }



        public IQueryable<CLLibraryData> CLLibraryDatas
        {
            get
            {

                return _libDbContext.CLLibraryDatas.AsNoTracking();
            }
        }
        public IQueryable<CLLibraryDataHistory> CLLibraryDataHistories
        {
            get
            {
                return _libDbContext.CLLibraryDataHistories.AsNoTracking();
            }
        }

        public bool UpdateLibDataFileServerPath(long dataId, string serverPath, string fileMD5)
        {
            var old = _libDbContext.CLLibraryDatas.FirstOrDefault(e => e.DataId == dataId);
            if (old != null)
            {
                old.ServerFilePath = serverPath;
                if (!string.IsNullOrEmpty(fileMD5))
                {
                    old.FileMD5 = fileMD5;
                }
                _libDbContext.CLLibraryDatas.Attach(old);
                _libDbContext.Entry(old).Property(a => a.ServerFilePath).IsModified = true;
                if (!string.IsNullOrEmpty(fileMD5))
                {
                    _libDbContext.Entry(old).Property(a => a.FileMD5).IsModified = true;
                }

                _libDbContext.SaveChanges();
                return true;
            }
            return false;
        }

        public IQueryable<CLCompanyLibInfo> CLCompanyLibInfos
        {
            get
            {
                return _libDbContext.CLCompanyLibInfos.AsNoTracking();
            }
        }

        public IQueryable<CLCompanyLibInfo> InitAllCLCompanyLibInfo()
        {
            var clientVersion = GrpcContextAccessor.GetClientVersion() ?? "";
            var clientId = GrpcContextAccessor.GetClientId() ?? "";
            var companylibs = _libDbContext.CLCompanyLibInfos;
            if (!companylibs.Any())
            {
                CLCompanyLibInfo cataLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "元件库",
                    LibDescription = "企业级元件库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enCatalog_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(cataLib);
                CLCompanyLibInfo paraLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "参数化组件库",
                    LibDescription = "企业级参数化组件库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enPara_Comp_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(paraLib);
                CLCompanyLibInfo reportLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "图纸和报表库",
                    LibDescription = "企业级图纸和报表库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enDraw_Report_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(reportLib);
                CLCompanyLibInfo configLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "设计配置库",
                    LibDescription = "企业级设计配置库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enDesign_Config_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(configLib);
                CLCompanyLibInfo pubResLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "公共资源库",
                    LibDescription = "企业级公共资源库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enPublic_Resource_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(pubResLib);
                CLCompanyLibInfo typeAttrLib = new CLCompanyLibInfo
                {
                    LibId = Guid.NewGuid(),
                    LibName = "类型及属性库",
                    LibDescription = "企业级类型及属性库",
                    CreateUser = "admin",
                    CreateTime = DateTime.Now,
                    LibType = LibType.enType_Attribute_Lib,
                    ExtendStr = "",
                    clientId = clientId,
                    ClientVersion = clientVersion
                };
                _libDbContext.CLCompanyLibInfos.Add(typeAttrLib);
            }
            else
            {
                var cataLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enCatalog_Lib);
                if (cataLib == null)
                {
                    cataLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "元件库",
                        LibDescription = "企业级元件库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enCatalog_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(cataLib);
                }
                var paraLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enPara_Comp_Lib);
                if (paraLib == null)
                {
                    paraLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "参数化组件库",
                        LibDescription = "企业级参数化组件库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enPara_Comp_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(paraLib);
                }
                var reportLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enDraw_Report_Lib);
                if (reportLib == null)
                {
                    reportLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "图纸和报表库",
                        LibDescription = "企业级图纸和报表库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enDraw_Report_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(reportLib);
                }
                var configLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enDesign_Config_Lib);
                if (configLib == null)
                {
                    configLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "设计配置库",
                        LibDescription = "企业级设计配置库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enDesign_Config_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(configLib);
                }
                var pubResLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enPublic_Resource_Lib);
                if (pubResLib == null)
                {
                    pubResLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "公共资源库",
                        LibDescription = "企业级公共资源库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enPublic_Resource_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(pubResLib);
                }
                var typeAttrLib = companylibs.FirstOrDefault(l => l.LibType == LibType.enType_Attribute_Lib);
                if (typeAttrLib == null)
                {
                    typeAttrLib = new CLCompanyLibInfo
                    {
                        LibId = Guid.NewGuid(),
                        LibName = "类型及属性库",
                        LibDescription = "企业级类型及属性库",
                        CreateUser = "admin",
                        CreateTime = DateTime.Now,
                        LibType = LibType.enType_Attribute_Lib,
                        ExtendStr = ""
                    };
                    _libDbContext.CLCompanyLibInfos.Add(typeAttrLib);
                }

            }


            _libDbContext.SaveChanges();
            return _libDbContext.CLCompanyLibInfos.AsNoTracking();
        }

        public CLCompanyLibInfo AddCLCompanyLibInfo(CLCompanyLibInfo mpLibraryInfo)
        {
            var select = _libDbContext.CLCompanyLibInfos.Where(mp => mp.LibId == mpLibraryInfo.LibId);
            if (select.Any())
            {
                throw new Exception("服务器已存在库ID\n请尝试使用其它库ID！");
            }
            select = _libDbContext.CLCompanyLibInfos.Where(tp => tp.LibType == mpLibraryInfo.LibType);
            if (select.Any())
            {
                throw new Exception("相同类型的库只能有一个\n");
            }
            mpLibraryInfo.CreateTime = DateTime.Now;
            mpLibraryInfo = _libDbContext.CLCompanyLibInfos.Add(mpLibraryInfo).Entity;
            _libDbContext.SaveChanges();
            return mpLibraryInfo;
        }

        public IQueryable<CLMainprojectLib> CLMainprojectLibs
        {
            get
            {
                return _libDbContext.CLMainprojectLibs.AsNoTracking();
            }
        }

        public bool DeleteMainprojectLib(Guid mainprojectGuid, Guid libGuid, List<long> treeIdList)
        {
            var oldTreeList = _libDbContext.CLMainprojectLibs.Where(c => c.MainProjectGuid == mainprojectGuid && c.LibGuid == libGuid).ToList();

            List<CLMainprojectLib> cLMainprojectLibs = new List<CLMainprojectLib>();
            foreach (var tree in treeIdList)
            {
                var exsitOld = oldTreeList.FirstOrDefault(o => o.TreeId == tree);
                if (exsitOld != null)
                {
                    cLMainprojectLibs.Add(exsitOld);
                }
            }
            _libDbContext.CLMainprojectLibs.RemoveRange(cLMainprojectLibs);
            _libDbContext.SaveChanges();
            return true;
        }

        public bool InitMainprojectLib(Guid mainprojectGuid, Guid libGuid, int libType)
        {
            var oldTreeList = _libDbContext.CLMainprojectLibs.Where(c => c.MainProjectGuid == mainprojectGuid && c.LibGuid == libGuid).ToList();
            if (!oldTreeList.Any())
            {
                CLMainprojectLib cLMainprojectLib = new CLMainprojectLib
                {
                    MainProjectGuid = mainprojectGuid,
                    LibGuid = libGuid,
                    TreeId = -1,
                    TreeType = libType
                };
                _libDbContext.CLMainprojectLibs.Add(cLMainprojectLib);
            }
            _libDbContext.SaveChanges();
            return true;
        }

        public bool AddCLMainprojectLib(Guid mainprojectGuid, Guid libGuid, List<MPCatalogTreeNode> treeIdList)
        {
            var oldTreeList = _libDbContext.CLMainprojectLibs.Where(c => c.MainProjectGuid == mainprojectGuid && c.LibGuid == libGuid).ToList();
            var notinittree = oldTreeList.Where(c => c.TreeId == -1).FirstOrDefault();
            if (notinittree != null)
            {
                _libDbContext.CLMainprojectLibs.Remove(notinittree);
            }
            List<CLMainprojectLib> cLMainprojectLibs = new List<CLMainprojectLib>();
            foreach (var tree in treeIdList)
            {
                var exsitOld = oldTreeList.FirstOrDefault(o => o.TreeId == tree.TreeId);
                if (exsitOld == null)
                {
                    CLMainprojectLib cLMainprojectLib = new CLMainprojectLib
                    {
                        MainProjectGuid = mainprojectGuid,
                        LibGuid = libGuid,
                        TreeId = tree.TreeId,
                        TreeType = (int)tree.TreeType
                    };
                    cLMainprojectLibs.Add(cLMainprojectLib);
                }
            }
            _libDbContext.CLMainprojectLibs.AddRange(cLMainprojectLibs);
            _libDbContext.SaveChanges();
            return true;
        }

        public bool AddCLMainprojectLibList(List<CLMainprojectLib> cLMainprojectLibs)
        {
            _libDbContext.CLMainprojectLibs.AddRange(cLMainprojectLibs);
            _libDbContext.SaveChanges();
            return true;
        }





        public bool Destroy()
        {
            return _libDbContext.Database.EnsureDeleted();
        }

        public bool Insert<T>(T t) where T : class
        {
            _libDbContext.Set<T>().Add(t);
            return _libDbContext.SaveChanges() > 0;
        }

        public bool Insert<T>(IEnumerable<T> tList) where T : class
        {
            _libDbContext.Set<T>().AddRange(tList);
            return _libDbContext.SaveChanges() > 0;
        }

        public void Update<T>(T t) where T : class
        {
            _libDbContext.Set<T>().Update(t);
            _libDbContext.SaveChanges();
        }

        public void Update<T>(IEnumerable<T> tList) where T : class
        {
            _libDbContext.Set<T>().UpdateRange(tList);
            _libDbContext.SaveChanges();
        }

        public void Delete<T>(T t) where T : class
        {
            _libDbContext.Set<T>().Remove(t);
            _libDbContext.SaveChanges();
        }

        public void Delete<T>(IEnumerable<T> tList) where T : class
        {
            _libDbContext.Set<T>().RemoveRange(tList);
            _libDbContext.SaveChanges();
        }

        public IQueryable<TResult> QueryFrom<TSource, TResult>(Expression<Func<TSource, bool>> predicate, Expression<Func<TSource, TResult>> projection) where TSource : class
        {
            var x = _libDbContext.Set<TSource>().Where(predicate).Select(projection);
            return x;
        }

        public IQueryable<TSource> QueryFrom<TSource>(Expression<Func<TSource, bool>> predicate = null) where TSource : class
        {
            if (predicate is null) return _libDbContext.Set<TSource>();
            var x = _libDbContext.Set<TSource>().Where(predicate);
            return x;
        }
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    (_libDbContext as IDisposable)?.Dispose();
                }

                disposedValue = true;
            }
        }
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }


        public bool WriteDataToLocalFile(long requestId,
            List<CLCatalogTreeNode> addtreeDatas,
            List<CLCatalogTreeNode> modifytreeDatas,
            List<CLCatalogTreeNode> deltreeDatas,
            List<CLLibraryData> addLibDatas,
            List<CLLibraryData> modifyLibDatas,
            List<CLLibraryData> delLibDatas,
            Guid libGuid,
            int verNo)
        {
            ILibraryBulkOperation bulkOperation = new LibraryBulkOperation();
            if (bulkOperation != null)
            {
                int versionNo = verNo;//CurrentVersionNo;
                if (versionNo > -1)
                {
                    bulkOperation.SaveAddCataLogTreeNodeToFile(addtreeDatas, requestId, verNo);
                    bulkOperation.SaveModifyCataLogTreeNodeToFile(_libDbContext, modifytreeDatas, requestId, versionNo);
                    bulkOperation.SaveDeleteCataLogTreeNodeIdToFile(deltreeDatas, requestId);


                    bulkOperation.SaveAddCLLibDataToFile(addLibDatas, requestId, versionNo);
                    bulkOperation.SaveModifyCLLibDataToFile(_libDbContext, modifyLibDatas, requestId, versionNo);
                    bulkOperation.SaveDeleteCLLibDataIdToFile(delLibDatas, requestId);
                    bulkOperation.SaveCLLibDataHistoryToFile(_libDbContext, addLibDatas, modifyLibDatas, delLibDatas, versionNo, requestId);
                    return true;
                }
            }

            return false;
        }

        public bool CheckInCLLibDataToDBFromLocalFile(long uploadRequestId)
        {
            ILibraryBulkOperation bulkOperation = new LibraryBulkOperation();
            if (bulkOperation != null)
            {
                bulkOperation.SaveCLCataLogTreeNodeToDB(_libDbContext, uploadRequestId);
                bulkOperation.SaveCLLibDataToDB(_libDbContext, uploadRequestId);
                bulkOperation.SaveCLLibDataHistoryToDB(_libDbContext, uploadRequestId);

                return true;
            }

            return false;
        }
    }
}
