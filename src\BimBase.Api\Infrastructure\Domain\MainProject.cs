﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    public class MainProject
    {

        public MainProject()
        {
            if (ID ==Guid.Empty)
            {
                ID = Guid.NewGuid();
            }
            CreationTime = DateTime.Now;
            LastUpdateTime = DateTime.Now;
        }
        /// <summary>
        /// 项目的唯一标识
        /// </summary>
        /// <remarks>以此GUID确认本地的项目数据是否从属于服务器上的特定项目。</remarks>
        [Key]
        public Guid ID { get; set; }
        /// <summary>
        /// 用于让用户识别项目的名字
        /// </summary>
        public string Name
        {
            get;
            set;
        }

        /// <summary>
        /// 项目的详细描述信息
        /// </summary>
        public String Description
        {
            get;
            set;
        }

        public DateTime CreationTime
        {
            get;
            set;
        }

        public string CreateUser { get; set; }

        public int RoleGroupId { get; set; }

        public string RoleString { get; set; }

        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        public byte[] ExtendData { get; set; }
        public string ExtendStr { get; set; }
        public int MainProjectType { get; set; }

        public string ShortName { get; set; }

        public string DesignStage { get; set; }
        public string EngineerType { get; set; }

        public int IsDelete { get; set; }
        
        /// <summary>
        /// 客户端版本号
        /// </summary>
        public string ClientVersion { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string clientId { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        public string status { get; set; }
    }

    public class MainProjects : IComparable
    {
        public int CompareTo(object obj)
        {
            return Name.CompareTo((obj as MainProjects).Name);
        }

        [DataMember]
        public Guid ID { get; set; }
        /// <summary>
        /// 用于让用户识别项目的名字
        /// </summary>
        [DataMember]
        public string Name
        {
            get;
            set;
        }

        /// <summary>
        /// 项目的详细描述信息
        /// </summary>
        [DataMember]
        public String Description
        {
            get;
            set;
        }

        [DataMember]
        public DateTime CreationTime
        {
            get;
            set;
        }

        [DataMember]
        public string CreateUser { get; set; }

        [DataMember]
        public int RoleGroupId { get; set; }

        [DataMember]
        public string RoleString { get; set; }
        [DataMember]
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        [DataMember]
        public byte[] ExtendData { get; set; }
        [DataMember]
        public string ExtendStr { get; set; }

        [DataMember]
        public string ShortName { get; set; }
        [DataMember]
        public int MainProjectType { get; set; }

        [DataMember]
        public int IsDelete { get; set; }
        [DataMember]
        public string DesignStage { get; set; }
        [DataMember]
        public string EngineerType { get; set; }
        
        /// <summary>
        /// 客户端版本号
        /// </summary>
        [DataMember]
        public string ClientVersion { get; set; }
        
        /// <summary>
        /// 客户端ID
        /// </summary>
        [DataMember]
        public string clientId { get; set; }
    }

    /// <summary>
    /// 用户组与权限关联表
    /// </summary>
    public class TeamGroupAuth
    {
        
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// 用户组id或用户id
        /// </summary>
        
        public string GroupOrMemberId { get; set; }
        /// <summary>
        /// 标记是用户组还是用户 0：用户组 1：用户
        /// </summary>

        
        public int IsGroupOrTeamMember { get; set; }

        
        public Guid MainProjectId { get; set; }
        
        public string AuthInfo { get; set; }

        /// <summary>
        /// 权限对象类型 1：项目 2：文件夹 3：模型
        /// </summary>
        
        public int ObjectType { get; set; }
        
        public Guid ObjectId { get; set; }

    }

    /// <summary>
    /// 权限表
    /// </summary>
    public class TeamAuth
    {
        
        [Key]
        public int Id { get; set; }
        
        public string AuthDisplayName { get; set; }

        
        public string AuthName { get; set; }



        /// <summary>
        /// 权限类型
        /// 0：角色的权限 新建项目
        /// 1：权限对象仅为项目
        /// 2：权限对象仅为文件夹
        /// 3：权限对象为项目及文件夹
        /// 4：权限对象仅为模型
        /// </summary>
        
        public int AuthType { get; set; }

        
        public int Permission { get; set; }
    }
    /// <summary>
    /// 用户用户组关联表
    /// </summary>
    [XmlRoot(Namespace = "")]
    public class TeamUserGroup
    {
        
        [Key]
        public int Id { get; set; }

        
        public Guid TeamMemberId { get; set; }
        
        public int GroupId { get; set; }
    }




    public class MainProjectUserRole
    {
        public MainProjectUserRole()
        {
            ID = Guid.NewGuid();
        }
        [Key]
        
        public Guid ID { get; set; }
        /// <summary>
        /// 项目ID
        /// </summary>
        
        public Guid MainProjectID
        {
            get;
            set;
        }

        /// <summary>
        /// PDMS项目ID
        /// </summary>
        
        public String PDMSProjectID
        {
            get;
            set;
        }

        /// <summary>
        /// 团队成员ID
        /// </summary>
        
        public Guid TeamMemberID
        {
            get;
            set;
        }

        
        public string PDMSMemberID { get; set; }

        
        public int Domain { get; set; }
        
        public string ExtendStr { get; set; }
    }
}
