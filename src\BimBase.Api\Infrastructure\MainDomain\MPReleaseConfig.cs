﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPReleaseConfig
    {
        [Key]
        
        public int ID { get; set; }

        
        public Guid SourceSubProjectGuid { get; set; }

        
        public Guid TargetSubProjectGuid { get; set; }

        
        public long TargetTreeNodeId { get; set; }
    }
}
