using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 数据库升级服务的扩展方法
    /// </summary>
    public static class DatabaseUpgradeServiceExtensions
    {
        /// <summary>
        /// 添加数据库升级服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDatabaseUpgradeServices(this IServiceCollection services)
        {
            // 注册数据库升级工厂
            services.AddSingleton<DatabaseUpgradeFactory>();
            
            // 注册数据库升级服务，为每个DbContext类型注册对应的服务
            services.AddScoped<DatabaseUpgradeService>(provider =>
            {
                // 这里使用 TeamDbContext 作为示例
                // 实际应用中，可以根据需要选择不同的 DbContext
                var dbContext = provider.GetRequiredService<TeamDbContext>();
                var factory = provider.GetRequiredService<DatabaseUpgradeFactory>();
                var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<DatabaseUpgradeService>>();
                return new DatabaseUpgradeService(dbContext, factory, logger);
            });
            
            return services;
        }
    }
} 