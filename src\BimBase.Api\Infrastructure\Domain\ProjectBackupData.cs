﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class ProjectBackupData
    {
        [Key]
        public Guid ID { get; set; }

        
        public Guid ProjectID { get; set; }

        
        public string ProjectName { get; set; }

        
        public string BackupName { get; set; }

        
        public string BackupServerPath { get; set; }

        
        public string BackupVirtualPath { get; set; }

        
        public string BackupDescription { get; set; }

        
        public string BackupNotice { get; set; }
        
        public int BackupNoticeSentFlag { get; set; }

        
        public DateTime BackupNoticeTime { get; set; }

        
        public DateTime BackupBeginTime { get; set; }

        
        public DateTime BackupFinishTime { get; set; }

        
        public int BackupDuration { get; set; }

        
        public decimal BackupSize { get; set; }

        
        public string BackupState { get; set; }
        
        public int BackupStateFlag { get; set; }
        
        public string BackupStateHistory { get; set; }

        
        public DateTime BackupCreateTime { get; set; }

        
        public int DeleteFlag { get; set; }
    }
}
