using BimBase.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Crypto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class TeamDbContext:DbContext
    {
        #region 动态查询过滤器属性
        
        /// <summary>
        /// 当前请求的客户端ID（用于查询过滤器）
        /// </summary>
        public string CurrentClientId { get; set; } = "";
        
        /// <summary>
        /// 当前请求的客户端版本（用于查询过滤器）
        /// </summary>
        public string CurrentClientVersion { get; set; } = "";
        
        /// <summary>
        /// 当前客户端的最小兼容版本（预计算结果，用于查询过滤器）
        /// </summary>
        public string CurrentMinCompatibleVersion { get; set; } = "";
        
        /// <summary>
        /// 当前客户端是否有版本兼容性配置（预计算结果，用于查询过滤器）
        /// </summary>
        public bool HasVersionCompatibilityConfig { get; set; } = false;
        
        /// <summary>
        /// 初始化动态过滤器上下文
        /// </summary>
        public void InitializeFilterContext()
        {
            // 从gRPC上下文获取当前请求信息
            CurrentClientId = Infrastructure.Grpc.GrpcContextAccessor.GetClientId() ?? "";
            CurrentClientVersion = Infrastructure.Grpc.GrpcContextAccessor.GetClientVersion() ?? "";
            
            // 预计算版本兼容性信息
            InitializeVersionCompatibility();
        }
        
        /// <summary>
        /// 预计算版本兼容性信息（使用独立的DbContext避免递归）
        /// </summary>
        private void InitializeVersionCompatibility()
        {
            if (string.IsNullOrEmpty(CurrentClientId) || string.IsNullOrEmpty(CurrentClientVersion))
            {
                HasVersionCompatibilityConfig = false;
                CurrentMinCompatibleVersion = "";
                return;
            }
            
            try
            {
                // 使用独立的DbContext实例来查询版本兼容性，这个DbContext不会注册拦截器
                var optionsBuilder = new DbContextOptionsBuilder<TeamDbContext>();
                
                // 获取当前DbContext的连接字符串
                var connectionString = Database.GetConnectionString();
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
                // 注意：这里不添加拦截器，所以不会导致递归
                
                using (var separateContext = new TeamDbContext(optionsBuilder.Options))
                {
                    // 查询版本兼容性配置
                    var compatibilityEntry = separateContext.ClientVersionCompatibilities
                        .Where(c => c.ClientId == CurrentClientId && c.ClientVersion == CurrentClientVersion)
                        .FirstOrDefault();
                    
                    if (compatibilityEntry != null)
                    {
                        HasVersionCompatibilityConfig = true;
                        CurrentMinCompatibleVersion = compatibilityEntry.MinCompatibleVersion ?? "";
                    }
                    else
                    {
                        // 检查是否有该ClientId的任何配置
                        HasVersionCompatibilityConfig = separateContext.ClientVersionCompatibilities
                            .Any(c => c.ClientId == CurrentClientId);
                        CurrentMinCompatibleVersion = "";
                    }
                }
            }
            catch
            {
                // 如果查询失败（比如表不存在），则使用默认值
                HasVersionCompatibilityConfig = false;
                CurrentMinCompatibleVersion = "";
            }
        }
        
        #endregion

        #region 数据表

        // 数据库版本管理相关表
        public DbSet<DatabaseVersion> DatabaseVersions { get; set; }
        public DbSet<ClientModuleVersion> ClientModuleVersions { get; set; }
        public DbSet<ClientVersionCompatibility> ClientVersionCompatibilities { get; set; }

        public DbSet<MainProjectTeamGroup> MainProjectTeamGroups { get; set; }

        public DbSet<MainProject> MainProjects { get; set; }

        public DbSet<TeamProject> TeamProjects { get; set; }
        public DbSet<TeamMember> TeamMembers { get; set; }

        //Message相关数据表，目前放在Team数据库下
        //public DbSet<MessageData> Messages { get; set; }
        //public DbSet<MessageFilter> MessageFilters { get; set; }

        public DbSet<DbVersion> DbVersions { get; set; }

        //数据版本
        public DbSet<SchemaVersion> Versions { get; set; }

        //备份文件保存路径
        public DbSet<BackupPath> BackupPaths { get; set; }

        //项目备份文件
        public DbSet<ProjectBackupData> ProjectBackupDatas { get; set; }

        //数据库备份文件
        public DbSet<DBBackupData> DBBackupDatas { get; set; }


        public DbSet<LoginSession> LoginSession { get; set; }

        public DbSet<FileDirectory> FileDirectories { get; set; }

        public DbSet<commonfiles> commonfiles { get; set; }

        public DbSet<cloudlinkfiles> cloudlinkfiles { get; set; }

        public DbSet<Volume> Volumes { get; set; }

        public DbSet<IpMap> IpMaps { get; set; }

        public DbSet<VolumeVersion> VolumeVersions { get; set; }

        /// <summary>
        /// 记录文件协同模型锁定状态
        /// </summary>
        public DbSet<TeamProjectModelLock> TeamProjectModelLocks { get; set; }

        public DbSet<RepositoryInformation> RepositoryInformations { get; set; }

        public DbSet<MainProjectUserGroupMember> MainProjectUserGroupMembers { get; set; }

        public DbSet<MainProjectUserGroupAuth> MainProjectUserGroupAuths { get; set; }

        public DbSet<MainProjectUserGroupLibAuth> MainProjectUserGroupLibAuths { get; set; }

        public DbSet<CacheMainproject> CacheMainprojects { get; set; }

        public DbSet<TeamGroup> TeamGroups { get; set; }
        public DbSet<TeamGroupAuth> TeamGroupAuths { get; set; }

        public DbSet<MainProjectUserRole> MainProjectUserRoles { get; set; }
        public DbSet<TeamUserGroup> TeamUserGroups { get; set; }

        public DbSet<TeamAuth> TeamAuths { get; set; }
        
        /// <summary>
        /// 节点名称检查配置表
        /// </summary>
        public DbSet<NodeNameCheckConfig> NodeNameCheckConfigs { get; set; }
        #endregion

        public TeamDbContext(DbContextOptions<TeamDbContext> options) : base(options)
        {

        }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<SchemaVersion>().ToTable("schemaversions");
            modelBuilder.Entity<ObjectInfo>().ToTable("objectinfoes");
            modelBuilder.Entity<MessageFilter>()
                .HasMany<MessageEvent>(r => r.EventFilters)
                .WithOne()
                .OnDelete(DeleteBehavior.ClientCascade);

            // 配置 ClientModuleVersions 表索引
            modelBuilder.Entity<ClientModuleVersion>()
                .HasIndex(e => e.ClientId)
                .HasDatabaseName("IX_ClientModuleVersions_ClientId");
            
            modelBuilder.Entity<ClientModuleVersion>()
                .HasIndex(e => e.ModuleId)
                .HasDatabaseName("IX_ClientModuleVersions_ModuleId");
            
            modelBuilder.Entity<ClientModuleVersion>()
                .HasIndex(e => e.ProjectId)
                .HasDatabaseName("IX_ClientModuleVersions_ProjectId");

            // 配置 ClientVersionCompatibility 表索引
            modelBuilder.Entity<ClientVersionCompatibility>()
                .HasIndex(e => e.ClientId)
                .HasDatabaseName("IDX_CLIENT_ID");

            // 配置 TeamUserGroups 表 TeamMemberId 索引
            modelBuilder.Entity<TeamUserGroup>()
                .HasIndex(e => e.TeamMemberId)
                .HasDatabaseName("IX_TeamMemberId");

            // 配置 MainProjectUserGroupMembers 表 TeamMemberId 索引
            modelBuilder.Entity<MainProjectUserGroupMember>()
                .HasIndex(e => e.TeamMemberId)
                .HasDatabaseName("IX_TeamMemberId");

            // 配置 MainProjectTeamGroups 表索引（已有的索引配置）
            modelBuilder.Entity<MainProjectTeamGroup>()
                .HasIndex(e => e.MainProjectId)
                .HasDatabaseName("IX_MainProjectId");
                
            modelBuilder.Entity<MainProjectTeamGroup>()
                .HasIndex(e => e.MemberType)
                .HasDatabaseName("IX_MemberType");

            // 配置 MainProjectUserGroupMembers 表 MainProjectId 索引（已有的索引配置）
            modelBuilder.Entity<MainProjectUserGroupMember>()
                .HasIndex(e => e.MainProjectId)
                .HasDatabaseName("IX_MainProjectId");

            // 配置 NodeNameCheckConfig 表索引
            modelBuilder.Entity<NodeNameCheckConfig>()
                .HasIndex(e => e.NodeTypeName)
                .HasDatabaseName("IX_NodeNameCheckConfig_NodeTypeName");
            
            modelBuilder.Entity<NodeNameCheckConfig>()
                .HasIndex(e => e.NodeTypeNum)
                .HasDatabaseName("IX_NodeNameCheckConfig_NodeTypeNum");

            // 完整的MainProject查询过滤器 - 使用预计算结果实现完整的版本兼容性检查
            modelBuilder.Entity<MainProject>()
                .HasQueryFilter(m => 
                    // 1. clientId 过滤逻辑
                    (string.IsNullOrEmpty(CurrentClientId)
                        ? (string.IsNullOrEmpty(m.clientId) || m.clientId == "PKPM-Plant")  // 当前请求clientId为空时，只能获取数据库中clientId为空或PKPM-Plant的数据
                        : CurrentClientId == "PKPM-Plant"
                            ? (string.IsNullOrEmpty(m.clientId) || m.clientId == "PKPM-Plant")  // PKPM-Plant可以获取空或PKPM-Plant的数据
                            : m.clientId == CurrentClientId  // 其他情况只能获取相等的数据
                    ) &&
                    
                    // 2. ClientVersion 兼容性过滤逻辑 - 根据是否有兼容版本信息采用不同策略
                    (string.IsNullOrEmpty(CurrentClientVersion)
                        ? (string.IsNullOrEmpty(m.ClientVersion) || m.ClientVersion == "BIMBase-2025R01.01")  // 当前请求ClientVersion为空时，可以获取ClientVersion为空或为"10000000"的数据
                        : HasVersionCompatibilityConfig
                            ? (string.IsNullOrEmpty(m.ClientVersion) ||  // 有兼容版本信息：允许访问没有版本限制的数据
                               (!string.IsNullOrEmpty(CurrentMinCompatibleVersion) && 
                                string.Compare(m.ClientVersion, CurrentMinCompatibleVersion) >= 0 && 
                                string.Compare(CurrentClientVersion,m.ClientVersion)>=0)
                                )  // 项目版本 >= 最小兼容版本
                            : (string.IsNullOrEmpty(m.ClientVersion) ||  // 没有兼容版本信息：允许访问没有版本限制的数据
                               m.ClientVersion == CurrentClientVersion)  // 精确匹配当前客户端版本
                    ) &&
                    
                    // 3. status 过滤逻辑
                    m.status != "creating"  // 排除status为"creating"的数据
                );
        }
    }
} 