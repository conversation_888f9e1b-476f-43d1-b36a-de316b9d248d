{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "{$mulText:DefaultConnection:server=127.0.0.1; port=3306; database=openauth; user=root; password=******;} Persist Security Info=False; Connect Timeout=300"
    //"DefaultConnection": "server=***************; port=6330; database=openauth; user=root; password=******; Persist Security Info=False; Connect Timeout=300"
  },
  "Kestrel": {
    //"EndpointDefaults": {
    //  "Protocols": "Http2"
    //}
    "Endpoints": {
      "Http": {
        "Url": "{$singleText:Url:http://localhost:9002}",
        "Protocols": "Http1AndHttp2"
      },
      "Grpc": {
        "Url": "{$singleText:Url:http://localhost:9001}",
        "Protocols": "Http2"
      }
    }
  },
  "AllowedHosts": "*"
}
