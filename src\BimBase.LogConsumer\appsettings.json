{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Environment": "Development", "LogSettings": {"RabbitMQ": {"HostName": "***************", "UserName": "guest", "Password": "guest", "VirtualHost": "/", "Port": 5672, "ExchangeName": "cooperate-default", "InterfaceLogQueue": "cooperate.interface.base", "ErrorLogQueue": "cooperate.error", "InterfaceLogRoutingKey": "log.interface.#", "ErrorLogRoutingKey": "log.*.error"}}, "ConnectionStrings": {"LoggerConnection": "server=***************; port=3389; database=bimbase-logdb-mq; user=root; password=********; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=40;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/consumer-.txt", "rollingInterval": "Day", "fileSizeLimitBytes": ********, "rollOnFileSizeLimit": true, "retainedFileTimeLimit": "7.00:00:00", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "EnableDbSync": true}