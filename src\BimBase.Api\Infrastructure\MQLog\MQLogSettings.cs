namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志相关配置项，供日志字段自动补全和消息发送使用
    /// </summary>
    public class MQLogSettings
    {
        /// <summary>应用名称</summary>
        public string AppName { get; set; }
        /// <summary>应用版本</summary>
        public string AppVersion { get; set; }
        /// <summary>环境</summary>
        public string Environment { get; set; }
        /// <summary>服务器IP</summary>
        public string ServerIp { get; set; }
        /// <summary>服务器名称</summary>
        public string ServerName { get; set; }

        /// <summary>RabbitMQ配置</summary>
        public RabbitMQSettings RabbitMQ { get; set; }
    }

    /// <summary>
    /// RabbitMQ配置项
    /// </summary>
    public class RabbitMQSettings
    {
        /// <summary>主机名</summary>
        public string HostName { get; set; }
        
        /// <summary>端口</summary>
        public int Port { get; set; }
        
        /// <summary>用户名</summary>
        public string UserName { get; set; }
        
        /// <summary>密码</summary>
        public string Password { get; set; }
        
        /// <summary>虚拟主机</summary>
        public string VirtualHost { get; set; }
        
        /// <summary>交换机名称</summary>
        public string ExchangeName { get; set; }
        
        /// <summary>接口日志队列名称</summary>
        public string InterfaceLogQueue { get; set; }
        
        /// <summary>错误日志队列名称</summary>
        public string ErrorLogQueue { get; set; }
        
        /// <summary>接口日志路由键</summary>
        public string InterfaceLogRoutingKey { get; set; }
        
        /// <summary>错误日志路由键</summary>
        public string ErrorLogRoutingKey { get; set; }
    }
} 