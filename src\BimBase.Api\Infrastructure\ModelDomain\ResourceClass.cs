﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// 模型资源占用表
    /// </summary>
    public class ResourceClass
    {
        [Key]
        public Int64 Id { get; set; }

        /// <summary>
        /// 客户端Schema名称
        /// </summary>
        
        public string SchemaName { get; set; }

        /// <summary>
        /// 客户端Class编号
        /// </summary>
        
        //public int ClassId { get; set; }
        public string DomainClassName { get; set; }

        /// <summary>
        /// 构件Id
        /// </summary>
        
        public long InstanceId { get; set; }

        /// <summary>
        /// 资源名称
        /// </summary>
        
        public string ResourceName { get; set; }

    }
}
