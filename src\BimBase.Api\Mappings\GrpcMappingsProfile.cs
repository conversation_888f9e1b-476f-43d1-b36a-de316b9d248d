﻿using AutoMapper;
using AutoMapper.Extensions.EnumMapping;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Protos;
using BimBaseAuth.Api.Protos;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text;

namespace BimBase.Api.Mappings
{
    public class GrpcMappingsProfile: Profile
    {
        public GrpcMappingsProfile()
        {
            //ForAllPropertyMaps(
            //  map => map.DestinationType.IsGenericType && map.DestinationType.GetGenericTypeDefinition() == typeof(RepeatedField<>),
            //  (map, options) => options.UseDestinationValue());
            CreateMap<string, Guid>().ConvertUsing(s => !string.IsNullOrWhiteSpace(s)?  Guid.Parse(s) : Guid.Empty);
            CreateMap<Guid, string>().ConvertUsing(g => g.ToString());

            CreateMap<Timestamp, DateTime>().ConvertUsing(ts => ts.ToDateTime());
            CreateMap<DateTime, Timestamp>().ConvertUsing(dt => Timestamp.FromDateTime(dt.ToUniversalTime()));
            
            CreateMap<ByteString, byte[]>().ConvertUsing(b => b.ToByteArray());
            CreateMap<byte[], ByteString> ().ConvertUsing(b =>b!=null? ByteString.CopyFrom(b):ByteString.Empty);
            //CreateMap<string, string>().ConvertUsing(b => !string.IsNullOrWhiteSpace(b) ? Encoding.UTF8.GetString(Encoding.UTF8.GetBytes(b)) : b);
            

            CreateMap(typeof(List<>), typeof(RepeatedField<>)).ConvertUsing(typeof(EnumerableToRepeatedFieldTypeConverter<,>));
            CreateMap(typeof(RepeatedField<>), typeof(List<>)).ConvertUsing(typeof(RepeatedFieldToListTypeConverter<,>));


            CreateMap<AuthInfoDto, GrpcAuthInfo>()
                .ForMember(desc => desc.RelatedId, opt => opt.MapFrom(src => src.AuthId))
                .ReverseMap();

            CreateMap<TeamMember, GrpcTeamMember>()
                .ReverseMap();


            CreateMap<TeamProject, GrpcTeamProject>()
                .ReverseMap();
            CreateMap<TeamUserGroup, GrpcTeamUserGroup>()
                .ReverseMap();
            CreateMap<TeamGroup,GrpcTeamGroup>()
                .ReverseMap();
            CreateMap<VersionData, GrpcVersionData>()
                .ReverseMap();
            CreateMap<MPUserGroupAuth, GrpcMPUserGroupAuth>()
                .ReverseMap();
            CreateMap<Infrastructure.ModelDomain.SchemaVersion, GrpcSchemaVersion>()
                .ReverseMap();

            CreateMap<RoleDto, GrpcRole>()
                .ReverseMap();

            CreateMap<ModelData, GrpcModelData>()
                .ReverseMap();
            CreateMap<TeamAuth, GrpcTeamAuth>()
                .ReverseMap();

            CreateMap<Relationship, GrpcRelationship>()
                .ReverseMap();

            CreateMap<VersionInformation, GrpcVersionInformation>()
                .ReverseMap();

            CreateMap<LockedComponents, GrpcLockedComponents>()
                .ReverseMap();

            CreateMap<ResourceClass, GrpcResourceClass>()
                .ReverseMap();

            CreateMap<SingletonClass, GrpcSingletonClass>()
                .ReverseMap();
            CreateMap<MainProject,GrpcMainProject>()
                .ReverseMap();
            CreateMap<FileDirectory, GrpcFileDirectory>().ReverseMap();
            
            CreateMap<TeamGroupAuth, GrpcTeamGroupAuth>()
                .ReverseMap();

            CreateMap<RelationshipType, GrpcRelationshipType>()
                .ConvertUsingEnumMapping(opt => opt
                    .MapValue(RelationshipType.Assemble, GrpcRelationshipType.Assemble)
            )
            .ReverseMap();

            CreateMap<OperationRecordType, GrpcOperationRecordType>()
                .ConvertUsingEnumMapping(opt => opt
                    .MapValue(OperationRecordType.Add, GrpcOperationRecordType.Add)
                    .MapValue(OperationRecordType.Delete,GrpcOperationRecordType.Delete)
                    .MapValue(OperationRecordType.Modify, GrpcOperationRecordType.Modify)
                    .MapValue(OperationRecordType.Unchange, GrpcOperationRecordType.Unchange)
                )
                .ReverseMap();

            CreateMap<Infrastructure.ModelDomain.ReleaseType, GrpcReleaseType>()
                .ConvertUsingEnumMapping(opt => opt
                    .MapValue(Infrastructure.ModelDomain.ReleaseType.None, GrpcReleaseType.None)
                    .MapValue(Infrastructure.ModelDomain.ReleaseType.MileStone, GrpcReleaseType.MileStone)
            )
            .ReverseMap();
            CreateMap<string, string>().ConvertUsing(s => s == null ? "" : s);

            CreateMap<MPLibraryInfo, GrpcMPLibraryInfo>()
                .ReverseMap();

            CreateMap<ProvideVersionInfo, GrpcProvideVersionInfo>()
                .ReverseMap();
            CreateMap<BPExObjectPublish,GrpcBPExObjectPublish>()
                .ReverseMap();
            CreateMap<BPExObject, GrpcBPExObject>()
                .ReverseMap();
            CreateMap<MPUserGroup,GrpcMPUserGroup>()
                .ReverseMap();
            CreateMap<MPUserGroupMember, GrpcMPUserGroupMember>()
                .ReverseMap();  
            CreateMap<MPTeamProject,GrpcMPTeamProject>()
                .ReverseMap();
            CreateMap<MPAuthInfo,GrpcMPAuthInfo>()
                .ReverseMap();
            CreateMap<MPProjectTreeNode, GrpcMPProjectTreeNode>()
                .ReverseMap();
            CreateMap<MPLibraryData, GrpcMPLibraryData>()
                .ReverseMap();
            CreateMap<MPCloudLinkFile, GrpcMPCloudLinkFile>()
                .ReverseMap();
            CreateMap<MPReleaseConfig, GrpcMPReleaseConfig>()
                .ReverseMap();
            CreateMap<MPMessage, GrpcMPMessage>()
                .ReverseMap();
            CreateMap<MPDrawing, GrpcMPDrawing>()
                .ForMember(dest => dest.CreateTime, opt => opt.MapFrom(src =>
                    src.CreateTime == default(DateTime) ? null : Timestamp.FromDateTime(src.CreateTime)))
                .ForMember(dest => dest.UpdateTime, opt => opt.MapFrom(src =>
                    src.UpdateTime == default(DateTime) ? null : Timestamp.FromDateTime(src.UpdateTime)))
                .ReverseMap()
                .ForMember(dest => dest.CreateTime, opt => opt.MapFrom(src =>
                    src.CreateTime == null ? default(DateTime) : src.CreateTime.ToDateTime()))
                .ForMember(dest => dest.UpdateTime, opt => opt.MapFrom(src =>
                    src.UpdateTime == null ? default(DateTime) : src.UpdateTime.ToDateTime()));
            CreateMap<MPCatalogTreeNode, GrpcMPCatalogTreeNode>()
                .ReverseMap();
            CreateMap<MPLibraryDataHistory,GrpcMPLibraryDataHistory>()
                .ReverseMap();
            CreateMap<CLCatalogTreeNode, GrpcCLCatalogTreeNode>()
                .ReverseMap();
            CreateMap<CLLibraryData, GrpcCLLibraryData>()
                .ReverseMap();
            CreateMap<CLLibraryDataHistory, GrpcCLLibraryDataHistory>()
                .ReverseMap();
            CreateMap<CLCompanyLibInfo, GrpcCLCompanyLibInfo>()
                .ReverseMap();
            CreateMap<HistoryData, GrpcHistoryData>()
                .ReverseMap();
            CreateMap<HistoryRelationship, GrpcHistoryRelationship>()
                .ReverseMap();
            CreateMap<MPUserGroupLibAuth, GrpcMPUserGroupLibAuth>()
                .ReverseMap();
            CreateMap<Volume, GrpcVolume>()
                .ReverseMap();
            CreateMap<MainProjectUserGroupMember,GrpcMainProjectUserGroupMember>()
                .ReverseMap();
            
            CreateMap<GrpcNodeNameCheckConfig, NodeNameCheckConfig>()
                .ReverseMap();
        }
    }


    class EnumerableToRepeatedFieldTypeConverter<TITemSource, TITemDest> : ITypeConverter<IEnumerable<TITemSource>, RepeatedField<TITemDest>>
    {
        public RepeatedField<TITemDest> Convert(IEnumerable<TITemSource> source, RepeatedField<TITemDest> destination, ResolutionContext context)
        {
            destination = destination ?? new RepeatedField<TITemDest>();
            foreach (var item in source)
            {
                // obviously we haven't performed the mapping for the item yet
                // since AutoMapper didn't recognise the list conversion
                // so we need to map the item here and then add it to the new
                // collection
                destination.Add(context.Mapper.Map<TITemDest>(item));
            }
            return destination;
        }
    }

    class RepeatedFieldToListTypeConverter<TITemSource, TITemDest> : ITypeConverter<RepeatedField<TITemSource>, List<TITemDest>>
    {
        public List<TITemDest> Convert(RepeatedField<TITemSource> source, List<TITemDest> destination, ResolutionContext context)
        {
            destination = destination ?? new List<TITemDest>();
            foreach (var item in source)
            {
                destination.Add(context.Mapper.Map<TITemDest>(item));
            }
            return destination;
        }
    }


}
