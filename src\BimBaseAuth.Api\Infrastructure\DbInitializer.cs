﻿using BimBaseAuth.Api.Infrastructure.Common;
using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure
{
    public static class DbInitializer
    {
        public static void Initialize(OpenAuthDBContext context)
        {
			//延迟执行 等待数据库启动成功
            //var maxAttemps = 12;
            //var delay = 5000;

            //for (int i = 0; i < maxAttemps; i++)
            //{
            //    if (!context.Database.CanConnect())
            //    {
            //        return;
            //    }
            //    Task.Delay(delay);
            //}
            context.Database.EnsureCreated();

            if (context.UserInfos.Any())
            {
				return;   // DB has been seeded
			}

            UserInfo userInfo = new UserInfo();
            userInfo.Id = Guid.Parse("8508F17C-6788-480F-8B73-D06C3BB19845".ToLower());
            userInfo.Account = "System";
            userInfo.Name = "系统管理员";
            userInfo.Password = "System";
            UserInfo userInfo2 = userInfo;
            context.UserInfos.Add(userInfo2);
            context.SaveChanges();


			Role role = new Role();
			role.Id = Guid.NewGuid();
			role.Name = ObjectHelper.GetEnumDescription(RoleType.SysManager);
			role.Type = 0;
			role.CreateId = userInfo2.Id;
			context.Roles.Add(role);

			Role role2 = new Role();
			role2.Id = Guid.NewGuid();
			role2.Name = ObjectHelper.GetEnumDescription(RoleType.SysManager);
			role2.Type = 0;
			role2.CreateId = Guid.Parse("e68488c9-6058-4881-b075-16cb7cf4f77d".ToLower());
            role2.Status = 0;
			context.Roles.Add(role2);

            Role role3 = new Role();
            role3.Id = Guid.NewGuid();
            role3.Name = ObjectHelper.GetEnumDescription(RoleType.TeamManager);
            role3.Type = 1;
            role3.CreateId = Guid.Parse("e68488c9-6058-4881-b075-16cb7cf4f77d".ToLower());
            role3.Status = 0;
            context.Roles.Add(role3);

            context.SaveChanges();
			UserRole userRole = new UserRole();
			userRole.Id = Guid.NewGuid();
			userRole.RoleId = role.Id;
            userRole.UserId = userInfo2.Id;
            context.UserRoles.Add(userRole);

            UserRole userRole1 = new UserRole();
            userRole1.Id = Guid.NewGuid();
            userRole1.RoleId = role3.Id;
            userRole1.UserId = Guid.Parse("e79a548e-e31f-465c-a12a-************".ToLower());
            context.UserRoles.Add(userRole1);

            UserRole userRole2 = new UserRole();
            userRole2.Id = Guid.NewGuid();
            userRole2.RoleId = role3.Id;
            userRole2.UserId = Guid.Parse("1284529a-cc6e-43f0-b817-4b8ac862d926".ToLower());
            context.UserRoles.Add(userRole2);

            UserRole userRole3 = new UserRole();
            userRole3.Id = Guid.NewGuid();
            userRole3.RoleId = role2.Id;
            userRole3.UserId = Guid.Parse("e68488c9-6058-4881-b075-16cb7cf4f77d".ToLower());
            context.UserRoles.Add(userRole3);

            context.SaveChanges();
			List<AuthInfo> list = new List<AuthInfo>();
			AuthInfo authInfo = new AuthInfo();
			authInfo.IsGlobalAuth = true;
			authInfo.Code = "";
			authInfo.Name = "专业与构件分类";
			authInfo.ParentId = Guid.Empty;
			authInfo.RelatedId = Guid.Empty;
			list.Add(authInfo);
			list.Add(new AuthInfo
			{
				IsGlobalAuth = true,
				Code = "",
				Name = "建筑",
				ParentId = authInfo.Id,
				RelatedId = Guid.Empty
			});
			list.Add(new AuthInfo
			{
				IsGlobalAuth = true,
				Code = "",
				Name = "墙",
				ParentId = authInfo.Id,
				RelatedId = Guid.Empty
			});
			context.AuthInfos.AddRange(list);
			context.SaveChanges();

		}
    }
}
