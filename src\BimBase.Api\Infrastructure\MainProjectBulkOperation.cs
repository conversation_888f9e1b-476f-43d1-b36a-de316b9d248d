﻿using BimBase.Api.Infrastructure.MainDomain;
using BimBase.Api.Infrastructure.DbInitializer;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System;
using System.Runtime.InteropServices;
using BimBase.Api.Infrastructure.ModelDomain;
using System.Data;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using MySqlConnector;
using BimBase.Api.Config;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;

namespace BimBase.Api.Infrastructure
{
    public class MainProjectBulkOperation : IMainProjectBulkOperation
    {
        const int DatabaseExecTimeout = 60000;
        const int RecordCountLimit = 10000;
        private string _foldPath;
        public const int CHUNK_STRING_LENGTH = 30000;
        private readonly char separator = Path.DirectorySeparatorChar;
        private readonly UrlsConfig _urls;

        public MainProjectBulkOperation(IOptions<UrlsConfig> config)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
            //_foldPath += "c:/temp/PBIMS/LoadinFileDir/";
            string temppath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                temppath = "C:/ProgramData/MySQL/MySQL Server 8.0/Uploads";
                temppath = temppath.Replace(@"\", @"/");
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                temppath = _urls.LoadDataSavePath;
            }

            //_foldPath = temppath+ @"/PBIMS/LoadinFileDir/";
            _foldPath = temppath+"/"; 
            if (!File.Exists(_foldPath))
            {
                Directory.CreateDirectory(_foldPath);
            }
        }

        private void ExecuteDbCommand(DbContext context, string foldPath, StringBuilder dataSb, string command)
        {
            Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + "data" + fileGuid + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.Create, FileAccess.Write))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);

                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {
                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());

                }
            }

            string realpath = "";
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    realpath = datafilePath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    realpath = Path.Combine(_urls.MysqLoadDataPath , "data" + fileGuid + ".txt");
                    //realpath = "/home/<USER>/LoadinFileDir/" + "data" + fileGuid + ".txt";
                }
                context.Database.SetCommandTimeout(DatabaseExecTimeout);


                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    int result = context.Database.ExecuteSqlRaw("load data infile '" + realpath + command);
                    if (result < 0)
                        throw new Exception("Load data in file fail");
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    int result = context.Database.ExecuteSqlRaw("load data infile '" + realpath + command);
                    if (result < 0)
                        throw new Exception("Load data in file fail");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("MainProjectBulkOperation ExecuteDbCommand Load data in file fail======================>" + ex.Message);
                Console.WriteLine("MainProjectBulkOperation ExecuteDbCommand Load data in file fail======================>" + ex.StackTrace);
                throw;
            }
            
            

            try
            {
                File.Delete(datafilePath);
            }
            catch (Exception)
            {
                Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
            }

        }
        private void ExecuteMainProjectDbCommand(DbContext context, string foldPath, string command, string dataFileName)
        {
            string datafilePath = foldPath + dataFileName;
            try
            {
                
                string realpath = "";
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    realpath = datafilePath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    realpath = Path.Combine(_urls.MysqLoadDataPath, dataFileName);//"/home/<USER>/LoadinFileDir/" + dataFileName;
                }
                Console.WriteLine($"realpath===>{realpath}");
                context.Database.SetCommandTimeout(DatabaseExecTimeout);
                var rawSql = "load data infile '" + realpath + command;
                int result = context.Database.ExecuteSqlRaw(rawSql);

            }
            catch (Exception ex)
            {
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.Message);
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.StackTrace);
                throw new Exception("Load data in file fail" + ex.Message);
            }


            try
            {
                File.Delete(datafilePath);
            }
            catch (Exception)
            {
                Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
            }

        }
        private string ReadDataFile(string foldPath, string filename)
        {
            //Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + filename;
            string strLine = "";
            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {

                using (StreamReader sr = new StreamReader(fileStream))
                {

                    strLine = sr.ReadToEnd();
                }
            }

            return strLine;
        }
        private void WriteDataFile(StringBuilder dataSb, string foldPath, string filename)
        {
            string datafilePath = foldPath + filename + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                fileStream.Position = fileStream.Length;
                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {

                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());
                }
            }


        }

        public void SaveAddMPTreeNodeDatasToFile(List<MPProjectTreeNode> recordDatas,  long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savemptreenode";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {

                i++;
                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" + data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" +
                    data.subProjectld + "^^" + data.level + "^^" + data.indexCode + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveAddCataLogTreeNodeToFile(List<MPCatalogTreeNode> recordDatas, long requestId,int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savempcatalogtreenode";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {

                i++;
                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                      data.LibId + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModifyCataLogTreeNodeToFile(DbContext context, List<MPCatalogTreeNode> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifympcatalogtreenode";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as MainProjectDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {

                i++;
                long dataId = data.InstanceId;
                var oldData = dataContext.MPCatalogTreeNodes.FirstOrDefault(d => d.InstanceId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" +
                      data.LibId + "^^" + version + "^^" + oldData.ID + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveAddMPLibDataToFile(List<MPLibraryData> recordDatas,long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savemplibdata";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModifyMPLibDataToFile(DbContext context, List<MPLibraryData> recordDatas, long requestId, int version)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifymplibdata";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as MainProjectDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {
                i++;

                long dataId = data.DataId;
                var oldData = dataContext.MPLibraryDatas.FirstOrDefault(d => d.DataId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + oldData.ID + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }
        public void SaveDeleteMPLibDataIdToFile(List<MPLibraryData> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deletemplibdata";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.DataId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }
        public void SaveDeleteCataLogTreeNodeIdToFile(List<MPCatalogTreeNode> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deletempcatalogtreenode";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.InstanceId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }
        public void SaveDeleteMPTreeNodeIdToFile(List<MPProjectTreeNode> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deletemptreenode";

            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.InstanceId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }


        public void SaveMPCataLogTreeNodeToDB(DbContext context, long requestId)
        {
            const string addmpcatalogcommand = @"' replace into table mpcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,VersionNo,LibId) ";
            string filename = requestId + "_savempcatalogtreenode.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, addmpcatalogcommand, filename);
            }
            const string modifympcatalogcommand = @"' replace into table mpcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,LibId,VersionNo,ID) ";
            string modifympcatalogfilename = requestId + "_modifympcatalogtreenode.txt";
            if (File.Exists(_foldPath + modifympcatalogfilename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, modifympcatalogcommand, modifympcatalogfilename);
            }

            string deletefilename = requestId + "_deletempcatalogtreenode.txt";

            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from mpcatalogtreenodes where InstanceId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveMPTreeNodeToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());

                try
                {
                    File.Delete(_foldPath + deletefilename);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + _foldPath + deletefilename + "无法释放，请手动删除");
                }
                
            }
        }

        public void SaveMPLibDataToDB(DbContext context, long requestId)
        {
            const string addmplibdatacommand = @"' replace into table mplibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savemplibdata.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, addmplibdatacommand, filename);
            }
            const string modifymplibdatacommand = @"' replace into table mplibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,ID,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            string modifymplibdatafilename = requestId + "_modifymplibdata.txt";
            if (File.Exists(_foldPath + modifymplibdatafilename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, modifymplibdatacommand, modifymplibdatafilename);
            }

            string deletefilename = requestId + "_deletemplibdata.txt";

            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from mplibrarydatas where DataId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveMPTreeNodeToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());

                try
                {
                    File.Delete(_foldPath + deletefilename);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + _foldPath + deletefilename + "无法释放，请手动删除");
                }

            }
        }

        public void SaveMPLibDataHistoryToDB(DbContext context, long requestId)
        {
            const string addmplibdatahistorycommand = @"' into table mplibrarydatahistories fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata,OperationType) set Data=UNHEX(@hexdata) ";
            string filename = requestId + "_mplibdatahistory.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, addmplibdatahistorycommand, filename);
            }
        }

        public void SaveMPTreeNodeToDB(DbContext context, long requestId)
        {
            const string command = "' replace into table mpprojecttreenodes fields terminated by '^^' lines terminated by '\n' (NodeId, InstanceId,TreeId, ParentNodeId, NodeType, NodeName, bPDataKey, modelnfoKey, subProjectld,level,indexCode) ";
            string filename = requestId + "_savemptreenode.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteMainProjectDbCommand(context, _foldPath, command, filename);
            }

            string deletefilename = requestId + "_deletemptreenode.txt";

            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from mpprojecttreenodes where InstanceId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveMPTreeNodeToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());

                try
                {
                    File.Delete(_foldPath + deletefilename);
                }
                catch (Exception)
                {
                    Console.WriteLine("文件:" + _foldPath + deletefilename + "无法释放，请手动删除");
                }
            }
            
        }
        public void DeleteBPExObjects(DbContext context, List<Guid> deleteGuidList)
        {
            //to do, should consider Ids count > 100,000
            if (!deleteGuidList.Any()) return;

            const string command = "delete from bpexobjects where Guid in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in deleteGuidList)
            {
                dataSb.Append("'" + data + "',");
            }

            dataSb.Append("'')");

            context.Database.ExecuteSqlRaw(dataSb.ToString());
        }

        public void SaveBPExObjects(DbContext context, List<BPExObject> recordDatas)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            if (!recordDatas.Any()) return;
            //const string commandmodify = "' replace into table bpexobjects fields terminated by ',' lines terminated by '\\n' (Id,ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,@hexdata,Type) set ExtendField=UNHEX(@hexdata)";
            //const string command = "' into table bpexobjects fields terminated by ',' lines terminated by '\\n' (ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,@hexdata,Type) set ExtendField=UNHEX(@hexdata)";
            const string commandmodify = @"' replace into table bpexobjects fields terminated by ',||,' lines terminated by '||\n' (Id,ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,ExtendField,Type) ";
            const string command = @"' into table bpexobjects fields terminated by ',||,' lines terminated by '||\n' (ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,ExtendField,Type) ";
            StringBuilder dataSb = new StringBuilder();
            StringBuilder dataSbModify = new StringBuilder();
            int i = 0;
            int j = 0;
            var dataContext = context as MainProjectDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {
                var oldData = dataContext.BPExObjects.FirstOrDefault(d => d.GUID == data.GUID);
                if (oldData == null)
                {
                    i++;
                    //string str = BitConverter.ToString(data.ExtendField);
                    //String[] tempArr = str.Split('-');
                    //string dataStr = string.Join("", tempArr);
                    dataSb.Append(data.ProvideUser + ",||," + data.ProvideTime + ",||," + data.ProvideState + ",||," + data.ProvideNotes + ",||," + data.AcceptUser + ",||," + data.AcceptTime + ",||," + data.AcceptState + ",||," +
                        data.StructNotes + ",||," + data.Domain + ",||," + data.LoadName + ",||," + data.GUID + ",||," +
                        (string.IsNullOrEmpty(data.ExtendField) ? "\\N" : data.ExtendField) + ",||," + data.Type + "||\n");

                    if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                    {
                        ExecuteDbCommand(context, _foldPath, dataSb, command);
                        i = 0;
                        dataSb.Clear();
                        dataSb.Capacity = 0;
                    }
                }
                else
                {
                    j++;
                    //string str = BitConverter.ToString(data.ExtendField);
                    //String[] tempArr = str.Split('-');
                    //string dataStr = string.Join("", tempArr);
                    dataSbModify.Append(oldData.Id + ",||," + data.ProvideUser + ",||," + data.ProvideTime + ",||," + data.ProvideState + ",||," + data.ProvideNotes + ",||," + data.AcceptUser + ",||," + data.AcceptTime + ",||," + data.AcceptState + ",||," +
                        data.StructNotes + ",||," + data.Domain + ",||," + data.LoadName + ",||," + data.GUID + ",||," +
                        (string.IsNullOrEmpty(data.ExtendField) ? "\\N" : data.ExtendField) + ",||," + data.Type + "||\n");

                    if (j >= RecordCountLimit || dataSbModify.Capacity >= 200 * 1024 * 1024)
                    {
                        ExecuteDbCommand(context, _foldPath, dataSbModify, commandmodify);
                        j = 0;
                        dataSbModify.Clear();
                        dataSbModify.Capacity = 0;
                    }
                }

            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
            if (dataSbModify.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSbModify, commandmodify);
            }
            sw.Stop();
        }

        public void SaveBPExObjectPublishs(DbContext context, List<BPExObjectPublish> recordDatas, int mainVersion, int subVersion)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            if (!recordDatas.Any()) return;
            //const string commandmodify = "' replace into table bpexobjectpublishes fields terminated by ',' lines terminated by '\\n' (Id,ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,@hexdata,MainVersion,SubVersion,Type) set ExtendField=UNHEX(@hexdata)";
            //const string command = "' into table bpexobjectpublishes fields terminated by ',' lines terminated by '\\n' (ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,@hexdata,MainVersion,SubVersion,Type) set ExtendField=UNHEX(@hexdata)";
            const string command = @"' into table bpexobjectpublishes fields terminated by ',||,' lines terminated by '||\n' (ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,ExtendField,MainVersion,SubVersion,Type) ";

            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;
                //string str = BitConverter.ToString(data.ExtendField);
                //String[] tempArr = str.Split('-');
                //string dataStr = string.Join("", tempArr);
                dataSb.Append(data.ProvideUser + ",||," + data.ProvideTime + ",||," + data.ProvideState + ",||," + data.ProvideNotes + ",||," + data.AcceptUser + ",||," + data.AcceptTime + ",||," + data.AcceptState + ",||," +
                    data.StructNotes + ",||," + data.Domain + ",||," + data.LoadName + ",||," + data.GUID + ",||," +
                    (string.IsNullOrEmpty(data.ExtendField) ? "\\N" : data.ExtendField) + ",||," + mainVersion + ",||," + subVersion + ",||," + data.Type + "||\n");

                if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
            sw.Stop();
        }

        public void SaveBPExObjectPublishsWithVersionName(DbContext context, List<BPExObjectPublish> recordDatas,string mainverName,string subverName)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            if (!recordDatas.Any()) return;
            const string command = @"' into table bpexobjectpublishes fields terminated by ',||,' lines terminated by '||\n' (ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,ExtendField,MainVersion,SubVersion,Type,VersionName,SubVersionName) ";
            const string commandmodify = @"' replace into table bpexobjectpublishes fields terminated by ',||,' lines terminated by '||\n' (Id,ProvideUser,ProvideTime,ProvideState,ProvideNotes,AcceptUser,AcceptTime,AcceptState,StructNotes,Domain,LoadName,GUID,ExtendField,MainVersion,SubVersion,Type,VersionName,SubVersionName) ";

            StringBuilder dataSb = new StringBuilder();
            StringBuilder dataSbModify = new StringBuilder();
            int i = 0;
            int j = 0;
            var dataContext = context as MainProjectDbContext;
            foreach (var data in recordDatas)
            {
                var oldData = dataContext.BPExObjectPublishes.FirstOrDefault(d => d.GUID == data.GUID && d.VersionName == mainverName && d.SubVersionName == subverName);
                i++;
                if (oldData == null)
                {
                    dataSb.Append(data.ProvideUser + ",||," + data.ProvideTime + ",||," + data.ProvideState + ",||," + data.ProvideNotes + ",||," + data.AcceptUser + ",||," + data.AcceptTime + ",||," + data.AcceptState + ",||," +
                    data.StructNotes + ",||," + data.Domain + ",||," + data.LoadName + ",||," + data.GUID + ",||," +
                    (string.IsNullOrEmpty(data.ExtendField) ? "\\N" : data.ExtendField) + ",||," + 0 + ",||," + 0 + ",||," + data.Type + ",||," + mainverName + ",||," + subverName + "||\n");

                    if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                    {
                        ExecuteDbCommand(context, _foldPath, dataSb, command);
                        i = 0;
                        dataSb.Clear();
                        dataSb.Capacity = 0;
                    }
                }
                else
                {
                    j++;
                    dataSbModify.Append(oldData.Id + ",||," + data.ProvideUser + ",||," + data.ProvideTime + ",||," + data.ProvideState + ",||," + data.ProvideNotes + ",||," + data.AcceptUser + ",||," + data.AcceptTime + ",||," + data.AcceptState + ",||," +
                    data.StructNotes + ",||," + data.Domain + ",||," + data.LoadName + ",||," + data.GUID + ",||," +
                    (string.IsNullOrEmpty(data.ExtendField) ? "\\N" : data.ExtendField) + ",||," + 0 + ",||," + 0 + ",||," + data.Type + ",||," + mainverName + ",||," + subverName + "||\n");

                    if (j >= RecordCountLimit || dataSbModify.Capacity >= 200 * 1024 * 1024)
                    {
                        ExecuteDbCommand(context, _foldPath, dataSbModify, commandmodify);
                        j = 0;
                        dataSbModify.Clear();
                        dataSbModify.Capacity = 0;
                    }
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
            if (dataSbModify.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSbModify, commandmodify);
            }
            sw.Stop();
        }

        public void SaveMPProjectTreeNodes(DbContext context, List<MPProjectTreeNode> recordDatas)
        {
            Stopwatch sw = new Stopwatch();
            sw.Start();
            if (!recordDatas.Any()) return;
            
            // 使用 ON DUPLICATE KEY UPDATE 方式处理节点名称冲突
            // 基于动态ConflictScope字段进行冲突检测
            // 当发现节点名称冲突时，自动重命名节点并记录冲突信息
            const string command = @"' into table mpprojecttreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId, InstanceId, TreeId, ParentNodeId, NodeType, NodeName, bPDataKey, modelnfoKey, subProjectld, level, indexCode, OriginalNodeName, HasConflict, ConflictScope) " +
                "ON DUPLICATE KEY UPDATE " +
                "OriginalNodeName = CASE WHEN OriginalNodeName IS NULL THEN VALUES(NodeName) ELSE OriginalNodeName END, " +
                "HasConflict = 1, " +
                "NodeName = CONCAT(VALUES(NodeName), '_', (SELECT COALESCE(MAX(CAST(SUBSTRING_INDEX(NodeName, '_', -1) AS UNSIGNED)), 0) + 1 FROM mpprojecttreenodes WHERE subProjectld = VALUES(subProjectld) AND ConflictScope = VALUES(ConflictScope) AND NodeName LIKE CONCAT(VALUES(NodeName), '_%'))), " +
                "TreeId = VALUES(TreeId), " +
                "NodeType = VALUES(NodeType), " +
                "bPDataKey = VALUES(bPDataKey), " +
                "modelnfoKey = VALUES(modelnfoKey), " +
                "level = VALUES(level), " +
                "indexCode = VALUES(indexCode) ";
            
            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            var dataContext = context as MainProjectDbContext;
            if (null == dataContext) return;
            
            foreach (var data in recordDatas)
            {
                //log.Info("SaveMPProjectTreeNodes===data.subProjectld==>"+data.subProjectld);
                i++;
                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" + data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" +
                    data.subProjectld + "^^" + data.level + "^^" + data.indexCode + "^^" + (data.OriginalNodeName ?? "") + "^^" + data.HasConflict + "^^" + (data.ConflictScope ?? "") + "\n");

                if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
            sw.Stop();
        }


        public void DeleteMPProjectTreeNodes(DbContext context, List<MPProjectTreeNode> delDatas)
        {
            if (!delDatas.Any()) return;

            var datacontext = context as MainProjectDbContext;

            var allnodes = datacontext.MPProjectTreeNodes.ToList();
            var matchednodes = allnodes.Where(mpProjectTreeNode => delDatas.Any(mp => mp.InstanceId == mpProjectTreeNode.InstanceId
                && mp.subProjectld == mpProjectTreeNode.subProjectld)).ToList();
            var ids = matchednodes.Select(s => s.ID).ToList();
            const string command = "delete from mpprojecttreenodes where id in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in ids)
            {
                dataSb.Append(data + ",");
            }

            dataSb.Append("-1)");

            context.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        public void DeleteBPExObjectPublishsWithVersionName(DbContext context, List<BPExObjectPublish> delDatas, string mainVersionName, string subVersionName)
        {
            if (!delDatas.Any()) return;

            string command = "delete from bpexobjectpublishes where VersionName= '" + mainVersionName + "' and SubVersionName = '" + subVersionName + "' and GUID in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append("'" + data.GUID + "',");
            }

            dataSb.Append("'-1')");

            context.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        public void DeleteTreeNodes(DbContext m_db, List<MPCatalogTreeNode> delDatas, Guid libId)
        {
            if (!delDatas.Any()) return;

            string command = "delete from mpcatalogtreenodes where LibId = '" + libId + "' and InstanceId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.InstanceId + ",");
            }

            dataSb.Append("-1)");

            m_db.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        public void ModifyTreeNodes(DbContext context, List<MPCatalogTreeNode> recordDatas, int version)
        {
            if (!recordDatas.Any()) return;
            const string command = @"' replace into table mpcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,LibId,VersionNo,ID) ";
            StringBuilder dataSb = new StringBuilder();
            int i = 0;

            var dataContext = context as MainProjectDbContext;
            if (null == dataContext) return;

            foreach (var data in recordDatas)
            {
                i++;


                long dataId = data.InstanceId;
                var oldData = dataContext.MPCatalogTreeNodes.FirstOrDefault(d => d.InstanceId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" +
                      data.LibId + "^^" + version + "^^" + oldData.ID + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void SaveTreeNodes(DbContext m_db, List<MPCatalogTreeNode> recordDatas, int version)
        {
            //PbimLog log = new PbimLog("Info");
            //log.Info("SaveTreeNodes===dataSb.Capacity");
            if (!recordDatas.Any()) return;
            const string command = @"' replace into table mpcatalogtreenodes fields terminated by '^^' lines terminated by '\n' " +
                "(NodeId,InstanceId,TreeId,ParentNodeId,NodeType,NodeName,bPDataKey,modelnfoKey,TreeType,VersionNo,LibId) ";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;
                dataSb.Append(data.NodeId + "^^" + data.InstanceId + "^^" + data.TreeId + "^^" + data.ParentNodeId + "^^" + (int)data.NodeType + "^^" +
                    data.NodeName + "^^" + data.bPDataKey + "^^" + data.modelnfoKey + "^^" + (int)data.TreeType + "^^" + version + "^^" +
                      data.LibId + "\n");

                if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                {
                    ExecuteDbCommand(m_db, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(m_db, _foldPath, dataSb, command);
            }
        }


        public void SaveLibDatas(DbContext m_db, List<MPLibraryData> recordDatas, int version)
        {
            //PbimLog log = new PbimLog("Info");
            //log.Info("SaveLibDatas===dataSb.Capacity");
            if (!recordDatas.Any()) return;
            const string command = @"' replace into table mplibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit || dataSb.Capacity >= 200 * 1024 * 1024)
                {
                    ExecuteDbCommand(m_db, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(m_db, _foldPath, dataSb, command);
            }
        }
        public void ModifyLibDatas(DbContext m_db, List<MPLibraryData> recordDatas, int version)
        {
            if (!recordDatas.Any()) return;
            //const string command = "' replace into table modeldatas fields terminated by ',' lines terminated by '\\n' (DomainClassName,Id,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            const string command = @"' replace into table mplibrarydatas fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,ID,TreeType,@hexdata) set Data=UNHEX(@hexdata)";
            StringBuilder dataSb = new StringBuilder();
            int i = 0;

            var dataContext = m_db as MainProjectDbContext;
            if (null == dataContext) return;

            foreach (var data in recordDatas)
            {
                i++;


                long dataId = data.DataId;
                var oldData = dataContext.MPLibraryDatas.FirstOrDefault(d => d.DataId == dataId && d.LibId == data.LibId);
                if (null == oldData) continue;

                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }


                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + oldData.ID + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(m_db, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(m_db, _foldPath, dataSb, command);
            }
        }
        public void DeleteLibDatas(DbContext m_db, List<MPLibraryData> delDatas, Guid libId)
        {
            if (!delDatas.Any()) return;

            string command = "delete from mplibrarydatas where LibId = '" + libId + "' and DataId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.DataId + ",");
            }

            dataSb.Append("-1)");

            m_db.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }

        public void SaveHistoryLibData(DbContext context, List<MPLibraryData> addDatas, List<MPLibraryData> modifyDatas, List<MPLibraryData> delDatas, int? version)
        {
            const string command =
                @"' into table mplibrarydatahistories fields terminated by ',' lines terminated by '\n' " +
                "(DataId,SchemaName,ClassName,LibId,VersionNo,TreeType,@hexdata,OperationType) set Data=UNHEX(@hexdata) ";

            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)MPOperationType.Add + "\n");
                //currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)MPOperationType.Modify + "\n");
                //currentVersion+","+data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    "\\N" + "," + (int)MPOperationType.Delete + "\n");
                //currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void SaveMPLibDataHistoryToFile(DbContext context, List<MPLibraryData> addDatas, 
            List<MPLibraryData> modifyDatas, List<MPLibraryData> delDatas, int version, long requestId)
        {
            string filename = requestId + "_mplibdatahistory";
            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)MPOperationType.Add + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + (int)MPOperationType.Modify + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.DataId + "," + data.SchemaName + "," + data.ClassName + "," + data.LibId + "," + version + "," + (int)data.TreeType + "," +
                    "\\N" + "," + (int)MPOperationType.Delete + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

    }
}
