syntax = "proto3";

import "google/protobuf/timestamp.proto";

option csharp_namespace = "BimBase.Api.Grpc";

package databasediagnostic;

// 数据库诊断服务
service DatabaseDiagnosticService {
    // 执行深度连接分析
    rpc AnalyzeConnections(DiagnosticRequest) returns (stream DiagnosticReport);
    
    // 获取连接摘要信息
    rpc GetConnectionSummary(DiagnosticRequest) returns (ConnectionSummary);
    
    // 强制GC并分析
    rpc ForceGCAndAnalyze(DiagnosticRequest) returns (ConnectionSummary);
}

// 诊断请求
message DiagnosticRequest {
    // 分析深度级别
    DiagnosticLevel level = 1;
    
    // 是否包含敏感信息
    bool include_sensitive_data = 2;
    
    // 是否分析引用链
    bool analyze_reference_chain = 3;
    
    // 是否强制GC
    bool force_gc = 4;
}

// 诊断级别
enum DiagnosticLevel {
    BASIC = 0;      // 基础统计
    DETAILED = 1;   // 详细分析
    DEEP = 2;       // 深度分析
}

// 连接摘要
message ConnectionSummary {
    google.protobuf.Timestamp scan_time = 1;
    int32 total_mysql_connections = 2;
    int32 total_dbcontexts = 3;
    int32 active_connections = 4;
    int32 closed_connections = 5;
    int32 broken_connections = 6;
    
    repeated DatabaseInfo databases = 7;
    repeated ConnectionPoolInfo pools = 8;
    
    // 内存信息
    MemoryInfo memory_info = 9;
    
    // 潜在问题
    repeated string potential_issues = 10;
}

// 详细诊断报告（流式返回）
message DiagnosticReport {
    ReportType type = 1;
    string title = 2;
    string content = 3;
    google.protobuf.Timestamp timestamp = 4;
    
    // 具体的连接信息
    ConnectionDetail connection_detail = 5;
}

// 报告类型
enum ReportType {
    SUMMARY = 0;
    MYSQL_CONNECTION = 1;
    DBCONTEXT = 2;
    CONNECTION_POOL = 3;
    MEMORY_ANALYSIS = 4;
    REFERENCE_CHAIN = 5;
    ISSUE_DETECTED = 6;
}

// 数据库信息
message DatabaseInfo {
    string database_name = 1;
    int32 connection_count = 2;
    string connection_string_hash = 3; // 不包含敏感信息的hash
}

// 连接池信息
message ConnectionPoolInfo {
    string pool_type = 1;
    int32 max_size = 2;
    int32 min_size = 3;
    int32 current_size = 4;
    int32 available_connections = 5;
    int32 busy_connections = 6;
}

// 内存信息
message MemoryInfo {
    int64 working_set_mb = 1;
    int32 gc_gen0_collections = 2;
    int32 gc_gen1_collections = 3;
    int32 gc_gen2_collections = 4;
    int64 total_memory_mb = 5;
}

// 连接详细信息
message ConnectionDetail {
    string object_type = 1;
    string connection_state = 2;
    string connection_string_preview = 3; // 脱敏后的连接字符串预览
    int32 object_hash_code = 4;
    int32 gc_generation = 5;
    string created_stack_trace = 6; // 如果可获取
    repeated string reference_chain = 7;
    
    // 特定类型的信息
    MySqlConnectionInfo mysql_info = 8;
    DbContextInfo dbcontext_info = 9;
}

// MySQL连接特定信息
message MySqlConnectionInfo {
    string server_version = 1;
    int32 connection_timeout = 2;
    string database = 3;
    bool is_pooled = 4;
    string pool_info = 5;
}

// DbContext特定信息
message DbContextInfo {
    string context_type = 1;
    bool is_disposed = 2;
    string database_provider = 3;
    bool change_tracker_has_changes = 4;
    int32 tracked_entities_count = 5;
}
 