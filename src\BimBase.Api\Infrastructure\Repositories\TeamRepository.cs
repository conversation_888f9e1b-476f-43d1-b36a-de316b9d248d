﻿using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.DbInitializer;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Pipelines.Sockets.Unofficial.Arenas;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using Serilog;
using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.MainDomain;
using Microsoft.CodeAnalysis;
using System.IO;
using K4os.Compression.LZ4.Internal;
using BimBase.Api.Protos;

namespace BimBase.Api.Infrastructure.Repositories
{
    public class TeamRepository : BaseRepository, ITeamRepository
    {
        private readonly TeamDbContext _teamDbContext;
        private readonly ILogger<TeamRepository> _logger;
        private readonly Func<DbConnection, Guid, IProjectRepository> _projectRepositoryFactory;
        private readonly Func<DbConnection, Guid, IMainProjectRepository> _mainProjectRepositoryFactory;
        private readonly char separator = '/';//Path.DirectorySeparatorChar;
        public TeamRepository(TeamDbContext teamDbContext, ILogger<TeamRepository> logger
            , Func<DbConnection, Guid, IProjectRepository> projectRepositoryFactory
            , Func<DbConnection,Guid,IMainProjectRepository> mainProjectRepositoryFactory):base(teamDbContext)
        {
            _teamDbContext = teamDbContext ?? throw new ArgumentNullException(nameof(teamDbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _projectRepositoryFactory = projectRepositoryFactory ?? throw new ArgumentNullException(nameof(projectRepositoryFactory));
            _mainProjectRepositoryFactory = mainProjectRepositoryFactory ?? throw new ArgumentNullException(nameof(mainProjectRepositoryFactory));
        }

        


        public async Task<TeamMember> GetCurrentUserInfoAsync(string sessionId)
        {
            var sessionInfo = await _teamDbContext.LoginSession.AsNoTracking().FirstOrDefaultAsync(s => s.SessionId == sessionId);
            if (sessionInfo is null) return null;
            var currentUser = await _teamDbContext.TeamMembers.AsNoTracking().FirstOrDefaultAsync(m => m.ID == sessionInfo.TeamMember_ID);
            return currentUser;
        }

        public async Task<List<GrpcOpenningSessionUser>> GetOpenningUserListAsync(Guid projectGuid)
        {
            List<GrpcOpenningSessionUser> ret = new List<GrpcOpenningSessionUser >();
            var sessionInfoList = await _teamDbContext.LoginSession.AsNoTracking().Where(s=>s.ProjectGuid == projectGuid&&s.SessionId!=null&&s.SessionId!="").ToListAsync();
            if (sessionInfoList is null) return null;
            var memberIds = sessionInfoList.Select(k => k.TeamMember_ID).ToList();
            var teamMemberList = _teamDbContext.TeamMembers.AsNoTracking().ToList().Where(s=> memberIds.Contains(s.ID)).ToList();
            List<KeyValuePair<string,Guid?>> list = new List<KeyValuePair<string, Guid?>>();
            foreach (var member in teamMemberList)
            {
                var sessionInfo = sessionInfoList.FirstOrDefault(k => k.TeamMember_ID == member.ID);
                if (sessionInfo != null)
                {
                    GrpcOpenningSessionUser grpcOpenningSessionUser = new GrpcOpenningSessionUser();
                    grpcOpenningSessionUser.Id = sessionInfo.ID;
                    grpcOpenningSessionUser.TeamMemberId = sessionInfo.TeamMember_ID.ToString();
                    grpcOpenningSessionUser.TeamMemberLoginName = member.LoginName;
                    grpcOpenningSessionUser.ProjectGuid = sessionInfo.ProjectGuid.ToString();
                    grpcOpenningSessionUser.ClientGuid = sessionInfo.ClientGuid.ToString();
                    ret.Add(grpcOpenningSessionUser);


                }
            }
            return ret;
        }

        public async Task<Guid> GetCurrentUserProjectIdAsync(string sessionId)
        {
            var sessionInfo = await _teamDbContext.LoginSession.AsNoTracking().FirstOrDefaultAsync(s => s.SessionId == sessionId);
            if (sessionInfo is null) return Guid.Empty;
            return sessionInfo.ProjectGuid;
        }

        public bool CheckUserPassword(string username, string passMD5, out TeamMember userInfo)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", "CheckUserPassword", new { username = username ,passMD5 = passMD5 });

            userInfo = null;
            var result = _teamDbContext.TeamMembers.SingleOrDefault(m => m.LoginName == username && m.PasswordMD5 == passMD5);
            if (result == null)
            {
                var temploginname = Members.FirstOrDefault(m => m.LoginName == username);
                if (temploginname != null)
                {
                    var sha256pwd = UtilityHelper.SHA256EncryptString(temploginname.PasswordMD5);
                    var sha256databasepwd = UtilityHelper.SHA256EncryptString(passMD5);
                    if (sha256pwd != passMD5)
                    {
                        if (sha256databasepwd != temploginname.PasswordMD5)
                        {
                            return false;
                        }
                    }
                    result = temploginname;
                }
                else
                {
                    return false;
                }
            }

            if (string.Compare(result.LoginName, username, StringComparison.CurrentCulture) == 0)
            {
                userInfo = result;
                return true;
            }
            return false;
        }

        public bool CheckUserLogname(string Logname)
        {
            var result = Members.SingleOrDefault(m => m.LoginName == Logname);
            if (result == null)
                return false;

            if (string.Compare(result.LoginName, Logname, StringComparison.CurrentCulture) == 0)
                return true;

            return false;
        }

        /// <summary>
        /// 根据用户ID 创建session ,记录到 LoginSession 表
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public string CreateSession(Guid userId)
        {
            var newSessionId = Guid.NewGuid().ToString("N");

            if(_teamDbContext.LoginSession.Any(s => s.TeamMember_ID == userId 
                && (s.SessionId != null || s.SessionId != string.Empty)))
            {
                DeleteUnUsedSessionByUserId(userId);
            }

            _teamDbContext.Add(new LoginSession
            {
                ID = Guid.NewGuid().ToString("N"),
                SessionId = newSessionId,
                TeamMember_ID = userId,
            }) ;

            if (_teamDbContext.SaveChanges() > 0)
            {
                return newSessionId;
            }
            else
            {
                return string.Empty;
            }
        }

        public bool UpdateSession(string sessionId,Guid projectGuid)
        {
            var loginDatas = _teamDbContext.LoginSession.Where(u => u.SessionId == sessionId);
            var user = loginDatas.FirstOrDefault();
            if (user!=null)
            {
                var userId = user.TeamMember_ID;
                if (_teamDbContext.LoginSession.Any(s => s.TeamMember_ID == userId
                && (s.SessionId != null || s.SessionId != string.Empty)
                &&s.ProjectGuid == projectGuid))
                {
                    DeleteUnUsedSessionByUserId(userId,projectGuid,sessionId);
                }
            }
            
            if (loginDatas.Any())
            {
                loginDatas.ToList().ForEach(s =>
                {
                    s.ProjectGuid = projectGuid;
                    s.ClientGuid = Guid.NewGuid();
                });
                _teamDbContext.LoginSession.UpdateRange(loginDatas);
                return _teamDbContext.SaveChanges() > 0;
            }
            return true;
        }

        /// <summary>
        /// 调用登录接口，创建新的sessionId ，先检查是否有异常导致的session 未清理
        /// 将该用户未清理的sessionId 进行清理
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="projectGuid"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        private bool DeleteUnUsedSessionByUserId(Guid userId,Guid projectGuid,string sessionId)
        {
            var loginDatas = _teamDbContext.LoginSession.Where(u => u.TeamMember_ID == userId 
            && (u.SessionId != null && u.SessionId != string.Empty)&&u.SessionId!=sessionId
            &&(u.ProjectGuid == projectGuid||DateTime.Now.AddDays(-7)>u.LoginAt));

            if (loginDatas.Any())
            {
                var user = _teamDbContext.TeamMembers.FirstOrDefault(u=>u.ID == userId);
                loginDatas.ToList().ForEach(
                    s =>
                    {
                        _logger.LogInformation($"{user.LoginName}登录服务器，打开{projectGuid}项目，顶掉原有session{s.SessionId};生成新session为{sessionId}");
                        s.SessionId = string.Empty;
                        
                    }); 
                _teamDbContext.LoginSession.UpdateRange(loginDatas);
                return _teamDbContext.SaveChanges() > 0;
            }
            return true;
        }
        private bool DeleteUnUsedSessionByUserId(Guid userId)
        {
            var loginDatas = _teamDbContext.LoginSession.Where(u => u.TeamMember_ID == userId
            && (u.SessionId != null && u.SessionId != string.Empty)
            && (u.ProjectGuid == Guid.Empty && DateTime.Now.AddDays(-7) > u.LoginAt));

            if (loginDatas.Any())
            {
                loginDatas.ToList().ForEach(s =>
                {
                    s.SessionId = string.Empty;
                });
                _teamDbContext.LoginSession.UpdateRange(loginDatas);
                return _teamDbContext.SaveChanges() > 0;
            }
            return true;
        }

        public bool DeleteSession(string sessionId)
        {
            var loginDatas =  _teamDbContext.LoginSession.Where(u=>u.SessionId == sessionId );

            if (loginDatas.Any())
            {
                loginDatas.ToList().ForEach(s=>s.SessionId = string.Empty);
                _teamDbContext.LoginSession.UpdateRange(loginDatas);
                return _teamDbContext.SaveChanges() > 0;
            }
            return true;

        }


        public IQueryable<string> GetOnlineUserlist()
        {
            var userIds = _teamDbContext.LoginSession
                .Where(u => (u.SessionId != null || u.SessionId != string.Empty))
                .Select(a => a.TeamMember_ID)
                .Distinct();

            if(userIds != null && userIds.Any())
            {
                return _teamDbContext.TeamMembers.Where(m => userIds.Contains(m.ID)).Select(a=>a.LoginName);
            }

            return null;

        }

        public bool IsCurrentLogin(string sessionId)
        {
            if (string.IsNullOrWhiteSpace(sessionId)) return false;
            var x = _teamDbContext.LoginSession.Where(u => u.SessionId == sessionId);
            if (x is null || !x.Any()) return false;
            return true;
        }

        public bool CheckProjectByPName(string projectName)
        {
            var select = _teamDbContext.TeamProjects.Where(tp => tp.Name.ToLower() == projectName.ToLower());
            if (select.Any())
            {
                return true;
            }
            return false;
        }

        public string GetProjectIdByName(string projectName)
        {
            return _teamDbContext.TeamProjects.SingleOrDefault(p => p.Name == projectName)?.ID.ToString();
        }

        public IList<TeamProject> GetProjectList(string sessionId)
        {
            List<TeamProject> rtProjects = new List<TeamProject>();
            List<TeamProject> projects = new List<TeamProject>();
            var userId = _teamDbContext.LoginSession.FirstOrDefault(m=>m.SessionId == sessionId)?.TeamMember_ID;
            if (userId != Guid.Empty)
            {
                var user = _teamDbContext.TeamMembers.FirstOrDefault(m => m.ID == userId);
                if (user != null)
                {
                    //List<TeamProject> projects;
                    if ((user.LoginName == BuildinAdministrators.BuildInTeamAdministratorLoginName) || (user.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName) || (user.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName))
                        projects = _teamDbContext.TeamProjects.ToList();
                    else
                        projects = GetTeamProjectListByMember(user.ID);//user.Projects.ToList();

                    foreach (var prj in projects)
                    {
                        TeamProject project = new TeamProject();
                        project.ID = prj.ID;
                        project.Name = prj.Name;
                        project.Description = prj.Description;
                        project.CreationTime = prj.CreationTime;
                        project.Avatar = prj.Avatar;
                        project.Leader = prj.Leader;
                        project.StartTime = prj.StartTime;
                        project.EndTime = prj.EndTime;
                        project.Progress = prj.Progress;
                        project.ExtendProperty = prj.ExtendProperty;
                        rtProjects.Add(project);
                    }
                    rtProjects.Sort();
                }
            }
            return rtProjects;
            //return projects;
        }

        public MainProject AddMainProject(MainProject mainProject)
        {
            var isExist = _teamDbContext.MainProjects.Any(tp => tp.ID == mainProject.ID || tp.Name.ToLower() == mainProject.Name.ToLower());
            if (isExist)
            {
                throw new Exception("服务器已存在相同的项目ID\n请尝试使用其它项目ID！");
            }
            mainProject.CreationTime = DateTime.Now;
            mainProject.LastUpdateTime = DateTime.Now;
            
            // 从GrpcContextAccessor获取客户端信息
            if (Infrastructure.Grpc.GrpcContextAccessor.TryGetContext(out var context))
            {

                var clientId = string.IsNullOrEmpty(mainProject.clientId) ? Infrastructure.Grpc.GrpcContextAccessor.GetClientId() : mainProject.clientId;
                var clientVersion = string.IsNullOrEmpty(mainProject.ClientVersion) ? Infrastructure.Grpc.GrpcContextAccessor.GetClientVersion() : mainProject.ClientVersion;

                if (!string.IsNullOrEmpty(clientId))
                {
                    mainProject.clientId = clientId;
                }
                
                if (!string.IsNullOrEmpty(clientVersion))
                {
                    mainProject.ClientVersion = clientVersion;
                }
            }
            
            _teamDbContext.MainProjects.Add(mainProject);
            _teamDbContext.SaveChanges();
            return mainProject;
        }
        public TeamProject AddProject(TeamProject prj)
        {
            var isExist = _teamDbContext.TeamProjects.Any(tp => tp.ID == prj.ID || (tp.Name.ToLower() == prj.Name.ToLower() && (prj.MainProjectID != Guid.Empty.ToString() && tp.MainProjectID.ToLower() == prj.MainProjectID.ToLower())));
            if (isExist)
            {
                throw new Exception("服务器已存在相同的项目ID\n请尝试使用其它项目ID！");
            }
            prj.CreationTime = DateTime.Now;
            _teamDbContext.TeamProjects.Add(prj);
            _teamDbContext.SaveChanges();
            return prj;
        }

        public IQueryable<TeamMember> Members
        {
            get
            {
                return _teamDbContext.TeamMembers.AsNoTracking();
            }
        }

        public IQueryable<TeamProject> Projects
        {
            get
            {
                return _teamDbContext.TeamProjects.AsNoTracking();
            }
        }

        public IQueryable<SchemaVersion> Versions
        {
            get
            {
                return _teamDbContext.Versions.AsNoTracking();
            }
        }

        public IQueryable<DbVersion> DbVersions
        {
            get
            {
                return _teamDbContext.DbVersions.AsNoTracking();
            }
        }
        public System.Linq.IQueryable<FileDirectory> FileDirectories
        {
            get
            {
                return _teamDbContext.FileDirectories.AsNoTracking();
            }
        }
        public IQueryable<MainProjectUserRole> MainProjectUserRoles
        {
            get
            {
                return _teamDbContext.MainProjectUserRoles;
            }
        }
        public IQueryable<MainProject> MainProjects
        {
            get
            {
                return _teamDbContext.MainProjects.AsNoTracking();
            }
        }

        /// <summary>
        /// 获取所有MainProject，忽略ClientVersion过滤条件
        /// </summary>
        public IQueryable<MainProject> MainProjectsIgnoreClientVersion
        {
            get
            {
                return _teamDbContext.MainProjects.IgnoreQueryFilters().AsNoTracking();
            }
        }

        public bool AddMemberToMainProject(TeamMember mbr, MainProject mainProject)
        {
            var select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.GroupOrTeamMemberId == mbr.ID.ToString() && mp.MainProjectId == mainProject.ID && (mp.ObjectId == mainProject.ID || mp.ObjectId == Guid.Empty)).ToList();
            if (!select.Any())
            {
                MainProjectTeamGroup tur = new MainProjectTeamGroup();
                tur.GroupOrTeamMemberId = mbr.ID.ToString();
                tur.MainProjectId = mainProject.ID;
                tur.MemberType = 1;
                tur.ObjectId = mainProject.ID;
                _teamDbContext.MainProjectTeamGroups.Add(tur);
                _teamDbContext.SaveChanges();
            }
            return true;
        }


        public ModelDomain.ProjectMember AddMemberToProject(TeamMember mbr, TeamProject proj, bool asAdmin = false)
        {
            var projectRepository = GetProjectRepository(proj.ID);

            if (null == projectRepository) return null;
            ModelDomain.ProjectMember prjMbr = null;
            if (asAdmin)
            {
                prjMbr = projectRepository.AddMember(mbr, true);
            }
            else
            {
                prjMbr = projectRepository.AddMember(mbr, false);
            }

            _teamDbContext.SaveChanges();
            return prjMbr;
        }

        /// <summary>
        /// 退出一个项目。
        /// </summary>
        public bool RemoveMemberFromProject(TeamMember mbr, TeamProject proj)
        {
            var projectRepository = GetProjectRepository(proj.ID);
            if (projectRepository != null)
            {
                //mbr.Projects.Remove(proj);
                //proj.Members.Remove(mbr);
                //_teamDbContext.SaveChanges();

                var prjMbr = projectRepository.Members.SingleOrDefault(m => m.TeamMemberID == mbr.ID);
                if (prjMbr == null) return false;
                projectRepository.DeleteMember(prjMbr);
                
                return true;
            }

            return false;
        }

        /// <summary>
        /// change to modeldb by teamdb
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public IProjectRepository GetProjectRepository(Guid projectId)
        {
            var projectRepository = _projectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), projectId);
            return projectRepository;
        }
        public IMainProjectRepository GetMainProjectRepository(Guid mainProjectId)
        {
            var mainProjectRepository = _mainProjectRepositoryFactory(_teamDbContext.Database.GetDbConnection(), mainProjectId);
            return mainProjectRepository;
        }
        public TeamProject DeleteProject(Guid id)
        {
            var selset = from tp in _teamDbContext.TeamProjects where tp.ID == id select tp;
            return DeleteProject(selset.FirstOrDefault());
        }

        private TeamProject DeleteProject(TeamProject tpToDel)
        {
            if (null != tpToDel)
            {
                //tpToDel.Members.Clear();
                TeamProject tp = _teamDbContext.TeamProjects.Remove(tpToDel).Entity;
                var projectRepository = GetProjectRepository(tp.ID);
                if (projectRepository != null)
                {
                    // modify by asdf 2018-09-20 删除数据库最耗时间，改为异步执行
                    Task.Run(() =>
                    {
                        projectRepository.Destroy();
                    });
                }
                _teamDbContext.SaveChanges();
                return tp;
            }
            return null;
        }

        public bool CheckProjectName(string pName)
        {
            var isExist = _teamDbContext.TeamProjects.Any(tp => tp.Name.ToLower() == pName.ToLower());
            if (isExist)
            {
                return false;
            }
            return true;
        }

        public bool ModifyProjectInfo(TeamProject teamProject)
        {
            _teamDbContext.Update(teamProject);
            return _teamDbContext.SaveChanges() == 1;
        }

        public TeamMember AddMember(TeamMember mbr)
        {
            var select = from m in _teamDbContext.TeamMembers.AsNoTracking() where m.LoginName == mbr.LoginName select m;
            if (select.Any()) return null;
            var x =  _teamDbContext.TeamMembers.Add(mbr).Entity;
            _teamDbContext.SaveChanges();
            return x;
        }

        public bool DeleteMember(string loginName, out string message)
        {
            message = string.Empty;
            if (loginName.ToLower().Equals(BuildinAdministrators.BuildInTeamAdministratorLoginName.ToLower()) || loginName.ToLower().Equals(BuildinAdministrators.BuildInSystemAdminLoginName.ToLower()))
            {
                message = "不能删除内建用户！";
                return false;
            }
            var selset = from mbr in _teamDbContext.TeamMembers where mbr.LoginName.Equals(loginName) select mbr;
            if (!selset.Any())
            {
                message = "用户不存在！";
                return false;
            }
            _teamDbContext.TeamMembers.Remove(selset.First());
            _teamDbContext.SaveChangesAsync();
            return true;
        }

        public bool AddMemberListToMainProject(List<TeamMember> mbrList, MainProject mainProject)
        {
            List<MainProjectTeamGroup> mtgList = new List<MainProjectTeamGroup>();
            var all = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.MainProjectId == mainProject.ID && (mp.ObjectId == mainProject.ID || mp.ObjectId == Guid.Empty)).ToList();
            foreach (var mbr in mbrList)
            {
                var select = all.Where(mp => mp.GroupOrTeamMemberId == mbr.ID.ToString());
                if (!select.Any())
                {
                    MainProjectTeamGroup tur = new MainProjectTeamGroup();
                    tur.GroupOrTeamMemberId = mbr.ID.ToString();
                    tur.MainProjectId = mainProject.ID;
                    tur.MemberType = 1;
                    tur.ObjectId = mainProject.ID;
                    mtgList.Add(tur);
                }
            }

            _teamDbContext.MainProjectTeamGroups.AddRange(mtgList);
            _teamDbContext.SaveChanges();

            return true;
        }

        private bool DeleteMainProjectFiles(MainProject tpToDel)
        {
            var sb = new StringBuilder();
            var projectGuid = _teamDbContext.TeamProjects.Where(s => s.MainProjectID == tpToDel.ID.ToString()).ToList();

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {


                    if (null != tpToDel)
                    {
                        //if (projectGuids.Any())
                        //{
                        //    _teamDbContext.Database.ExecuteSqlCommand(deleteStr);
                        //}
                        foreach (var item in projectGuid)
                        {
                            DeleteProject(item);
                        }
                        var filelist = _teamDbContext.FileDirectories.Where(s => s.MainProjectId == tpToDel.ID).ToList();
                        if (filelist.Any())
                        {
                            _teamDbContext.FileDirectories.RemoveRange(filelist);
                        }
                        var volumelist = _teamDbContext.Volumes.Where(v => v.MainProjectId == tpToDel.ID).ToList();
                        if (volumelist.Any())
                        {
                            var volids = volumelist.Select(v => v.VolumeId).ToList();
                            var volverlist = _teamDbContext.VolumeVersions.Where(vv => volids.Contains(vv.VolumeId)).ToList();
                            if (volverlist.Any())
                            {
                                _teamDbContext.VolumeVersions.RemoveRange(volverlist);
                            }
                            _teamDbContext.Volumes.RemoveRange(volumelist);
                        }
                        var linkfiles = _teamDbContext.cloudlinkfiles.Where(c => c.SavePath == tpToDel.ID.ToString().ToLower()).ToList();
                        if (linkfiles.Any())
                        {
                            _teamDbContext.cloudlinkfiles.RemoveRange(linkfiles);
                        }
                        dbContextTransaction.Commit();
                        return true;
                    }


                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
            return false;
        }
        public bool DeleteMainProjectFiles(Guid id)
        {
            var selset = from tp in _teamDbContext.MainProjects where tp.ID == id select tp;
            return DeleteMainProjectFiles(selset.FirstOrDefault());
        }
        private bool DeleteMainProject(MainProject tpToDel)
        {
            if (null != tpToDel)
            {
                _teamDbContext.MainProjects.Remove(tpToDel);
                var mainprojectManager = GetMainProjectRepository(tpToDel.ID);
                if (mainprojectManager != null)
                {
                    Task.Run(() =>
                    {
                        mainprojectManager.Destroy();
                    });
                }
                _teamDbContext.SaveChanges();
                return true;

            }
            return false;
        }
        public bool DeleteMainProject(Guid id)
        {
            var selset = from tp in _teamDbContext.MainProjects where tp.ID == id select tp;
            return DeleteMainProject(selset.FirstOrDefault());
        }

        public bool AddFileDirectoreyList(List<FileDirectory> fileDicList)
        {
            _teamDbContext.FileDirectories.AddRange(fileDicList);
            _teamDbContext.SaveChanges();
            return true;
        }
        private FileDirectory DeleteFileDirectory(FileDirectory fdToDel)
        {
            if (null != fdToDel)
            {
                FileDirectory tp = _teamDbContext.FileDirectories.Remove(fdToDel).Entity;
                return tp;
            }
            return null;
        }
        public FileDirectory DeleteFileDirectory(Guid id)
        {
            var selset = from tp in _teamDbContext.FileDirectories where tp.ID == id select tp;
            return DeleteFileDirectory(selset.FirstOrDefault());
        }
        public bool UpdateFileDirectoryOrderNo(Guid dirGuid, string upOrDown)
        {
            if (upOrDown == "up")
            {
                var select = _teamDbContext.FileDirectories.Where(mp => mp.ID == dirGuid).FirstOrDefault();
                var perSelect = _teamDbContext.FileDirectories.Where(mp => mp.MainProjectId == select.MainProjectId && mp.ParentID == select.ParentID && mp.OrderNo < select.OrderNo).OrderByDescending(mp => mp.OrderNo).FirstOrDefault();
                if (perSelect != null)
                {
                    var selectorderno = select.OrderNo;
                    var perorderno = perSelect.OrderNo;
                    select.OrderNo = perorderno;
                    perSelect.OrderNo = selectorderno;
                    _teamDbContext.FileDirectories.Attach(select);
                    _teamDbContext.Entry(select).Property(a => a.OrderNo).IsModified = true;
                    //_teamDbContext.SaveChanges();
                    _teamDbContext.FileDirectories.Attach(perSelect);
                    _teamDbContext.Entry(perSelect).Property(a => a.OrderNo).IsModified = true;
                    _teamDbContext.SaveChanges();
                    return true;
                }

            }
            else
            {
                var select = _teamDbContext.FileDirectories.Where(mp => mp.ID == dirGuid).FirstOrDefault();
                var nextSelect = _teamDbContext.FileDirectories.Where(mp => mp.MainProjectId == select.MainProjectId && mp.ParentID == select.ParentID && mp.OrderNo > select.OrderNo).OrderBy(mp => mp.OrderNo).FirstOrDefault();
                if (nextSelect != null)
                {
                    var selectorderno = select.OrderNo;
                    var nextorderno = nextSelect.OrderNo;
                    select.OrderNo = nextorderno;
                    nextSelect.OrderNo = selectorderno;
                    _teamDbContext.FileDirectories.Attach(select);
                    _teamDbContext.Entry(select).Property(a => a.OrderNo).IsModified = true;
                    //_teamDbContext.SaveChanges();
                    _teamDbContext.FileDirectories.Attach(nextSelect);
                    _teamDbContext.Entry(nextSelect).Property(a => a.OrderNo).IsModified = true;
                    _teamDbContext.SaveChanges();
                    return true;
                }
            }

            return true;
        }
        public FileDirectory AddFileDirectorey(FileDirectory filedirectory)
        {
            var select = _teamDbContext.FileDirectories.AsNoTracking().Where(m=> m.Name == filedirectory.Name&&m.MainProjectId == filedirectory.MainProjectId);
            if (select.Any()) return null;
            //增加文件夹排序字段
            if (filedirectory.OrderNo == 0)
            {
                int maxOrder = 0;
                var maxFile = _teamDbContext.FileDirectories.Where(tp => tp.ParentID == filedirectory.ParentID).ToList();
                if (maxFile.Any())
                {
                    maxOrder = maxFile.Max(f => f.OrderNo);
                    maxOrder = maxOrder + 1;
                }
                filedirectory.OrderNo = maxOrder;
            }
            var x = _teamDbContext.FileDirectories.Add(filedirectory).Entity;
            _teamDbContext.SaveChanges();
            return x;
        }

        public bool UpdateMemberInfo(TeamMember member)
        {
            _teamDbContext.Update(member);
            return _teamDbContext.SaveChanges() == 1;
        }
        public bool CheckFileDirectoryName(FileDirectory filedir)
        {
            var select = _teamDbContext.FileDirectories.Where(tp => tp.Name.ToLower() == filedir.Name.ToLower() && tp.MainProjectId == filedir.MainProjectId && tp.ParentID == filedir.ParentID);
            if (select.Any())
            {
                return false;
            }
            return true;
        }

        public bool UpdateFileDirectoryInfo(FileDirectory directory)
        {
            _teamDbContext.Update(directory);
            return _teamDbContext.SaveChanges() == 1;
        }
        public bool UpdateMainprojectInfo(MainProject mainproject)
        {
            _teamDbContext.Update(mainproject);
            return _teamDbContext.SaveChanges() == 1;
        }

        public bool CheckMainProjectName(String mainPName)
        {
            var select = _teamDbContext.MainProjects.Where(tp => tp.Name.ToLower() == mainPName.ToLower());
            if (select.Any())
            {
                return false;
            }
            return true;
        }

        public bool CheckProjectName(String pName, string mainProjectId)
        {
            var select = _teamDbContext.TeamProjects.Where(tp => tp.Name.ToLower() == pName.ToLower() && tp.MainProjectID == mainProjectId);
            if (select.Any())
            {
                return false;
            }
            return true;
        }

        public bool UpdateMainProjectLastUpdateTime(Guid mGuid)
        {
            var old = _teamDbContext.MainProjects.FirstOrDefault(m => m.ID == mGuid);
            if (old == null)
            {
                return false;
            }

            old.LastUpdateTime = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            _teamDbContext.MainProjects.Attach(old);
            _teamDbContext.Entry(old).Property(a => a.LastUpdateTime).IsModified = true;
            _teamDbContext.SaveChanges();
            return true;
        }

        public IpMap AddIpMap(IpMap ipMap)
        {
            var select = _teamDbContext.IpMaps.Where(mp => mp.Id == ipMap.Id);
            if (select.Any())
            {
                return null;
            }
            select = _teamDbContext.IpMaps.Where(tp => tp.Address == ipMap.Address && tp.Ip == ipMap.Ip);
            if (select.Any())
            {
                return null;
            }

            ipMap = _teamDbContext.IpMaps.Add(ipMap).Entity;
            _teamDbContext.SaveChanges();
            return ipMap;
        }

        public IQueryable<IpMap> IpMaps
        {
            get
            {
                return _teamDbContext.IpMaps.AsNoTracking();
            }
        }

        public bool CheckIpAddress(string address)
        {
            var select = _teamDbContext.IpMaps.Where(tp => tp.Address == address);
            if (select.Any())
            {
                return false;
            }
            return true;
        }

        public bool UpdateIpMap(IpMap server)
        {
            var old = _teamDbContext.IpMaps.FirstOrDefault(m => m.Address == server.Address && m.Ip == server.Ip);
            if (old == null)
            {
                return false;
            }

            old.Ip = server.Ip;
            old.Port = server.Port;
            _teamDbContext.IpMaps.Attach(old);
            //_teamDbContext.Entry(old).Property(a => a.Ip).IsModified = true;
            _teamDbContext.Entry(old).Property(a => a.Port).IsModified = true;
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool RemoveMemberFromTeamGroup(TeamMember mbr, TeamGroup teamGroup)
        {
            var select = _teamDbContext.TeamUserGroups.Where(mp => mp.GroupId == teamGroup.Id && mp.TeamMemberId == mbr.ID);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _teamDbContext.TeamUserGroups.Remove(select.First());
                _teamDbContext.SaveChanges();
                return true;
            }
        }


        public bool AddMemberToTeamGroup(TeamMember mbr, TeamGroup teamGroup)
        {
            var select = _teamDbContext.TeamUserGroups.Where(mp => mp.GroupId == teamGroup.Id && mp.TeamMemberId == mbr.ID);
            if (select.Any())
            {
                return false;
            }
            TeamUserGroup tur = new TeamUserGroup();
            tur.GroupId = teamGroup.Id;
            tur.TeamMemberId = mbr.ID;
            _teamDbContext.TeamUserGroups.Add(tur);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool AddTeamGroupToMainproject(TeamGroup tg, MainProject mainProject, Guid objectId)
        {
            var select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.GroupOrTeamMemberId == tg.Id.ToString() && mp.MainProjectId == mainProject.ID && mp.ObjectId == objectId);
            if (select.Any())
            {
                return false;
            }
            MainProjectTeamGroup tur = new MainProjectTeamGroup();
            tur.GroupOrTeamMemberId = tg.Id.ToString();
            tur.MainProjectId = mainProject.ID;
            tur.ObjectId = objectId;
            tur.MemberType = 0;
            _teamDbContext.MainProjectTeamGroups.Add(tur);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool AddTeamGrouMemberToMainProject(TeamMember meb, MainProject mainProject, Guid objectId)
        {
            var select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.GroupOrTeamMemberId == meb.ID.ToString() && mp.MainProjectId == mainProject.ID && mp.ObjectId == objectId);
            if (select.Any())
            {
                return false;
            }
            MainProjectTeamGroup tur = new MainProjectTeamGroup();
            tur.GroupOrTeamMemberId = meb.ID.ToString();
            tur.MainProjectId = mainProject.ID;
            tur.MemberType = 1;
            tur.ObjectId = objectId;
            _teamDbContext.MainProjectTeamGroups.Add(tur);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool DeleteTeamGroupAuth(TeamGroupAuth teamGroupAuth)
        {
            var select = _teamDbContext.TeamGroupAuths.Where(t => t.GroupOrMemberId == teamGroupAuth.GroupOrMemberId && t.ObjectId == teamGroupAuth.ObjectId);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _teamDbContext.TeamGroupAuths.RemoveRange(select);
                _teamDbContext.SaveChanges();
                return true;
            }
        }
        public bool DeleteTeamGroupAuth(Guid objectGuid, string groupormemberid)
        {
            var select = _teamDbContext.TeamGroupAuths.Where(t => t.GroupOrMemberId == groupormemberid && t.ObjectId == objectGuid);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _teamDbContext.TeamGroupAuths.RemoveRange(select);
                _teamDbContext.SaveChanges();
                return true;
            }
        }

        public bool GiveAuthToTeamGroup(TeamGroupAuth teamGroupAuth)
        {
            _teamDbContext.TeamGroupAuths.Add(teamGroupAuth);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool GiveAuthToTeamGroup(TeamGroupAuth teamGroupAuth, List<TeamAuth> authList)
        {
            var select = _teamDbContext.TeamGroupAuths.Where(t => t.GroupOrMemberId == teamGroupAuth.GroupOrMemberId && t.ObjectId == teamGroupAuth.ObjectId && t.MainProjectId == teamGroupAuth.MainProjectId).FirstOrDefault();
            if (select != null)
            {
                List<TeamAuth> curAuth = System.Text.Json.JsonSerializer.Deserialize<List<TeamAuth>>(select.AuthInfo);//JsonSerializer.Deserialize<List<TeamAuth>>(select.AuthInfo);
                foreach (var au in authList)
                {
                    var toChange = curAuth.FirstOrDefault(x => x.AuthName == au.AuthName);
                    if (toChange != null)
                    {
                        toChange.Permission = au.Permission;
                    }
                    else
                    {
                        curAuth.Add(au);
                    }
                }
                select.AuthInfo = JsonSerializer.Serialize(curAuth);
                _teamDbContext.TeamGroupAuths.Attach(select);
                _teamDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;
                _teamDbContext.SaveChanges();
                return true;
            }
            else
            {
                teamGroupAuth.AuthInfo = System.Text.Json.JsonSerializer.Serialize(authList);
                _teamDbContext.TeamGroupAuths.Add(teamGroupAuth);
                _teamDbContext.SaveChanges();
                return true;
            }

        }
        public bool DeleteGroupOrMemberListFromMainProject(Guid mainprojectGuid, Guid objectGuid, List<string> groupormemberids)
        {
            var select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.MainProjectId == mainprojectGuid && mp.ObjectId == objectGuid && groupormemberids.Contains(mp.GroupOrTeamMemberId));
            if (objectGuid == mainprojectGuid)
            {
                select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.MainProjectId == mainprojectGuid && (mp.ObjectId == objectGuid || mp.ObjectId == Guid.Empty) && groupormemberids.Contains(mp.GroupOrTeamMemberId));
            }
            if (!select.Any())
            {
                return true;
            }
            _teamDbContext.MainProjectTeamGroups.RemoveRange(select);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool DeleteGroupOrMemberFromMainProject(Guid mainprojectGuid, Guid objectGuid, string groupormemberid)
        {
            var select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.GroupOrTeamMemberId == groupormemberid && mp.MainProjectId == mainprojectGuid && mp.ObjectId == objectGuid);
            if (objectGuid == mainprojectGuid)
            {
                select = _teamDbContext.MainProjectTeamGroups.Where(mp => mp.GroupOrTeamMemberId == groupormemberid && mp.MainProjectId == mainprojectGuid && (mp.ObjectId == objectGuid || mp.ObjectId == Guid.Empty));
            }
            if (!select.Any())
            {
                return true;
            }
            _teamDbContext.MainProjectTeamGroups.Remove(select.First());
            _teamDbContext.SaveChanges();
            return true;
        }

        public IQueryable<TeamUserGroup> TeamUserGroups
        {
            get
            {
                return _teamDbContext.TeamUserGroups.AsNoTracking();
            }
        }

        public IQueryable<TeamGroup> TeamGroups
        {
            get
            {
                return _teamDbContext.TeamGroups.AsNoTracking();
            }
        }

        public TeamGroup AddTeamGroup(TeamGroup teamGroup)
        {
            var select = _teamDbContext.TeamGroups.Where(mp => mp.GroupName == teamGroup.GroupName);
            if (select.Any())
            {
                return null;
            }
            TeamGroup newGroup = new TeamGroup
            {

                GroupName = teamGroup.GroupName,
                Description = teamGroup.Description

            };
            teamGroup = _teamDbContext.TeamGroups.Add(newGroup).Entity;
            _teamDbContext.SaveChanges();
            return teamGroup;
        }

        public bool UpdateTeamGroup(TeamGroup teamGroup)
        {
            _teamDbContext.Update(teamGroup);

            return _teamDbContext.SaveChanges() == 1;
        }

        public bool DeleteTeamGroup(int id)
        {
            var oldTeamGroup = _teamDbContext.TeamGroups.Where(t => t.Id == id).FirstOrDefault();
            if (oldTeamGroup!=null)
            {
                var oldTeamGroupUser = _teamDbContext.TeamUserGroups.Where(t => t.GroupId == id).ToList();
                var oldTeamGroupAuth = _teamDbContext.TeamGroupAuths.Where(t => t.GroupOrMemberId == id.ToString()).ToList();
                var oldMainprojectGroups = _teamDbContext.MainProjectTeamGroups.Where(t => t.GroupOrTeamMemberId == id.ToString()).ToList();
                _teamDbContext.TeamGroups.Remove(oldTeamGroup);
                _teamDbContext.TeamUserGroups.RemoveRange(oldTeamGroupUser);
                _teamDbContext.TeamGroupAuths.RemoveRange(oldTeamGroupAuth);
                _teamDbContext.MainProjectTeamGroups.RemoveRange(oldMainprojectGroups);
                _teamDbContext.SaveChanges();
            }
            return true;
        }

        public IQueryable<TeamAuth> TeamAuths
        {
            get
            {
                return _teamDbContext.TeamAuths.AsNoTracking();
            }
        }
        public IQueryable<MainProjectTeamGroup> MainProjectTeamGroups
        {
            get
            {
                return _teamDbContext.MainProjectTeamGroups.AsNoTracking();
            }
        }

        public IQueryable<TeamGroupAuth> TeamGroupAuths
        {
            get
            {
                return _teamDbContext.TeamGroupAuths.AsNoTracking();
            }
        }

        public IQueryable<Volume> Volumes
        {
            get
            {
                return _teamDbContext.Volumes.AsNoTracking();
            }
        }

        public bool CreateMPdataBase(Guid mainProjectGuid)
        {
            
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("CREATE DATABASE `PKPM-PBIMServer-MPDB-" + mainProjectGuid + "` DEFAULT CHARACTER SET UTF8;");
            var deleteStr = sb.ToString();
            _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool CreateModeldataBase(Guid projectGuid)
        {
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("CREATE DATABASE `PKPM-PBIMServer-ModelDB-" + projectGuid + "` DEFAULT CHARACTER SET UTF8;");
            var deleteStr = sb.ToString();
            _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool UpdateModelFileInfo(Guid projectGuid, Guid newProjectGuid, string newPath)
        {
            var newTP = _teamDbContext.TeamProjects.FirstOrDefault(vd => vd.ID == newProjectGuid);
            if (newTP == null)
            {
                return false;
            }
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-modeldb-" + newProjectGuid + "`.modelfiles set " +
                "FilePath = CONCAT('" + newPath + "',SUBSTRING_INDEX( filepath, '" + projectGuid + "', -1))," +
                "FilePath=REPLACE(FilePath, '\\\\', \"" + separator+"\") where LOCATE('" + projectGuid + "', filepath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }

        }

        public bool ClearLockedComponents(Guid newProjectGuid)
        {
            var deleteStr = "TRUNCATE TABLE `pkpm-pbimserver-modeldb-" + newProjectGuid + "`.lockedcomponents;";

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool ClearLockModelfiles(Guid newProjectGuid)
        {
            var deleteStr = "TRUNCATE TABLE `pkpm-pbimserver-modeldb-" + newProjectGuid + "`.modellocks;";

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMileStoneFileInfo(Guid projectGuid, string oldMainprojectName, Guid newProjectGuid, string newPath)
        {
            var newTP = _teamDbContext.TeamProjects.FirstOrDefault(vd => vd.ID == newProjectGuid);
            if (newTP == null)
            {
                return false;
            }
            var sb = new StringBuilder();
            //var values = string.Join(",", oldTP.Select(s => s.ID));

            sb.Append("UPDATE `pkpm-pbimserver-modeldb-" + newProjectGuid + "`.milestonefiles set SavePath = CONCAT('" + newPath + "',SUBSTRING_INDEX( SavePath, '" + oldMainprojectName + "', -1)),SavePath=REPLACE(SavePath, '\\\\', \"" + separator+"\") where LOCATE('" + oldMainprojectName + "', SavePath) > 0");

            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public IQueryable<TeamProjectModelLock> TeamProjectModelLocks
        {
            get
            {
                return _teamDbContext.TeamProjectModelLocks.AsNoTracking();
            }
        }

        public Volume AddVolume(Volume vol)
        {
            var select = _teamDbContext.Volumes.Where(mp => mp.VolumeId == vol.VolumeId);
            if (select.Any())
            {
                throw new Exception("服务器已存在相同的卷册ID\n请尝试使用其它卷册ID！");
            }
            select = _teamDbContext.Volumes.Where(tp => tp.VolumeName.ToLower() == vol.VolumeName.ToLower() && tp.MainProjectId == vol.MainProjectId && tp.FileDirectoryId == vol.FileDirectoryId);
            if (select.Any())
            {
                throw new Exception("该项目下服务器已存在同名卷册\n请尝试修改卷册图档名称");
            }
            //mainproject.CreationTime = DateTime.Now;
            var x = _teamDbContext.Volumes.Add(vol).Entity;
            _teamDbContext.SaveChanges();
            return x;
        }

        public bool AddVolumeList(List<Volume> volList)
        {
            _teamDbContext.Volumes.AddRange(volList);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool DeleteVolume(Guid volGuid)
        {
            var select = _teamDbContext.Volumes.Where(t => t.VolumeId == volGuid);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _teamDbContext.Volumes.RemoveRange(select);
                _teamDbContext.SaveChanges();
                return true;
            }
        }

        public IQueryable<VolumeVersion> VolumeVersions
        {
            get
            {
                return _teamDbContext.VolumeVersions.AsNoTracking();
            }
        }

        public bool AddVolumeVersion(VolumeVersion volVer)
        {
            if (_teamDbContext.VolumeVersions.Any(s => s.VolumeId == volVer.VolumeId && s.VerNo == volVer.VerNo))
                return false;
            _teamDbContext.VolumeVersions.Add(volVer);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool AddVolumeVersionList(List<VolumeVersion> volVerList)
        {
            _teamDbContext.VolumeVersions.AddRange(volVerList);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool AddTeamProjectModelLock(TeamProjectModelLock teamProjectModelLock)
        {
            var select = _teamDbContext.TeamProjectModelLocks.FirstOrDefault(s => s.ProjectGuid == teamProjectModelLock.ProjectGuid);
            if (select != null)
            {
                select.ModelLockUser = teamProjectModelLock.ModelLockUser;
                _teamDbContext.TeamProjectModelLocks.Attach(select);
                _teamDbContext.Entry(select).Property(a => a.ModelLockUser).IsModified = true;
                _teamDbContext.SaveChanges();
                return true;
            }
            else
            {
                _teamDbContext.TeamProjectModelLocks.Add(teamProjectModelLock);
                _teamDbContext.SaveChanges();
                return true;
            }



        }
        void getTreeList(Guid dirId, ref List<TeamProject> treeList)
        {
            List<FileDirectory> dt = _teamDbContext.FileDirectories.Where(f => f.ParentID == dirId).ToList();
            foreach (var dr in dt)
            {

                var tp = _teamDbContext.TeamProjects.Where(t => (!string.IsNullOrEmpty(t.FileDirectoryID)) && t.FileDirectoryID.ToLower() == dr.ID.ToString().ToLower()).ToList();
                foreach (var item in tp)
                {
                    treeList.Add(item);
                }

                getTreeList(dr.ID, ref treeList);
            }
        }
        public bool DeleteTeamProjectModelLock(string lockuser, Guid projectGuid)
        {
            var delModelLock = _teamDbContext.TeamProjectModelLocks.Where(vd => vd.ProjectGuid == projectGuid);
            if (!delModelLock.Any())
            {
                return true;
            }
            var sb = new StringBuilder();
            var values = string.Join(",", delModelLock.Select(s => s.Id));

            sb.Append("UPDATE `teamprojectmodellocks`  SET ModelLockUser = ''  WHERE ID in (");
            sb.Append(values);
            sb.Append(")");
            var deleteStr = sb.ToString();

            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(deleteStr);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }

        }

        //获取用户参与的mainproject列表
        public List<MainProject> GetMainProjectListByMember(Guid memberGuid)
        {
            List<MainProject> mainProjectList = new List<MainProject>();
            //查询用户所参与的teamgroup
            var teamuserGroups = _teamDbContext.TeamUserGroups.Where(g => g.TeamMemberId == memberGuid).ToList();

            //查询当前用户参与的teamgroupid
            var groupids = new List<int>();
            if (teamuserGroups.Any())
            {
                groupids = teamuserGroups.Select(s => s.GroupId).ToList();
            }

            List<string> groupormemberids = new List<string>();
            groupormemberids.Add(memberGuid.ToString());
            if (groupids.Any())
            {
                foreach (var g in groupids)
                {
                    groupormemberids.Add(g.ToString());
                }
            }
            //查询teamgroupids和memberguid参与的mainproject
            var mainprojectTeamGroups = _teamDbContext.MainProjectTeamGroups.Where(m => groupormemberids.Contains(m.GroupOrTeamMemberId)).ToList();

            var allDicMain = _teamDbContext.MainProjects.AsNoTracking().ToDictionary(g => g.ID, g => g);
            Console.WriteLine("allDicMain============>"+ allDicMain.Count);
            if (mainprojectTeamGroups.Any())
            {
                var mpids = mainprojectTeamGroups.Select(s => s.MainProjectId).Distinct().ToList();
                foreach (var mpid in mpids)
                {
                    MainProject mainproject = new MainProject();
                    //筛选出objectid为mainprojectid的数据（参与的项目）
                    allDicMain.TryGetValue(mpid, out mainproject);//_teamDbContext.MainProjects.FirstOrDefault(m => m.ID == mpid);
                    if (mainproject != null)
                    {
                        mainProjectList.Add(mainproject);
                    }
                }
            }


            return mainProjectList;
        }

        public List<TeamProject> GetTeamProjectListByMemberAndMainprojectId(Guid memberGuid, Guid mainprojectId)
        {
            List<TeamProject> teamProjectList = new List<TeamProject>();
            //查询用户所参与的teamgroup
            var teamuserGroups = _teamDbContext.TeamUserGroups.Where(g => g.TeamMemberId == memberGuid).ToList();

            //查询当前用户参与的teamgroupid
            var groupids = new List<int>();
            if (teamuserGroups.Any())
            {
                groupids = teamuserGroups.Select(s => s.GroupId).ToList();
            }

            List<string> groupormemberids = new List<string>();
            groupormemberids.Add(memberGuid.ToString());
            if (groupids.Any())
            {
                foreach (var g in groupids)
                {
                    groupormemberids.Add(g.ToString());
                }
            }
            var allprojectlist = _teamDbContext.TeamProjects.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && t.MainProjectID.ToLower() == mainprojectId.ToString()).ToList();
            var allfiledirlist = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainprojectId).ToList();
            //查询teamgroupids和memberguid参与的mainproject
            var mainprojectTeamGroups = _teamDbContext.MainProjectTeamGroups.Where(m => groupormemberids.Contains(m.GroupOrTeamMemberId) && m.MainProjectId == mainprojectId).ToList();
            if (mainprojectTeamGroups.Any())
            {
                var realMP = mainprojectTeamGroups.Where(m => m.MainProjectId == mainprojectId && (m.ObjectId == Guid.Empty || m.ObjectId == mainprojectId)).FirstOrDefault();
                if (realMP != null)
                {
                    //var mpTPList = allprojectlist.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && t.MainProjectID.ToLower() == mpid.ToString().ToLower()).ToList();
                    if (allprojectlist.Any())
                    {
                        foreach (var tp in allprojectlist)
                        {
                            if (!teamProjectList.Any(t => t.ID == tp.ID))
                            {
                                teamProjectList.Add(tp);
                            }

                        }
                    }

                }
                else
                {
                    var realObject = mainprojectTeamGroups.Where(m => m.MainProjectId == mainprojectId && m.ObjectId != Guid.Empty && m.ObjectId != mainprojectId).ToList();
                    foreach (var ob in realObject)
                    {
                        var filedir = allfiledirlist.Where(f => f.ID == ob.ObjectId).FirstOrDefault();
                        var objectTP = allprojectlist.Where(t => t.ID == ob.ObjectId).FirstOrDefault();
                        if (filedir != null)
                        {
                            //objectid是文件夹，递归获取该文件夹下的所有teamproject
                            var tp = allprojectlist.Where(t => (!string.IsNullOrEmpty(t.FileDirectoryID)) && t.FileDirectoryID.ToLower() == filedir.ID.ToString().ToLower()).ToList();
                            foreach (var item in tp)
                            {
                                if (!teamProjectList.Any(t => t.ID == item.ID))
                                {
                                    teamProjectList.Add(item);
                                }
                            }

                            getTreeList(filedir.ID, ref teamProjectList);
                        }
                        else if (objectTP != null)
                        {
                            if (!teamProjectList.Any(t => t.ID == objectTP.ID))
                            {
                                teamProjectList.Add(objectTP);
                            }
                        }
                    }
                }
            }
            return teamProjectList;
        }

        public List<TeamProject> GetTeamProjectListByMemberForPDMS(Guid memberGuid)
        {
            List<TeamProject> teamProjectList = new List<TeamProject>();
            var mpusergroupmembers = _teamDbContext.MainProjectUserGroupMembers.Where(s => s.TeamMemberId == memberGuid).ToList();
            if (mpusergroupmembers.Any())
            {
                var mplist = mpusergroupmembers.Select(s => s.MainProjectId.ToString().ToLower()).Distinct().ToList();
                teamProjectList = _teamDbContext.TeamProjects.Where(p => mplist.Contains(p.MainProjectID)).ToList();
            }
            return teamProjectList;
        }

        public List<TeamProject> GetTeamProjectListByMember(Guid memberGuid)
        {
            List<TeamProject> teamProjectList = new List<TeamProject>();
            //查询用户所参与的teamgroup
            var teamuserGroups = _teamDbContext.TeamUserGroups.Where(g => g.TeamMemberId == memberGuid).ToList();

            //查询当前用户参与的teamgroupid
            var groupids = new List<int>();
            if (teamuserGroups.Any())
            {
                groupids = teamuserGroups.Select(s => s.GroupId).ToList();
            }

            List<string> groupormemberids = new List<string>();
            groupormemberids.Add(memberGuid.ToString());
            if (groupids.Any())
            {
                foreach (var g in groupids)
                {
                    groupormemberids.Add(g.ToString());
                }
            }
            var allprojectlist = _teamDbContext.TeamProjects.AsNoTracking().ToList();
            var allfiledirlist = _teamDbContext.FileDirectories.ToList();
            //查询teamgroupids和memberguid参与的mainproject
            var mainprojectTeamGroups = _teamDbContext.MainProjectTeamGroups.Where(m => groupormemberids.Contains(m.GroupOrTeamMemberId)).ToList();
            if (mainprojectTeamGroups.Any())
            {
                var mpids = mainprojectTeamGroups.Select(s => s.MainProjectId).Distinct().ToList();
                foreach (var mpid in mpids)
                {
                    //筛选出objectid为mainprojectid的数据（参与的项目）
                    var realMP = mainprojectTeamGroups.Where(m => m.MainProjectId == mpid && (m.ObjectId == Guid.Empty || m.ObjectId == mpid)).FirstOrDefault();
                    if (realMP != null)
                    {
                        var mpTPList = allprojectlist.Where(t => (!string.IsNullOrEmpty(t.MainProjectID)) && t.MainProjectID.ToLower() == mpid.ToString().ToLower()).ToList();
                        if (mpTPList.Any())
                        {
                            foreach (var tp in mpTPList)
                            {
                                if (!teamProjectList.Any(t => t.ID == tp.ID))
                                {
                                    teamProjectList.Add(tp);
                                }

                            }
                        }

                    }
                    else
                    {
                        var realObject = mainprojectTeamGroups.Where(m => m.MainProjectId == mpid && m.ObjectId != Guid.Empty && m.ObjectId != mpid).ToList();
                        foreach (var ob in realObject)
                        {
                            var filedir = allfiledirlist.Where(f => f.ID == ob.ObjectId).FirstOrDefault();
                            var objectTP = allprojectlist.Where(t => t.ID == ob.ObjectId).FirstOrDefault();
                            if (filedir != null)
                            {
                                //objectid是文件夹，递归获取该文件夹下的所有teamproject
                                var tp = allprojectlist.Where(t => (!string.IsNullOrEmpty(t.FileDirectoryID)) && t.FileDirectoryID.ToLower() == filedir.ID.ToString().ToLower()).ToList();
                                foreach (var item in tp)
                                {
                                    if (!teamProjectList.Any(t => t.ID == item.ID))
                                    {
                                        teamProjectList.Add(item);
                                    }
                                }

                                getTreeList(filedir.ID, ref teamProjectList);
                            }
                            else if (objectTP != null)
                            {
                                if (!teamProjectList.Any(t => t.ID == objectTP.ID))
                                {
                                    teamProjectList.Add(objectTP);
                                }
                            }
                        }
                    }

                }
            }

            //List<TeamProject> teamProjectList = new List<TeamProject>();
            var mpusergroupmembers = _teamDbContext.MainProjectUserGroupMembers.Where(s => s.TeamMemberId == memberGuid).ToList();
            if (mpusergroupmembers.Any())
            {
                var mplist = mpusergroupmembers.Select(s => s.MainProjectId.ToString().ToLower()).Distinct().ToList();
                var templist = _teamDbContext.TeamProjects.Where(p => mplist.Contains(p.MainProjectID)).ToList();
                if (templist.Any())
                {
                    foreach (var item in templist)
                    {
                        if (!teamProjectList.Any(t => t.ID == item.ID))
                        {
                            teamProjectList.Add(item);
                        }
                    }
                }
            }
            return teamProjectList;
            //return teamProjectList;
        }
        public List<TeamMember> GetTeamMembersByMainProject(Guid mainProjectGuid)
        {
            //从项目用户关联表中获取所有数据
            var allMainProjectTeamGroups = _teamDbContext.MainProjectTeamGroups.ToList();
            //获取当前项目的用户组列表 MemberType = 1为用户
            var currentMainProjectTeamMembers = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 1).ToList();
            var currentMainProjectTeamGroups = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 0).ToList();
            List<TeamMember> members = new List<TeamMember>();
            if (currentMainProjectTeamMembers != null && currentMainProjectTeamMembers.Any())
            {


                //获取用户组id，去重
                var currentTeamMemberIds = currentMainProjectTeamMembers.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamMemberIds != null && currentTeamMemberIds.Any())
                {
                    var intCurrentTeamGroupIds = currentTeamMemberIds.Select<string, Guid>(x => Guid.Parse(x)).ToList();
                    var retTeamGroups = _teamDbContext.TeamMembers.Where(t => intCurrentTeamGroupIds.Contains(t.ID)).ToList();
                    retTeamGroups.RemoveAll(mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));
                    //return CommonTool.CopyToObject(retTeamGroups);
                    members.AddRange(retTeamGroups);
                }
            }
            if (currentMainProjectTeamGroups != null && currentMainProjectTeamGroups.Any())
            {
                var currentTeamGroupIds = currentMainProjectTeamGroups.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamGroupIds != null && currentTeamGroupIds.Any())
                {
                    var intlist = currentTeamGroupIds.Select(a => int.Parse(a)).ToList();
                    var GroupMembers = _teamDbContext.TeamUserGroups.Where(t => intlist.Contains(t.GroupId)).ToList();
                    foreach (var item in GroupMembers)
                    {
                        var tem = members.FirstOrDefault(t => t.ID == item.TeamMemberId);
                        if (tem == null)
                        {
                            var teammem = _teamDbContext.TeamMembers.Where(t => t.ID == item.TeamMemberId).FirstOrDefault();
                            if (teammem != null)
                            {
                                members.Add(teammem);
                            }
                        }
                    }
                }
            }

            var mpusergroupmembers = _teamDbContext.MainProjectUserGroupMembers.Where(s => s.MainProjectId == mainProjectGuid).ToList();
            if (mpusergroupmembers.Any())
            {
                var mplist = mpusergroupmembers.Select(s => s.TeamMemberId).Distinct().ToList();
                var templist = _teamDbContext.TeamMembers.Where(p => mplist.Contains(p.ID)).ToList();
                if (templist.Any())
                {
                    foreach (var item in templist)
                    {
                        if (!members.Any(t => t.ID == item.ID))
                        {
                            members.Add(item);
                        }
                    }
                }
            }

            members.RemoveAll(mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));
            //return CommonTool.CopyToObject(members);
            return members;
        }

        /// <summary>
        /// 判断权限
        /// </summary>
        /// <param name="objectid">操作对象id：项目guid\文件夹guid\模型guid</param>
        /// <param name="mainProjectId">项目Guid</param>
        /// <param name="objecttype">权限对象类型 1：项目 2：文件夹 3：模型</param>
        /// <returns></returns>
        public List<TeamAuth> GetAuthByObjectId(Guid objectid, Guid mainProjectId, int objecttype, string username)
        {
            //查询当前用户对于objectid对象及祖辈节点的权限列表
            //PbimLog log = new PbimLog("Info");

            try
            {
                var cutUser = _teamDbContext.TeamMembers.FirstOrDefault(m => m.LoginName == username);
                Guid teammemberid = cutUser.ID;
                var teamGroupAuths = _teamDbContext.TeamGroupAuths.ToList();
                var allMainProjectTeamGroups = _teamDbContext.MainProjectTeamGroups.ToList();
                List<TeamAuth> retList = new List<TeamAuth>();
                List<TeamAuth> allAuthList = new List<TeamAuth>();
                var currentMainProjectTeamGroups = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectId).ToList();
                //查询当前用户参与的teamgroup
                var teamuserGroups = _teamDbContext.TeamUserGroups.Where(g => g.TeamMemberId == teammemberid).ToList();
                //查询当前用户参与的teamgroupid
                var groupids = new List<int>();
                if (teamuserGroups.Any())
                {
                    groupids = teamuserGroups.Select(s => s.GroupId).ToList();
                }
                List<Guid> objIds = new List<Guid>();
                objIds.Add(mainProjectId);
                if (objecttype == 1)
                {

                    //权限对象为项目，只需直接查找mainprojectid的权限列表即可
                    var objectGroupAuth = teamGroupAuths.Where(a => a.ObjectId == objectid && a.GroupOrMemberId == teammemberid.ToString()).FirstOrDefault();
                    if (objectGroupAuth != null)
                    {
                        List<TeamAuth> authList = JsonSerializer.Deserialize<List<TeamAuth>>(objectGroupAuth.AuthInfo);
                        if (authList.Any())
                        {
                            allAuthList.AddRange(authList);
                        }
                    }


                    if (groupids.Any())
                    {
                        //groupids具有的权限列表
                        foreach (var gid in groupids)
                        {
                            var groupauth = teamGroupAuths.Where(a => a.ObjectId == objectid && a.GroupOrMemberId == gid.ToString()).FirstOrDefault();
                            if (groupauth != null)
                            {
                                List<TeamAuth> gauth = JsonSerializer.Deserialize<List<TeamAuth>>(groupauth.AuthInfo);
                                if (gauth.Any())
                                {
                                    allAuthList.AddRange(gauth);
                                }
                            }

                        }
                    }

                }
                else if (objecttype == 2)
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainProjectId).ToList();

                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectid);
                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }

                    //权限列表，权限对象id为所有父辈节点id
                    var objectGroupAuth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == teammemberid.ToString()).ToList();
                    foreach (var ob in objectGroupAuth)
                    {
                        List<TeamAuth> authList = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                        if (authList.Any())
                        {
                            allAuthList.AddRange(authList);
                        }
                    }

                    if (groupids.Any())
                    {
                        //groupids具有的权限列表
                        foreach (var gid in groupids)
                        {
                            var groupauth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == gid.ToString()).ToList();
                            foreach (var ob in groupauth)
                            {
                                List<TeamAuth> gauth = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                                if (gauth.Any())
                                {
                                    allAuthList.AddRange(gauth);
                                }
                            }
                        }
                    }
                }
                else
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainProjectId).ToList();
                    //var teamproject = _teamDbContext.Projects.FirstOrDefault(t => t.ID == objectid);



                    //log.Info("GetAuthByObectId=======查询上级文件夹id:");
                    Guid fileGuid = Guid.Empty;
                    if (objecttype == 3)
                    {
                        var teamproject = _teamDbContext.TeamProjects.FirstOrDefault(t => t.ID == objectid);

                        if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
                        {
                            fileGuid = Guid.Parse(teamproject.FileDirectoryID);
                        }
                    }
                    else if (objecttype == 4)
                    {
                        var volume = _teamDbContext.Volumes.FirstOrDefault(t => t.VolumeId == objectid);
                        if (volume != null)
                        {
                            fileGuid = volume.FileDirectoryId;
                        }
                    }

                    objIds.Add(mainProjectId);
                    objIds.Add(objectid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);


                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }

                    //权限列表，权限对象id为所有父辈节点id
                    var objectGroupAuth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == teammemberid.ToString()).ToList();
                    foreach (var ob in objectGroupAuth)
                    {
                        List<TeamAuth> authList = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                        if (authList.Any())
                        {
                            allAuthList.AddRange(authList);
                        }
                    }
                }







                List<string> gIds = new List<string>();
                if (groupids.Any())
                {
                    //groupids具有的权限列表
                    foreach (var gid in groupids)
                    {
                        var groupauth = teamGroupAuths.Where(a => objIds.Contains(a.ObjectId) && a.GroupOrMemberId == gid.ToString()).ToList();
                        foreach (var ob in groupauth)
                        {
                            List<TeamAuth> gauth = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                            if (gauth.Any())
                            {
                                allAuthList.AddRange(gauth);
                            }
                        }
                        gIds.Add(gid.ToString());
                    }
                }



                currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => (t.GroupOrTeamMemberId == teammemberid.ToString() || gIds.Contains(t.GroupOrTeamMemberId)) && (objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty)).ToList();

                if (currentMainProjectTeamGroups != null && currentMainProjectTeamGroups.Any())
                {
                    List<string> authNameList = allAuthList.Select(s => s.AuthName).Distinct().ToList();
                    foreach (var authName in authNameList)
                    {
                        TeamAuth ta = new TeamAuth();
                        ta.AuthName = authName;
                        var noAuth = allAuthList.Where(a => a.AuthName == authName && a.Permission == 0).ToList();
                        if (noAuth.Any())
                        {
                            ta.Permission = 0;
                        }
                        else
                        {
                            ta.Permission = 1;
                        }
                        retList.Add(ta);
                    }
                    return retList;
                }
                else
                {
                    return null;
                }


            }
            catch (Exception ex)
            {
                //ExceptionHandler.HandleException(username, ex);
            }
            return null;
        }

        public bool GetEditModelAuth(Guid objectid, Guid mainProjectId, string username)
        {
            try
            {
                var cutUser = _teamDbContext.TeamMembers.FirstOrDefault(m => m.LoginName == username);
                Guid teammemberid = cutUser.ID;
                var teamGroupAuths = _teamDbContext.TeamGroupAuths.ToList();
                var allMainProjectTeamGroups = _teamDbContext.MainProjectTeamGroups.ToList();
                List<TeamAuth> retList = new List<TeamAuth>();
                List<TeamAuth> allAuthList = new List<TeamAuth>();
                var currentMainProjectTeamGroups = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectId).ToList();
                //查询当前用户参与的teamgroup
                var teamuserGroups = _teamDbContext.TeamUserGroups.Where(g => g.TeamMemberId == teammemberid).ToList();
                //查询当前用户参与的teamgroupid
                var groupids = new List<int>();
                if (teamuserGroups.Any())
                {
                    groupids = teamuserGroups.Select(s => s.GroupId).ToList();
                }
                List<Guid> objIds = new List<Guid>();
                objIds.Add(mainProjectId);
                objIds.Add(objectid);
                Guid fileGuid = Guid.Empty;
                var teamproject = _teamDbContext.TeamProjects.FirstOrDefault(t => t.ID == objectid);

                if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
                {
                    fileGuid = Guid.Parse(teamproject.FileDirectoryID);
                }
                var allFileDirectories = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainProjectId).ToList();
                var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);


                if (objectFileDir != null)
                {
                    objIds.Add(objectFileDir.ID);

                    while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                    {
                        objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                        if (objectFileDir != null)
                        {
                            objIds.Add(objectFileDir.ID);
                        }

                    }
                }
                List<string> gIds = new List<string>();
                if (groupids.Any())
                {
                    //groupids具有的权限列表
                    foreach (var gid in groupids)
                    {
                        gIds.Add(gid.ToString());
                    }
                }
                currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t =>
                    (t.GroupOrTeamMemberId == teammemberid.ToString() || gIds.Contains(t.GroupOrTeamMemberId))
                    &&
                    t.MainProjectId == mainProjectId && objIds.Contains(t.ObjectId)).ToList();

                if (currentMainProjectTeamGroups != null && currentMainProjectTeamGroups.Any())
                {
                    var objectGroupAuth = teamGroupAuths.Where(a => a.ObjectId == objectid && (a.GroupOrMemberId == teammemberid.ToString() || gIds.Contains(a.GroupOrMemberId))).ToList();
                    foreach (var ob in objectGroupAuth)
                    {
                        List<TeamAuth> authList = JsonSerializer.Deserialize<List<TeamAuth>>(ob.AuthInfo);
                        if (authList.Any(a => a.AuthName == "editmodel" && a.Permission == 0))
                        {
                            //log.Info("GetEditModelAuth===>" + username + "用户对模型" + teamproject.Name + "无编辑锁定权限;模型id：" + teamproject.ID);
                            return false;
                        }
                    }
                    return true;
                }
                else
                {
                    //log.Info("GetEditModelAuth===>" + username + "用户未参与模型" + teamproject.Name + "；模型id：" + teamproject.ID);
                    return false;
                }

            }
            catch (Exception ex)
            {
                //ExceptionHandler.HandleException(username, ex);
            }
            return false;
        }


        public List<TeamMember> GetTeamMembersByObjectId(Guid mainProjectGuid, Guid objectGuid, int objecttype)
        {
            //从项目用户关联表中获取所有数据
            var allMainProjectTeamGroups = _teamDbContext.MainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && (t.MemberType == 1 || t.MemberType == 0));
            //获取当前项目的用户组列表 MemberType = 1为用户
            var currentMainProjectTeamMembers = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 1).ToList();
            var currentMainProjectTeamGroups = allMainProjectTeamGroups.Where(t => t.MainProjectId == mainProjectGuid && t.MemberType == 0).ToList();
            if (currentMainProjectTeamMembers != null && currentMainProjectTeamMembers.Any())
            {
                if (objecttype == 1)
                {
                    currentMainProjectTeamMembers = currentMainProjectTeamMembers.Where(t => t.ObjectId == objectGuid || t.ObjectId == Guid.Empty).ToList();
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => t.ObjectId == objectGuid || t.ObjectId == Guid.Empty).ToList();
                }
                else if (objecttype == 2)
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectGuid);
                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamMembers = currentMainProjectTeamMembers.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                else
                {
                    //获取文件夹的父辈文件夹节点
                    var allFileDirectories = _teamDbContext.FileDirectories.Where(f => f.MainProjectId == mainProjectGuid).ToList();
                    Guid fileGuid = Guid.Empty;
                    if (objecttype == 3)
                    {
                        var teamproject = _teamDbContext.TeamProjects.FirstOrDefault(t => t.ID == objectGuid);

                        if (!string.IsNullOrEmpty(teamproject.FileDirectoryID))
                        {
                            fileGuid = Guid.Parse(teamproject.FileDirectoryID);
                        }
                    }
                    else if (objecttype == 4)
                    {
                        var volume = _teamDbContext.Volumes.FirstOrDefault(t => t.VolumeId == objectGuid);
                        if (volume != null)
                        {
                            fileGuid = volume.FileDirectoryId;
                        }
                    }

                    //log.Info("GetAuthByObectId=======查询上级文件夹id:");

                    List<Guid> objIds = new List<Guid>();
                    objIds.Add(objectGuid);
                    objIds.Add(mainProjectGuid);
                    var objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == fileGuid);


                    if (objectFileDir != null)
                    {
                        objIds.Add(objectFileDir.ID);

                        while (objectFileDir != null && objectFileDir.ParentID != null && objectFileDir.ParentID != Guid.Empty)
                        {
                            objectFileDir = allFileDirectories.FirstOrDefault(f => f.ID == objectFileDir.ParentID);
                            if (objectFileDir != null)
                            {
                                objIds.Add(objectFileDir.ID);
                            }

                        }
                    }
                    currentMainProjectTeamMembers = currentMainProjectTeamMembers.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                    currentMainProjectTeamGroups = currentMainProjectTeamGroups.Where(t => objIds.Contains(t.ObjectId) || t.ObjectId == Guid.Empty).ToList();
                }
                List<TeamMember> members = new List<TeamMember>();
                //获取用户组id，去重
                var currentTeamMemberIds = currentMainProjectTeamMembers.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamMemberIds != null && currentTeamMemberIds.Any())
                {
                    var intCurrentTeamGroupIds = currentTeamMemberIds.Select<string, Guid>(x => Guid.Parse(x)).ToList();
                    var retTeamGroups = _teamDbContext.TeamMembers.Where(t => intCurrentTeamGroupIds.Contains(t.ID)).ToList();
                    retTeamGroups.RemoveAll(mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));
                    //return CommonTool.CopyToObject(retTeamGroups);
                    members.AddRange(retTeamGroups);
                }

                var currentTeamGroupIds = currentMainProjectTeamGroups.Select(t => t.GroupOrTeamMemberId).Distinct().ToList();
                if (currentTeamGroupIds != null && currentTeamGroupIds.Any())
                {
                    var intlist = currentTeamGroupIds.Select(a => int.Parse(a)).ToList();
                    var GroupMembers = _teamDbContext.TeamUserGroups.Where(t => intlist.Contains(t.GroupId)).ToList();
                    foreach (var item in GroupMembers)
                    {
                        var tem = members.FirstOrDefault(t => t.ID == item.TeamMemberId);
                        if (tem == null)
                        {
                            var teammem = _teamDbContext.TeamMembers.Where(t => t.ID == item.TeamMemberId).FirstOrDefault();
                            if (teammem != null)
                            {
                                members.Add(teammem);
                            }
                        }
                    }
                }


                var mpusergroupmembers = _teamDbContext.MainProjectUserGroupMembers.Where(s => s.MainProjectId == mainProjectGuid).ToList();
                if (mpusergroupmembers.Any())
                {
                    var mplist = mpusergroupmembers.Select(s => s.TeamMemberId).Distinct().ToList();
                    var templist = _teamDbContext.TeamMembers.Where(p => mplist.Contains(p.ID)).ToList();
                    if (templist.Any())
                    {
                        foreach (var item in templist)
                        {
                            if (!members.Any(t => t.ID == item.ID))
                            {
                                members.Add(item);
                            }
                        }
                    }
                }

                members.RemoveAll(mbr =>
                    (mbr.LoginName == BuildinAdministrators.BuildInSystemAdminLoginName ||
                     mbr.LoginName == BuildinAdministrators.BuildInSystemModelAdminLoginName));
                //return CommonTool.CopyToObject(members);
                return members;
            }
            return null;
        }


        public bool UpdateRepository(long rep)
        {
            var reid = _teamDbContext.RepositoryInformations.FirstOrDefault();
            if (reid != null)
            {
                if (reid.value > rep)
                {
                    reid.value = reid.value + 1;
                }
                else
                {
                    reid.value = rep + 1;
                }

                _teamDbContext.RepositoryInformations.Attach(reid);
                //_teamDbContext.Entry(old).Property(a => a.Ip).IsModified = true;
                _teamDbContext.Entry(reid).Property(a => a.value).IsModified = true;
                _teamDbContext.SaveChanges();
            }
            else
            {
                reid = new RepositoryInformation { RepId = 1, value = rep + 1 };
                _teamDbContext.RepositoryInformations.Add(reid);
                _teamDbContext.SaveChanges();
            }
            return true;
        }

        public RepositoryInformation GetRepository()
        {
            var select = _teamDbContext.RepositoryInformations.AsNoTracking().FirstOrDefault();
            return select;
        }
        public long CreateRepositoryId()
        {
            long newid = 0;
            var reid = _teamDbContext.RepositoryInformations.FirstOrDefault();
            if (reid != null)
            {
                newid = reid.value + 1;
                reid.value = newid;
                _teamDbContext.RepositoryInformations.Attach(reid);
                //_teamDbContext.Entry(old).Property(a => a.Ip).IsModified = true;
                _teamDbContext.Entry(reid).Property(a => a.value).IsModified = true;
                _teamDbContext.SaveChanges();
            }
            else
            {
                newid = 1;
                reid = new RepositoryInformation { RepId = 1, value = 1 };
                _teamDbContext.RepositoryInformations.Add(reid);
                _teamDbContext.SaveChanges();
            }

            return newid;
        }

        public IQueryable<MainProjectUserGroupMember> mainProjectUserGroupMembers
        {
            get
            {
                return _teamDbContext.MainProjectUserGroupMembers.AsNoTracking();
            }
        }


        /// <summary>
        /// 添加用户到用户组
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        public bool AddTeamMemberToMPUsergroup(Guid memberid, int usergroupid, string userGroupName, Guid mainprojectGuid)
        {
            var select = _teamDbContext.MainProjectUserGroupMembers.Where(mp => mp.UserGroupId == usergroupid && mp.TeamMemberId == memberid && mp.MainProjectId == mainprojectGuid);
            if (select.Any())
            {
                return true;
            }
            MainProjectUserGroupMember tur = new MainProjectUserGroupMember();
            tur.UserGroupId = usergroupid;
            tur.TeamMemberId = memberid;
            tur.MainProjectId = mainprojectGuid;
            tur.UserGroupName = userGroupName;
            _teamDbContext.MainProjectUserGroupMembers.Add(tur);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool AddTeamMemberListToMPUsergroup(List<Guid> memberidList, int usergroupid, string usergroupname, Guid mainprojectGuid)
        {
            List<MainProjectUserGroupMember> members = new List<MainProjectUserGroupMember>();
            foreach (var memberid in memberidList)
            {
                var select = _teamDbContext.MainProjectUserGroupMembers.Where(mp => mp.UserGroupId == usergroupid && mp.TeamMemberId == memberid && mp.MainProjectId == mainprojectGuid);
                if (!select.Any())
                {
                    MainProjectUserGroupMember tur = new MainProjectUserGroupMember();
                    tur.UserGroupId = usergroupid;
                    tur.TeamMemberId = memberid;
                    tur.MainProjectId = mainprojectGuid;
                    tur.UserGroupName = usergroupname;
                    members.Add(tur);
                }
            }
            _teamDbContext.MainProjectUserGroupMembers.AddRange(members);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool AddMainProjectUserGroupMemberList(List<MainProjectUserGroupMember> members)
        {
            _teamDbContext.MainProjectUserGroupMembers.AddRange(members);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool AddMainProjectUserGroupAuthList(List<MainProjectUserGroupAuth> members)
        {
            _teamDbContext.MainProjectUserGroupAuths.AddRange(members);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool AddMainProjectTeamGroupList(List<MainProjectTeamGroup> members)
        {
            _teamDbContext.MainProjectTeamGroups.AddRange(members);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool AddTeamUserGroupList(List<TeamUserGroup> teamUserGroups)
        {
            _teamDbContext.TeamUserGroups.AddRange(teamUserGroups);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool RemoveTeamMemberFromMPUsergroupList(Guid memberid, List<int> usergroupidList, Guid mainprojectGuid)
        {
            var all = _teamDbContext.MainProjectUserGroupMembers.Where(mp => mp.TeamMemberId == memberid && mp.MainProjectId == mainprojectGuid).ToList();
            List<MainProjectUserGroupMember> delList = new List<MainProjectUserGroupMember>();
            foreach (var usergroupid in usergroupidList)
            {
                var select = all.Where(mp => mp.UserGroupId == usergroupid).ToList();
                if (select.Any())
                {
                    delList.AddRange(select);
                }
            }
            _teamDbContext.MainProjectUserGroupMembers.RemoveRange(delList);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool RemoveTeamMemberListFromMPUsergroupList(List<Guid> memberids, List<int> usergroupidList, Guid mainprojectGuid)
        {
            var all = _teamDbContext.MainProjectUserGroupMembers
                .Where(mp => mp.MainProjectId == mainprojectGuid && memberids.Contains(mp.TeamMemberId) && usergroupidList.Contains(mp.UserGroupId))
                .ToList();

            _teamDbContext.MainProjectUserGroupMembers.RemoveRange(all);
            _teamDbContext.SaveChanges();
            return true;
        }
        public bool RemoveTeamMemberListFromMPUsergroup(List<Guid> memberidList, int usergroupid, Guid mainprojectGuid)
        {
            var all = _teamDbContext.MainProjectUserGroupMembers.Where(mp => mp.UserGroupId == usergroupid && mp.MainProjectId == mainprojectGuid).ToList();
            List<MainProjectUserGroupMember> delList = new List<MainProjectUserGroupMember>();
            foreach (var memberid in memberidList)
            {
                var select = all.Where(mp => mp.TeamMemberId == memberid).ToList();
                if (select.Any())
                {
                    delList.AddRange(select);
                }
            }
            _teamDbContext.MainProjectUserGroupMembers.RemoveRange(delList);
            _teamDbContext.SaveChanges();
            return true;

        }
        /// <summary>
        /// 从用户组中移除用户
        /// </summary>
        /// <param name="teamMember"></param>
        /// <param name="mPUserGroup"></param>
        /// <returns></returns>
        public bool RemoveTeamMemberFromMPUsergroup(Guid memberid, int usergroupid, Guid mainprojectGuid)
        {
            var select = _teamDbContext.MainProjectUserGroupMembers.Where(mp => mp.UserGroupId == usergroupid && mp.TeamMemberId == memberid && mp.MainProjectId == mainprojectGuid);
            if (!select.Any())
            {
                return true;
            }
            else
            {
                _teamDbContext.MainProjectUserGroupMembers.Remove(select.First());
                _teamDbContext.SaveChanges();
                return true;
            }
        }

        public IQueryable<MainProjectUserGroupAuth> mainProjectUserGroupAuths
        {
            get
            {
                return _teamDbContext.MainProjectUserGroupAuths.AsNoTracking();
            }
        }

        public bool GiveAuthToMPUserGroupMultiple(Guid mainprojectGuid, List<MPUserGroupAuth> userGroupAuthList)
        {
            foreach (var userGroupAuth in userGroupAuthList)
            {
                var authListJson = userGroupAuth.AuthInfo;
                var authList = JsonSerializer.Deserialize<List<MPAuthInfo>>(authListJson);
                var select = _teamDbContext.MainProjectUserGroupAuths.Where(t => t.MainProjectID == mainprojectGuid
                    && t.GroupOrMemberId == userGroupAuth.GroupOrMemberId
                    && t.ObjectId == userGroupAuth.ObjectId
                    && t.InstanceId == userGroupAuth.InstanceId).FirstOrDefault();
                if (select != null)
                {
                    List<MPAuthInfo> curAuth = JsonSerializer.Deserialize<List<MPAuthInfo>>(select.AuthInfo);
                    foreach (var au in authList)
                    {
                        var toChange = curAuth.FirstOrDefault(x => x.AuthName == au.AuthName);
                        if (toChange != null)
                        {
                            toChange.Permission = au.Permission;
                        }
                        else
                        {
                            curAuth.Add(au);
                        }
                    }
                    select.AuthInfo = JsonSerializer.Serialize(curAuth);
                    _teamDbContext.MainProjectUserGroupAuths.Attach(select);
                    _teamDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;

                }
                else
                {
                    MainProjectUserGroupAuth mainProjectUserGroupAuth = new MainProjectUserGroupAuth
                    {
                        MainProjectID = mainprojectGuid,
                        GroupOrMemberId = userGroupAuth.GroupOrMemberId,
                        IsGroupOrTeamMember = userGroupAuth.IsGroupOrTeamMember,
                        AuthInfo = userGroupAuth.AuthInfo,
                        ObjectType = userGroupAuth.ObjectType,
                        ObjectId = userGroupAuth.ObjectId,
                        InstanceId = userGroupAuth.InstanceId,
                        TreeId = userGroupAuth.TreeId
                    };
                    userGroupAuth.AuthInfo = JsonSerializer.Serialize(authList);
                    _teamDbContext.MainProjectUserGroupAuths.Add(mainProjectUserGroupAuth);
                    //_teamDbContext.SaveChanges();
                    //return true;
                }
            }
            _teamDbContext.SaveChanges();
            return true;

        }


        public bool GiveAuthToMPUserGroup(Guid mainprojectGuid, MPUserGroupAuth userGroupAuth, List<MPAuthInfo> authList)
        {
            var select = _teamDbContext.MainProjectUserGroupAuths.Where(t => t.MainProjectID == mainprojectGuid
            && t.GroupOrMemberId == userGroupAuth.GroupOrMemberId
            && t.ObjectId == userGroupAuth.ObjectId
            && t.InstanceId == userGroupAuth.InstanceId).FirstOrDefault();
            if (select != null)
            {
                List<MPAuthInfo> curAuth = JsonSerializer.Deserialize<List<MPAuthInfo>>(select.AuthInfo);
                foreach (var au in authList)
                {
                    var toChange = curAuth.FirstOrDefault(x => x.AuthName == au.AuthName);
                    if (toChange != null)
                    {
                        toChange.Permission = au.Permission;
                    }
                    else
                    {
                        curAuth.Add(au);
                    }
                }
                select.AuthInfo = JsonSerializer.Serialize(curAuth);
                _teamDbContext.MainProjectUserGroupAuths.Attach(select);
                _teamDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;
                _teamDbContext.SaveChanges();
                return true;
            }
            else
            {
                MainProjectUserGroupAuth mainProjectUserGroupAuth = new MainProjectUserGroupAuth
                {
                    MainProjectID = mainprojectGuid,
                    GroupOrMemberId = userGroupAuth.GroupOrMemberId,
                    IsGroupOrTeamMember = userGroupAuth.IsGroupOrTeamMember,
                    AuthInfo = userGroupAuth.AuthInfo,
                    ObjectType = userGroupAuth.ObjectType,
                    ObjectId = userGroupAuth.ObjectId,
                    InstanceId = userGroupAuth.InstanceId,
                    TreeId = userGroupAuth.TreeId
                };
                userGroupAuth.AuthInfo = JsonSerializer.Serialize(authList);
                _teamDbContext.MainProjectUserGroupAuths.Add(mainProjectUserGroupAuth);
                _teamDbContext.SaveChanges();
                return true;
            }
        }

        public IQueryable<MainProjectUserGroupLibAuth> mainProjectUserGroupLibAuths
        {
            get
            {
                return _teamDbContext.MainProjectUserGroupLibAuths.AsNoTracking();
            }
        }
        public bool SetMPUserGroupLibAuth(Guid mainprojectGuid, MPUserGroupLibAuth mpUserGroupLibAuth)
        {
            var select = _teamDbContext.MainProjectUserGroupLibAuths.Where(t => t.MainProjectID == mainprojectGuid
            && t.MPUserGroupId == mpUserGroupLibAuth.MPUserGroupId
            && t.LibType == mpUserGroupLibAuth.LibType).FirstOrDefault();
            if (select != null)
            {
                select.Permission = mpUserGroupLibAuth.Permission;
                select.AuthInfo = mpUserGroupLibAuth.AuthInfo;
                _teamDbContext.MainProjectUserGroupLibAuths.Attach(select);
                _teamDbContext.Entry(select).Property(a => a.Permission).IsModified = true;
                _teamDbContext.Entry(select).Property(a => a.AuthInfo).IsModified = true;
                _teamDbContext.SaveChanges();
                return true;
            }
            else
            {
                MainProjectUserGroupLibAuth mainProjectUserGroupLibAuth = new MainProjectUserGroupLibAuth
                {
                    MainProjectID = mainprojectGuid,
                    MPUserGroupId = mpUserGroupLibAuth.MPUserGroupId,
                    LibType = mpUserGroupLibAuth.LibType,
                    Permission = mpUserGroupLibAuth.Permission,
                    AuthInfo = mpUserGroupLibAuth.AuthInfo,
                    ExtendStr = mpUserGroupLibAuth.ExtendStr
                };
                _teamDbContext.MainProjectUserGroupLibAuths.Add(mainProjectUserGroupLibAuth);
                _teamDbContext.SaveChanges();
                return true;
            }
        }
        public bool AddMainProjectUserGroupLibAuthList(List<MainProjectUserGroupLibAuth> mpUserGroupLibAuth)
        {
            _teamDbContext.MainProjectUserGroupLibAuths.AddRange(mpUserGroupLibAuth);
            _teamDbContext.SaveChanges();
            return true;
        }

        public bool UpdataProjectFileDirId(Guid mpGuid, List<Guid> prjGuidList, Guid newDirId)
        {
            var newTP = _teamDbContext.TeamProjects.Where(vd => vd.MainProjectID.ToLower() == mpGuid.ToString().ToLower() && prjGuidList.Contains(vd.ID)).ToList();
            if (!newTP.Any())
            {
                return false;
            }
            var sb = new StringBuilder();
            var volids = newTP.Select(s => s.ID).ToList();
            sb.Append("UPDATE teamprojects set FileDirectoryId = '" + newDirId + "' where ID in (");
            foreach (var vid in volids)
            {
                sb.Append("'" + vid + "',");
            }
            sb.Append("'-1')");

            var updatesql = sb.ToString();
            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(updatesql);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {

                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }
        public bool UpdateVolumeFileDirId(Guid mpGuid, List<Guid> volGuidList, Guid newDirId)
        {
            var newTP = _teamDbContext.Volumes.Where(vd => vd.MainProjectId == mpGuid && volGuidList.Contains(vd.VolumeId)).ToList();
            if (!newTP.Any())
            {
                return false;
            }
            var sb = new StringBuilder();
            var volids = newTP.Select(s => s.VolumeId).ToList();
            sb.Append("UPDATE volumes set FileDirectoryId = '" + newDirId + "' where VolumeId in (");
            foreach (var vid in volids)
            {
                sb.Append("'" + vid + "',");
            }
            sb.Append("'-1')");

            var updatesql = sb.ToString();
            using (var dbContextTransaction = _teamDbContext.Database.BeginTransaction())
            {
                try
                {
                    _teamDbContext.Database.ExecuteSqlRaw(updatesql);
                    dbContextTransaction.Commit();
                    return true;
                }
                catch (Exception ex)
                {

                    Console.WriteLine(ex.Message);
                    dbContextTransaction.Rollback();
                    throw ex;
                }
            }
        }

        public bool UpdateMainprojectDeleteState(MainProject mainproject)
        {
            var oldProject = _teamDbContext.MainProjects.FirstOrDefault(m => m.ID == mainproject.ID);
            if (oldProject == null)
            {
                return false;
            }
            oldProject.IsDelete = mainproject.IsDelete;
            _teamDbContext.MainProjects.Attach(oldProject);
            _teamDbContext.Entry(oldProject).Property(a => a.IsDelete).IsModified = true;
            _teamDbContext.SaveChanges();
            return true;
        }

        public IQueryable<ClientModuleVersion> ClientModuleVersions 
        { 
            get 
            {
                return _teamDbContext.ClientModuleVersions.AsNoTracking();
            } 
        }

        public bool AddClientModuleVersions(List<ClientModuleVersion> clientModuleVersion)
        {
            _teamDbContext.ClientModuleVersions.AddRange(clientModuleVersion);
            _teamDbContext.SaveChanges();
            return true;
        }

        public IQueryable<ClientVersionCompatibility> ClientVersionCompatibilities 
        {
            get
            {
                return _teamDbContext.ClientVersionCompatibilities.AsNoTracking();
            }
        }

        public bool AddNodeNameCheckConfigs(List<NodeNameCheckConfig> nodeNameCheckConfigs)
        {
            try
            {
                if (nodeNameCheckConfigs == null || !nodeNameCheckConfigs.Any())
                {
                    _logger.LogWarning("NodeNameCheckConfigs列表为空，跳过添加");
                    return true;
                }

                _teamDbContext.NodeNameCheckConfigs.AddRange(nodeNameCheckConfigs);
                _teamDbContext.SaveChanges();
                _logger.LogInformation($"成功添加 {nodeNameCheckConfigs.Count} 条节点名称检查配置");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加节点名称检查配置失败");
                return false;
            }
        }

        public IQueryable<NodeNameCheckConfig> NodeNameCheckConfigs
        {
            get { return _teamDbContext.NodeNameCheckConfigs; }
        }
    }
}
