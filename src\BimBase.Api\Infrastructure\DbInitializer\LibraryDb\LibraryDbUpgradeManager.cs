using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.LibraryDb
{
    /// <summary>
    /// LibraryDb升级管理器
    /// </summary>
    public class LibraryDbUpgradeManager
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, ILibraryDbUpgrade> _upgrades;

        public LibraryDbUpgradeManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _upgrades = new Dictionary<string, ILibraryDbUpgrade>();
            RegisterUpgrades();
        }

        private void RegisterUpgrades()
        {
            var upgrades = new List<ILibraryDbUpgrade>
            {
                new LibraryDbUpgrade_v0_to_v1(_logger),
                // 未来可以在这里添加更多升级类
                new LibraryDbUpgrade_v1_to_v2(_logger),
                new LibraryDbUpgrade_v2_to_v3(_logger),
                new LibraryDbUpgrade_v3_to_v4(_logger),
                new LibraryDbUpgrade_v4_to_v5(_logger),
                new LibraryDbUpgrade_v5_to_v6(_logger),
                new LibraryDbUpgrade_v6_to_v7(_logger)
            };

            foreach (var upgrade in upgrades)
            {
                var key = $"{upgrade.FromVersion}->{upgrade.ToVersion}";
                _upgrades[key] = upgrade;
                _logger.LogDebug($"注册LibraryDb升级: {key}");
            }
        }

        public List<ILibraryDbUpgrade> CalculateUpgradePath(string fromVersion, string toVersion)
        {
            _logger.LogInformation($"计算LibraryDb升级路径: {fromVersion} -> {toVersion}");
            var path = new List<ILibraryDbUpgrade>();
            var currentVersion = fromVersion;
            while (currentVersion != toVersion)
            {
                var nextUpgrade = _upgrades.Values.FirstOrDefault(u => u.FromVersion == currentVersion);
                if (nextUpgrade == null)
                    throw new InvalidOperationException($"无法找到从 {currentVersion} 开始的升级路径");
                path.Add(nextUpgrade);
                currentVersion = nextUpgrade.ToVersion;
                if (path.Count > 10)
                    throw new InvalidOperationException("升级路径过长，可能存在循环依赖");
            }
            _logger.LogInformation($"计算出的升级路径: {string.Join(" -> ", path.Select(u => $"{u.FromVersion}->{u.ToVersion}"))}");
            return path;
        }

        public async Task ExecuteUpgradePathAsync(LibraryDbContext context, string fromVersion, string toVersion)
        {
            _logger.LogInformation($"开始LibraryDb升级: {fromVersion} -> {toVersion}");
            if (fromVersion == toVersion)
            {
                _logger.LogInformation("源版本与目标版本相同，无需升级");
                return;
            }
            var upgradePath = CalculateUpgradePath(fromVersion, toVersion);
            foreach (var upgrade in upgradePath)
            {
                await upgrade.UpgradeAsync(context);
            }
            _logger.LogInformation("LibraryDb升级路径执行完成");
        }
    }
} 