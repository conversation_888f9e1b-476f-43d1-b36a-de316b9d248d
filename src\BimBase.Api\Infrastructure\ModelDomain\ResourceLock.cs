﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public enum LockStatus
    {
        Free,
        TemporaryLock,
        PermanentLock
    }


    /// <summary>
    /// 资源锁
    /// </summary>
    public class ResourceLock
    {
        [Key]
        public Int64 Id { get; set; }

        /// <summary>
        /// 锁定用户Id
        /// </summary>
        
        public int UserId { get; set; }

        /// <summary>
        /// 是否为独占资源锁
        /// </summary>
        
        public bool ExclusiveSchema { get; set; }

        /// <summary>
        /// Schema名称
        /// </summary>
        
        public string SchemaName { get; set; }

        /// <summary>
        /// 类名
        /// </summary>
        
        public int ClassId { get; set; }

        /// <summary>
        /// 资源名称
        /// </summary>
        
        public string ResourceName { get; set; }

        /// <summary>
        /// 状态 未占用 临时占用 永久占用
        /// </summary>
        
        public LockStatus Status { get; set; }
    }
}
