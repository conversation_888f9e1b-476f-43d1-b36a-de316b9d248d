﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public enum RelationshipType
    {
        Contain,
        Assemble,
        Dependence,
        Reference,
        Incidence
    }


    public class Relationship : BasicModelDataInfomation
    {
        [Required]
        public Int64 SourceID { get; set; }

        public string SourceDomainClassName { get; set; }

        [Required]
        public String SourceECSchemaName { get; set; }

        [Required]
        public Int64 TargetID { get; set; }

        public string TargetDomainClassName { get; set; }

        [Required]
        public String TargetECSchemaName { get; set; }

        [Required, EnumDataType(typeof(RelationshipType))]
        public RelationshipType Type { get; set; }

        public bool IsForward { get; set; }

        public HistoryRelationship CreateHistoryRelationship(OperationRecordType op, int versionNo = 0)
        {
            HistoryRelationship hr = new HistoryRelationship();
            base.CopyTo(ref hr);
            hr.SourceID = SourceID;
            //hr.SourceDomainClassID = SourceDomainClassID;
            hr.SourceDomainClassName = SourceDomainClassName;
            hr.SourceECSchemaName = SourceECSchemaName;
            hr.TargetID = TargetID;
            //hr.TargetDomainClassID = TargetDomainClassID;
            hr.TargetDomainClassName = TargetDomainClassName;
            hr.TargetECSchemaName = TargetECSchemaName;
            hr.Type = Type;
            hr.OperationRecordType = op;
            hr.VersionNo = versionNo;
            hr.IsForward = IsForward;
            return hr;
        }
    }
}
