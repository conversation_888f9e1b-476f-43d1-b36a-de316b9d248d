using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace BimBase.Api.Infrastructure.MQLog
{
    /// <summary>
    /// MQ日志字段自动补全工具类，负责补全MQInterfaceLog和MQErrorLog的通用字段
    /// </summary>
    public static class MQLogEnricher
    {
        /// <summary>
        /// 自动补全MQInterfaceLog的通用字段（如AppName、RequestId、ClientIp等）
        /// </summary>
        public static void EnrichInterfaceLog(MQInterfaceLog log, HttpContext httpContext, IConfiguration config)
        {
            // 配置相关
            log.AppName = config["AppName"] ?? "BimBase.Api";
            log.AppVersion = config["AppVersion"] ?? "1.0.0";
            log.Environment = config["Environment"] ?? config["ASPNETCORE_ENVIRONMENT"] ?? "Production";

            // 上下文相关
            if (httpContext != null)
            {
                log.ClientIp = httpContext.Connection.RemoteIpAddress?.ToString() ?? log.ClientIp;
                log.RequestId = httpContext.TraceIdentifier ?? log.RequestId;
                if (httpContext.User?.Identity?.IsAuthenticated == true)
                {
                    log.UserName = httpContext.User.Identity.Name ?? log.UserName;
                }
                // 可扩展：从Claims等获取UserId
            }

            // 时间相关
            if (log.RequestTime == default)
                log.RequestTime = DateTime.UtcNow;
            if (log.ResponseTime == default)
                log.ResponseTime = DateTime.UtcNow;
            if (log.AddTime == default)
                log.AddTime = DateTime.UtcNow;
          //  if (log.TotalMilliseconds == 0 && log.RequestTime != default && log.ResponseTime != default)
               // log.TotalMilliseconds = (log.ResponseTime - log.RequestTime).TotalMilliseconds;

            // 是否成功
            if (log.ResponseStatusCode != 0)
                log.IsSuccess = log.ResponseStatusCode >= 200 && log.ResponseStatusCode < 300;
            // 其他字段可根据实际情况补全
        }

        /// <summary>
        /// 自动补全MQErrorLog的通用字段（如AppName、RequestId等）
        /// </summary>
        public static void EnrichErrorLog(MQErrorLog log, HttpContext httpContext, IConfiguration config)
        {
            log.AppName = config["AppName"] ?? "BimBase.Api";
            log.AppVersion = config["AppVersion"] ?? "1.0.0";
            log.Environment = config["Environment"] ?? config["ASPNETCORE_ENVIRONMENT"] ?? "Production";

            if (httpContext != null)
            {
                log.RequestId = httpContext.TraceIdentifier ?? log.RequestId;
            }

            log.AddTime = log.AddTime == default ? DateTime.Now : log.AddTime;
        }
    }
} 