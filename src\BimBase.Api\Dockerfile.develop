FROM mcr.microsoft.com/dotnet/sdk:8.0
ARG BUILD_CONFIGURATION=Debug
ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_URLS=http://+:80
ENV DOTNET_USE_POLLING_FILE_WATCHER=true
EXPOSE 80

WORKDIR /src
COPY ["src/BimBase.Api/BimBase.Api.csproj", "src/BimBase.Api/"]

RUN dotnet restore "src/BimBase.Api/BimBase.Api.csproj"
COPY . .
WORKDIR "/src/src/BimBase.Api"
RUN dotnet build --no-restore "BimBase.Api.csproj" -c $BUILD_CONFIGURATION

RUN echo "exec dotnet run --no-build --no-launch-profile -c $BUILD_CONFIGURATION --" > /entrypoint.sh

ENTRYPOINT ["/bin/bash", "/entrypoint.sh"]