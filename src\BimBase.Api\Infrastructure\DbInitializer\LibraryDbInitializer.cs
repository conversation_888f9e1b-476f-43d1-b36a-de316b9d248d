﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Infrastructure.LibDomain;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.DbInitializer.LibraryDb;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public static class LibraryDbInitializer
    {
        private const string InitialVersion = "v0";

        /// <summary>
        /// 初始化数据库并执行版本管理
        /// </summary>
        public static void Initialize(LibraryDbContext context, IServiceProvider serviceProvider)
        {
            try
            {
                InitializeAsync(context, serviceProvider).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LibraryDb初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 异步初始化数据库并执行版本管理
        /// </summary>
        public static async Task InitializeAsync(LibraryDbContext context, IServiceProvider serviceProvider)
        {
            var logger = GetLogger(serviceProvider);

            try
            {
                logger.LogInformation("开始LibraryDb数据库初始化和版本管理...");

                // 1. 确保数据库存在，并根据是否新创建决定是否需要初始数据
                var databaseCreated = await EnsureDatabaseExistsAsync(context, logger);

                // 2. 只有在数据库新创建时才创建初始数据
                if (databaseCreated)
                {
                    await EnsureInitialDataAsync(context, logger);
                }

                // 3. 执行版本管理 - 使用全局配置提供者
                await ExecuteVersionManagementAsync(context, logger);

                logger.LogInformation("LibraryDb数据库初始化和版本管理完成");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "LibraryDb初始化过程中发生错误");
                await HandleRollbackAsync(context, logger, ex);
                throw;
            }
        }

        /// <summary>
        /// 确保数据库存在
        /// </summary>
        /// <returns>如果数据库是新创建的返回true，已存在返回false</returns>
        private static async Task<bool> EnsureDatabaseExistsAsync(LibraryDbContext context, ILogger logger)
        {
            logger.LogInformation("检查LibraryDb数据库是否存在...");
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                logger.LogInformation("LibraryDb数据库已创建");
            }
            else
            {
                logger.LogInformation("LibraryDb数据库已存在");
            }
            return created;
        }

        /// <summary>
        /// 确保初始数据存在
        /// </summary>
        private static async Task EnsureInitialDataAsync(LibraryDbContext context, ILogger logger)
        {
            // 检查是否已有数据
            if (await context.CLCompanyLibInfos.AnyAsync())
            {
                logger.LogInformation("检测到已有数据，跳过初始数据创建");
                // 但仍需确保ClientVersionCompatibilities有初始数据
                await EnsureClientVersionCompatibilitiesAsync(context, logger);
                return;
            }

            logger.LogInformation("创建初始数据...");
            await CreateInitialDataAsync(context, logger);
            // 创建初始数据后，确保ClientVersionCompatibilities有初始数据
            await EnsureClientVersionCompatibilitiesAsync(context, logger);
        }

        /// <summary>
        /// 创建初始数据（可根据业务需求扩展）
        /// </summary>
        private static async Task CreateInitialDataAsync(LibraryDbContext context, ILogger logger)
        {
            logger.LogInformation("开始创建LibraryDb初始数据...");
            // 可根据业务需求添加初始数据
            // ...
            await context.SaveChangesAsync();
            logger.LogInformation("LibraryDb初始数据创建完成");
        }

        /// <summary>
        /// 确保ClientVersionCompatibilities有初始数据
        /// </summary>
        private static async Task EnsureClientVersionCompatibilitiesAsync(LibraryDbContext context, ILogger logger)
        {
            if (!await context.ClientVersionCompatibilities.AnyAsync())
            {
                context.ClientVersionCompatibilities.Add(new ClientVersionCompatibility
                {
                    ClientId = "PKPM-Plant",
                    ClientVersion = "BIMBase-2025R01.01",
                    MinCompatibleVersion = "BIMBase-2025R01.01"
                });
                logger.LogInformation("添加初始客户端版本兼容性配置: PKPM-Plant");
                await context.SaveChangesAsync();
            }
        }

        /// <summary>
        /// 获取日志记录器
        /// </summary>
        private static ILogger GetLogger(IServiceProvider serviceProvider)
        {
            var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
            return loggerFactory.CreateLogger("LibraryDbInitializer");
        }

        /// <summary>
        /// 执行版本管理
        /// </summary>
        private static async Task ExecuteVersionManagementAsync(LibraryDbContext context, ILogger logger)
        {
            logger.LogInformation("开始执行LibraryDb数据库版本管理...");

            if (!DatabaseVersioningOptionsProvider.ShouldAutoExecute())
            {
                logger.LogInformation("LibraryDb数据库自动版本控制已禁用，跳过执行");
                return;
            }

            await EnsureVersionTableAsync(context, logger);

            var currentVersion = await GetCurrentVersionAsync(context, logger);
            var targetVersion = DatabaseVersioningOptionsProvider.GetTargetVersion() ?? "v1";

            logger.LogInformation($"当前版本: {currentVersion}, 目标版本: {targetVersion}");

            if (currentVersion == targetVersion)
            {
                logger.LogInformation("LibraryDb数据库已经是目标版本，无需升级");
                return;
            }

            await ExecuteUpgradePathWithManagerAsync(context, logger, currentVersion, targetVersion);
        }

        /// <summary>
        /// 使用升级管理器执行升级路径
        /// </summary>
        private static async Task ExecuteUpgradePathWithManagerAsync(LibraryDbContext context, ILogger logger, string currentVersion, string targetVersion)
        {
            logger.LogInformation($"开始使用升级管理器执行升级路径: {currentVersion} -> {targetVersion}");

            var upgradeManager = new LibraryDbUpgradeManager(logger);

            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                if (IsUpgrade(currentVersion, targetVersion))
                {
                    await upgradeManager.ExecuteUpgradePathAsync(context, currentVersion, targetVersion);
                }
                else
                {
                    // 如有回滚实现，可在此调用回滚方法
                    logger.LogWarning("当前未实现回滚路径，跳过回滚");
                }

                await context.SaveChangesAsync();
                await transaction.CommitAsync();
                logger.LogInformation("LibraryDb升级路径执行完成");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                logger.LogError(ex, "LibraryDb升级过程中发生错误，已回滚");
                throw;
            }
        }

        /// <summary>
        /// 判断是否为升级操作
        /// </summary>
        private static bool IsUpgrade(string currentVersion, string targetVersion)
        {
            var currentVersionNumber = GetVersionNumber(currentVersion);
            var targetVersionNumber = GetVersionNumber(targetVersion);
            return targetVersionNumber > currentVersionNumber;
        }

        /// <summary>
        /// 获取版本号（用于比较）
        /// </summary>
        private static int GetVersionNumber(string version)
        {
            return version switch
            {
                "v0" => 0,
                "v1" => 1,
                "v2" => 2,
                "v3" => 3,
                "v4" => 4,
                "v5" => 5,
                "v6" => 6,
                "v7" => 7,
                "v8" => 8,
                _ => -1
            };
        }

        /// <summary>
        /// 确保版本表存在
        /// </summary>
        private static async Task EnsureVersionTableAsync(LibraryDbContext context, ILogger logger)
        {
            if (await DatabaseVersionTableExistsAsync(context))
            {
                logger.LogInformation("LibraryDb版本管理相关表已存在");
            }
            else
            {
                logger.LogInformation("LibraryDb版本管理相关表不存在，将在升级过程中创建");
            }
        }

        /// <summary>
        /// 获取当前数据库版本
        /// </summary>
        private static async Task<string> GetCurrentVersionAsync(LibraryDbContext context, ILogger logger)
        {
            try
            {
                if (!await DatabaseVersionTableExistsAsync(context))
                {
                    logger.LogInformation("LibraryDb DatabaseVersion 表不存在，使用初始版本");
                    return InitialVersion;
                }

                var latestVersion = await context.DatabaseVersions
                    .OrderByDescending(v => v.Id)
                    .FirstOrDefaultAsync();

                var version = latestVersion?.Version ?? InitialVersion;
                logger.LogInformation($"检测到当前LibraryDb数据库版本: {version}");
                return version;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "检测LibraryDb数据库版本时发生异常，使用初始版本");
                return InitialVersion;
            }
        }

        /// <summary>
        /// 检查 DatabaseVersions 表是否存在
        /// </summary>
        private static async Task<bool> DatabaseVersionTableExistsAsync(LibraryDbContext context)
        {
            try
            {
                var databaseName = context.Database.GetDbConnection().Database;
                var sql = $@"
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = '{databaseName}' 
                      AND table_name = 'DatabaseVersion'";
                var result = await context.Database.SqlQueryRaw<int>(sql).ToListAsync();
                return result.FirstOrDefault() > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 处理回滚
        /// </summary>
        private static async Task HandleRollbackAsync(LibraryDbContext context, ILogger logger, Exception originalException)
        {
            try
            {
                logger.LogWarning("尝试执行LibraryDb数据库回滚操作...");
                // 可根据需要添加具体回滚逻辑
                logger.LogInformation("LibraryDb回滚操作完成");
            }
            catch (Exception rollbackException)
            {
                logger.LogError(rollbackException, "LibraryDb回滚操作也失败了");
            }
        }
    }
}
