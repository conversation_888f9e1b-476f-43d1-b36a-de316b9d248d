﻿using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class BPExObject
    {
        
        [Key]
        public Int64 Id { get; set; }
        /// <summary>
        /// 提资人
        /// </summary>
        
        public string ProvideUser { get; set; }
        /// <summary>
        /// 提资日期
        /// </summary>
        
        public string ProvideTime
        {
            get;
            set;
        }
        /// <summary>
        /// 提资状态
        /// </summary>
        
        public int ProvideState { get; set; }
        /// <summary>
        /// 提资备注
        /// </summary>
        
        public string ProvideNotes { get; set; }
        /// <summary>
        /// 收资人
        /// </summary>
        
        public string AcceptUser { get; set; }

        /// <summary>
        /// 反资日期
        /// </summary>
        
        public string AcceptTime
        {
            get;
            set;
        }
        /// <summary>
        /// 收资状态
        /// </summary>
        
        public int AcceptState { get; set; }

        /// <summary>
        /// 结构备注
        /// </summary>
        
        public string StructNotes { get; set; }
        /// <summary>
        /// 专业
        /// </summary>
        
        public int Domain { get; set; }
        /// <summary>
        /// 荷载名称
        /// </summary>
        
        public string LoadName { get; set; }
        /// <summary>
        /// GUID
        /// </summary>
        
        public Guid GUID { get; set; }
        /// <summary>
        /// 大版本号，发布时生成大版本
        /// </summary>
        
        public int MainVersion { get; set; }
        /// <summary>
        /// 小版本，更新时不生成大版本，生成小版本
        /// </summary>
        
        public int SubVersion { get; set; }
        /// <summary>
        /// 扩展字段
        /// </summary>
        
        //public byte[] ExtendField { get; set; }
        public string ExtendField { get; set; }

        /// <summary>
        /// 提资类型
        /// </summary>
        
        public int Type { get; set; }
    }
}
