using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    [XmlRoot(Namespace = "")]
    public class NodeNameCheckConfig
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }
        
        /// <summary>
        /// 节点类型名称
        /// </summary>
        public string NodeTypeName { get; set; }
        
        /// <summary>
        /// 类型编号
        /// </summary>
        public int NodeTypeNum { get; set; }
        
        /// <summary>
        /// 冲突判断范围 全局/同级节点
        /// </summary>
        public int CheckType { get; set; }
    }
} 