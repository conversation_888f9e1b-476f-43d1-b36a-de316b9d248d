﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class Participant
    {
        public int ID { get; set; }
        [<PERSON><PERSON><PERSON>("Member")]
        public int MemberID { get; set; }
        public virtual ProjectMember Member { get; set; }
        public bool Confirmed { get; set; }
    }
}
