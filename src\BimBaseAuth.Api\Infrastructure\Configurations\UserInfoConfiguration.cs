﻿using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Configurations
{
    public partial class UserInfoConfiguration : IEntityTypeConfiguration<UserInfo>
    {
        public void Configure(EntityTypeBuilder<UserInfo> entity)
        {
            entity.ToTable("userinfo");
            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.Account)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.AppId)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.BizCode)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.CreateId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.Name)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.Password)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<UserInfo> entity);
    }
}
