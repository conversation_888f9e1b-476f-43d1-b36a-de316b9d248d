syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";


package bimbase.api;

message ModelUploadGrpcResult{
	reserved 1;
	bool issuccess = 2;
	string message = 3;
	google.protobuf.Any result = 4;
}

service GrpcModelUpload {


}