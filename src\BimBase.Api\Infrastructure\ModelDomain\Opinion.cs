﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    //public enum OpinionType
    //{
    //    Demand,
    //    Question,
    //    Other
    //}


    //public class Opinion
    //{
    //    public int ID { get; set; }
    //    public String Title { get; set; }
    //    public String Description { get; set; }
    //    public OpinionType Type { get; set; }
    //    public DateTime Time { get; set; }
    //    [<PERSON>Key("Author")]
    //    public int AuthorMemberID { get; set; }
    //    public virtual ProjectMember Author { get; set; }
    //    public virtual List<OpinionAttachment> Attachments { get; set; }
    //}
}
