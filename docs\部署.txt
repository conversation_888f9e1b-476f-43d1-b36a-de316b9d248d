1、准备环境
    1.1 宿主机创建/data文件夹，按实际需求可将/data挂载到数据盘
2、部署
    2.0 部署MySQL8.0和redis镜像（如用户环境已有可跳过）
        2.0.1 将 mysql_8.0.tar、redis_latest.tar 上传到服务器
        2.0.2 加载mysql、redis：docker load --input mysql_8.0.tar、redis_latest.tar
    2.1 加载bimbase和bimbaseauth镜像： 
        2.1.0 将 bimbaseauth-api_v2025040806.tar、bimbase-api_v2025041711.tar上传到服务器
	2.1.1加载bimbase服务： docker load --input bimbase-api_v2025041711.tar
        2.1.2加载bimbaseauth服务：docker load --input bimbaseauth-api_v2025040806.tar
    2.2 部署grpcweb服务
       2.2.0 将grpcweb_v2025050801.tar上传到服务器
       2.2.1 加载grpcweb服务：docker load --input grpcweb_v2025050801.tar
    2.3 将docker-compose.yml  和 docker-compose.override.yml 上传到服务器
    2.4 服务器上启动服务： 进入 docker-compose 文件目录下，执行 docker-compose -f docker-compose.override.yml up命令 (启动前先把上一次的服务停掉)
    2.5 停止服务： docker-compose -f docker-compose.override.yml  down
3、异常处理
   3.1 若未启动成功可尝试先启动数据库服务
        docker-compose -f docker-compose.override.yml up libdata logdata teamdata authdata
   3.2 停止数据库服务后，重新启动全部服务
       docker-compose -f docker-compose.override.yml  down libdata logdata teamdata authdata
       docker-compose -f docker-compose.override.yml up
   

   