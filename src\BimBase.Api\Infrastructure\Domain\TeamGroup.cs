﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.Domain
{
    [XmlRoot(Namespace = "")]
    public class TeamGroup
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }
        public string GroupName { get; set; }

        public string Description { get; set; }
    }
}
