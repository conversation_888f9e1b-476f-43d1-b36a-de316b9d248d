﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class BackupPath
    {
        [Key]
        public Guid ID { get; set; }

        public string BackupServerPath { get; set; }

        public string BackupVirtualPath { get; set; }
        public string SiteName { get; set; }

        public int DeleteFlag { get; set; }
    }
}
