﻿using BimBaseAuth.Api.Protos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public class RoleTemplate
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public int Status { get; set; }

        public int Type { get; set; }

        public List<GrpcAuthInfo> AuthInfos { get; set; }
    }
}
