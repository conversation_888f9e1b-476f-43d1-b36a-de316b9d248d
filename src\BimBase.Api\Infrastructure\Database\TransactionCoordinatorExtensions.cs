using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace BimBase.Api.Infrastructure.Database
{
    /// <summary>
    /// 事务协调器的扩展方法
    /// </summary>
    public static class TransactionCoordinatorExtensions
    {
        /// <summary>
        /// 注册多个数据库上下文到事务协调器
        /// </summary>
        /// <param name="coordinator">事务协调器</param>
        /// <param name="contexts">数据库上下文字典，键为名称，值为上下文</param>
        /// <returns>事务协调器实例，用于链式调用</returns>
        public static async Task<TransactionCoordinator> RegisterContextsAsync(
            this TransactionCoordinator coordinator, 
            IDictionary<string, DbContext> contexts)
        {
            if (coordinator == null)
                throw new ArgumentNullException(nameof(coordinator));
            
            if (contexts == null || !contexts.Any())
                return coordinator;
            
            foreach (var (name, context) in contexts)
            {
                await coordinator.RegisterContextAsync(name, context);
            }
            
            return coordinator;
        }
        
        /// <summary>
        /// 使用事务协调器执行操作并自动提交
        /// </summary>
        /// <param name="coordinator">事务协调器</param>
        /// <param name="action">要执行的操作委托</param>
        public static async Task ExecuteAsync(this TransactionCoordinator coordinator, Func<Task> action)
        {
            if (coordinator == null)
                throw new ArgumentNullException(nameof(coordinator));
            
            if (action == null)
                throw new ArgumentNullException(nameof(action));
            
            try
            {
                // 执行操作
                await action();
                
                // 提交事务
                await coordinator.CommitAsync();
            }
            catch (Exception)
            {
                // 发生异常时回滚事务
                await coordinator.RollbackAsync();
                
                // 重新抛出异常
                throw;
            }
        }
    }
} 