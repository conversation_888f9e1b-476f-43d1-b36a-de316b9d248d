using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using Microsoft.Extensions.DependencyInjection;
using BimBase.Api.Infrastructure;
using BimBase.Api.Infrastructure.DbInitializer;
using System.Data.Common;

namespace BimBase.Api.Infrastructure.Database.Upgrades
{
    /// <summary>
    /// 数据库从v0版本升级到v1版本的实现。
    /// 主要变更包括：
    /// 1. 向 `MainProjects` 表添加 `ClientVersion` (LONGTEXT NULL) 列。
    /// 2. 向 `MainProjects` 表添加 `clientId` (VARCHAR(50) NULL) 列。
    /// 3. 创建 `ClientModuleVersions` 表 (如果不存在)，用于记录客户端各模块的版本信息。
    ///    该表包含字段：`Id`, `ClientId`, `ModuleId`, `Version`, `CreatedAt`, `UpdatedAt`。
    /// </summary>
    public class Upgrade_v0_to_v1 : AbstractDatabaseUpgrade
    {
        public override string SourceVersion => "v0";
        public override string TargetVersion => "v1";
        public override string Description => "添加客户端版本管理相关表和字段";
        
        public Upgrade_v0_to_v1(IServiceProvider serviceProvider, ILogger<Upgrade_v0_to_v1> logger) 
            : base(serviceProvider, logger)
        {
        }
        
        protected override async Task UpgradeInternalAsync()
        {
            Logger.LogInformation("开始执行 v0 到 v1 的升级操作");
            
            var teamDbContext = GetTeamDbContext();
            await RegisterContextAsync(nameof(TeamDbContext), teamDbContext);
            
            // 1. 添加ClientVersion列 - 直接执行，任何错误将冒泡并中止操作
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `MainProjects` ADD COLUMN `ClientVersion` LONGTEXT NULL;
            ");
            Logger.LogInformation("成功添加ClientVersion列到MainProjects表");
            
            // 2. 添加clientId列 - 直接执行
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `MainProjects` ADD COLUMN `clientId` VARCHAR(50) NULL;
            ");
            Logger.LogInformation("成功添加clientId列到MainProjects表");
            
            // 3. 创建客户端模块版本记录表 (IF NOT EXISTS 是幂等的)
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS `ClientModuleVersions` (
                    `Id` int(11) NOT NULL AUTO_INCREMENT,
                    `ClientId` varchar(50) NOT NULL,
                    `ModuleId` varchar(50) NOT NULL,
                    `Version` varchar(50) NOT NULL,
                    `CreatedAt` datetime(6) NOT NULL,
                    `UpdatedAt` datetime(6) NOT NULL,
                    PRIMARY KEY (`Id`),
                    KEY `IX_ClientModuleVersions_ClientId` (`ClientId`),
                    KEY `IX_ClientModuleVersions_ModuleId` (`ModuleId`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
            Logger.LogInformation("已尝试创建ClientModuleVersions表 (如果不存在)");
            
            Logger.LogInformation("完成 v0 到 v1 的升级操作");
        }
        
        protected override async Task RollbackInternalAsync()
        {
            Logger.LogInformation("开始执行 v1 到 v0 的回滚操作");
            
            var teamDbContext = GetTeamDbContext();
            await RegisterContextAsync(nameof(TeamDbContext), teamDbContext);
            
            // 1. 移除ClientModuleVersions表 (IF EXISTS 是幂等的)
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                DROP TABLE IF EXISTS `ClientModuleVersions`;
            ");
            Logger.LogInformation("已尝试移除ClientModuleVersions表 (如果存在)");

            // 2. 从MainProjects表中移除ClientVersion字段 - 直接执行
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `MainProjects` DROP COLUMN `ClientVersion`;
            ");
            Logger.LogInformation("成功从MainProjects表中移除ClientVersion列");
            
            // 3. 从MainProjects表中移除clientId字段 - 直接执行
            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `MainProjects` DROP COLUMN `clientId`;
            ");
            Logger.LogInformation("成功从MainProjects表中移除clientId列");
            
            Logger.LogInformation("完成 v1 到 v0 的回滚操作");
        }
        
        protected override Task UpgradeMainProjectInternalAsync(Guid mainProjectId)
        {
            Logger.LogInformation($"主项目数据库 v0 到 v1 升级针对项目ID: {mainProjectId} 无特定操作。");
            return Task.CompletedTask;
        }

        // Implement missing abstract members
        protected override Task RollbackMainProjectInternalAsync(Guid mainProjectId)
        {
            Logger.LogInformation($"主项目数据库 v1 到 v0 回滚针对项目ID: {mainProjectId} 无特定操作。");
            return Task.CompletedTask;
        }

        protected override Task UpgradeModelInternalAsync(Guid modelId)
        {
            Logger.LogInformation($"模型数据库 v0 到 v1 升级针对模型ID: {modelId} 无特定操作。");
            return Task.CompletedTask;
        }

        protected override Task RollbackModelInternalAsync(Guid modelId)
        {
            Logger.LogInformation($"模型数据库 v1 到 v0 回滚针对模型ID: {modelId} 无特定操作。");
            return Task.CompletedTask;
        }

        protected override Task UpgradeLibraryInternalAsync(Guid libraryId)
        {
            Logger.LogInformation($"库数据库 v0 到 v1 升级针对库ID: {libraryId} 无特定操作。");
            return Task.CompletedTask;
        }

        protected override Task RollbackLibraryInternalAsync(Guid libraryId)
        {
            Logger.LogInformation($"库数据库 v1 到 v0 回滚针对库ID: {libraryId} 无特定操作。");
            return Task.CompletedTask;
        }
    }
} 