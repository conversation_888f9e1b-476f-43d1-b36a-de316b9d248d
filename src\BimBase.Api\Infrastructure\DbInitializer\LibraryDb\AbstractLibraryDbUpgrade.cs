using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace BimBase.Api.Infrastructure.DbInitializer.LibraryDb
{
    /// <summary>
    /// LibraryDb数据库升级抽象基类
    /// 继承通用数据库升级基类，提供LibraryDb特定的升级功能
    /// </summary>
    public abstract class AbstractLibraryDbUpgrade : AbstractDatabaseUpgrade<LibraryDbContext>, ILibraryDbUpgrade
    {
        protected AbstractLibraryDbUpgrade(ILogger logger) : base(logger)
        {
        }

        public override abstract string FromVersion { get; }
        public override abstract string ToVersion { get; }
        public override abstract string Description { get; }
        public override bool SupportsRollback => false;

        public async Task UpgradeAsync(LibraryDbContext context)
        {
            Logger.LogInformation($"开始执行LibraryDb升级: {FromVersion} -> {ToVersion}");
            Logger.LogInformation($"升级描述: {Description}");

            try
            {
                await ExecuteUpgradeAsync(context);
                await UpdateVersionRecordAsync(context);
                Logger.LogInformation($"LibraryDb升级完成: {FromVersion} -> {ToVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"LibraryDb升级失败: {FromVersion} -> {ToVersion}");
                throw;
            }
        }

        public async Task RollbackAsync(LibraryDbContext context)
        {
            if (!SupportsRollback)
            {
                throw new NotSupportedException($"升级 {FromVersion} -> {ToVersion} 不支持回滚");
            }

            Logger.LogInformation($"开始回滚LibraryDb升级: {ToVersion} -> {FromVersion}");

            try
            {
                await ExecuteRollbackAsync(context);
                await UpdateVersionRecordForRollbackAsync(context);
                Logger.LogInformation($"LibraryDb回滚完成: {ToVersion} -> {FromVersion}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"LibraryDb回滚失败: {ToVersion} -> {FromVersion}");
                throw;
            }
        }

        protected abstract Task ExecuteUpgradeAsync(LibraryDbContext context);
        protected virtual Task ExecuteRollbackAsync(LibraryDbContext context)
        {
            throw new NotSupportedException("此升级不支持回滚");
        }

        /// <summary>
        /// 更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordAsync(LibraryDbContext context)
        {
            try
            {
                // 检查版本记录是否已存在于数据库中
                var existingRecord = await context.DatabaseVersions
                    .AsNoTracking()
                    .FirstOrDefaultAsync(v => v.Version == ToVersion);

                if (existingRecord == null)
                {
                    // 检查是否已经在当前上下文中跟踪了相同版本的实体
                    var trackedEntity = context.ChangeTracker.Entries<BimBase.Api.Infrastructure.LibDomain.DatabaseVersion>()
                        .FirstOrDefault(e => e.Entity.Version == ToVersion);

                    if (trackedEntity == null)
                    {
                        var versionRecord = new BimBase.Api.Infrastructure.LibDomain.DatabaseVersion
                        {
                            Version = ToVersion,
                            UpgradedAt = DateTime.Now,
                            Description = Description
                        };
                        context.DatabaseVersions.Add(versionRecord);
                        Logger.LogInformation($"版本记录已添加到上下文: {ToVersion}");
                    }
                    else
                    {
                        Logger.LogInformation($"版本 {ToVersion} 已在上下文中跟踪，跳过添加");
                    }
                }
                else
                {
                    Logger.LogInformation($"版本 {ToVersion} 已存在于数据库中，跳过添加");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"更新版本记录失败: {ToVersion}");
                throw;
            }
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }

        /// <summary>
        /// 回滚时更新版本记录
        /// </summary>
        private async Task UpdateVersionRecordForRollbackAsync(LibraryDbContext context)
        {
            // 删除当前版本记录
            var currentVersionRecord = await context.DatabaseVersions
                .Where(v => v.Version == ToVersion)
                .FirstOrDefaultAsync();

            if (currentVersionRecord != null)
            {
                context.DatabaseVersions.Remove(currentVersionRecord);
                Logger.LogInformation($"版本记录已从上下文中删除: {ToVersion}");
            }
            else
            {
                Logger.LogInformation($"未找到版本记录: {ToVersion}，跳过删除");
            }
            // 注意：这里不调用SaveChanges，让调用者统一处理
        }
    }
} 