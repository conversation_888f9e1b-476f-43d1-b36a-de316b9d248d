using System;
using System.Linq;
using System.Threading.Tasks;
using BimBase.Api.Infrastructure.DbInitializer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace BimBase.Api.Infrastructure.Database.Upgrades
{
    /// <summary>
    /// 数据库从v1版本升级到v2版本的实现
    /// 主要变更包括：
    /// 1. 向 `ClientModuleVersions` 表添加 `ProjectId` (VARCHAR(50) NULL) 列，用于关联项目信息
    /// 2. 向 `ClientModuleVersions` 表添加 `ClientIp` (VARCHAR(50) NULL) 列，用于记录客户机器IP
    /// 3. 向 `ClientModuleVersions` 表添加 `UserId` (VARCHAR(50) NULL) 列，用于记录用户ID
    /// 4. 向 `ClientModuleVersions` 表添加 `UserName` (VARCHAR(100) NULL) 列，用于记录用户名
    /// 5. 向 `loginsession` 表添加 `ClientGuid` (CHAR(36) NULL) 列，用于记录客户端GUID
    /// 6. 向 `mainprojects` 表添加 `status` (CHAR(30) NULL) 列，用于记录项目状态
    /// </summary>
    public class Upgrade_v1_to_v2 : AbstractDatabaseUpgrade
    {
        public override string SourceVersion => "v1";
        public override string TargetVersion => "v2";
        public override string Description => "为ClientModuleVersions表添加ProjectId、ClientIp、UserId、UserName字段，为loginsession表添加ClientGuid字段，为mainprojects表添加status字段";

        public Upgrade_v1_to_v2(IServiceProvider serviceProvider, ILogger<Upgrade_v1_to_v2> logger)
            : base(serviceProvider, logger)
        {
        }

        protected override async Task UpgradeInternalAsync()
        {
            Logger.LogInformation($"开始执行 {SourceVersion} 到 {TargetVersion} 的升级操作");
            
            var teamDbContext = GetTeamDbContext();
            await RegisterContextAsync(nameof(TeamDbContext), teamDbContext);

            // 添加ClientModuleVersions表的字段
            await AddProjectIdColumnAsync(teamDbContext);
            await AddClientIpColumnAsync(teamDbContext);
            await AddUserIdColumnAsync(teamDbContext);
            await AddUserNameColumnAsync(teamDbContext);

            // 添加其他表的字段
            await AddClientGuidToLoginSessionAsync(teamDbContext);
            await AddStatusToMainProjectsAsync(teamDbContext);

            // 创建索引
            await CreateProjectIdIndexAsync(teamDbContext);
            
            Logger.LogInformation($"完成 {SourceVersion} 到 {TargetVersion} 的升级操作");
        }

        protected override async Task RollbackInternalAsync()
        {
            Logger.LogInformation($"开始执行 {TargetVersion} 到 {SourceVersion} 的回滚操作");
            
            var teamDbContext = GetTeamDbContext();
            await RegisterContextAsync(nameof(TeamDbContext), teamDbContext);

            // 删除索引
            await DropProjectIdIndexAsync(teamDbContext);

            // 删除其他表的字段
            await DropStatusFromMainProjectsAsync(teamDbContext);
            await DropClientGuidFromLoginSessionAsync(teamDbContext);

            // 删除ClientModuleVersions表的字段（按相反顺序）
            await DropUserNameColumnAsync(teamDbContext);
            await DropUserIdColumnAsync(teamDbContext);
            await DropClientIpColumnAsync(teamDbContext);
            await DropProjectIdColumnAsync(teamDbContext);
            
            Logger.LogInformation($"完成 {TargetVersion} 到 {SourceVersion} 的回滚操作");
        }

        protected override Task UpgradeMainProjectInternalAsync(Guid mainProjectId)
        {
            Logger.LogInformation($"主项目数据库升级 ({SourceVersion}->{TargetVersion})，项目ID: {mainProjectId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        protected override Task RollbackMainProjectInternalAsync(Guid mainProjectId)
        {
            Logger.LogInformation($"主项目数据库回滚 ({TargetVersion}->{SourceVersion})，项目ID: {mainProjectId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        protected override Task UpgradeModelInternalAsync(Guid modelId)
        {
            Logger.LogInformation($"模型数据库升级 ({SourceVersion}->{TargetVersion})，模型ID: {modelId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        protected override Task RollbackModelInternalAsync(Guid modelId)
        {
            Logger.LogInformation($"模型数据库回滚 ({TargetVersion}->{SourceVersion})，模型ID: {modelId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        protected override Task UpgradeLibraryInternalAsync(Guid libraryId)
        {
            Logger.LogInformation($"库数据库升级 ({SourceVersion}->{TargetVersion})，库ID: {libraryId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        protected override Task RollbackLibraryInternalAsync(Guid libraryId)
        {
            Logger.LogInformation($"库数据库回滚 ({TargetVersion}->{SourceVersion})，库ID: {libraryId} - 无特定操作需要执行。");
            return Task.CompletedTask;
        }

        #region 私有方法 - 升级操作

        /// <summary>
        /// 添加ProjectId字段
        /// </summary>
        private async Task AddProjectIdColumnAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "ProjectId"))
            {
                Logger.LogInformation("ClientModuleVersions表ProjectId字段已存在，跳过添加");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` ADD COLUMN `ProjectId` VARCHAR(50) NULL;
                ");
                Logger.LogInformation("成功添加ProjectId列到ClientModuleVersions表");
        }

        /// <summary>
        /// 添加ClientIp字段
        /// </summary>
        private async Task AddClientIpColumnAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "ClientIp"))
            {
                Logger.LogInformation("ClientModuleVersions表ClientIp字段已存在，跳过添加");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` ADD COLUMN `ClientIp` VARCHAR(50) NULL;
                ");
                Logger.LogInformation("成功添加ClientIp列到ClientModuleVersions表");
        }

        /// <summary>
        /// 添加UserId字段
        /// </summary>
        private async Task AddUserIdColumnAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "UserId"))
            {
                Logger.LogInformation("ClientModuleVersions表UserId字段已存在，跳过添加");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` ADD COLUMN `UserId` VARCHAR(50) NULL;
                ");
                Logger.LogInformation("成功添加UserId列到ClientModuleVersions表");
        }

        /// <summary>
        /// 添加UserName字段
        /// </summary>
        private async Task AddUserNameColumnAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "UserName"))
            {
                Logger.LogInformation("ClientModuleVersions表UserName字段已存在，跳过添加");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` ADD COLUMN `UserName` VARCHAR(100) NULL;
                ");
                Logger.LogInformation("成功添加UserName列到ClientModuleVersions表");
        }

        /// <summary>
        /// 创建ProjectId索引
        /// </summary>
        private async Task CreateProjectIdIndexAsync(TeamDbContext teamDbContext)
        {
            if (await IndexExistsAsync(teamDbContext, "ClientModuleVersions", "IX_ClientModuleVersions_ProjectId"))
            {
                Logger.LogInformation("ClientModuleVersions表ProjectId字段的索引已存在，跳过创建");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    CREATE INDEX `IX_ClientModuleVersions_ProjectId` ON `ClientModuleVersions` (`ProjectId`);
                ");
                Logger.LogInformation("成功为ClientModuleVersions表的ProjectId字段创建索引");
            }

        /// <summary>
        /// 向loginsession表添加ClientGuid字段
        /// </summary>
        private async Task AddClientGuidToLoginSessionAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "loginsession", "ClientGuid"))
            {
                Logger.LogInformation("loginsession表ClientGuid字段已存在，跳过添加");
                return;
            }

            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `loginsession` ADD COLUMN `ClientGuid` CHAR(36) NULL;
            ");
            Logger.LogInformation("成功添加ClientGuid列到loginsession表");
        }

        /// <summary>
        /// 向mainprojects表添加status字段
        /// </summary>
        private async Task AddStatusToMainProjectsAsync(TeamDbContext teamDbContext)
        {
            if (await ColumnExistsAsync(teamDbContext, "mainprojects", "status"))
            {
                Logger.LogInformation("mainprojects表status字段已存在，跳过添加");
                return;
            }

            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `mainprojects` ADD COLUMN `status` CHAR(30) NULL;
            ");
            Logger.LogInformation("成功添加status列到mainprojects表");
        }

        #endregion

        #region 私有方法 - 回滚操作

        /// <summary>
        /// 删除ProjectId索引
        /// </summary>
        private async Task DropProjectIdIndexAsync(TeamDbContext teamDbContext)
        {
            if (!await IndexExistsAsync(teamDbContext, "ClientModuleVersions", "IX_ClientModuleVersions_ProjectId"))
            {
                Logger.LogInformation("ClientModuleVersions表ProjectId字段的索引不存在，跳过删除");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    DROP INDEX `IX_ClientModuleVersions_ProjectId` ON `ClientModuleVersions`;
                ");
                Logger.LogInformation("成功移除ClientModuleVersions表ProjectId字段的索引");
        }

        /// <summary>
        /// 删除ProjectId字段
        /// </summary>
        private async Task DropProjectIdColumnAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "ProjectId"))
            {
                Logger.LogInformation("ClientModuleVersions表ProjectId字段不存在，跳过删除");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` DROP COLUMN `ProjectId`;
                ");
                Logger.LogInformation("成功从ClientModuleVersions表中移除ProjectId列");
        }

        /// <summary>
        /// 删除ClientIp字段
        /// </summary>
        private async Task DropClientIpColumnAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "ClientIp"))
            {
                Logger.LogInformation("ClientModuleVersions表ClientIp字段不存在，跳过删除");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` DROP COLUMN `ClientIp`;
                ");
                Logger.LogInformation("成功从ClientModuleVersions表中移除ClientIp列");
        }

        /// <summary>
        /// 删除UserId字段
        /// </summary>
        private async Task DropUserIdColumnAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "UserId"))
            {
                Logger.LogInformation("ClientModuleVersions表UserId字段不存在，跳过删除");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` DROP COLUMN `UserId`;
                ");
                Logger.LogInformation("成功从ClientModuleVersions表中移除UserId列");
        }

        /// <summary>
        /// 删除UserName字段
        /// </summary>
        private async Task DropUserNameColumnAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "ClientModuleVersions", "UserName"))
            {
                Logger.LogInformation("ClientModuleVersions表UserName字段不存在，跳过删除");
                return;
            }

                await teamDbContext.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `ClientModuleVersions` DROP COLUMN `UserName`;
                ");
                Logger.LogInformation("成功从ClientModuleVersions表中移除UserName列");
            }

        /// <summary>
        /// 从loginsession表删除ClientGuid字段
        /// </summary>
        private async Task DropClientGuidFromLoginSessionAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "loginsession", "ClientGuid"))
            {
                Logger.LogInformation("loginsession表ClientGuid字段不存在，跳过删除");
                return;
            }

            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `loginsession` DROP COLUMN `ClientGuid`;
            ");
            Logger.LogInformation("成功从loginsession表中移除ClientGuid列");
        }

        /// <summary>
        /// 从mainprojects表删除status字段
        /// </summary>
        private async Task DropStatusFromMainProjectsAsync(TeamDbContext teamDbContext)
        {
            if (!await ColumnExistsAsync(teamDbContext, "mainprojects", "status"))
            {
                Logger.LogInformation("mainprojects表status字段不存在，跳过删除");
                return;
            }

            await teamDbContext.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE `mainprojects` DROP COLUMN `status`;
            ");
            Logger.LogInformation("成功从mainprojects表中移除status列");
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检查指定表的列是否存在
        /// </summary>
        /// <param name="teamDbContext">数据库上下文</param>
        /// <param name="tableName">表名</param>
        /// <param name="columnName">列名</param>
        /// <returns>是否存在</returns>
        private async Task<bool> ColumnExistsAsync(TeamDbContext teamDbContext, string tableName, string columnName)
        {
            var databaseName = teamDbContext.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND column_name = '{columnName}'";

            var result = await teamDbContext.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        /// <summary>
        /// 检查指定表的索引是否存在
        /// </summary>
        /// <param name="teamDbContext">数据库上下文</param>
        /// <param name="tableName">表名</param>
        /// <param name="indexName">索引名</param>
        /// <returns>是否存在</returns>
        private async Task<bool> IndexExistsAsync(TeamDbContext teamDbContext, string tableName, string indexName)
        {
            var databaseName = teamDbContext.Database.GetDbConnection().Database;
            
            var sql = $@"
                SELECT COUNT(*) 
                FROM information_schema.statistics 
                WHERE table_schema = '{databaseName}' 
                  AND table_name = '{tableName}' 
                  AND index_name = '{indexName}'";

            var result = await teamDbContext.Database.SqlQueryRaw<int>(sql).ToListAsync();
            return result.FirstOrDefault() > 0;
        }

        #endregion
    }
} 