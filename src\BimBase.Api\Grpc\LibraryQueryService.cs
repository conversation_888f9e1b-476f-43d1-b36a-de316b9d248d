﻿using AutoMapper;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Repositories;
using BimBase.Api.Infrastructure;
using BimBase.Api.Protos;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data.Common;
using System;
using static BimBase.Api.Protos.Library.GrpcLibraryQuery;
using System.Threading.Tasks;
using Grpc.Core;
using BimBase.Api.Infrastructure.Domain;
using BimBase.Api.Infrastructure.Common;
using System.Collections.Generic;
using BimBase.Api.Infrastructure.MainDomain;
using Serilog;
using System.Linq;
using BimBase.Api.Protos.Library;
using BimBase.Api.Infrastructure.LibDomain;
using System.Diagnostics;
using System.IO;
using Google.Protobuf;
using BimBase.Api.Infrastructure.DbInitializer;

namespace BimBase.Api.Grpc
{
    public class LibDataIdVersionOperType
    {
        public int Id;
        public long DataId;
        public OperationType OperType;
        public int VersionNo;
    }
    public class LibraryQueryService:GrpcLibraryQueryBase
    {
        private readonly UrlsConfig _urls;
        private readonly ITeamRepository _teamRepository;
        private readonly ILogger<LibraryQueryService> _logger;
        private readonly ILibraryRepository _libRepository;
        private IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;


        private readonly LibraryDbContext _libraryDbContext;
        public LibraryQueryService(IOptions<UrlsConfig> config, ILibraryRepository libraryRepository, ITeamRepository teamRepository
            , ILogger<LibraryQueryService> logger
            , IHttpContextAccessor httpContextAccessor
            , IMapper mapper
            , LibraryDbContext libraryDbContext)
        {
            _urls = config.Value ?? throw new ArgumentNullException(nameof(config));
            _libRepository = libraryRepository ?? throw new ArgumentNullException(nameof(libraryRepository));
            _teamRepository = teamRepository ?? throw new ArgumentNullException(nameof(teamRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _libraryDbContext = libraryDbContext ?? throw new ArgumentNullException(nameof(libraryDbContext));
        }
        /// <summary>
        /// 根据treeid获取树所在的项目id和mplibraryinfo
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetMainProjectGuidByLibTreeIdResponse> GetMainProjectGuidByLibTreeId(GetMainProjectGuidByLibTreeIdRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetMainProjectGuidByLibTreeIdResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var username = currentUser.LoginName;
            
            var treenode = _libRepository.CLCatalogTreeNodes.FirstOrDefault(t => t.TreeId == request.TreeId);
            if (treenode != null)
            {
                var libid = treenode.LibId;
                //查询企业级库中是否存在
                var companyLib = _libRepository.CLCompanyLibInfos.FirstOrDefault(c => c.LibId == libid);
                if (companyLib != null)
                {
                    x.Message = "树在企业库中";
                    return x;
                }
                else
                {
                    var mpList = _teamRepository.MainProjects.ToList();
                    foreach (var p in mpList)
                    {
                        var mpmanager = _teamRepository.GetMainProjectRepository(p.ID);
                        if (mpmanager != null)
                        {
                            var mplib = mpmanager.MPLibraryInfos.FirstOrDefault(s => s.LibId == libid);
                            if (mplib != null)
                            {
                                x.MainprojectGuid = p.ID.ToString();
                                x.LibraryInfo = _mapper.Map<GrpcMPLibraryInfo>(mplib);
                                x.IsSuccess = true;
                                return x;
                            }
                        }
                    }
                }

            }
            else
            {
                var mpList = _teamRepository.MainProjects.ToList();
                foreach (var p in mpList)
                {
                    var mpmanager = _teamRepository.GetMainProjectRepository(p.ID);
                    if (mpmanager != null)
                    {
                        var tree = mpmanager.MPCatalogTreeNodes.FirstOrDefault(t => t.TreeId == request.TreeId);
                        if (tree != null)
                        {
                            var mplib = mpmanager.MPLibraryInfos.FirstOrDefault(s => s.LibId == tree.LibId);
                            if (mplib != null)
                            {
                                x.MainprojectGuid = p.ID.ToString();
                                x.LibraryInfo = _mapper.Map<GrpcMPLibraryInfo>(mplib);
                                x.IsSuccess = true;
                                return x;
                            }
                        }

                    }
                }
            }
            return x;
        }
        /// <summary>
        /// 下载树节点的data数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<Protos.Library.DownLoadLibraryTreeNodeResponse> DownLoadLibraryTreeNode(Protos.Library.DownLoadLibraryTreeNodeRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new Protos.Library.DownLoadLibraryTreeNodeResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            string username = currentUser.LoginName;
            var libGuidAndTreeId = request.LibGuidAndTreeId;
            var libList = libGuidAndTreeId.Select(s => Guid.Parse(s.LibGuid)).ToList();
            var serverTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => libList.Contains(c.LibId)).ToList();
            var serverTreeNodeVersions = _libRepository.CLTreeNodesVersions.ToList();
            var serverLibDataHistories = _libRepository.CLLibraryDataHistories.Where(c => libList.Contains(c.LibId)).ToList();
            List<CLCatalogTreeNode> treeNodeInfoList = new List<CLCatalogTreeNode>();
            Dictionary<string, int> dicLibVersion = new Dictionary<string, int>();
            List<CLLibraryDataHistory> cLLibraryDataHistories = new List<CLLibraryDataHistory>();
            _logger.LogInformation(username + "==>lib=>DownLoadLibraryTreeNode 获取总量数据耗时：serverLibDataHistories:"+ serverLibDataHistories.Count);
            foreach (var item in libGuidAndTreeId)
            {
                var libGuid = Guid.Parse(item.LibGuid);
                var treeId = item.TreeId;
                var clientVer = item.ClientVersion;
                var treeNodeInfoListbylibguid = serverTreeNode.Where(c => c.LibId == libGuid && c.TreeId == treeId).ToList();
                treeNodeInfoList.AddRange(treeNodeInfoListbylibguid);
                //从历史表中获取历史记录
                _logger.LogInformation(username + "==>lib=>获取libGuid：" + libGuid + "的历史数据");
                var libverList = serverTreeNodeVersions.Where(s => s.LibId == libGuid).ToList();
                var currentUserVersion = libverList.Where(v => v.UserName == username).ToList();
                List<int> curVersions = new List<int>();
                if (currentUserVersion.Any())
                {
                    curVersions = currentUserVersion.Select(v => v.VersionNo).ToList();
                }
                //当前库最大版本号
                int maxLibVersion = 0;
                if (treeNodeInfoListbylibguid.Any())
                {
                    if (libverList.Any())
                    {
                        maxLibVersion = libverList.Max(v => v.VersionNo);
                    }

                }
                if (!dicLibVersion.ContainsKey(libGuid.ToString()))
                {
                    dicLibVersion.Add(libGuid.ToString(), maxLibVersion);
                }
                _logger.LogInformation(username + "==>lib=>libGuid：" + libGuid + "的当前最新版本号：" + maxLibVersion);
                _logger.LogInformation(username + "==>lib=>DownLoadLibraryTreeNode 获取libGuid " + libGuid );

                var querytree = treeNodeInfoListbylibguid.FirstOrDefault(c => c.LibId == libGuid && c.TreeId == treeId);
                if (querytree != null)
                {
                    var querytreeType = querytree.TreeType;
                    var libdataHistories = serverLibDataHistories.Where(c => c.LibId == libGuid && c.TreeType == querytreeType && c.VersionNo > clientVer && c.VersionNo <= maxLibVersion).ToList();
                    var dicLibHistory = libdataHistories.ToDictionary(g => g.ID, g => g);
                    var groupby = libdataHistories.Select(g => (new LibDataIdVersionOperType { Id = g.ID, DataId = g.DataId, OperType = g.OperationType, VersionNo = g.VersionNo }))
                                                  .ToList().GroupBy(s => s.DataId).ToDictionary(g => g.Key, g => g.ToList());

                    var manyhistory = libdataHistories.GroupBy(b => b.DataId).Select(g => new { dataid = g.Key, count = g.Count() }).Where(t => t.count > 1).ToList();

                    //dataid去重
                    var distinctdataidlist = libdataHistories.Select(t => t.DataId).Distinct().ToList();
                    
                    int i = 0;
                    foreach (var mul in groupby)
                    {

                        List<LibDataIdVersionOperType> hdvalueList = mul.Value;
                        var dataid = mul.Key;
                        //var datacount = libdataHistories.Where(h => h.DataId == dataid).ToList();
                        if (hdvalueList.Count > 1)
                        {
                            var maxOper = hdvalueList.Where(h => h.DataId == dataid).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                            var minOper = hdvalueList.Where(h => h.DataId == dataid).OrderBy(d => d.VersionNo).FirstOrDefault();
                            if (maxOper != null && minOper != null)
                            {
                                if (maxOper.OperType == OperationType.Delete)
                                {
                                    //20250325修改逻辑：先增后删的直接返回删除
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s=>s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        cLLibraryDataHistories.Add(change);
                                    }

                                }
                                else if (minOper.OperType == OperationType.Add)
                                {
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s => s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        change.OperationType = OperationType.Add;
                                        cLLibraryDataHistories.Add(change);
                                    }
                                }
                                else
                                {
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s => s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        change.OperationType = OperationType.Modify;
                                        cLLibraryDataHistories.Add(change);
                                    }
                                }
                            }
                        }
                        else
                        {
                            var only = hdvalueList.FirstOrDefault();
                            if (only != null)
                            {
                                CLLibraryDataHistory change = new CLLibraryDataHistory();
                                dicLibHistory.TryGetValue(only.Id, out change);
                                if (change != null)
                                {
                                    //change.OperationType = OperationType.Modify;
                                    cLLibraryDataHistories.Add(change);
                                }
                            }
                        }
                        i++;
                    }
                }
            }
            x.TreeNodeInfoList.AddRange(_mapper.Map<List<GrpcCLCatalogTreeNode>>(treeNodeInfoList));
            x.DicLibVersion.Add(dicLibVersion);
            x.LibDataHistoryList.AddRange(_mapper.Map<List<GrpcCLLibraryDataHistory>>(cLLibraryDataHistories));
            _logger.LogInformation(username + "==>lib=>DownLoadLibraryTreeNode：end:LibDataHistoryList:"+ x.LibDataHistoryList.Count);
            x.IsSuccess = true;
            return x;
        }
        private void SpliteCLLibHistoryData(List<long> libdataidlist,
            List<GrpcOperationRecordType> libdataopertypelist, int limitCount,
            Dictionary<long, GrpcOperationRecordType> dicLibOper,
            int limitLength,
            out List<GrpcCLLibraryDataHistory> outLibdata,
            out List<long> outlibdataidList,
            out List<GrpcOperationRecordType> outlibdataOperTypeList)
        {
            outLibdata = new List<GrpcCLLibraryDataHistory>();
            outlibdataOperTypeList = new List<GrpcOperationRecordType>();
            outlibdataidList = new List<long>();

            List<CLLibraryDataHistory> firstHdList = new List<CLLibraryDataHistory>();
            List<long> firstHdIdList = new List<long>();
            int hdEndSplit = 0;
            int minCount = Math.Min(libdataidlist.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                int j = 1;
                int hdidscount = minCount / j;
                var hdids = libdataidlist.Take(hdidscount).ToList();
                HashSet<int> hsids = new HashSet<int>(hdids.Select(k => (int)k));
                _logger.LogInformation("SpliteCLLibHistoryData:查询数据库：" + hdidscount + "条数据");
                var dataLength = _libRepository.CLLibraryDataHistories.Where(s => hsids.Contains(s.ID)).ToList();
                _logger.LogInformation("SpliteCLLibHistoryData:查询数据库：" + hdidscount + "条数据 end");
                //int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                hdEndSplit = hdidscount;
                firstHdList = dataLength;
                firstHdIdList = hdids;
            }
            _logger.LogInformation("SpliteCLLibHistoryData:构件分段数量hdEndSplit：" + hdEndSplit);
            _logger.LogInformation("SpliteCLLibHistoryData：开始组装outLibdata：");
            var temptest = firstHdList.Where(s => firstHdIdList.Contains(s.ID)).ToList();
            if (hdEndSplit > 0)
            {

                foreach (var item in temptest)
                {
                    item.OperationType = (OperationType)System.Enum.Parse(typeof(OperationType), dicLibOper[item.ID].ToString());//dicLibOper[item.ID];
                    outLibdata.Add(_mapper.Map<GrpcCLLibraryDataHistory>(item));
                }
                libdataidlist.RemoveRange(0, hdEndSplit);
                libdataopertypelist.RemoveRange(0, hdEndSplit);
            }
            
            _logger.LogInformation("SpliteMPTreeNode：结束组装outLibdata");
            outlibdataidList = libdataidlist;
            outlibdataOperTypeList = libdataopertypelist;
            _logger.LogInformation("SpliteMPTreeNode:剩余待下载mplibtreenodehistory数量：" + libdataidlist.Count());
        }
        private void SpliteCLCataLogTree(List<int> catalogidlist,
            int limitCount,
            int limitLength,
            out List<GrpcCLCatalogTreeNode> outCataLog,
            out List<int> outcatalogidList)
        {
            outCataLog = new List<GrpcCLCatalogTreeNode>();
            outcatalogidList = new List<int>();
            //开始MPCataLog数据分段
            int limitCataLogLenth = limitLength;
            int realCataLogLenth = 0;
            List<CLCatalogTreeNode> firstHdList = new List<CLCatalogTreeNode>();
            List<int> firstHdIdList = new List<int>();
            int hdEndSplit = 0;
            int minCount = Math.Min(catalogidlist.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                int j = 1;
                int hdidscount = minCount / j;
                var hdids = catalogidlist.Take(hdidscount).ToList();
                HashSet<int> hsids = new HashSet<int>(hdids);
                var dataLength = _libRepository.CLCatalogTreeNodes.Where(s => (hsids.Contains(s.ID))).ToList();
                //int x = dataLength.Where(s => s.NodeName != null).Sum(s => s.NodeName.Length);

                hdEndSplit = hdidscount;
                firstHdList = dataLength;
                firstHdIdList = hdids;
            }
            _logger.LogInformation("SpliteCLCataLogTree:首次构件分段数量hdEndSplit：" + hdEndSplit);
            if (hdEndSplit > 0)
            {
                outCataLog.AddRange(_mapper.Map<List<GrpcCLCatalogTreeNode>>(firstHdList));
                catalogidlist.RemoveRange(0, hdEndSplit);


            }
            limitCataLogLenth = limitCataLogLenth - realCataLogLenth;
            realCataLogLenth = 0;
            outcatalogidList = catalogidlist;
            _logger.LogInformation("SpliteCLCataLogTree:剩余待下载clcatalogtreenode数量：" + catalogidlist.Count());
        }

        private bool DownLoadCLLibDataCommon(string username, int limitCount, int limitLength, Dictionary<string, int> dicLibVersion, List<CLDownLoadLibTree> libGuidAndTreeId,
            out List<int> outcatalogidList,
            out List<long> outlibdataidList,
            out List<GrpcOperationRecordType> outlibdataOperTypeList,
            out List<GrpcCLLibraryDataHistory> outLibdata,
            out List<GrpcCLCatalogTreeNode> outCataLog)
        {
            outcatalogidList = new List<int>();
            outlibdataidList = new List<long>();
            outlibdataOperTypeList = new List<GrpcOperationRecordType>();
            outLibdata = new List<GrpcCLLibraryDataHistory>();
            outCataLog = new List<GrpcCLCatalogTreeNode>();

            List<int> catalogidlist = new List<int>();
            List<long> libdataidlist = new List<long>();
            List<GrpcOperationRecordType> libdataopertypelist = new List<GrpcOperationRecordType>();
            var outDicHistoryOper = new Dictionary<int, OperationType>();
            int minCount = 0;
            var libList = libGuidAndTreeId.Select(s => Guid.Parse(s.LibGuid)).ToList();

            var serverTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => libList.Contains(c.LibId)).ToList();
            var serverTreeNodeVersions = _libRepository.CLTreeNodesVersions.ToList();
            var serverLibDataHistories = _libRepository.CLLibraryDataHistories.Where(c => libList.Contains(c.LibId)).ToList();

            foreach (var item in libGuidAndTreeId)
            {

                var libGuid = item.LibGuid;
                var libid = Guid.Parse(libGuid);
                var treeId = item.TreeId;
                _logger.LogInformation(username + "==>DownLoadLibDataCommon TreeId:" + treeId);
                var clientVer = item.ClientVersion;
                _logger.LogInformation(username + "==>DownLoadLibDataCommon clientVer:" + clientVer);
                //从历史表中获取历史记录
                _logger.LogInformation(username + "获取libGuid：" + libGuid + "的历史数据");
                var libverList = serverTreeNodeVersions.Where(s => s.LibId == libid).ToList();
                var currentUserVersion = libverList.Where(v => v.UserName == username).ToList();

                var treeNodeInfoListbylibguid = serverTreeNode.Where(c => c.TreeId == treeId).ToList();
                var catalogids = treeNodeInfoListbylibguid.Select(s => s.ID).ToList();
                //treeNodeInfoList.AddRange(treeNodeInfoListbylibguid);
                catalogidlist.AddRange(catalogids);
                //当前库最大版本号
                int maxLibVersion = 0;

                if (dicLibVersion.ContainsKey(libGuid))
                {
                    dicLibVersion.TryGetValue(libGuid, out maxLibVersion);
                }
                _logger.LogInformation(username + "libGuid：" + libGuid + "的当前最新版本号：" + maxLibVersion);


                var querytree = treeNodeInfoListbylibguid.FirstOrDefault();
                if (querytree != null)
                {
                    var querytreeType = querytree.TreeType;
                    var libdataHistories = serverLibDataHistories.Where(c => c.TreeType == querytreeType && c.VersionNo > clientVer && c.VersionNo <= maxLibVersion).ToList();

                    var dicLibHistory = libdataHistories.ToDictionary(g => g.ID, g => g);

                    var groupby = libdataHistories.Select(g => (new LibDataIdVersionOperType { Id = g.ID, DataId = g.DataId, OperType = g.OperationType, VersionNo = g.VersionNo }))
                                              .ToList().GroupBy(s => s.DataId).ToDictionary(g => g.Key, g => g.ToList());

                    //dataid去重
                    var distinctdataidlist = libdataHistories.Select(t => t.DataId).Distinct().ToList();

                    foreach (var mul in groupby)
                    {
                        List<LibDataIdVersionOperType> hdvalueList = mul.Value;
                        var dataid = mul.Key;
                        if (hdvalueList.Count > 1)
                        {
                            var maxOper = hdvalueList.Where(h => h.DataId == dataid).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                            var minOper = hdvalueList.Where(h => h.DataId == dataid).OrderBy(d => d.VersionNo).FirstOrDefault();
                            if (maxOper != null && minOper != null)
                            {
                                if (maxOper.OperType == OperationType.Delete)
                                {
                                    //20250325修改逻辑：先增后删的直接返回删除
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    if (change != null)
                                    {
                                        if (!outDicHistoryOper.ContainsKey(change.ID))
                                        {
                                            outDicHistoryOper.Add(change.ID, change.OperationType);
                                        }
                                        libdataidlist.Add(change.ID);
                                        libdataopertypelist.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), change.OperationType.ToString()));
                                    }
                                }
                                else if (minOper.OperType == OperationType.Add)
                                {
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    if (change != null)
                                    {
                                        change.OperationType = OperationType.Add;
                                        if (!outDicHistoryOper.ContainsKey(change.ID))
                                        {
                                            outDicHistoryOper.Add(change.ID, change.OperationType);
                                        }
                                        libdataidlist.Add(change.ID);
                                        libdataopertypelist.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), change.OperationType.ToString()));
                                    }
                                }
                                else
                                {
                                    CLLibraryDataHistory change = new CLLibraryDataHistory();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    if (change != null)
                                    {
                                        change.OperationType = OperationType.Modify;
                                        if (!outDicHistoryOper.ContainsKey(change.ID))
                                        {
                                            outDicHistoryOper.Add(change.ID, change.OperationType);
                                        }
                                        libdataidlist.Add(change.ID);
                                        libdataopertypelist.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), change.OperationType.ToString()));
                                    }
                                }
                            }
                        }
                        else
                        {
                            var only = hdvalueList.FirstOrDefault();
                            if (only != null)
                            {
                                CLLibraryDataHistory change = new CLLibraryDataHistory();
                                dicLibHistory.TryGetValue(only.Id, out change);
                                // var change = libdataHistories.FirstOrDefault(s => s.ID == only.Id);
                                if (change != null)
                                {
                                    //change.OperationType = MPOperationType.Modify;
                                    if (!outDicHistoryOper.ContainsKey(change.ID))
                                    {
                                        outDicHistoryOper.Add(change.ID, change.OperationType);
                                    }
                                    libdataidlist.Add(change.ID);
                                    libdataopertypelist.Add((GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), change.OperationType.ToString()));
                                }
                            }
                        }
                    }

                    _logger.LogInformation(username + "|libGuid==>" + libGuid + "==>DownLoadLibDataCommon 第一次获取数据 end");
                }
            }


            //开始MPLibraryDataHistory数据分段
            int limitLibDataLenth = limitLength;
            List<CLLibraryDataHistory> firstHdList = new List<CLLibraryDataHistory>();
            List<long> firstHdIdList = new List<long>();
            int hdEndSplit = 0;
            minCount = Math.Min(libdataidlist.Count, limitCount);
            if (minCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                int j = 1;
                int hdidscount = minCount / j;
                var hdids = libdataidlist.Take(hdidscount).ToList();
                HashSet<long> hsids = new HashSet<long>(hdids);
                _logger.LogInformation("DownLoadLibDataCommon:HashSet：" + hsids.Count);
                var dataLength = serverLibDataHistories.Where(s => (hsids.Contains(s.ID))).ToList();
                _logger.LogInformation("DownLoadLibDataCommon:dataLength：" + dataLength.Count);
                //int x = dataLength.Where(s => s.Data != null).Sum(s => s.Data.Length);

                hdEndSplit = hdidscount;
                firstHdList = dataLength;
                firstHdIdList = hdids;
            }
            _logger.LogInformation("DownLoadLibDataCommon:测试直接获取：");
            var temptest = firstHdList.Where(s => firstHdIdList.Contains(s.ID)).ToList();
            if (hdEndSplit > 0)
            {

                foreach (var item in temptest)
                {
                    item.OperationType = outDicHistoryOper[item.ID];
                    outLibdata.Add(_mapper.Map<GrpcCLLibraryDataHistory>(item));
                }
                libdataidlist.RemoveRange(0, hdEndSplit);
                libdataopertypelist.RemoveRange(0, hdEndSplit);
            }
            _logger.LogInformation("DownLoadLibDataCommon:测试直接获取：end");
            //log.Info("DownLoadLibDataCommon:首次构件分段数量hdEndSplit：" + hdEndSplit);
            //if (hdEndSplit > 0)
            //{
            //    firstHdList.Where(s => firstHdIdList.Contains(s.ID)).ToList();
            //    for (int i = 0; i < firstHdIdList.Count(); i++)
            //    {
            //        var id = firstHdIdList[i];
            //        var change = firstHdList.Where(s => s.ID == id).FirstOrDefault();
            //        if (change != null)
            //        {
            //            change.OperationType = libdataopertypelist[i];
            //            outLibdata.Add(change);
            //        }
            //    }
            //    libdataidlist.RemoveRange(0, hdEndSplit);
            //    libdataopertypelist.RemoveRange(0, hdEndSplit);

            //}
            //log.Info("DownLoadLibDataCommon:首次构件分段end：" );
            outlibdataidList = libdataidlist;
            outlibdataOperTypeList = libdataopertypelist;
            _logger.LogInformation("DownLoadLibDataCommon:剩余待下载mplibtreenodehistory数量：" + libdataidlist.Count());

            //开始MPCataLog数据分段
            List<CLCatalogTreeNode> CafirstHdList = new List<CLCatalogTreeNode>();
            List<int> CafirstHdIdList = new List<int>();
            int CahdEndSplit = 0;
            int CaminCount = Math.Min(catalogidlist.Count, limitCount);
            if (CaminCount > 0)
            {
                //数量超过10000，必分段
                //每段data长度不能超过500M，超过500M，再长度减半重新判断是否超过500M
                int j = 1;
                int hdidscount = CaminCount / j;
                var hdids = catalogidlist.Take(hdidscount).ToList();
                HashSet<int> hsids = new HashSet<int>(hdids);
                _logger.LogInformation("DownLoadLibDataCommon:HashSet：" + hsids.Count);
                var dataLength = serverTreeNode.Where(s => (hsids.Contains(s.ID))).ToList();
                _logger.LogInformation("DownLoadLibDataCommon:dataLength：" + dataLength.Count);
                //int x = dataLength.Where(s => s.NodeName != null).Sum(s => s.NodeName.Length);

                CahdEndSplit = hdidscount;
                CafirstHdList = dataLength;
                CafirstHdIdList = hdids;
            }
            _logger.LogInformation("DownLoadLibDataCommon:首次构件分段数量hdEndSplit：" + hdEndSplit);
            if (CahdEndSplit > 0)
            {

                outCataLog.AddRange(_mapper.Map<List<GrpcCLCatalogTreeNode>>(CafirstHdList));
                catalogidlist.RemoveRange(0, CahdEndSplit);

            }
            outcatalogidList = catalogidlist;
            _logger.LogInformation("DownLoadLibDataCommon:剩余待下载mpcatalogtreenode数量：" + catalogidlist.Count());
            return true;
        }
        public override async Task<Protos.Library.DownLoadLibraryTreeNodeLargeResponse> DownLoadLibraryTreeNodeLarge(Protos.Library.DownLoadLibraryTreeNodeLargeRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {sessionid},{libGuidAndTreeId}", context.Method, request.SessionId,request.LibGuidAndTreeId);
            var x = new Protos.Library.DownLoadLibraryTreeNodeLargeResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            int limitLength = 500 * 1024 * 1024;
            int limitCount = 20000;
            var requestId = request.RequestId;
            var libGuidAndTreeId = request.LibGuidAndTreeId.ToList();
            var toDownCLCataLogTreeNodeIdList = request.ToDownCLCataLogTreeNodeIdList.ToList();
            var toDownCLLibDataHistoryIdList = request.ToDownCLLibDataHistoryIdList.ToList();
            var toDownCLLibDataHistoryOpertypeList = request.ToDownCLLibDataHistoryOpertypeList.ToList();
            Dictionary<string, int> dicLibVersion = new Dictionary<string, int>();
            var username = currentUser.LoginName;
            //log.Info(username + "==>DownLoadLibraryTreeNode mpguidlist:" + mpguidlist.Count);
            List<GrpcCLCatalogTreeNode> treeNodeInfoList = new List<GrpcCLCatalogTreeNode>();
            List<GrpcCLLibraryDataHistory> libDataHistoryList = new List<GrpcCLLibraryDataHistory>();
            //通过toDownMPCataLogTreeNodeIdList和toDownMPLibDataHistoryIdList判断是否第一次下载，如果都为空就是第一次下载
            List<GrpcCLLibraryDataHistory> outLibDatas = new List<GrpcCLLibraryDataHistory>();
            List<GrpcCLCatalogTreeNode> outCataLogs = new List<GrpcCLCatalogTreeNode>();
            
            List<long> outLibIds = new List<long>();
            List<int> outCataLogIds = new List<int>();
            List<GrpcOperationRecordType> outLibOperTypeList = new List<GrpcOperationRecordType>();
            if (toDownCLCataLogTreeNodeIdList.Any() || toDownCLLibDataHistoryIdList.Any())
            {
                //第二次下载
                if (toDownCLLibDataHistoryIdList.Any())
                {
                    //MPLibDataHistory分段


                    //MPCataLogTreeNodeId 分段
                    //SpliteMPTreeNode();
                    Dictionary<long, GrpcOperationRecordType> dicLibOper = toDownCLLibDataHistoryIdList.Zip(toDownCLLibDataHistoryOpertypeList, (k, v) => new { k, v })
                                  .ToDictionary(x => x.k, x => x.v);
                    SpliteCLLibHistoryData(toDownCLLibDataHistoryIdList, toDownCLLibDataHistoryOpertypeList,
                        limitCount, dicLibOper, limitLength,
                        out outLibDatas, out outLibIds, out outLibOperTypeList);
                }
                if (toDownCLCataLogTreeNodeIdList.Any())
                {

                    //MPCataLogTreeNode分段

                    SpliteCLCataLogTree(toDownCLCataLogTreeNodeIdList, limitCount, limitLength, out outCataLogs,
                        out outCataLogIds);
                }
                if (outLibIds.Count > 0)
                {
                    x.ToDownCLLibDataHistoryIdList.AddRange(outLibIds);
                    x.ToDownCLLibDataHistoryOpertypeList.AddRange(outLibOperTypeList);
                }
                if (outCataLogIds.Count > 0)
                {
                    x.ToDownCLCataLogTreeNodeIdList.AddRange(outCataLogIds);
                }
                if (outLibIds.Count <= 0 && outCataLogIds.Count <= 0)
                {
                    requestId = 0;
                }
                x.IsSuccess = true;
                x.RequestId = requestId;
            }
            else
            {
                //第一次下载
                var libList = libGuidAndTreeId.Select(s =>Guid.Parse(s.LibGuid)).ToList();

                var serverTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => libList.Contains(c.LibId)).ToList();
                var serverTreeNodeVersions = _libRepository.CLTreeNodesVersions.ToList();
                var serverLibDataHistories = _libRepository.CLLibraryDataHistories.Where(c => libList.Contains(c.LibId)).ToList();

                foreach (var item in libGuidAndTreeId)
                {
                    var libGuid = item.LibGuid;
                    var libid = Guid.Parse(libGuid);
                    var treeId = item.TreeId;
                    var libverList = serverTreeNodeVersions.Where(s => s.LibId == libid).ToList();
                    var treeNodeInfoListbylibguid = serverTreeNode.Where(c => c.TreeId == treeId).ToList();
                    //treeNodeInfoList.AddRange(treeNodeInfoListbylibguid);

                    //当前库最大版本号
                    int maxLibVersion = 0;
                    if (treeNodeInfoListbylibguid.Any())
                    {
                        maxLibVersion = libverList.Max(v => v.VersionNo);
                    }
                    if (!dicLibVersion.ContainsKey(libGuid))
                    {
                        dicLibVersion.Add(libGuid, maxLibVersion);
                    }
                    _logger.LogInformation(username + "Protos.Library.DownLoadLibraryTreeNodeLarge ==>cllibGuid：" + libid + "的当前最新版本号：" + maxLibVersion);

                    //List<int> mpcatalogIds = new List<int>();        //项目资源库自增id列表
                    //List<long> mplibHistoryIds = new List<long>();  //项目资源库节点历史记录自增id
                    //List<GrpcOperationRecordType> mplibopertypelist = new List<GrpcOperationRecordType>();  //项目资源库节点历史记录操作方式（增删改）
                    DownLoadCLLibDataCommon(username, limitCount, limitLength, dicLibVersion, libGuidAndTreeId, out outCataLogIds,
                        out outLibIds, out outLibOperTypeList, out libDataHistoryList, out treeNodeInfoList);
                    if (outLibIds.Count > 0)
                    {
                        x.ToDownCLLibDataHistoryIdList.AddRange(outLibIds);
                        x.ToDownCLLibDataHistoryOpertypeList.AddRange(outLibOperTypeList);
                    }
                    if (outCataLogIds.Count > 0)
                    {
                        x.ToDownCLCataLogTreeNodeIdList.AddRange(outCataLogIds);
                    }
                    if (outLibIds.Count <= 0 && outCataLogIds.Count <= 0)
                    {
                        requestId = 0;
                    }
                    x.IsSuccess = true;
                    x.RequestId = requestId;
                }
            }
            //log.Info(username + "==>DownLoadLibraryTreeNode end,一共耗时：" + stopwatch.ElapsedMilliseconds);
            x.DicLibVersion.Add(dicLibVersion);
            x.IsSuccess = true;
            return x;
        }
        public override async Task<Protos.Library.DownLoadLibraryTreeNodeChangeResponse> DownLoadLibraryTreeNodeChange(Protos.Library.DownLoadLibraryTreeNodeChangeRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);
            var x = new Protos.Library.DownLoadLibraryTreeNodeChangeResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            string username = currentUser.LoginName;
            var libGuidAndTreeId = request.LibGuidAndTreeId;
            var libList = libGuidAndTreeId.Select(s => Guid.Parse(s.LibGuid)).ToList();
            var libStrList = libGuidAndTreeId.Select(s => s.LibGuid).ToList();
            var cllibinfo = _libRepository.CLCompanyLibInfos.Where(c => libList.Contains(c.LibId)).ToList();
            var serverTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => libList.Contains(c.LibId)).ToList();
            var serverTreeNodeVersions = _libRepository.CLTreeNodesVersions.ToList();
            var serverLibDataHistories = _libRepository.CLLibraryDataHistories
                .Select(
                        c => new CLLibraryDataHistoryForClient
                        {
                            ID = c.ID,
                            LibId = c.LibId.ToString().ToLower(),
                            DataId = c.DataId,
                            OperationType = (GrpcOperationRecordType)System.Enum.Parse(typeof(GrpcOperationRecordType), c.OperationType.ToString()),
                            VersionNo = c.VersionNo,
                            ClassName = c.ClassName,
                            SchemaName = c.SchemaName
                        })
                .Where(c => libStrList.Contains(c.LibId)).ToList();
            List<CLCatalogTreeNode> treeNodeInfoList = new List<CLCatalogTreeNode>();
            Dictionary<string, int> dicLibVersion = new Dictionary<string, int>();
            List<CLLibraryDataHistoryForClient> cLLibraryDataHistories = new List<CLLibraryDataHistoryForClient>();
            //log.Info(username + "==>lib=>DownLoadLibraryTreeNode 获取总量数据耗时：" + stopwatch.ElapsedMilliseconds + "毫秒");
            foreach (var item in libGuidAndTreeId)
            {
                var libGuid = Guid.Parse(item.LibGuid);
                var treeId = item.TreeId;
                var clientVer = item.ClientVersion;
                var treeNodeInfoListbylibguid = serverTreeNode.Where(c => c.LibId == libGuid && c.TreeId == treeId).ToList();
                treeNodeInfoList.AddRange(treeNodeInfoListbylibguid);
                //从历史表中获取历史记录
                //log.Info(username + "==>lib=>获取libGuid：" + libGuid + "的历史数据");
                var libverList = serverTreeNodeVersions.Where(s => s.LibId == libGuid).ToList();
                var currentUserVersion = libverList.Where(v => v.UserName == username).ToList();
                List<int> curVersions = new List<int>();
                if (currentUserVersion.Any())
                {
                    curVersions = currentUserVersion.Select(v => v.VersionNo).ToList();
                }
                //当前库最大版本号
                int maxLibVersion = 0;
                if (treeNodeInfoListbylibguid.Any())
                {
                    if (libverList.Any())
                    {
                        maxLibVersion = libverList.Max(v => v.VersionNo);
                    }

                }
                if (!dicLibVersion.ContainsKey(libGuid.ToString()))
                {
                    dicLibVersion.Add(libGuid.ToString(), maxLibVersion);
                }
                //log.Info(username + "==>lib=>libGuid：" + libGuid + "的当前最新版本号：" + maxLibVersion);
                //log.Info(username + "==>lib=>DownLoadLibraryTreeNode 获取libGuid " + libGuid + " 版本号耗时：" + stopwatch.ElapsedMilliseconds + "毫秒");

                var querytree = cllibinfo.FirstOrDefault(c => c.LibId == libGuid);
                if (querytree != null)
                {
                    var libdataHistories = serverLibDataHistories.Where(c => c.LibId == libGuid.ToString().ToLower() && c.VersionNo > clientVer && c.VersionNo <= maxLibVersion).ToList();
                    var dicLibHistory = libdataHistories.ToDictionary(g => g.ID, g => g);
                    var groupby = libdataHistories.Select(g => (new LibDataIdVersionOperType { 
                        Id = g.ID, 
                        DataId = g.DataId, 
                        OperType = (OperationType)System.Enum.Parse(typeof(OperationType), g.OperationType.ToString()),
                        VersionNo = g.VersionNo }
                    )).ToList().GroupBy(s => s.DataId).ToDictionary(g => g.Key, g => g.ToList());

                    var manyhistory = libdataHistories.GroupBy(b => b.DataId).Select(g => new { dataid = g.Key, count = g.Count() }).Where(t => t.count > 1).ToList();

                    //dataid去重
                    var distinctdataidlist = libdataHistories.Select(t => t.DataId).Distinct().ToList();

                    int i = 0;
                    foreach (var mul in groupby)
                    {

                        List<LibDataIdVersionOperType> hdvalueList = mul.Value;
                        var dataid = mul.Key;
                        //var datacount = libdataHistories.Where(h => h.DataId == dataid).ToList();
                        if (hdvalueList.Count > 1)
                        {
                            var maxOper = hdvalueList.Where(h => h.DataId == dataid).OrderByDescending(d => d.VersionNo).FirstOrDefault();
                            var minOper = hdvalueList.Where(h => h.DataId == dataid).OrderBy(d => d.VersionNo).FirstOrDefault();
                            if (maxOper != null && minOper != null)
                            {
                                if (maxOper.OperType == OperationType.Delete)
                                {
                                    CLLibraryDataHistoryForClient change = new CLLibraryDataHistoryForClient();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s=>s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        cLLibraryDataHistories.Add(change);
                                    }
                                }
                                else if (minOper.OperType == OperationType.Add)
                                {
                                    CLLibraryDataHistoryForClient change = new CLLibraryDataHistoryForClient();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s => s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        change.OperationType = GrpcOperationRecordType.Add;
                                        cLLibraryDataHistories.Add(change);
                                    }
                                }
                                else
                                {
                                    CLLibraryDataHistoryForClient change = new CLLibraryDataHistoryForClient();
                                    dicLibHistory.TryGetValue(maxOper.Id, out change);
                                    //var change = libdataHistories.FirstOrDefault(s => s.ID == maxOper.Id);
                                    if (change != null)
                                    {
                                        change.OperationType = GrpcOperationRecordType.Modify;
                                        cLLibraryDataHistories.Add(change);
                                    }
                                }
                            }
                        }
                        else
                        {
                            var only = hdvalueList.FirstOrDefault();
                            if (only != null)
                            {
                                CLLibraryDataHistoryForClient change = new CLLibraryDataHistoryForClient();
                                dicLibHistory.TryGetValue(only.Id, out change);
                                if (change != null)
                                {
                                    //change.OperationType = OperationType.Modify;
                                    cLLibraryDataHistories.Add(change);
                                }
                            }
                        }
                        i++;
                    }
                }
            }
            
            x.DicLibVersion.Add(dicLibVersion);
            x.LibDataHistoryList.AddRange(cLLibraryDataHistories);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 获取企业库信息
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetAllCompanyLibraryInfoListResponse> GetAllCompanyLibraryInfoList(SessionRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetAllCompanyLibraryInfoListResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var username = currentUser.LoginName;

            var mainprojectliblist = _libRepository.CLCompanyLibInfos.ToList();
            if (!mainprojectliblist.Any())
            {
                mainprojectliblist = _libRepository.InitAllCLCompanyLibInfo().ToList();
            }
            List<CLCatalogTreeNode> libAllTreeInfoList = new List<CLCatalogTreeNode>();
            if (mainprojectliblist.Any())
            {
                var libids = mainprojectliblist.Select(s => s.LibId).ToList();
                var serverTreeNodes = _libRepository.CLCatalogTreeNodes.Where(c => libids.Contains(c.LibId)).ToList();
                foreach (var item in mainprojectliblist)
                {
                    var templist = serverTreeNodes.Where(l => l.LibId == item.LibId && (l.ParentNodeId == -2)).ToList();
                    if (templist.Any())
                    {
                        libAllTreeInfoList.AddRange(templist);
                    }
                    else
                    {

                        CLCatalogTreeNode libTreeNode = new CLCatalogTreeNode
                        {
                            LibId = item.LibId,
                            TreeId = -1,
                            TreeType = item.LibType,
                            ID = 0,
                            InstanceId = 0,
                            NodeId = 0,
                            ParentNodeId = -2,
                            NodeType = 0,
                            NodeName = "",
                            bPDataKey = 0,
                            modelnfoKey = 0
                        };
                        libAllTreeInfoList.Add(libTreeNode);
                    }
                }
            }
            List<GrpcCLCatalogTreeNode> retGrpcList = _mapper.Map<List<GrpcCLCatalogTreeNode>>(libAllTreeInfoList);
            x.LibAllTreeInfoList.AddRange(retGrpcList);
            //foreach (var item in x.LibAllTreeInfoList)
            //{
            //    x.Message += item.LibId+"|"+item.TreeType.ToString()+";";
            //}
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 获取treeid下所有nodeid对应的锁定信息及版本号
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetLibraryElementVersionAndLockInfoByTreeListResponse> GetLibraryElementVersionAndLockInfoByTreeList(GetLibraryElementVersionAndLockInfoByTreeListRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetLibraryElementVersionAndLockInfoByTreeListResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }

            var username = currentUser.LoginName;
            string errMsg = "";
            var treeIdList = request.TreeIdList;
            var servermainprojectlibs = _libRepository.CLMainprojectLibs.Where(c => treeIdList.Contains(c.TreeId)).ToList();
            var alllockedlib = _libRepository.CLTreeNodesLocks.ToList();
            var allClTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => treeIdList.Contains(c.TreeId)).ToList();
            List<LibLockandVersionInfo> libInfoList = new List<LibLockandVersionInfo>();
            var requestmainprojectid = request.MainprojectGuid;
            var mainprojectGuid = Guid.Empty;
            if (!Guid.TryParse(request.MainprojectGuid, out mainprojectGuid))
            {
                x.Message = $"参数{request.MainprojectGuid}格式有误！";
                return x;
            }
            foreach (long id in treeIdList)
            {
                LibLockandVersionInfo libLockandVersionInfo = new LibLockandVersionInfo();
                libLockandVersionInfo.TreeId = id;
                List<NodeWithLockAndVersionInfo> nodeInfoList = new List<NodeWithLockAndVersionInfo>();
                var MainprojectLib = servermainprojectlibs.FirstOrDefault(c => c.TreeId == id&&c.MainProjectGuid == mainprojectGuid);


                List<CLTreeNodeLock> toLockLib = new List<CLTreeNodeLock>();

                List<long> instanceidlist = new List<long>();
                Dictionary<long, int> serverDatas = new Dictionary<long, int>();
                Guid libGuid = Guid.Empty;
                
                if (MainprojectLib == null)
                {
                    var msg = "企业库";
                    //为企业库
                    _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeList err:" + msg);
                    var companyLib = allClTreeNode.FirstOrDefault(c => c.TreeId == id);
                    if (companyLib == null)
                    {
                        errMsg = "未找到treeid" + id + "所在的库信息,";
                        _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeList err:" + errMsg);
                        x.Message = errMsg;
                        x.IsSuccess = false;
                        return x;
                    }
                    libGuid = companyLib.LibId;
                    //判断是否被其他人锁定
                    var libnodeinfos = allClTreeNode.Where(l => l.LibId == libGuid && l.TreeId == id).ToList();
                    instanceidlist = libnodeinfos.Select(s => s.InstanceId).ToList();
                    serverDatas = libnodeinfos.Where(a => instanceidlist.Contains(a.InstanceId)).ToDictionary(g => g.InstanceId, g => g.VersionNo);//.ToList();

                }
                else
                {

                    libGuid = MainprojectLib.LibGuid;
                    var mainprojectManager = _teamRepository.GetMainProjectRepository(mainprojectGuid);
                    if (mainprojectManager != null)
                    {
                        var libnodeinfos = mainprojectManager.MPCatalogTreeNodes.Where(l => l.LibId == libGuid && l.TreeId == id).ToList();
                        instanceidlist = libnodeinfos.Select(s => s.InstanceId).ToList();
                        serverDatas = libnodeinfos.Where(a => instanceidlist.Contains(a.InstanceId)).ToDictionary(g => g.InstanceId, g => g.VersionNo);//.ToList();
                    }
                    else
                    {
                        continue;
                    }
                }
                HashSet<long> hsInstances = new HashSet<long>(instanceidlist);
                //获取锁定信息
                var locked = alllockedlib.Where(l => l.LibId == libGuid && hsInstances.Contains(l.NodeId)).ToList().GroupBy(s => s.NodeId).ToDictionary(g => g.Key, g => g.ToList());

                foreach (var node in instanceidlist)
                {
                    NodeWithLockAndVersionInfo nodeinfo = new NodeWithLockAndVersionInfo();
                    nodeinfo.NodeId = node;
                    if (locked.TryGetValue(node, out var value))
                    {
                        var LockUserName = value.Select(s => s.LockUserName).ToList();
                        nodeinfo.LockUserName.Add(LockUserName);
                        nodeinfo.LockType = value.FirstOrDefault().LockType;
                    }
                    else
                    {
                        nodeinfo.LockUserName.Add(new List<string>());
                        nodeinfo.LockType = -1;
                    }
                    if (serverDatas.TryGetValue(node, out var version))
                    {
                        nodeinfo.VersionNo = version;
                    }
                    nodeInfoList.Add(nodeinfo);
                }
                libLockandVersionInfo.LibInfoList.AddRange(nodeInfoList);
                _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeList nodeInfoList:" + nodeInfoList.Count);
                libInfoList.Add(libLockandVersionInfo);
            }
            _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeList libInfoList:" + libInfoList.Count);
            x.LibInfoList.AddRange(libInfoList);
            x.IsSuccess = true;
            return x;
        }
        /// <summary>
        /// 获取mainproject的treeid下所有nodeid对应的锁定信息及版本号
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetLibraryElementVersionAndLockInfoByTreeListChangeResponse> GetLibraryElementVersionAndLockInfoByTreeListChange(GetLibraryElementVersionAndLockInfoByTreeListChangeRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetLibraryElementVersionAndLockInfoByTreeListChangeResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }

            var username = currentUser.LoginName;
            string errMsg = "";
            //var treeIdList = request.TreeIdList;
            var mainprojectGuid = request.MainprojectGuid;
            var libGuidWithVersion = request.LibGuidWithVersion.ToDictionary(); ;
            _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange begin");
            var serverCLLibInfo = _libRepository.CLCompanyLibInfos.ToList();
            List<string> libGuidList = libGuidWithVersion.Keys.ToList();
            List<Guid> guids = libGuidList.Select(s=>Guid.Parse(s)).ToList();
            var alllockedlib = _libRepository.CLTreeNodesLocks.Where(c => guids.Contains(c.LibId)).ToList();
            var allClTreeNode = _libRepository.CLCatalogTreeNodes.Where(c => guids.Contains(c.LibId)).ToList();
            IMainProjectRepository mainprojectManager = null;
            if (!string.IsNullOrEmpty(mainprojectGuid))
            {
                mainprojectManager = _teamRepository.GetMainProjectRepository(Guid.Parse(mainprojectGuid));
            }
                
            foreach (string lid in libGuidList)
            {
                LibLockandVersionInfo libLockandVersionInfo = new LibLockandVersionInfo();
                //libLockandVersionInfo.TreeId = id;
                List<NodeWithLockAndVersionInfo> nodeInfoList = new List<NodeWithLockAndVersionInfo>();
                //var MainprojectLib = servermainprojectlibs.FirstOrDefault(c => c.TreeId == id && c.MainProjectGuid == mainprojectGuid);


                //List<CLTreeNodeLock> toLockLib = new List<CLTreeNodeLock>();

                Dictionary<long, int> serverDatas = new Dictionary<long, int>();
                Guid libGuid = Guid.Empty;
                
                //if (MainprojectLib == null)
                if (string.IsNullOrEmpty(mainprojectGuid))
                {
                    var msg = "企业库";
                    //为企业库
                    _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange err:" + msg);
                    if (!Guid.TryParse(lid, out libGuid))
                    {
                        errMsg = "库id格式错误";
                        _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange err:" + errMsg);
                        //return false;
                        continue;
                    }
                    var companyLib = serverCLLibInfo.FirstOrDefault(c => c.LibId == libGuid);
                    if (companyLib == null)
                    {
                        errMsg = "未找到库" + lid + "的信息";
                        _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange err:" + errMsg);
                        //return false;
                        continue;
                    }
                    //版本号大于客户端给定的lib对应的版本号的treenode数据 
                    int clientLibVer = 0;
                    _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 企业库开始查询 " + libGuid + " 全部所需数据");
                    Stopwatch sw1 = new Stopwatch();
                    sw1.Start();
                    if (libGuidWithVersion.TryGetValue(lid, out clientLibVer))
                    {
                        var libnodeinfos = allClTreeNode.Where(l => l.LibId == libGuid && l.VersionNo > clientLibVer).ToList();
                        //instanceidlist = libnodeinfos.Select(s => s.InstanceId).ToList();
                        serverDatas = libnodeinfos.ToDictionary(g => g.InstanceId, g => g.VersionNo);//.ToList();
                    }
                    sw1.Stop();
                    _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 企业库查询 " + libGuid + " 全部所需数据完毕；serverdata数量：" + serverDatas.Count + "|耗时：" + sw1.ElapsedMilliseconds + "毫秒");
                }
                else
                {

                    if (mainprojectManager != null)
                    {
                        int clientLibVer = 0;

                        if (libGuidWithVersion.TryGetValue(lid, out clientLibVer))
                        {
                            _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 项目库开始查询 " + libGuid + " 全部所需数据");
                            Stopwatch sw1 = new Stopwatch();
                            sw1.Start();
                            var libnodeinfos = mainprojectManager.MPCatalogTreeNodes.Where(l => l.LibId == libGuid && l.VersionNo > clientLibVer).ToList();
                            
                            serverDatas = libnodeinfos.ToDictionary(g => g.InstanceId, g => g.VersionNo);//.ToList();
                            sw1.Stop();
                            _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 项目库查询 " + libGuid + " 全部所需数据完毕；serverdata数量：" + serverDatas.Count + "|耗时：" + sw1.ElapsedMilliseconds + "毫秒");
                        }
                        else
                        {
                            continue;
                        }
                    }
                    else
                    {
                        continue;
                    }
                }

                //获取锁定信息


                var locked = alllockedlib.Where(l => l.LibId == libGuid).ToList().GroupBy(s => s.NodeId).ToDictionary(g => g.Key, g => g.ToList());
                _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 开始组装libGuid的返回数据；locked数量：" + locked.Count);
                Stopwatch sw = new Stopwatch();
                sw.Start();

                foreach (var node in locked.Keys)
                {
                    NodeWithLockAndVersionInfo nodeinfo = new NodeWithLockAndVersionInfo();
                    nodeinfo.NodeId = node;
                    if (locked.TryGetValue(node, out var value))
                    {
                        var LockUserName = value.Select(s => s.LockUserName).ToList();
                        nodeinfo.LockUserName.AddRange(LockUserName);
                        nodeinfo.LockType = value.FirstOrDefault().LockType;
                    }
                    if (serverDatas.TryGetValue(node, out var version))
                    {
                        nodeinfo.VersionNo = version;
                        //_logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeList nodeid:" + node + "|version:" + version);
                    }
                    else
                    {
                        _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 未获取到nodeid：" + node + "的版本信息");
                    }
                    nodeInfoList.Add(nodeinfo);
                }
                sw.Stop();
                _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange 组装libGuid的返回数据完毕；耗时：" + sw.ElapsedMilliseconds + "毫秒");
                libLockandVersionInfo.LibInfoList.AddRange(nodeInfoList);
                x.LibInfoList.Add(libLockandVersionInfo);
            }

            _logger.LogInformation(username + "==>GetLibraryElementVersionAndLockInfoByTreeListChange end true");
            
            x.IsSuccess = true;
            return x;
        }

        /// <summary>
        /// 获取库树节点
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<GetLibAllTreeInfoResponse> GetLibAllTreeInfo(GetLibAllTreeInfoRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetLibAllTreeInfoResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.LibGuid, out var libGuid))
            {
                x.Message = $"参数{request.LibGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            string errMsg = "";
            List<CLCatalogTreeNode> libAllTreeInfoList = new List<CLCatalogTreeNode>();
            libAllTreeInfoList = _libRepository.CLCatalogTreeNodes.Where(l => l.LibId == libGuid).ToList();
            x.LibAllTreeInfoList.AddRange(_mapper.Map<List<GrpcCLCatalogTreeNode>>(libAllTreeInfoList));
            x.IsSuccess = true;
            return x;
        }

        public override async Task<GetProjectLibTreesResponse> GetProjectLibTrees(GetProjectLibTreesRequest request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new GetProjectLibTreesResponse();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            var username = currentUser.LoginName;
            var mainprojectGuidList = _mapper.Map<List<Guid>>(request.MainprojectGuidList);
            var mainprojectliblist = _libRepository.CLCompanyLibInfos.ToList();
            List<MPGuidMPCatalogTreeNode> dicLibAllTreeInfo = new List<MPGuidMPCatalogTreeNode>();
            List<CLCatalogTreeNode> libAllTreeInfoList = new List<CLCatalogTreeNode>();
            var allMainTreeNodeList = _libRepository.CLMainprojectLibs.Where(m => mainprojectGuidList.Contains(m.MainProjectGuid))
                        .GroupBy(g => g.MainProjectGuid)
                        .ToDictionary(g => g.Key, g => g.ToList());
            //.ToList();
            foreach (var mainprojectGuid in mainprojectGuidList)
            {
                MPGuidMPCatalogTreeNode mPGuidMPCatalogTreeNode = new MPGuidMPCatalogTreeNode();
                List<MPCatalogTreeNode> treeList = new List<MPCatalogTreeNode>();
                mPGuidMPCatalogTreeNode.MainprojectGuid = mainprojectGuid.ToString();
                List<CLMainprojectLib> clmainlibList = new List<CLMainprojectLib>();
                if (allMainTreeNodeList.TryGetValue(mainprojectGuid, out clmainlibList))
                {
                    foreach (var lib in clmainlibList)
                    {
                        MPCatalogTreeNode libTreeNode = new MPCatalogTreeNode
                        {
                            LibId = lib.LibGuid,
                            TreeId = lib.TreeId,
                            TreeType = (LibType)lib.TreeType,
                            ID = 0,
                            NodeId = 0,
                            InstanceId = 0,
                            ParentNodeId = -2,
                            NodeType = 0,
                            NodeName = "",
                            bPDataKey = 0,
                            modelnfoKey = 0
                        };
                        treeList.Add(libTreeNode);
                    }
                }
                mPGuidMPCatalogTreeNode.LibAllTreeInfo.AddRange(_mapper.Map<List<GrpcMPCatalogTreeNode>>(treeList));
                dicLibAllTreeInfo.Add(mPGuidMPCatalogTreeNode);
            }
            x.DicLibAllTreeInfo.AddRange(dicLibAllTreeInfo);
            x.IsSuccess = true;
            return x;
        }

        public override async Task<Protos.Library.GetLibraryFileMD5Response> GetLibraryFileMD5(Protos.Library.GetLibraryFileMD5Request request, ServerCallContext context)
        {
            _logger.LogInformation("Begin grpc call from method {Method} with request paramter {request}", context.Method, request.SessionId);

            var x = new Protos.Library.GetLibraryFileMD5Response();
            x.Message = string.Empty;

            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }
            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户未登录！";
                return x;
            }
            if (!Guid.TryParse(request.LibGuid, out var libGuid))
            {
                x.Message = $"参数{request.LibGuid}格式有误！";
                return x;
            }
            var username = currentUser.LoginName;
            string errMsg = "";
            var dataId = request.DataId;
            
            var libdata = _libRepository.CLLibraryDatas.FirstOrDefault(c => c.DataId == dataId && c.LibId == libGuid);
            if (libdata != null)
            {
                if (!string.IsNullOrEmpty(libdata.FileMD5))
                {
                    x.FileMD5 = libdata.FileMD5;
                    x.IsSuccess = true;
                }
                else
                {
                    errMsg = "fileMD5为空";
                    //log.Info(username + "==>GetLibraryFileMD5 " + errMsg);
                    x.Message = errMsg;
                    x.IsSuccess = false;
                }
                
                return x;

            }
            else
            {
                errMsg = "未找到" + libGuid + "|" + dataId + "的信息";
                //log.Info(username + "==>GetLibraryFileMD5 " + errMsg);
                x.Message = errMsg;
                x.IsSuccess = false;
                return x;
            }
        }
        /// <summary>
        /// 下载库文件数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public override async Task<Protos.Library.DownLoadLibFileDataResponse> DownLoadLibFileData(Protos.Library.DownLoadLibFileDataRequest request, ServerCallContext context)
        {
            var x = new Protos.Library.DownLoadLibFileDataResponse();
            if (string.IsNullOrWhiteSpace(request.SessionId))
            {
                x.Message = $"参数 SessionId 有误！";
                return x;
            }

            var currentUser = await _teamRepository.GetCurrentUserInfoAsync(request.SessionId);
            if (currentUser is null)
            {
                x.Message = "用户登录已失效，请保存退出并重新登录服务器";
                return x;
            }
            if (!Guid.TryParse(request.LibGuid, out var libGuid))
            {
                x.Message = $"参数{request.LibGuid}格式有误！";
                return x;
            }

            var libdata = _libRepository.CLLibraryDatas.FirstOrDefault(c => c.DataId == request.DataId && c.LibId == libGuid);
            if (libdata != null)
            {

                var fileServerPath = libdata.ServerFilePath;
                if (!string.IsNullOrEmpty(fileServerPath))
                {
                    x.Filename = Path.GetFileName(fileServerPath);
                    ////log.Info(@"DownLoadMPDrawing 查询到的文件路径==>" + fileServerPath);
                    var stream = File.OpenRead(fileServerPath);
                    ////log.Info("DownLoadMPDrawing=>文件总长度" + stream.Length);

                    _logger.LogInformation("LibraryQueryService " + context.Method + "DownLoadLibFileData=>文件总长度" + stream.Length);
                    byte[] outFileData = new byte[stream.Length];
                    stream.Position = 0;
                    stream.Read(outFileData, 0, outFileData.Length);
                    stream.Close();
                    stream.Dispose();
                    ////log.Info("DownLoadMPDrawing=>完毕；outFileData.Length==" + outFileData.Length);
                    x.IsSuccess = true;
                    x.OutFileData = ByteString.CopyFrom(outFileData);
                    outFileData = null;
                    return x;
                }
                x.Message = "未上传库文件";
            }
            else
            {
                x.Message = "未找到对应的图纸";
            }
            return x;
        }
    }
}
