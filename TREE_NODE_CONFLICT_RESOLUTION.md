# 树节点名称冲突检查和处理功能

## 功能概述

在 `UploadTreeNodeData` 接口中实现了树节点名称冲突检查和处理功能，通过 ON DUPLICATE KEY UPDATE 方式在数据库层面直接处理 nodename 重名冲突，并在数据库中记录冲突信息和修改后的节点名称。

## 实现的功能

### 1. 数据库层面冲突处理

- **无前置判断**：直接在数据库层面处理冲突，无需前置检测
- **自动冲突检测**：基于唯一索引 `{subProjectld, ParentNodeId, NodeName}` 自动检测冲突
- **数据库层面重命名**：通过 ON DUPLICATE KEY UPDATE 在数据库层面自动重命名
- **冲突信息记录**：在数据库中记录原始节点名称和冲突标记

### 2. 冲突记录字段

在 `MPProjectTreeNode` 表中添加了冲突记录字段：
- `OriginalNodeName`：原始节点名称（用于记录冲突前的名称）
- `HasConflict`：冲突标记（1=存在冲突，0=无冲突）

### 3. 冲突信息查询

- 保存后查询有冲突标记的节点
- 使用 `GrcpConflictNodeList` 结构返回冲突信息
- 包含原始名称、新名称、冲突节点列表等完整信息

## 核心方法

### GetConflictNodesAfterSave
```csharp
private async Task<List<GrcpConflictNodeList>> GetConflictNodesAfterSave(
    IMainProjectRepository mainProjectManager, 
    List<GrpcMPProjectTreeNode> addTreeDatas, 
    Guid subProjectGuid)
```

**功能**：
- 保存后查询有冲突标记的节点
- 从数据库获取冲突信息
- 生成完整的冲突报告
- 记录详细的冲突处理日志

## 工作流程

1. **参数验证**：验证用户登录状态和项目ID
2. **数据分类**：将树节点数据分为添加、修改、删除三类
3. **直接保存**：无需前置冲突检查，直接保存到数据库
4. **数据库层面冲突处理**：使用 `ON DUPLICATE KEY UPDATE` 在数据库层面自动处理冲突
5. **冲突信息记录**：数据库自动记录原始节点名称和冲突标记
6. **冲突信息查询**：保存后查询有冲突标记的节点，生成冲突报告

## 日志记录

系统会记录以下日志信息：
- 检测到的冲突数量和详情
- 应用的冲突解决方案
- 异常情况处理

## 使用示例

当客户端调用 `UploadTreeNodeData` 接口时：

1. 如果检测到名称冲突，响应中会包含 `conflictNodeList` 字段
2. 冲突的节点会自动重命名（如：`节点名` -> `节点名_1`）
3. 客户端可以根据冲突信息进行相应的UI处理

## 数据库索引

依赖以下数据库索引：
- `IX_MPProjectTreeNodes_InstanceId_subProjectld`：普通索引，用于快速查询
- `idx_subprojectId_parentId_nodename`：唯一索引，用于节点名称冲突检测
- `idx_hasConflict`：普通索引，用于快速查询冲突节点

## 数据库字段

新增的冲突记录字段：
- `OriginalNodeName`：VARCHAR(190)，记录冲突前的原始节点名称
- `HasConflict`：INT，冲突标记（1=存在冲突，0=无冲突）

## 性能优化

1. **无前置检测**：直接在数据库层面处理冲突，避免应用层计算
2. **数据库层面处理**：冲突解决在数据库层面进行，减少网络往返
3. **高效查询**：利用数据库索引进行快速冲突检测
4. **批量处理**：支持大量数据的批量插入和冲突处理

## 注意事项

1. 冲突检查基于节点名称的小写比较，确保大小写不敏感
2. 数据库自动重命名策略会保持原始名称，只添加数字后缀
3. 冲突信息会完整记录在数据库中，便于后续查询和处理
4. 使用 `ON DUPLICATE KEY UPDATE` 确保数据库操作的原子性和高效性
5. 需要执行数据库升级脚本添加冲突记录字段 