﻿using Microsoft.OpenApi.Models;
using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public enum MPOperationType
    {
        Add,
        Delete,
        Modify,
        Unchange
    }
    public class MPLibraryData
    {
        [Key]
        
        public int ID { get; set; }
        
        public long DataId { get; set; }

        
        public string SchemaName { get; set; }

        
        public string ClassName { get; set; }

        /// <summary>
        /// 库id 如果为企业级则为guid.empty
        /// </summary>
        
        public Guid LibId { get; set; }

        
        [Required, EnumDataType(typeof(LibType))]
        public LibType TreeType { get; set; }

        /// <summary>
        /// 二进制数据块
        /// </summary>
        
        public byte[] Data { get; set; }

        /// <summary>
        /// 服务端文件存储路径
        /// </summary>
        
        public string ServerFilePath { get; set; }

        /// <summary>
        /// 文件MD5值
        /// </summary>
        
        public string FileMD5 { get; set; }

        
        public int VersionNo { get; set; }
    }

    public class MPLibraryDataHistory
    {
        [Key]
        
        public int ID { get; set; }
        
        public long DataId { get; set; }

        
        public string SchemaName { get; set; }

        
        public string ClassName { get; set; }

        /// <summary>
        /// 库id 如果为企业级则为guid.empty
        /// </summary>
        
        public Guid LibId { get; set; }

        
        [Required, EnumDataType(typeof(LibType))]
        public LibType TreeType { get; set; }

        /// <summary>
        /// 二进制数据块
        /// </summary>
        
        public byte[] Data { get; set; }

        /// <summary>
        /// 服务端文件存储路径
        /// </summary>
        
        public string ServerFilePath { get; set; }

        /// <summary>
        /// 文件MD5值
        /// </summary>
        
        public string FileMD5 { get; set; }

        
        public int VersionNo { get; set; }
        
        [Required, EnumDataType(typeof(MPOperationType))]
        public MPOperationType OperationType { get; set; }
    }
}
