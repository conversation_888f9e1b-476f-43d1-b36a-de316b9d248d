using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库从v2升级到v3
    /// 这是一个空的升级，仅用于版本标记
    /// </summary>
    public class TeamDbUpgrade_v2_to_v3 : AbstractTeamDbUpgrade
    {
        public override string SourceVersion => "v2";
        public override string TargetVersion => "v3";
        public override string Description => "TeamDb v2到v3空升级（无实际更改）";

        public TeamDbUpgrade_v2_to_v3(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {SourceVersion} -> {TargetVersion} 升级...");
            
            // 空升级，无需执行任何操作
            Logger.LogInformation("这是一个空升级，无需执行任何数据库更改");
            
            // 模拟一些异步操作
            await Task.CompletedTask;
            
            Logger.LogInformation($"{SourceVersion} -> {TargetVersion} 升级完成");
        }

        protected override async Task ExecuteRollbackAsync(TeamDbContext context)
        {
            Logger.LogInformation($"开始执行 {TargetVersion} -> {SourceVersion} 回滚...");
            
            // 空回滚，无需执行任何操作
            Logger.LogInformation("这是一个空回滚，无需执行任何数据库更改");
            
            // 模拟一些异步操作
            await Task.CompletedTask;
            
            Logger.LogInformation($"{TargetVersion} -> {SourceVersion} 回滚完成");
        }
    }
} 