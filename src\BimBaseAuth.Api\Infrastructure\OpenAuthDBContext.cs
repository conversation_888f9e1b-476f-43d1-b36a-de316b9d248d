﻿using BimBaseAuth.Api.Infrastructure.Configurations;
using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure
{
    public partial class OpenAuthDBContext : DbContext
    {

		public DbSet<AuthInfo> AuthInfos
		{
			get;
			set;
		}

		public DbSet<Role> Roles
		{
			get;
			set;
		}

		public DbSet<UserInfo> UserInfos
		{
			get;
			set;
		}

		public DbSet<RoleExtend> RoleExtends
		{
			get;
			set;
		}

		public DbSet<UserRole> UserRoles
		{
			get;
			set;
		}

		public DbSet<RoleRelation> RoleRelations
		{
			get;
			set;
		}

		public DbSet<UserRelation> UserRelations
		{
			get;
			set;
		}

		public DbSet<UserRelated> UserRelateds
		{
			get;
			set;
		}
		public OpenAuthDBContext(DbContextOptions<OpenAuthDBContext> options) : base(options)
        {
			
		}

		protected override void OnModelCreating(ModelBuilder modelBuilder)
		{
			modelBuilder.ApplyConfiguration(new AuthInfoConfiguration());
            modelBuilder.ApplyConfiguration(new RoleConfiguration());
            modelBuilder.ApplyConfiguration(new RoleExtendConfiguration());
            modelBuilder.ApplyConfiguration(new RoleRelationConfiguration());
            modelBuilder.ApplyConfiguration(new UserInfoConfiguration());
            modelBuilder.ApplyConfiguration(new UserRelatedConfiguration());
            modelBuilder.ApplyConfiguration(new UserRelationConfiguration());
            modelBuilder.ApplyConfiguration(new UserRoleConfiguration());

            OnModelCreatingPartial(modelBuilder);
		}

		partial void OnModelCreatingPartial(ModelBuilder modelBuilder);


	}
}
