﻿using BimBase.Api.Infrastructure.ModelDomain;
using BimBase.Api.Infrastructure.DbInitializer;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;

namespace BimBase.Api.Infrastructure
{
    public class ModelBulkOperationByMysql : IModelBulkOperation
    {
        const int DatabaseExecTimeout = 60000;
        const int RecordCountLimit = 10000;
        private string _foldPath;
        public const int CHUNK_STRING_LENGTH = 30000;
        private readonly char separator = Path.DirectorySeparatorChar;
        public ModelBulkOperationByMysql()
        {
            string temppath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                temppath = "C:/ProgramData/MySQL/MySQL Server 8.0/Uploads";
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                var options = serviceProvider.GetService<IOptions<UrlsConfig>>();
                temppath = options?.Value?.LoadDataSavePath;
            }
            temppath = temppath.Replace(@"\", separator.ToString());
            //_foldPath = temppath + @"/PBIMS/LoadinFileDir/";
            _foldPath = temppath + "/";
            if (!Directory.Exists(_foldPath))
            {
                Directory.CreateDirectory(_foldPath);
            }
        }

        private string ReadDataFile(string foldPath,string filename)
        {
            //Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + filename;
            string strLine = "";
            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {

                using (StreamReader sr = new StreamReader(fileStream))
                {

                    strLine = sr.ReadToEnd();
                }
            }

            return strLine;
        }
        private void WriteDataFile(StringBuilder dataSb, string foldPath, string filename)
        {
            string datafilePath = foldPath + filename + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                fileStream.Position = fileStream.Length;
                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {

                    while (dataSb.Length > CHUNK_STRING_LENGTH)
                    {
                        sw.Write(dataSb.ToString(0, CHUNK_STRING_LENGTH));
                        dataSb.Remove(0, CHUNK_STRING_LENGTH);
                    }
                    sw.Write(dataSb.ToString());
                }
            }


        }
        private void ExecuteDbCommand(DbContext context, string foldPath, StringBuilder dataSb, string command)
        {
            Guid fileGuid = Guid.NewGuid();
            string datafilePath = foldPath + "data" + fileGuid + ".txt";

            using (var fileStream = new FileStream(datafilePath, FileMode.Create, FileAccess.Write))
            {
                var utf8WithoutBom = new System.Text.UTF8Encoding(false);
                using (StreamWriter sw = new StreamWriter(fileStream, utf8WithoutBom))
                {
                    sw.Write(dataSb.ToString());
                }
            }
            string realpath = "";
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    realpath = datafilePath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                    var options = serviceProvider.GetService<IOptions<UrlsConfig>>();
                    //此路径需要传递给集群中的MySQL执行load data file命令，需要使用MysqLoadDataPath配置路径
                    realpath = Path.Combine(options?.Value?.MysqLoadDataPath, "data" + fileGuid + ".txt");
                    //realpath = "/home/<USER>/LoadinFileDir/" + "data" + fileGuid + ".txt";
                }
                var rawSql = "load data infile '" + realpath + command;
                context.Database.SetCommandTimeout(DatabaseExecTimeout);
                int result = context.Database.ExecuteSqlRaw(rawSql);

                if (result < 0)
                    throw new Exception("Load data in file fail");
            }
            catch (Exception ex)
            {
                Console.WriteLine("ModelBulkOperationByMysql ExecuteDbCommand Load data in file fail======================>" + ex.Message);
                Console.WriteLine("ModelBulkOperationByMysql ExecuteDbCommand Load data in file fail======================>" + ex.StackTrace);
                throw;
            }
            

            try
            {
                File.Delete(datafilePath);
            }
            catch (Exception)
            {
                Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
            }

        }



        private void ExecuteModelDbCommand(DbContext context, string foldPath, string command, string dataFileName)
        {
            string datafilePath = foldPath + dataFileName;
            string realpath = "";
            try
            {
                
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    realpath = datafilePath;
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    realpath = "/home/<USER>/LoadinFileDir/" + dataFileName;
                }
                Console.WriteLine($"realpath===>{realpath}");
                context.Database.SetCommandTimeout(DatabaseExecTimeout);
                var rawSql = "load data infile '" + realpath + command;
                int result = context.Database.ExecuteSqlRaw(rawSql);

            }
            catch (Exception ex)
            {
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.Message);
                Console.WriteLine("ExecuteModelDbCommand fail======================>" + ex.StackTrace);
                throw new Exception("Load data in file fail" + ex.Message);
            }

            try
            {
                File.Delete(realpath);
            }
            catch (Exception)
            {
                Console.WriteLine("文件:" + datafilePath + "无法释放，请手动删除");
            }

        }

        public int SaveVersionInfo(DbContext context, VersionData version)
        {
            var versionContext = context as ModelDbContext;

            if (versionContext == null) return -1;

            versionContext.VersionDatas.Add(version);
            versionContext.SaveChanges();

            var addedversion =
                context.ChangeTracker.Entries<VersionData>()
                    .FirstOrDefault(entry => entry.State == EntityState.Unchanged);

            if (addedversion != null) return addedversion.Entity.VersionNo;

            return -1;
        }



        public void SaveAddModelDatasToFile(List<ModelData> recordDatas, int? currentVersion,long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savemodel";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;

                string lockerId = "\\N";
                if (data.LockUserID.HasValue) lockerId = data.LockUserID.ToString();
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + lockerId + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                     currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile( dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }
        public void SaveModifyModelDatasToFile(DbContext context,List<ModelData> recordDatas, int? currentVersion, long requestId) 
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifymodel";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as ModelDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {
                i++;

                string lockerId = "\\N";
                if (data.LockUserID.HasValue) lockerId = data.LockUserID.ToString();
                long dataId = data.InstanceId;
                var oldData = dataContext.ModelDatas.FirstOrDefault(d => d.InstanceId == dataId);
                if (null == oldData) continue;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DomainClassName + "," + oldData.Id + "," + data.InstanceId + "," + lockerId + "," + (data.Data == null
                    ? "\\N" : dataStr) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveDeleteIdToFile(List<ModelData> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deletemodel";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.InstanceId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveAddRelationshipDataToFile(List<Relationship> recordDatas, int? currentVersion, long requestId)

        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_saverelationship";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;

                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.ECSchemaName + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + (data.Data == null
                                  ? "\\N" : dataStr) + "," + data.DomainClassName + "," +
                    currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModifyRelationshipDataToFile(DbContext context, List<Relationship> recordDatas, int? currentVersion, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modifyrelationship";
            StringBuilder dataSb = new StringBuilder();

            int i = 0;
            var dataContext = context as ModelDbContext;
            if (null == dataContext) return;
            foreach (var data in recordDatas)
            {
                i++;

                long dataId = data.InstanceId;
                var oldData = dataContext.Relationships.FirstOrDefault(d => d.InstanceId == dataId);
                if (null == oldData) continue;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.SourceDomainClassName + "," + oldData.Id + "," + data.InstanceId + "," + data.ECSchemaName + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + (data.Data == null
                              ? "\\N" : dataStr) + "," + data.DomainClassName + "," +
                              currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveDeleteRelationshipIdToFile(List<Relationship> recordDatas, long requestId)
        {
            if (!recordDatas.Any()) return;

            //const string command = "' replace into table datas fields terminated by ',' lines terminated by '\\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_deleterelationship";


            StringBuilder dataSb = new StringBuilder();
            int i = 0;
            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.InstanceId + ",");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }
            dataSb.Append("-1");
            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveHistoryDataToFile(DbContext context, List<ModelData> addDatas, List<ModelData> modifyDatas, List<ModelData> delDatas, int? currentVersion, long requestId)
        {
            string filename = requestId + "_modelhistory";
            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Add + "," + (data.Data == null
                              ? "\\N" : dataStr) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Modify + "," + (data.Data == null
                              ? "\\N" : dataStr) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }   

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Delete + ",\\N," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveHistoryRelationToFile(DbContext context, List<Relationship> addDatas, List<Relationship> modifyDatas, List<Relationship> delDatas, int? currentVersion, long requestId)
        {
            string filename = requestId + "_relationhistory";
            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Add + "," + (data.Data == null
                                  ? "\\N" : dataStr) + "," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Modify + "," + (data.Data == null
                                  ? "\\N" : dataStr) + "," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Delete + ",\\N," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    WriteDataFile(dataSb, _foldPath, filename);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                WriteDataFile(dataSb, _foldPath, filename);
            }
        }

        public void SaveModelToDB(DbContext context, long requestId)
        {
            const string command = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_savemodel.txt";
            if (File.Exists(_foldPath+ filename))
            {
                ExecuteModelDbCommand(context, _foldPath, command, filename);
            }
            

            const string modifycommand = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,Id,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string modifyfilename = requestId + "_modifymodel.txt";
            if (File.Exists(_foldPath + modifyfilename))
            {
                ExecuteModelDbCommand(context, _foldPath, modifycommand, modifyfilename);
            }
            

            string deletefilename = requestId + "_deletemodel.txt";
            
            if (File.Exists(_foldPath + deletefilename))
            {
                const string deletecommand = "delete from modeldatas where InstanceId in (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);
                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append(")");
                //Console.WriteLine($"SaveModelToDB _deletemodel===>{deletedataSb.ToString()}");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());
            }
        }

        public void SaveModelHistoryToDB(DbContext context, long requestId)
        {
            const string command =
                @"' into table historydatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,OperationRecordType,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_modelhistory.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteModelDbCommand(context, _foldPath, command, filename);
            }
        }
            
        public void SaveRelationshipHistoryToDB(DbContext context, long requestId)
        {
            const string command =
                @"' replace into table historyrelationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,InstanceId,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,ECSchemaName,OperationRecordType,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_relationhistory.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteModelDbCommand(context, _foldPath, command, filename);
            }
            
        }
        public void SaveRelationToDB(DbContext context, long requestId)
        {
            const string command = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string filename = requestId + "_saverelationship.txt";
            if (File.Exists(_foldPath + filename))
            {
                ExecuteModelDbCommand(context, _foldPath, command, filename);
            }

            const string modifycommand = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,Id,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            string modifyfilename = requestId + "_modifyrelationship.txt";
            if (File.Exists(_foldPath + modifyfilename))
            {
                ExecuteModelDbCommand(context, _foldPath, modifycommand, modifyfilename);
            }
            

            string deletefilename = requestId + "_deleterelationship.txt";

            if (File.Exists(_foldPath+deletefilename))
            {
                const string deletecommand = "delete from relationships where InstanceId in  (";

                StringBuilder deletedataSb = new StringBuilder();
                deletedataSb.Append(deletecommand);

                string deleteids = ReadDataFile(_foldPath, deletefilename);
                deletedataSb.Append(deleteids);

                deletedataSb.Append("-1)");
                context.Database.ExecuteSqlRaw(deletedataSb.ToString());
            }
            
        }
        public void SaveModelDatas(DbContext context, List<ModelData> recordDatas, int? currentVersion)
        {
            if (!recordDatas.Any()) return;

            const string command = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";

            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;

                string lockerId = "\\N";
                if (data.LockUserID.HasValue) lockerId = data.LockUserID.ToString();
                string dataStr = "";
                if (data.Data != null)
                {
                    string str = BitConverter.ToString(data.Data);
                    String[] tempArr = str.Split('-');
                    dataStr = string.Join("", tempArr);
                }
                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + lockerId + "," +
                    (data.Data == null ? "\\N" : dataStr) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                     currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }
        public void SaveModelDatasForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";

            ExecuteModelDbCommand(context, path, command, "modeldata.txt");
        }


        public void ModifyModelDatas(DbContext context, List<ModelData> recordDatas, int? currentVersion)
        {
            if (!recordDatas.Any()) return;

            const string command = @"' replace into table modeldatas fields terminated by ',' lines terminated by '\n' (DomainClassName,Id,InstanceId,LockUserID,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";
            StringBuilder dataSb = new StringBuilder();
            int i = 0;

            var dataContext = context as ModelDbContext;
            if (null == dataContext) return;

            foreach (var data in recordDatas)
            {
                i++;

                string lockerId = "\\N";
                if (data.LockUserID.HasValue) lockerId = data.LockUserID.ToString();

                long dataId = data.InstanceId;
                var oldData = dataContext.ModelDatas.FirstOrDefault(d => d.InstanceId == dataId);
                if (null == oldData) continue;

                dataSb.Append(data.DomainClassName + "," + oldData.Id + "," + data.InstanceId + "," + lockerId + "," + (data.Data == null
                    ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void DeleteModelDatas(DbContext context, List<ModelData> delDatas)
        {
            if (!delDatas.Any()) return;

            const string command = "delete from modeldatas where InstanceId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.InstanceId + ",");
            }

            dataSb.Append("-1)");

            context.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }
        public void SaveHistoryDatasForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command =
               @"' into table historydatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,OperationRecordType,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";

            ExecuteModelDbCommand(context, path, command, "historyData.txt");
        }
        public void SaveResourceForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command =
                @"' into table resourceclasses fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,SchemaName,ResourceName) ";

            ExecuteModelDbCommand(context, path, command, "resourceClass.txt");
        }
        public void SaveHistoryDatas(DbContext context, List<ModelData> addDatas, List<ModelData> modifyDatas, List<ModelData> delDatas, int? currentVersion)
        {
            const string command =
                @"' into table historydatas fields terminated by ',' lines terminated by '\n' (DomainClassName,InstanceId,OperationRecordType,@hexdata,ECSchemaName,StoreyID,Domain,VersionNo,StoreyGuid) set Data=UNHEX(@hexdata)";

            StringBuilder dataSb = new StringBuilder();

            //生成添加数据的历史数据
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;

                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Add + "," + (data.Data == null
                              ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改数据的历史数据
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;

                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Modify + "," + (data.Data == null
                              ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除数据的历史数据
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.DomainClassName + "," + data.InstanceId + "," + (int)OperationRecordType.Delete + ",\\N," + data.ECSchemaName + "," + data.StoreyID + "," + data.Domain + "," +
                              currentVersion + "," + data.StoreyGuid + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void SaveRelationshipDatasForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command =
                @"' replace into table relationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,InstanceId,ECSchemaName,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";
            ExecuteModelDbCommand(context, path, command, "relationship.txt");
        }
        public void SaveRelationshipDatas(DbContext context, List<Relationship> recordDatas, int? currentVersion)
        {
            if (!recordDatas.Any()) return;

            const string command =
                @"' replace into table relationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,InstanceId,ECSchemaName,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";


            StringBuilder dataSb = new StringBuilder();

            int i = 0;

            foreach (var data in recordDatas)
            {
                i++;

                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.ECSchemaName + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + (data.Data == null
                                  ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.DomainClassName + "," +
                    currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void ReplaceResourceClasses(DbContext context, List<ResourceClass> resourceClasses)
        {
            if (!resourceClasses.Any()) return;

            const string command =
                @"' replace into table resourceclasses fields terminated by ',|' lines terminated by '\n' (SchemaName,DomainClassName,InstanceId,ResourceName) ";
            StringBuilder dataSb = new StringBuilder();
            int i = 0;

            var dataContext = context as ModelDbContext;
            if (null == dataContext) return;

            foreach (var data in resourceClasses)
            {
                i++;

                dataSb.Append(data.SchemaName + ",|" + data.DomainClassName + ",|" + data.InstanceId + ",|" + data.ResourceName + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void ModifyRelationshipDatas(DbContext context, List<Relationship> recordDatas, int? currentVersion)
        {
            if (!recordDatas.Any()) return;

            const string command =
                @"' replace into table relationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,Id,InstanceId,ECSchemaName,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";
            StringBuilder dataSb = new StringBuilder();
            int i = 0;

            var dataContext = context as ModelDbContext;
            if (null == dataContext) return;

            foreach (var data in recordDatas)
            {
                i++;

                long dataId = data.InstanceId;
                var oldData = dataContext.Relationships.FirstOrDefault(d => d.InstanceId == dataId);
                if (null == oldData) continue;

                dataSb.Append(data.SourceDomainClassName + "," + oldData.Id + "," + data.InstanceId + "," + data.ECSchemaName + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + (data.Data == null
                              ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.DomainClassName + "," +
                              currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

        public void DeleteRelationshipDatas(DbContext context, List<Relationship> delDatas)
        {
            if (!delDatas.Any()) return;

            const string command = "delete from relationships where InstanceId in (";

            StringBuilder dataSb = new StringBuilder();
            dataSb.Append(command);

            foreach (var data in delDatas)
            {
                dataSb.Append(data.InstanceId + ",");
            }

            dataSb.Append("-1)");

            context.Database.ExecuteSqlRaw(dataSb.ToString());

            dataSb.Clear();
            dataSb.Capacity = 0;
        }
        public void SaveHistoryRelationshipDatasForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command =
                @"' replace into table historyrelationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,InstanceId,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,ECSchemaName,OperationRecordType,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";
            ExecuteModelDbCommand(context, path, command, "historyRelationship.txt");
        }

        public void SaveSingletonClassDatasForWeb(DbContext context, string path, int? currentVersion)
        {
            const string command =
                @"' into table singletonclasses fields terminated by ',' lines terminated by '\n' (SchemaName,DomainClassName,InstanceId) ";

            ExecuteModelDbCommand(context, path, command, "singletonclass.txt");
        }
        public void SaveHistoryRelationshipDatas(DbContext context, List<Relationship> addDatas, List<Relationship> modifyDatas, List<Relationship> delDatas, int? currentVersion)
        {
            const string command =
                @"' replace into table historyrelationships fields terminated by ',' lines terminated by '\n' (SourceDomainClassName,InstanceId,SourceID,SourceECSchemaName,TargetID,TargetDomainClassName,TargetECSchemaName,Type,ECSchemaName,OperationRecordType,@hexdata,DomainClassName,VersionNo,IsForward) set Data=UNHEX(@hexdata)";

            StringBuilder dataSb = new StringBuilder();

            //生成添加关系的历史记录
            int i = 0;

            foreach (var data in addDatas)
            {
                i++;

                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Add + "," + (data.Data == null
                                  ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成修改关系的历史记录
            i = 0;

            foreach (var data in modifyDatas)
            {
                i++;

                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Modify + "," + (data.Data == null
                                  ? "\\N" : BitConverter.ToString(data.Data).Replace("-", string.Empty)) + "," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }

            dataSb.Clear();
            dataSb.Capacity = 0;

            //生成删除关系的历史记录
            i = 0;

            foreach (var data in delDatas)
            {
                i++;

                dataSb.Append(data.SourceDomainClassName + "," + data.InstanceId + "," + data.SourceID + "," + data.SourceECSchemaName + "," + data.TargetID + "," + data.TargetDomainClassName + "," + data.TargetECSchemaName + "," + (int)data.Type + "," + data.ECSchemaName + "," + (int)OperationRecordType.Delete + ",\\N," + data.DomainClassName + "," +
                               currentVersion + "," + (data.IsForward ? 1 : 0) + "\n");

                if (i >= RecordCountLimit)
                {
                    ExecuteDbCommand(context, _foldPath, dataSb, command);
                    i = 0;
                    dataSb.Clear();
                    dataSb.Capacity = 0;
                }
            }

            if (dataSb.Length > 0)
            {
                ExecuteDbCommand(context, _foldPath, dataSb, command);
            }
        }

    }
}
