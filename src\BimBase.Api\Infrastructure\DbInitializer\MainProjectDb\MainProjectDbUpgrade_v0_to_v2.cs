using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb从v0升级到v2
    /// 升级内容：
    /// 1. 创建数据库基础结构（如果需要）
    /// 2. 初始化基础数据
    /// </summary>
    public class MainProjectDbUpgrade_v0_to_v2 : AbstractMainProjectDbUpgrade
    {
        public MainProjectDbUpgrade_v0_to_v2(ILogger logger) : base(logger)
        {
        }

        public override string FromVersion => "v0";
        public override string ToVersion => "v2";
        public override string Description => "MainProjectDb初始化升级 - 创建基础数据库结构";

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始执行MainProjectDb v0->v2升级");

            // 1. 确保数据库结构存在
            Logger.LogInformation("检查并创建数据库结构...");
            var created = await context.Database.EnsureCreatedAsync();
            if (created)
            {
                Logger.LogInformation("MainProjectDb数据库结构已创建");
            }
            else
            {
                Logger.LogInformation("MainProjectDb数据库结构已存在，开始执行升级");
                
                // 2. 创建DatabaseVersion表（版本管理基础设施）
                await EnsureDatabaseVersionTableAsync(context);
                
                // 3. 升级MPProjectTreeNodes表结构
                await UpgradeMPProjectTreeNodesTableAsync(context);
                
                // 4. 创建触发器
                await CreateMPProjectTreeNodesTriggersAsync(context);
            }

            // 5. 初始化基础数据
            await InitializeBasicDataAsync(context);

            Logger.LogInformation("MainProjectDb v0->v2升级完成");
        }

        /// <summary>
        /// 确保DatabaseVersion表存在
        /// </summary>
        private async Task EnsureDatabaseVersionTableAsync(MainProjectDbContext context)
        {
            var tableDefinition = @"
                    `Version` VARCHAR(50) NOT NULL,
                    `UpgradeTime` DATETIME NOT NULL,
                    `Description` VARCHAR(500) NULL,
                    `UpgradedBy` VARCHAR(100) NOT NULL DEFAULT 'System',
                    PRIMARY KEY (`Version`)";
            
            await CreateTableIfNotExistsAsync(context, "databaseversion", tableDefinition);
        }

        /// <summary>
        /// 升级MPProjectTreeNodes表结构
        /// </summary>
        private async Task UpgradeMPProjectTreeNodesTableAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始升级MPProjectTreeNodes表结构...");
            
            if (await TableExistsAsync(context, "mpprojecttreenodes"))
            {
                // 1. 修改NodeName字段：longtext -> char(190)
                await ModifyColumnIfExistsAsync(context, "mpprojecttreenodes", "NodeName", "char(190) DEFAULT NULL");
                
                // 2. 添加level字段
                await AddColumnIfNotExistsAsync(context, "mpprojecttreenodes", "level", "int(11) NOT NULL DEFAULT 0");
                
                // 3. 添加indexCode字段
                await AddColumnIfNotExistsAsync(context, "mpprojecttreenodes", "indexCode", "longtext");
                
                // 4. 添加indexCode2字段
               // await AddColumnIfNotExistsAsync(context, "mpprojecttreenodes", "indexCode2", "char(190) DEFAULT NULL");
                await EnsureColumnWithTypeAsync(context, "mpprojecttreenodes", "indexCode2", "char(190) DEFAULT NULL");

                // 5. 创建索引
                await CreateMPProjectTreeNodesIndexesAsync(context);
                
                // 6. 更新现有数据的indexCode2字段
                await UpdateExistingIndexCode2DataAsync(context);
            }
            
            Logger.LogInformation("MPProjectTreeNodes表结构升级完成");
        }

        /// <summary>
        /// 创建MPProjectTreeNodes表索引
        /// </summary>
        private async Task CreateMPProjectTreeNodesIndexesAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始创建MPProjectTreeNodes表索引...");
            
            // 创建各种性能索引
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_subprojectId", "`subProjectld`");
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_subprojectId_nodename", "`subProjectld`,`NodeName`");
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_subproject_node", "`subProjectld`,`NodeId`");
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_newindecode", "`subProjectld`,`level`,`indexCode2`");
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_indexcode2", "`subProjectld`,`indexCode2`,`level`");
            await CreateIndexIfNotExistsAsync(context, "mpprojecttreenodes", "idx_parentId", "`ParentNodeId`");
            
            Logger.LogInformation("MPProjectTreeNodes表索引创建完成");
        }

        /// <summary>
        /// 创建MPProjectTreeNodes表触发器
        /// </summary>
        private async Task CreateMPProjectTreeNodesTriggersAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始创建MPProjectTreeNodes表触发器...");
            
            // 创建INSERT触发器
            var insertTriggerDefinition = @"
                CREATE TRIGGER `trg_before_insert_indexcode2`
                BEFORE INSERT ON `mpprojecttreenodes`
                FOR EACH ROW
                BEGIN
                    SET NEW.indexCode2 = LEFT(NEW.indexCode, 190);
                END";
            
            // 创建UPDATE触发器
            var updateTriggerDefinition = @"
                CREATE TRIGGER `trg_before_update_indexcode2`
                BEFORE UPDATE ON `mpprojecttreenodes`
                FOR EACH ROW
                BEGIN
                    SET NEW.indexCode2 = LEFT(NEW.indexCode, 190);
                END";
            
            // 使用基类方法创建触发器
            await CreateOrReplaceTriggerAsync(context, "trg_before_insert_indexcode2", insertTriggerDefinition);
            await CreateOrReplaceTriggerAsync(context, "trg_before_update_indexcode2", updateTriggerDefinition);
            
            Logger.LogInformation("MPProjectTreeNodes表触发器创建完成");
        }

        /// <summary>
        /// 更新现有数据的indexCode2字段
        /// </summary>
        private async Task UpdateExistingIndexCode2DataAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始更新现有数据的indexCode2字段...");
            
            var updateSql = "UPDATE `mpprojecttreenodes` SET `indexCode2` = LEFT(`indexCode`, 190) WHERE `indexCode2` IS NULL OR `indexCode2` = ''";
            await context.Database.ExecuteSqlRawAsync(updateSql);
            
            Logger.LogInformation("现有数据的indexCode2字段更新完成");
        }



        /// <summary>
        /// 初始化基础数据
        /// </summary>
        private async Task InitializeBasicDataAsync(MainProjectDbContext context)
        {
            Logger.LogInformation("开始初始化MainProjectDb基础数据...");
            
            // 这里可以添加必要的初始数据创建逻辑
            // 例如：默认用户组、默认配置等
            
            // 暂时没有特殊的初始数据需求
            Logger.LogInformation("MainProjectDb基础数据初始化完成");
            
            await Task.CompletedTask;
        }
    }
} 