﻿using BimBase.Api.Infrastructure.Common;
using BimBase.Api.Protos;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Policy;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure
{
    public static class BimBaseServerTemplates
    {
        static readonly char separator = Path.DirectorySeparatorChar;
        static readonly string fileRoot = GetAssemblyDirectory() + "TemplateFiles" + separator;
        static readonly string teamRoleRoot = fileRoot + "TeamRoleRoot" + separator;
        static readonly string teamMemberRoot = fileRoot + "TeamMemberRoot" + separator;
        static readonly string projectRoleRoot = fileRoot + "ProjectRoleRoot" + separator;
        static readonly string projectMemberRoot = fileRoot + "ProjectMemberRoot" + separator;
        static readonly string domainClassRoot = fileRoot + "DomainClassRoot" + separator;
        static readonly string wgRoot = fileRoot + "WorkGroupRoot" + separator;
        static readonly string releaseRoot = fileRoot + "ReleaseRoot" + separator;
        static readonly string modelRoot = fileRoot + "ModelForWeb" + separator;
        static readonly string fileModelRoot = fileRoot + "FileModelForWeb" + separator;
        static readonly string mainProjectMemberAndPermissionRoot = fileRoot + "MainProjectMemberAndPermissionRoot" + separator;

        static string GetAssemblyDirectory()
        {
            var location = Assembly.GetExecutingAssembly().Location;
            if (location.StartsWith("file:///"))
            {
                location = location.Substring("file:///".Length);
            }
            // 这里不需要替换分隔符
            var dir = Path.GetDirectoryName(location);
            return dir ?? string.Empty;
        }
        public static string MainProjectMemberAndPermissionRoot
        {
            get { return mainProjectMemberAndPermissionRoot; }
        }
        public static string TeamRoleRoot
        {
            get { return teamRoleRoot; }
        }
        public static string TeamMemberRoot
        {
            get { return teamMemberRoot; }
        }
        public static string ProjectRoleRoot
        {
            get { return projectRoleRoot; }
        }
        public static string ProjectMemberRoot
        {
            get { return projectMemberRoot; }
        }
        public static string DomainClassRoot
        {
            get { return domainClassRoot; }
        }

        public static string ReleaseRoot
        {
            get { return releaseRoot; }
        }

        public static string WGRoot
        {
            get { return wgRoot; }
        }
        public static string ModelRoot
        {
            get { return modelRoot; }
        }
        public static string FileModelRoot
        {
            get { return fileModelRoot; }
        }
        static BimBaseServerTemplates()
        {
            if (!Directory.Exists(fileRoot))
            {
                Directory.CreateDirectory(fileRoot);
                Directory.CreateDirectory(TeamRoleRoot);
                Directory.CreateDirectory(TeamMemberRoot);
                Directory.CreateDirectory(ProjectRoleRoot);
                Directory.CreateDirectory(ProjectMemberRoot);
                Directory.CreateDirectory(domainClassRoot);
                Directory.CreateDirectory(ReleaseRoot);
                Directory.CreateDirectory(WGRoot);
                Directory.CreateDirectory(ModelRoot);
                Directory.CreateDirectory(FileModelRoot);
                Directory.CreateDirectory(MainProjectMemberAndPermissionRoot);
            }
            else
            {
                if (!Directory.Exists(TeamRoleRoot))
                    Directory.CreateDirectory(TeamRoleRoot);
                if (!Directory.Exists(TeamMemberRoot))
                    Directory.CreateDirectory(TeamMemberRoot);
                if (!Directory.Exists(ProjectRoleRoot))
                    Directory.CreateDirectory(ProjectRoleRoot);
                if (!Directory.Exists(ProjectMemberRoot))
                    Directory.CreateDirectory(ProjectMemberRoot);
                if (!Directory.Exists(domainClassRoot))
                    Directory.CreateDirectory(domainClassRoot);
                if (!Directory.Exists(ReleaseRoot))
                    Directory.CreateDirectory(ReleaseRoot);
                if (!Directory.Exists(WGRoot))
                    Directory.CreateDirectory(WGRoot);
                if (!Directory.Exists(ModelRoot))
                {
                    Directory.CreateDirectory(ModelRoot);
                }
                if (!Directory.Exists(FileModelRoot))
                {
                    Directory.CreateDirectory(FileModelRoot);
                }
                if (!Directory.Exists(MainProjectMemberAndPermissionRoot))
                {
                    Directory.CreateDirectory(MainProjectMemberAndPermissionRoot);
                }
            }
        }

        public static List<string> GetTemplateNameList(string templatePath)
        {
            var temps = new List<string>();
            if (!templatePath.Equals(DomainClassRoot))
            {
                string[] fis = Directory.GetFiles(templatePath);
                if (fis.Length > 0)
                {
                    foreach (string fi in fis)
                    {
                        string f = fi.Replace(templatePath, "");
                        temps.Add(f.Remove(f.LastIndexOf(".")));
                    }
                }
            }
            else
            {
                string[] folder = Directory.GetDirectories(templatePath);
                if (folder.Length > 0)
                {
                    foreach (string fo in folder)
                        temps.Add(fo.Replace(templatePath, ""));
                }
            }
            return temps;
        }
        public static bool CreateDir(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return true;
        }
        public static bool RenameTemplate(TemplateType type, string directoryName, string newDirectoryName)
        {
            try
            {
                switch (type)
                {
                    //case TemplateType.DOMAIN:
                    case TemplateType.Domianclass:
                        directoryName = BimBaseServerTemplates.DomainClassRoot + directoryName;
                        newDirectoryName = BimBaseServerTemplates.DomainClassRoot + newDirectoryName;
                        break;
                    case TemplateType.Projectrole:
                        directoryName = BimBaseServerTemplates.ProjectRoleRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.ProjectRoleRoot + newDirectoryName + ".tplt";
                        break;
                    case TemplateType.Projectmember:
                        directoryName = BimBaseServerTemplates.ProjectMemberRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.ProjectMemberRoot + newDirectoryName + ".tplt";
                        break;
                    case TemplateType.Teamrole:
                        directoryName = BimBaseServerTemplates.TeamRoleRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.TeamRoleRoot + newDirectoryName + ".tplt";
                        break;
                    case TemplateType.Teammember:
                        directoryName = BimBaseServerTemplates.TeamMemberRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.TeamMemberRoot + newDirectoryName + ".tplt";
                        break;
                    case TemplateType.Release:
                        directoryName = BimBaseServerTemplates.releaseRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.releaseRoot + newDirectoryName + ".tplt";
                        break;
                    case TemplateType.Workgroup:
                        directoryName = BimBaseServerTemplates.wgRoot + directoryName + ".tplt";
                        newDirectoryName = BimBaseServerTemplates.wgRoot + newDirectoryName + ".tplt";
                        break;
                }
                if (Directory.Exists(directoryName) || File.Exists(directoryName))
                {
                    Directory.Move(directoryName, newDirectoryName);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
            return false;
        }

        public static bool DeleteTemplate(TemplateType type, string templateName)
        {
            try
            {
                switch (type)
                {
                    case TemplateType.Domianclass:
                        templateName = BimBaseServerTemplates.DomainClassRoot + templateName;
                        break;
                    case TemplateType.Projectrole:
                        templateName = BimBaseServerTemplates.ProjectRoleRoot + templateName + ".tplt";
                        break;
                    case TemplateType.Projectmember:
                        templateName = BimBaseServerTemplates.ProjectMemberRoot + templateName + ".tplt";
                        break;
                    case TemplateType.Teamrole:
                        templateName = BimBaseServerTemplates.TeamRoleRoot + templateName + ".tplt";
                        break;
                    case TemplateType.Teammember:
                        templateName = BimBaseServerTemplates.TeamMemberRoot + templateName + ".tplt";
                        break;
                    case TemplateType.Release:
                        templateName = BimBaseServerTemplates.releaseRoot + templateName + ".tplt";
                        break;
                    case TemplateType.Workgroup:
                        templateName = BimBaseServerTemplates.wgRoot + templateName + ".tplt";
                        break;
                }
                if (Directory.Exists(templateName))
                    Directory.Delete(templateName);
                else if (File.Exists(templateName))
                    File.Delete(templateName);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
            return false;
        }

        public static T ReadTemplateFile<T>(string xmlPath) where T : class
        {
            if (File.Exists(xmlPath))
                return BimBaseServerXmlSerializer.FromXmlFile<T>(xmlPath);
            return null;
        }

        public static bool SaveTemplateFile<T>(T obj, string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.ToXmlFile<T>(obj, xmlPath);
        }
    }
}
