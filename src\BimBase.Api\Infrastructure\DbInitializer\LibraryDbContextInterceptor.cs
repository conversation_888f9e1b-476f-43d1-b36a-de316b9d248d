using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    /// <summary>
    /// LibraryDbContext拦截器，用于自动初始化动态查询过滤器上下文
    /// </summary>
    public class LibraryDbContextInterceptor : DbConnectionInterceptor
    {
        // 使用 ThreadLocal 来避免递归
        private static readonly ThreadLocal<bool> _isInitializing = new ThreadLocal<bool>(() => false);

        /// <summary>
        /// 连接打开前初始化过滤器上下文
        /// </summary>
        public override InterceptionResult ConnectionOpening(DbConnection connection, ConnectionEventData eventData, InterceptionResult result)
        {
            InitializeFilterContextIfNeeded(eventData.Context);
            return base.ConnectionOpening(connection, eventData, result);
        }

        /// <summary>
        /// 异步连接打开前初始化过滤器上下文
        /// </summary>
        public override async ValueTask<InterceptionResult> ConnectionOpeningAsync(DbConnection connection, ConnectionEventData eventData, InterceptionResult result, CancellationToken cancellationToken = default)
        {
            InitializeFilterContextIfNeeded(eventData.Context);
            return await base.ConnectionOpeningAsync(connection, eventData, result, cancellationToken);
        }

        /// <summary>
        /// 如果需要，初始化过滤器上下文
        /// </summary>
        private void InitializeFilterContextIfNeeded(DbContext context)
        {
            if (context is LibraryDbContext libraryDbContext)
            {
                // 检查是否已经在初始化过程中，避免递归
                if (_isInitializing.Value)
                {
                    return;
                }

                try
                {
                    _isInitializing.Value = true;
                    
                    // 每次连接打开时重新初始化过滤器上下文
                    // 这确保了每次查询都使用最新的请求上下文信息
                    libraryDbContext.InitializeFilterContext();
                }
                finally
                {
                    _isInitializing.Value = false;
                }
            }
        }
    }
} 