﻿using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System;
using System.Runtime.InteropServices;
using BimBase.Api.Config;
using BimBase.Api.Infrastructure.Repositories;
using Microsoft.Extensions.Options;
using Microsoft.CodeAnalysis.Options;
using Microsoft.Extensions.DependencyInjection;


namespace BimBase.Api.Infrastructure.Common
{
    public static class PBIMServerTemplates
    {
        static readonly char separator = Path.DirectorySeparatorChar;
        static readonly string fileRoot = GetAssemblyDirectory() + "TemplateFiles"+separator;
        static readonly string teamRoleRoot = fileRoot + "TeamRoleRoot" + separator;
        static readonly string teamMemberRoot = fileRoot + "TeamMemberRoot" + separator;
        static readonly string projectRoleRoot = fileRoot + "ProjectRoleRoot" + separator;
        static readonly string projectMemberRoot = fileRoot + "ProjectMemberRoot" + separator;
        static readonly string domainClassRoot = fileRoot + "DomainClassRoot" + separator;
        static readonly string wgRoot = fileRoot + "WorkGroupRoot" + separator;
        static readonly string releaseRoot = fileRoot + "ReleaseRoot" + separator;
        static readonly string modelRoot = fileRoot + "ModelForWeb" + separator;
        static readonly string fileModelRoot = fileRoot + "FileModelForWeb" + separator;
        static readonly string mainProjectMemberAndPermissionRoot = fileRoot + "mainProjectMemberAndPermissionRoot" + separator;

        static string GetAssemblyDirectory()
        {

            string temppath = "";
            string _foldPath = "";
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                temppath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                _foldPath = temppath + @"/PBIMS/template/";
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var serviceProvider = ProjectRepository.GetGlobalServiceProvider();
                var options = serviceProvider.GetService<IOptions<UrlsConfig>>();

                temppath = options?.Value?.LoadDataSavePath;
                //_foldPath = @"/home/<USER>/LoadinFileDir/";
            }
            temppath = temppath.Replace(@"\", separator.ToString());
            _foldPath = temppath + @"/PBIMS/template/";
            //_foldPath =  @"/home/<USER>/LoadinFileDir/";
            if (!Directory.Exists(_foldPath))
            {
                Directory.CreateDirectory(_foldPath);
            }
            return _foldPath;
        }

        #region 模板管理功能
        public static string MainProjectMemberAndPermissionRoot
        {
            get { return mainProjectMemberAndPermissionRoot; }
        }
        public static string TeamRoleRoot
        {
            get { return teamRoleRoot; }
        }
        public static string TeamMemberRoot
        {
            get { return teamMemberRoot; }
        }
        public static string ProjectRoleRoot
        {
            get { return projectRoleRoot; }
        }
        public static string ProjectMemberRoot
        {
            get { return projectMemberRoot; }
        }
        public static string DomainClassRoot
        {
            get { return domainClassRoot; }
        }

        public static string ReleaseRoot
        {
            get { return releaseRoot; }
        }

        public static string WGRoot
        {
            get { return wgRoot; }
        }
        public static string ModelRoot
        {
            get { return modelRoot; }
        }
        public static string FileModelRoot
        {
            get { return fileModelRoot; }
        }
        static PBIMServerTemplates()
        {
            if (!Directory.Exists(fileRoot))
            {
                Directory.CreateDirectory(fileRoot);
                Directory.CreateDirectory(TeamRoleRoot);
                Directory.CreateDirectory(TeamMemberRoot);
                Directory.CreateDirectory(ProjectRoleRoot);
                Directory.CreateDirectory(ProjectMemberRoot);
                Directory.CreateDirectory(domainClassRoot);
                Directory.CreateDirectory(ReleaseRoot);
                Directory.CreateDirectory(WGRoot);
                Directory.CreateDirectory(ModelRoot);
                Directory.CreateDirectory(FileModelRoot);
                Directory.CreateDirectory(MainProjectMemberAndPermissionRoot);
            }
            else
            {
                if (!Directory.Exists(TeamRoleRoot))
                    Directory.CreateDirectory(TeamRoleRoot);
                if (!Directory.Exists(TeamMemberRoot))
                    Directory.CreateDirectory(TeamMemberRoot);
                if (!Directory.Exists(ProjectRoleRoot))
                    Directory.CreateDirectory(ProjectRoleRoot);
                if (!Directory.Exists(ProjectMemberRoot))
                    Directory.CreateDirectory(ProjectMemberRoot);
                if (!Directory.Exists(domainClassRoot))
                    Directory.CreateDirectory(domainClassRoot);
                if (!Directory.Exists(ReleaseRoot))
                    Directory.CreateDirectory(ReleaseRoot);
                if (!Directory.Exists(WGRoot))
                    Directory.CreateDirectory(WGRoot);
                if (!Directory.Exists(ModelRoot))
                {
                    Directory.CreateDirectory(ModelRoot);
                }
                if (!Directory.Exists(FileModelRoot))
                {
                    Directory.CreateDirectory(FileModelRoot);
                }
                if (!Directory.Exists(MainProjectMemberAndPermissionRoot))
                {
                    Directory.CreateDirectory(MainProjectMemberAndPermissionRoot);
                }
            }
        }

        public static List<string> GetTemplateNameList(string templatePath)
        {
            var temps = new List<string>();
            if (!templatePath.Equals(DomainClassRoot))
            {
                string[] fis = Directory.GetFiles(templatePath);
                if (fis.Length > 0)
                {
                    foreach (string fi in fis)
                    {
                        string f = fi.Replace(templatePath, "");
                        temps.Add(f.Remove(f.LastIndexOf(".")));
                    }
                }
            }
            else
            {
                string[] folder = Directory.GetDirectories(templatePath);
                if (folder.Length > 0)
                {
                    foreach (string fo in folder)
                        temps.Add(fo.Replace(templatePath, ""));
                }
            }
            return temps;
        }
        public static bool CreateDir(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return true;
        }
        #endregion

        public static T ReadTemplateFile<T>(string xmlPath) where T : class
        {
            if (File.Exists(xmlPath))
                return BimBaseServerXmlSerializer.FromXmlFile<T>(xmlPath);
            return null;
        }

        public static bool SaveTemplateFile<T>(T obj, string xmlPath) where T : class
        {
            return BimBaseServerXmlSerializer.ToXmlFile<T>(obj, xmlPath);
        }
    }
}
