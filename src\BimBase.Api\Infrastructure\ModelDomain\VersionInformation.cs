﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public enum ReleaseType
    {
        None,
        MileStone
    }


    public class VersionInformation : IComparable
    {
        public int CompareTo(object obj)
        {
            var versionInformation = obj as VersionInformation;
            if (versionInformation != null) return Time.CompareTo(versionInformation.Time);
            return -1;
        }

        [Key]
        public int VersionNo
        {
            get;
            set;
        }

        [Required]
        public string Author
        {
            get;
            set;
        }

        public string Description
        {
            get;
            set;
        }

        public DateTime Time
        {
            get { return m_Time; }
            set { m_Time = value; }
        }


        private DateTime m_Time = DateTime.Now;

        [Required, EnumDataType(typeof(ReleaseType))]
        public ReleaseType ReleaseType { get; set; }

        public int IsComplete { get; set; }
    }
}
