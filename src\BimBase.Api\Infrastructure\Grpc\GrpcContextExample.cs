using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// gRPC上下文使用示例
    /// </summary>
    public class GrpcContextExample
    {
        private readonly ILogger<GrpcContextExample> _logger;
        
        public GrpcContextExample(ILogger<GrpcContextExample> logger)
        {
            _logger = logger;
        }
        
        /// <summary>
        /// 演示如何在服务方法中使用请求上下文
        /// </summary>
        public void DemoUsingRequestContext()
        {
            try
            {
                // 获取当前请求信息
                string requestId = GrpcContextAccessor.GetRequestId();
                string userId = GrpcContextAccessor.GetUserId();
                string clientId = GrpcContextAccessor.GetClientId();
                string clientIp = GrpcContextAccessor.GetClientIp();
                string methodName = GrpcContextAccessor.GetMethodName();
                
                // 获取整个请求上下文
                var context = GrpcContextAccessor.GetContext();
                
                // 向请求上下文中添加自定义数据
                GrpcContextAccessor.SetItem("MyCustomData", new { Name = "测试数据", Value = 123 });
                
                // 从请求上下文中获取之前存储的数据
                var myData = GrpcContextAccessor.GetItem<dynamic>("MyCustomData");
                
                // 获取请求已经耗时多久
                long elapsedMs = GrpcContextAccessor.GetElapsedMilliseconds();
                
                // 日志中使用请求上下文信息
                _logger.LogInformation(
                    $"处理请求: {methodName}, RequestId: {requestId}, " +
                    $"UserId: {userId}, ClientId: {clientId}, ClientIp: {clientIp}, " +
                    $"已耗时: {elapsedMs}ms");
                
                // 也可以使用安全的TryGetContext方式获取上下文
                if (GrpcContextAccessor.TryGetContext(out var ctx))
                {
                    _logger.LogInformation($"成功获取上下文，请求ID: {ctx.RequestId}");
                }
            }
            catch (InvalidOperationException ex)
            {
                // 如果当前不在gRPC请求上下文中调用，会抛出异常
                _logger.LogWarning(ex, "当前不在gRPC请求上下文中");
            }
        }
        
        /// <summary>
        /// 跨类传递信息的示例
        /// </summary>
        public void DemoPassingDataBetweenHandlers()
        {
            // 在处理A中保存数据
            GrpcContextAccessor.SetItem("ProcessData", "Handler A 处理的数据");
            
            // ... 其他业务逻辑 ...
            
            // 调用另一个服务/处理器
            CallAnotherHandler();
        }
        
        private void CallAnotherHandler()
        {
            // 在处理B中获取处理A保存的数据
            var dataFromHandlerA = GrpcContextAccessor.GetItem<string>("ProcessData");
            
            _logger.LogInformation($"从处理器A获取的数据: {dataFromHandlerA}");
            
            // ... 处理B的业务逻辑 ...
        }
    }
} 