syntax = "proto3";

option csharp_namespace = "BimBaseAuth.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

package AuthApi;

message GrpcRole{
	string id = 1;
	string createId = 2;  //c# guid
	string appId = 3;
	google.protobuf.Timestamp updateTime = 4;
	int32 delFlag = 5;
	string name = 6;
	int32 status = 7;
	int32 type = 8;
	google.protobuf.Timestamp createTime = 9;
}


message GrpcRoleExtend{
	string id = 1;
	string createId = 2;  //c# guid
	string appId = 3;
	google.protobuf.Timestamp updateTime = 4;
	int32 delFlag = 5;
	string name = 6;
	int32 status = 7;
	int32 type = 8;
	google.protobuf.Timestamp createTime = 9;
	string relatedId = 10; //c# guid
	bool isAdmin = 11;
}



message GrpcAuthInfo{
	string id = 1;
	string createId = 2;  //c# guid
	string appId = 3;
	google.protobuf.Timestamp updateTime = 4;
	int32 delFlag = 5;
	string name = 6;
	string code = 7;
	string type = 8;
	int32 permission = 9;
	bool isGlobalAuth = 10;
	string parentId = 11;
	string relatedId = 12;
}





message GrpcUserRoleWithAccessedCtrls{
	repeated GrpcRole roleList = 1;
	repeated GrpcAuthInfo globalAuth = 2;
	repeated string projectInfoList = 3;
}


message AddProjectRoleRequest {
	GrpcRoleExtend roleExtend = 1;
}

message ProjectRoleResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcRole roleList = 4;
}

message GetProjectRoleRequest {
	string projectguid = 1;
}


message AddRoleRequest{
	GrpcRole role = 1;
}

message DeleteProjectRequest{
	string projectId = 1;
}

message DeleteProjectRoleRequest{
	repeated string  roleIds = 1;
}

message DeleteRoleAuthRequest{
	string roleId = 1;
}

message DeleteRoleUserRequest{
	string roleId = 1;
	repeated string userIds = 2;
}

message GetCurrentUserAuthRequest{
	string userId = 1;
}

message CurrentUserAuthResponse{
	int32 status = 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcRole roleList = 4;
	repeated GrpcAuthInfo globalAuth = 5;
	repeated string projectInfoList = 6;
}

message GetOwnRoleListRequest{
	string userId = 1;
}

message OwnRoleListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcRole roleList = 4;
}

message AuthResult{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	google.protobuf.Any result = 4;
}




/*

message AddTeamManagerRequest{
	string currentUserId = 1;
}

message DeleteRoleRequest{
	repeated string roleIds = 1;
}
*/


message GetUsersFromRoleIdRequest{
	string roleId = 1;
}

message GetRoleInfoByUserIdAndProjectIdRequest{
	string userId = 1;
	string projecId = 2;
}

message GetRoleInfoByUserIdAndProjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcRole roleList = 4;
}


message GetProjectAuthByUserIdRequest{
	string userId = 1;
	string projectId = 2;
}

message GetProjectAuthByUserIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcAuthInfo authInfoList = 4;
}

message GiveRoleAuthRequest{
	string roleId = 1;
	repeated GrpcAuthInfo authInfoList = 2;
}

message GetRoleAuthRequest{
	string roleId = 1;
}

message GetRoleAuthResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcAuthInfo authInfoList = 4;
}
message GiveRoleUserRequest{
	string roleId = 1;
	repeated string userId = 2;
}
message GiveProjectRoleRequest{
	string projectId = 1;
	string roleId = 2;
}
service AuthGrpc {
  rpc AddProjectRole (AddProjectRoleRequest) returns (AuthResult) {}
  rpc GetProjectRole (GetProjectRoleRequest) returns (ProjectRoleResponse) {}
  rpc AddRole (AddRoleRequest) returns (AuthResult) {}
  rpc DeleteProject (DeleteProjectRequest) returns (AuthResult) {}
  rpc DeleteProjectRole(DeleteProjectRoleRequest) returns(AuthResult){}
  rpc DeleteRoleAuth (DeleteRoleAuthRequest) returns (AuthResult) {}
  rpc DeleteRoleUser(DeleteRoleUserRequest) returns (AuthResult) {}
  rpc GetCurrentUserAuth(GetCurrentUserAuthRequest) returns (CurrentUserAuthResponse) {}
  rpc GetOwnRoleList(GetOwnRoleListRequest) returns (OwnRoleListResponse) {}
  rpc GetUsersFromRoleId(GetUsersFromRoleIdRequest) returns (AuthResult){}
  rpc GetRoleInfoByUserIdAndProjectId(GetRoleInfoByUserIdAndProjectIdRequest) returns(GetRoleInfoByUserIdAndProjectIdResponse){}
  rpc GetProjectAuthByUserId(GetProjectAuthByUserIdRequest) returns(GetProjectAuthByUserIdResponse){}
  rpc GiveRoleAuth(GiveRoleAuthRequest) returns (AuthResult){}
  rpc GetRoleAuth(GetRoleAuthRequest) returns (GetRoleAuthResponse){}
  rpc GiveRoleUser(GiveRoleUserRequest) returns (AuthResult){}
  /*
  rpc AddTeamManager (AddTeamManagerRequest) returns (AuthResult){}
  rpc DeleteRole (DeleteRoleRequest) returns (AuthResult){}
  */
}




