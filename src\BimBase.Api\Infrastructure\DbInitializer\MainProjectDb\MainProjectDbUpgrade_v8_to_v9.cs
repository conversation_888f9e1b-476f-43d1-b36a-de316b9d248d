using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb数据库从v8升级到v9
    /// 主要变更：
    /// 1. 添加OriginalNodeName字段，用于记录冲突前的原始节点名称
    /// 2. 添加HasConflict字段，用于标记是否存在冲突
    /// 3. 添加相关索引以支持冲突查询
    /// </summary>
    public class MainProjectDbUpgrade_v8_to_v9 : AbstractMainProjectDbUpgrade
    {
        public override string FromVersion => "v8";
        public override string ToVersion => "v9";
        public override string Description => "添加节点名称冲突记录字段，支持数据库层面的冲突检测和记录";

        public MainProjectDbUpgrade_v8_to_v9(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{FromVersion} -> {ToVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v8 到 v9 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(MainProjectDbContext context)
        {
            // 1. 添加OriginalNodeName字段
            await AddOriginalNodeNameFieldAsync(context);

            // 2. 添加HasConflict字段
            await AddHasConflictFieldAsync(context);

            // 3. 添加冲突相关索引
            await AddConflictIndexAsync(context);
        }

        /// <summary>
        /// 添加OriginalNodeName字段
        /// </summary>
        private async Task AddOriginalNodeNameFieldAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD COLUMN `OriginalNodeName` VARCHAR(190) NULL COMMENT '原始节点名称（用于记录冲突前的名称）';
                ");
                Logger.LogInformation("成功添加 OriginalNodeName 字段");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加 OriginalNodeName 字段失败");
                throw;
            }
        }

        /// <summary>
        /// 添加HasConflict字段
        /// </summary>
        private async Task AddHasConflictFieldAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD COLUMN `HasConflict` INT NOT NULL DEFAULT 0 COMMENT '冲突标记（1=存在冲突，0=无冲突）';
                ");
                Logger.LogInformation("成功添加 HasConflict 字段");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加 HasConflict 字段失败");
                throw;
            }
        }

        /// <summary>
        /// 添加冲突相关索引
        /// </summary>
        private async Task AddConflictIndexAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD INDEX `idx_hasConflict` (`HasConflict`);
                ");
                Logger.LogInformation("成功添加 HasConflict 索引");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加 HasConflict 索引失败");
                throw;
            }
        }

        protected override async Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {ToVersion} -> {FromVersion} 回滚...");

            try
            {
                // 移除索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_hasConflict`;
                ");
                Logger.LogInformation("成功移除 HasConflict 索引");

                // 移除字段
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP COLUMN `HasConflict`;
                ");
                Logger.LogInformation("成功移除 HasConflict 字段");

                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP COLUMN `OriginalNodeName`;
                ");
                Logger.LogInformation("成功移除 OriginalNodeName 字段");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "回滚操作失败");
                throw;
            }

            Logger.LogInformation($"{ToVersion} -> {FromVersion} 回滚完成");
        }
    }
} 