﻿using Microsoft.EntityFrameworkCore.Design;
using Microsoft.EntityFrameworkCore;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class LibraryDbContextFactory : IDesignTimeDbContextFactory<LibraryDbContext>
    {
        public LibraryDbContext CreateDbContext(string[] args)
        {
            var connectionString = args[0];
            var optionsBuilder = new DbContextOptionsBuilder<LibraryDbContext>()
                .UseMySql(connectionString, ServerVersion.AutoDetect(connectionString)
                , mySqlOptions => mySqlOptions
        .EnableRetryOnFailure()
        .CommandTimeout(30));

            return new LibraryDbContext(optionsBuilder.Options);
        }
    }
}
