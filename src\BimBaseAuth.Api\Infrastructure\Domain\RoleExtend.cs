﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Domain
{
	public class RoleExtend : Entity
	{
		public string Name
		{
			get;
			set;
		}

		public int Status
		{
			get;
			set;
		}

		public int Type
		{
			get;
			set;
		}

		public DateTime CreateTime
		{
			get;
			set;
		}

		public Guid RelatedId
		{
			get;
			set;
		}

		public bool IsAdmin
		{
			get;
			set;
		}
	}
}
