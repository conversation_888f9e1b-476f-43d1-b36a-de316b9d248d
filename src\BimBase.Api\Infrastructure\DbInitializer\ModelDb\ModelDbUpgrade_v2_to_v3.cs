using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using k8s.Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace BimBase.Api.Infrastructure.DbInitializer.ModelDb
{
    /// <summary>
    /// ModelDb升级：v2 -> v3
    /// 主要变更：重建modeldatas_version_tracker表数据
    /// </summary>
    public class ModelDbUpgrade_v2_to_v3 : AbstractModelDbUpgrade
    {
        public ModelDbUpgrade_v2_to_v3(ILogger logger) : base(logger)
        {
        }

        public override string FromVersion => "v2";
        public override string ToVersion => "v3";
        public override string Description => "重建ModelDb版本跟踪表数据";
        public override bool SupportsRollback => true;

        protected override async Task ExecuteUpgradeAsync(ModelDbContext context)
        {
            Logger.LogInformation($"开始执行ModelDb {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"ModelDb {FromVersion} -> {ToVersion} 升级完成");
        }

        protected override async Task ExecuteRollbackAsync(ModelDbContext context)
        {
            Logger.LogInformation($"开始执行ModelDb {ToVersion} -> {FromVersion} 回滚...");

            // 空回滚，无需执行任何操作
            Logger.LogInformation("这是一个空回滚，无需执行任何数据库更改");
            
            // 模拟一些异步操作
            await Task.CompletedTask;

            Logger.LogInformation($"ModelDb {ToVersion} -> {FromVersion} 回滚完成");
        }

        /// <summary>
        /// 执行 v2 到 v3 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(ModelDbContext context)
        {
            // 检查version_tracker表是否存在
            if (!await TableExistsAsync(context, "modeldatas_version_tracker"))
            {
                Logger.LogWarning("modeldatas_version_tracker表不存在，跳过数据重建操作");
                return;
            }

            Logger.LogInformation("开始重建modeldatas_version_tracker表数据...");

            // 1. 清空目标表数据（使用 TRUNCATE 更高效）
            await TruncateVersionTrackerTableAsync(context);
            // 2. 重命名created_at列
            await ModiyVersionTrackerCreateAtColumAsync(context);
            // 3. 插入查询结果到目标表
            await RebuildVersionTrackerDataAsync(context);

            Logger.LogInformation("modeldatas_version_tracker表数据重建完成");
        }

        /// <summary>
        /// 清空版本跟踪表数据
        /// </summary>
        private async Task TruncateVersionTrackerTableAsync(ModelDbContext context)
        {
            Logger.LogInformation("清空modeldatas_version_tracker表数据...");
            
            try
            {
                await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE modeldatas_version_tracker;");
                Logger.LogInformation("成功清空modeldatas_version_tracker表数据");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "清空modeldatas_version_tracker表数据失败，尝试使用DELETE方式");
                // 如果TRUNCATE失败，使用DELETE作为备选方案
                await context.Database.ExecuteSqlRawAsync("DELETE FROM modeldatas_version_tracker;");
                Logger.LogInformation("使用DELETE方式成功清空modeldatas_version_tracker表数据");
            }
        }

        
        private async Task ModiyVersionTrackerCreateAtColumAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始重命名created_at列...");
            if (await ColumnExistsAsync(context, "modeldatas_version_tracker", "created_at")
                &&(!await ColumnExistsAsync(context, "modeldatas_version_tracker", "create_at")))
            {
                //重命名created_at为create_at
                await context.Database.ExecuteSqlRawAsync(@"
                        ALTER TABLE `modeldatas_version_tracker`
                        CHANGE COLUMN `created_at` `create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ;");
            }
        }
             

        /// <summary>
        /// 重建版本跟踪表数据
        /// </summary>
        private async Task RebuildVersionTrackerDataAsync(ModelDbContext context)
        {
            Logger.LogInformation("开始重建modeldatas_version_tracker表数据...");

            var insertSql = @"
                 INSERT INTO modeldatas_version_tracker (data_id, instance_id, max_version,create_at)
                 SELECT max(id) AS data_id, instanceid AS instance_id, MAX(versionno) AS max_version,now()
                 FROM modeldatas
                 GROUP BY instanceid;";

            try
            {
                var rowsAffected = await context.Database.ExecuteSqlRawAsync(insertSql);
                Logger.LogInformation($"成功重建modeldatas_version_tracker表数据，影响行数：{rowsAffected}");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "重建modeldatas_version_tracker表数据失败");
                throw; // 重新抛出异常，因为这是关键操作
            }
        }
    }
} 