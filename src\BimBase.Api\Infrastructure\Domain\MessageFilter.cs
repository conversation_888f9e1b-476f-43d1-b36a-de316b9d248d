﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.Domain
{
    public class MessageFilter
    {
        [Key]
        public int ID { get; set; }

        [Required]
        public Guid UserID { get; set; }

        [Required]
        public virtual List<MessageEvent> EventFilters { get; set; }
    }
}
