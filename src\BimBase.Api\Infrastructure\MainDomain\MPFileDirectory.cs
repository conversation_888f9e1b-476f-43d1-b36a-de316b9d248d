﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPFileDirectory
    {
        public MPFileDirectory()
        {
            ID = Guid.NewGuid();
        }

        /// <summary>
        /// 唯一ID，主键
        /// </summary>
        [Key]
        
        public Guid ID { get; set; }
        /// <summary>
        /// 用于让用户识别项目的名字
        /// </summary>
        
        public string Name
        {
            get;
            set;
        }
        /// <summary>
        /// 父目录ID
        /// </summary>
        
        public Guid ParentID
        {
            get;
            set;
        }

        
        public string CreateUser { get; set; }

        /// <summary>
        /// 文件夹类型1：模型文件夹 2：卷册文件夹 3：云链接文件夹 4：资源文件夹
        /// </summary>
        
        public int Type { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        
        public int OrderNo { get; set; }
    }
}
