using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.TeamDb
{
    /// <summary>
    /// TeamDb数据库升级接口
    /// </summary>
    public interface ITeamDbUpgrade
    {
        /// <summary>
        /// 获取升级的源版本
        /// </summary>
        string SourceVersion { get; }
        
        /// <summary>
        /// 获取升级的目标版本
        /// </summary>
        string TargetVersion { get; }
        
        /// <summary>
        /// 获取升级描述
        /// </summary>
        string Description { get; }
        
        /// <summary>
        /// 执行数据库升级
        /// </summary>
        /// <param name="context">TeamDb上下文</param>
        Task UpgradeAsync(TeamDbContext context);
        
        /// <summary>
        /// 回滚数据库升级
        /// </summary>
        /// <param name="context">TeamDb上下文</param>
        Task RollbackAsync(TeamDbContext context);
    }
} 