﻿using BimBase.Api.Infrastructure.LibDomain;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class LibraryDbContext:DbContext
    {
        #region 动态查询过滤器属性

        /// <summary>
        /// 当前请求的客户端ID（用于查询过滤器）
        /// </summary>
        public string CurrentClientId { get; set; } = "";

        /// <summary>
        /// 当前请求的客户端版本（用于查询过滤器）
        /// </summary>
        public string CurrentClientVersion { get; set; } = "";

        /// <summary>
        /// 当前客户端的最小兼容版本（预计算结果，用于查询过滤器）
        /// </summary>
        public string CurrentMinCompatibleVersion { get; set; } = "";
        
        /// <summary>
        /// 当前客户端是否有版本兼容性配置（预计算结果，用于查询过滤器）
        /// </summary>
        public bool HasVersionCompatibilityConfig { get; set; } = false;

        private bool Initialized { get; set; } = false;

        /// <summary>
        /// 初始化动态过滤器上下文
        /// </summary>
        public void InitializeFilterContext()
        {
            // 从gRPC上下文获取当前请求信息
            CurrentClientId = Infrastructure.Grpc.GrpcContextAccessor.GetClientId() ?? "";
            CurrentClientVersion = Infrastructure.Grpc.GrpcContextAccessor.GetClientVersion() ?? "";
            
            // 预计算版本兼容性信息
            InitializeVersionCompatibility();
            Initialized = true;
        }

        /// <summary>
        /// 预计算版本兼容性信息（使用独立的DbContext避免递归）
        /// </summary>
        private void InitializeVersionCompatibility()
        {
            if (string.IsNullOrEmpty(CurrentClientId) || string.IsNullOrEmpty(CurrentClientVersion))
            {
                HasVersionCompatibilityConfig = false;
                CurrentMinCompatibleVersion = "";
                return;
            }
            
            try
            {
                // 使用独立的DbContext实例来查询版本兼容性，这个DbContext不会注册拦截器
                var optionsBuilder = new DbContextOptionsBuilder<LibraryDbContext>();
                
                // 获取当前DbContext的连接字符串
                var connectionString = Database.GetConnectionString();
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
                // 注意：这里不添加拦截器，所以不会导致递归
                
                using (var separateContext = new LibraryDbContext(optionsBuilder.Options,true))
                {
                    // 查询版本兼容性配置
                    var compatibilityEntry = separateContext.ClientVersionCompatibilities
                        .Where(c => c.ClientId == CurrentClientId && c.ClientVersion == CurrentClientVersion)
                        .FirstOrDefault();
                    
                    if (compatibilityEntry != null)
                    {
                        HasVersionCompatibilityConfig = true;
                        CurrentMinCompatibleVersion = compatibilityEntry.MinCompatibleVersion ?? "";
                    }
                    else
                    {
                        // 检查是否有该ClientId的任何配置
                        HasVersionCompatibilityConfig = separateContext.ClientVersionCompatibilities
                            .Any(c => c.ClientId == CurrentClientId);
                        CurrentMinCompatibleVersion = "";
                    }
                }
            }
            catch
            {
                // 如果查询失败（比如表不存在），则使用默认值
                HasVersionCompatibilityConfig = false;
                CurrentMinCompatibleVersion = "";
            }
        }

        #endregion
        public LibraryDbContext(DbContextOptions<LibraryDbContext> options,bool initialized = false) : base(options)
        {
            if (!Initialized && !initialized)
            {
                // 初始化动态查询过滤器上下文
                InitializeFilterContext();
            }

        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<CLCompanyLibInfo>().ToTable("clcompanylibinfoes");
            modelBuilder.Entity<CLCatalogTreeNode>()
                .HasIndex(u => new { u.LibId, u.InstanceId })
                .IsUnique();
            modelBuilder.Entity<CLLibraryData>()
                .HasIndex(u => new { u.LibId, u.DataId })
                .IsUnique();
            modelBuilder.Entity<CLTreeNodeLock>()
                .HasIndex(u => new { u.LockUserName, u.NodeId })
                .IsUnique();
            
            // 配置 ClientVersionCompatibility 表索引
            modelBuilder.Entity<ClientVersionCompatibility>()
                .HasIndex(e => e.ClientId)
                .HasDatabaseName("IDX_CLIENT_ID");
            
            // 其他配置...
            // 完整的CLCompanyLibInfo查询过滤器 - 使用预计算结果实现完整的版本兼容性检查
            modelBuilder.Entity<CLCompanyLibInfo>()
                .HasQueryFilter(m =>
                    // 1. clientId 过滤逻辑
                    (string.IsNullOrEmpty(CurrentClientId)
                        ? (string.IsNullOrEmpty(m.clientId) || m.clientId == "PKPM-Plant")  // 当前请求clientId为空时，只能获取数据库中clientId为空或PKPM-Plant的数据
                        : CurrentClientId == "PKPM-Plant"
                            ? (string.IsNullOrEmpty(m.clientId) || m.clientId == "PKPM-Plant")  // PKPM-Plant可以获取空或PKMP-Plant的数据
                            : m.clientId == CurrentClientId  // 其他情况只能获取相等的数据
                    ) &&

                    // 2. ClientVersion 兼容性过滤逻辑 - 根据是否有兼容版本信息采用不同策略
                    (string.IsNullOrEmpty(CurrentClientVersion)
                        ? (string.IsNullOrEmpty(m.ClientVersion) || m.ClientVersion == "BIMBase-2025R01.01")  // 当前请求ClientVersion为空时，可以获取ClientVersion为空或为"10000000"的数据
                        : HasVersionCompatibilityConfig
                            ? (string.IsNullOrEmpty(m.ClientVersion) ||  // 有兼容版本信息：允许访问没有版本限制的数据
                               (!string.IsNullOrEmpty(CurrentMinCompatibleVersion) && 
                                string.Compare(m.ClientVersion, CurrentMinCompatibleVersion) >= 0))  // 库版本 >= 最小兼容版本
                            : (string.IsNullOrEmpty(m.ClientVersion) ||  // 没有兼容版本信息：允许访问没有版本限制的数据
                               m.ClientVersion == CurrentClientVersion)  // 精确匹配当前客户端版本
                    )
                );
            base.OnModelCreating(modelBuilder);
        }
        #region 数据库表

        // 版本管理表
        public DbSet<BimBase.Api.Infrastructure.LibDomain.DatabaseVersion> DatabaseVersions { get; set; }

        // 客户端版本兼容性配置表
        public DbSet<ClientVersionCompatibility> ClientVersionCompatibilities { get; set; }

        public DbSet<CLCatalogTreeNode> CLCatalogTreeNodes { get; set; }
        public DbSet<CLTreeNodeLock> CLTreeNodeLocks { get; set; }
        public DbSet<CLTreeNodeVersion> CLTreeNodeVersions { get; set; }

        public DbSet<CLLibraryData> CLLibraryDatas { get; set; }

        public DbSet<CLCompanyLibInfo> CLCompanyLibInfos { get; set; }

        public DbSet<CLLibraryDataHistory> CLLibraryDataHistories { get; set; }

        //public DbSet<CLCatalogTreeNodeHistory> CLCatalogTreeNodeHistories {  get; set; }    
        public DbSet<CLMainprojectLib> CLMainprojectLibs { get; set; }

        #endregion
    }
}
