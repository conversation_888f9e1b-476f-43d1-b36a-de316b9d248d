﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.LibDomain
{
    public class CLTreeNodeLock

    {
        /// <summary>
        /// 自增id
        /// </summary>
        [Key]
        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public long NodeId { get; set; }
        /// <summary>
        /// 锁定人loginname
        /// </summary>
        [DataMember]
        public string LockUserName { get; set; }
        /// <summary>
        /// 所属库guid
        /// </summary>
        [DataMember]
        public Guid LibId { get; set; }

        /// <summary>
        /// 锁类型 0：独占锁，1：共享锁
        /// </summary>
        [DataMember]
        public int LockType { get; set; }

    }
}
