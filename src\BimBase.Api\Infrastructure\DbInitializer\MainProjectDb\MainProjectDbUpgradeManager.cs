using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using BimBase.Api.Infrastructure.MainDomain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb升级管理器
    /// </summary>
    public class MainProjectDbUpgradeManager
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, IMainProjectDbUpgrade> _upgrades;
        
        public MainProjectDbUpgradeManager(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _upgrades = new Dictionary<string, IMainProjectDbUpgrade>();
            
            // 注册所有升级类
            RegisterUpgrades();
        }
        
        /// <summary>
        /// 注册所有升级类
        /// </summary>
        private void RegisterUpgrades()
        {
            var upgrades = new List<IMainProjectDbUpgrade>
            {
                new MainProjectDbUpgrade_v0_to_v2(_logger),
                new MainProjectDbUpgrade_v2_to_v3(_logger),
                new MainProjectDbUpgrade_v3_to_v4(_logger),
                new MainProjectDbUpgrade_v4_to_v5(_logger),
                new MainProjectDbUpgrade_v5_to_v6(_logger),
                new MainProjectDbUpgrade_v6_to_v7(_logger),
                new MainProjectDbUpgrade_v7_to_v8(_logger), // 新增：优化索引结构，支持高效节点名称冲突检测
                new MainProjectDbUpgrade_v8_to_v9(_logger), // 新增：添加冲突记录字段，支持数据库层面冲突检测
                new MainProjectDbUpgrade_v9_to_v10(_logger), // 新增：添加全局冲突检测索引，支持配置化冲突检测
                new MainProjectDbUpgrade_v10_to_v11(_logger) // 新增：添加ConflictScope字段，实现数据库层面的动态冲突检测
                // 未来可以在这里添加更多升级类
                // new MainProjectDbUpgrade_v3_to_v4(_logger),
            };
            
            foreach (var upgrade in upgrades)
            {
                var key = $"{upgrade.FromVersion}->{upgrade.ToVersion}";
                _upgrades[key] = upgrade;
                _logger.LogDebug($"注册MainProjectDb升级: {key}");
            }
        }
        
        /// <summary>
        /// 计算升级路径
        /// </summary>
        public List<IMainProjectDbUpgrade> CalculateUpgradePath(string fromVersion, string toVersion)
        {
            _logger.LogInformation($"计算MainProjectDb升级路径: {fromVersion} -> {toVersion}");
            
            var path = new List<IMainProjectDbUpgrade>();
            var currentVersion = fromVersion;
            
            // 简单的顺序升级逻辑
            while (currentVersion != toVersion)
            {
                var nextUpgrade = FindNextUpgrade(currentVersion);
                if (nextUpgrade == null)
                {
                    throw new InvalidOperationException($"无法找到从 {currentVersion} 开始的升级路径");
                }
                
                path.Add(nextUpgrade);
                currentVersion = nextUpgrade.ToVersion;
                
                // 防止无限循环
                if (path.Count > 10)
                {
                    throw new InvalidOperationException("升级路径过长，可能存在循环依赖");
                }
            }
            
            _logger.LogInformation($"计算出的升级路径: {string.Join(" -> ", path.Select(u => $"{u.FromVersion}->{u.ToVersion}"))}");
            return path;
        }
        
        /// <summary>
        /// 计算回滚路径
        /// </summary>
        public List<IMainProjectDbUpgrade> CalculateRollbackPath(string fromVersion, string toVersion)
        {
            _logger.LogInformation($"计算MainProjectDb回滚路径: {fromVersion} -> {toVersion}");
            
            // 先计算正向路径，然后反转
            var forwardPath = CalculateUpgradePath(toVersion, fromVersion);
            var rollbackPath = new List<IMainProjectDbUpgrade>();
            
            // 反转路径并检查是否支持回滚
            for (int i = forwardPath.Count - 1; i >= 0; i--)
            {
                var upgrade = forwardPath[i];
                if (!((AbstractMainProjectDbUpgrade)upgrade).SupportsRollback)
                {
                    throw new NotSupportedException($"升级 {upgrade.FromVersion} -> {upgrade.ToVersion} 不支持回滚");
                }
                rollbackPath.Add(upgrade);
            }
            
            _logger.LogInformation($"计算出的回滚路径: {string.Join(" -> ", rollbackPath.Select(u => $"{u.ToVersion}->{u.FromVersion}"))}");
            return rollbackPath;
        }
        
        /// <summary>
        /// 执行升级路径（主要入口方法）
        /// </summary>
        public async Task ExecuteUpgradePathAsync(MainProjectDbContext context, string fromVersion, string toVersion)
        {
            _logger.LogInformation($"开始MainProjectDb升级: {fromVersion} -> {toVersion}");
            
            if (fromVersion == toVersion)
            {
                _logger.LogInformation("源版本与目标版本相同，无需升级");
                return;
            }
            
            var upgradePath = CalculateUpgradePath(fromVersion, toVersion);
            await ExecuteUpgradePathAsync(context, upgradePath);
        }

        /// <summary>
        /// 执行升级路径
        /// </summary>
        public async Task ExecuteUpgradePathAsync(MainProjectDbContext context, List<IMainProjectDbUpgrade> upgradePath)
        {
            _logger.LogInformation($"开始执行MainProjectDb升级路径，共 {upgradePath.Count} 个步骤");
            
            for (int i = 0; i < upgradePath.Count; i++)
            {
                var upgrade = upgradePath[i];
                _logger.LogInformation($"执行升级步骤 {i + 1}/{upgradePath.Count}: {upgrade.FromVersion} -> {upgrade.ToVersion}");
                
                await ((AbstractMainProjectDbUpgrade)upgrade).UpgradeAsync(context);
            }
            
            _logger.LogInformation("MainProjectDb升级路径执行完成");
        }
        
        /// <summary>
        /// 执行回滚路径
        /// </summary>
        public async Task ExecuteRollbackPathAsync(MainProjectDbContext context, List<IMainProjectDbUpgrade> rollbackPath)
        {
            _logger.LogInformation($"开始执行MainProjectDb回滚路径，共 {rollbackPath.Count} 个步骤");
            
            for (int i = 0; i < rollbackPath.Count; i++)
            {
                var upgrade = rollbackPath[i];
                _logger.LogInformation($"执行回滚步骤 {i + 1}/{rollbackPath.Count}: {upgrade.ToVersion} -> {upgrade.FromVersion}");
                
                await ((AbstractMainProjectDbUpgrade)upgrade).RollbackAsync(context);
            }
            
            _logger.LogInformation("MainProjectDb回滚路径执行完成");
        }
        
        /// <summary>
        /// 查找从指定版本开始的下一个升级
        /// </summary>
        private IMainProjectDbUpgrade FindNextUpgrade(string fromVersion)
        {
            return _upgrades.Values.FirstOrDefault(u => u.FromVersion == fromVersion);
        }
        
        /// <summary>
        /// 获取所有可用的升级
        /// </summary>
        public IReadOnlyDictionary<string, IMainProjectDbUpgrade> GetAllUpgrades()
        {
            return _upgrades.AsReadOnly();
        }
    }
} 