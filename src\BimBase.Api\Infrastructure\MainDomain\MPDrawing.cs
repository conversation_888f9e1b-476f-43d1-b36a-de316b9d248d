﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System;

namespace BimBase.Api.Infrastructure.MainDomain
{
    public class MPDrawing
    {
        
        [Key]
        public Guid DrawingID { get; set; }

        
        public Guid SubProjectId { get; set; }

        
        public long InstanceId { get; set; }
        /// <summary>
        /// 图纸存储路径
        /// </summary>
        
        public string FullPath { get; set; }
        
        public string FileName { get; set; }

        
        public DateTime CreateTime { get; set; }
        
        public DateTime UpdateTime { get; set; }
        
        public int VersionNo { get; set; }

        
        public string LockUser { get; set; }
        /// <summary>
        /// 文件MD5值
        /// </summary>
        
        public string FileMD5 { get; set; }


    }
}
