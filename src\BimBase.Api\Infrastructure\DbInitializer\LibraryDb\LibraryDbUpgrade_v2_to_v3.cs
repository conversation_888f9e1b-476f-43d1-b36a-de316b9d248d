﻿using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.LibraryDb
{
    public class LibraryDbUpgrade_v2_to_v3 : AbstractLibraryDbUpgrade
    {
        public override string FromVersion => "v2";
        public override string ToVersion => "v3";
        public override string Description => "LibraryDb v2到v3空升级（无实际更改）";
        public LibraryDbUpgrade_v2_to_v3(ILogger logger) : base(logger) { }
        protected override async Task ExecuteUpgradeAsync(LibraryDbContext context)
        {
            Logger.LogInformation($"开始执行LibraryDb {FromVersion}->{ToVersion}升级");

            // 空升级，无需执行任何操作
            Logger.LogInformation("这是一个空升级，无需执行任何数据库更改");

            // 模拟一些异步操作
            await Task.CompletedTask;

            Logger.LogInformation($"LibraryDb {FromVersion}->{ToVersion}升级完成");
        }

        protected override async Task ExecuteRollbackAsync(LibraryDbContext context)
        {
            Logger.LogInformation($"开始执行LibraryDb {ToVersion}->{FromVersion}回滚");

            // 空回滚，无需执行任何操作
            Logger.LogInformation("这是一个空回滚，无需执行任何数据库更改");

            // 模拟一些异步操作
            await Task.CompletedTask;

            Logger.LogInformation($"LibraryDb {ToVersion}->{FromVersion}回滚完成");
        }
    }
}
