using Microsoft.Extensions.DependencyInjection;
using Grpc.Core.Interceptors;

namespace BimBase.Api.Infrastructure.Grpc
{
    /// <summary>
    /// gRPC服务扩展方法
    /// </summary>
    public static class GrpcExtensions
    {
        /// <summary>
        /// 添加BimBase gRPC拦截器
        /// </summary>
        public static IServiceCollection AddBimBaseGrpcInterceptors(this IServiceCollection services)
        {
            // 注册拦截器服务
            services.AddSingleton<GrpcPerformanceInterceptor>();
            
            return services;
        }
        
        /// <summary>
        /// 添加gRPC服务及拦截器
        /// </summary>
        public static IServiceCollection AddBimBaseGrpcServices(this IServiceCollection services)
        {
            // 添加拦截器
            services.AddBimBaseGrpcInterceptors();
            
            // 添加gRPC服务
            services.AddGrpc(options =>
            {
                // 注册拦截器
                options.Interceptors.Add<GrpcPerformanceInterceptor>();
            });
            
            return services;
        }
    }
} 