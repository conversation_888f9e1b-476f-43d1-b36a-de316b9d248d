﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    /// <summary>
    /// 被锁定的构件  add by asdf 2018-06-06
    /// 缓存保存数据后，未同步到数据库，锁定构建加入到此表中，
    /// 解除锁定后要删除对应的数据
    /// </summary>
    public class LockedComponents
    {
        [System.ComponentModel.DataAnnotations.Key]
        public Int64 Id { get; set; }
        /// <summary>
        /// 专业，按专业锁定使用
        /// </summary>
        public int Domain { get; set; }
        /// <summary>
        /// 构件Id
        /// </summary>
        public Int64 InstanceId { get; set; }
        /// <summary>
        /// projectmembers 表主键
        /// </summary>
        public int LockUserId { get; set; }
        /// <summary>
        /// 锁定时间
        /// </summary>
        public DateTime LockedAt { get; set; }
        /// <summary>
        /// 锁类型  0：独占锁，1：共享锁
        /// </summary>
        public int LockType { get; set; }

    }
}
