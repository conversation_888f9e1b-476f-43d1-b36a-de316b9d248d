﻿using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    public class MainProjectDbUpgrade_v5_to_v6:AbstractMainProjectDbUpgrade
    {
        public MainProjectDbUpgrade_v5_to_v6(ILogger logger) : base(logger)
        {
        }

        public override string FromVersion => "v5";
        public override string ToVersion => "v6";
        public override string Description => "MainProjectDb v5到v6空升级（无实际更改）";

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行MainProjectDb {FromVersion}->{ToVersion}升级");

            // 空升级，无需执行任何操作
            Logger.LogInformation("这是一个空升级，无需执行任何数据库更改");

            // 模拟一些异步操作
            await Task.CompletedTask;

            Logger.LogInformation($"MainProjectDb {FromVersion}->{ToVersion}升级完成");
        }

        protected override async Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行MainProjectDb {ToVersion}->{FromVersion}回滚");

            // 空回滚，无需执行任何操作
            Logger.LogInformation("这是一个空回滚，无需执行任何数据库更改");

            // 模拟一些异步操作
            await Task.CompletedTask;

            Logger.LogInformation($"MainProjectDb {ToVersion}->{FromVersion}回滚完成");
        }
    }
}
