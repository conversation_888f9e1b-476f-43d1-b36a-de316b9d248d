﻿using BimBase.Api.Infrastructure.MainDomain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using System;

namespace BimBase.Api.Infrastructure.DbInitializer
{
    public class MainProjectDbContext:DbContext
    {
        private string connectionString;
        public MainProjectDbContext(string nameOrConnectionString)
        {
            connectionString = nameOrConnectionString;
        }

        public MainProjectDbContext(DbContextOptions<MainProjectDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            optionsBuilder.LogTo(Console.WriteLine, LogLevel.Warning, DbContextLoggerOptions.DefaultWithLocalTime | DbContextLoggerOptions.SingleLine)
                //.EnableSensitiveDataLogging()
                .EnableDetailedErrors();
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<MPCatalogTreeNode>()
                .HasIndex(u => new { u.LibId, u.InstanceId })
                .IsUnique();
            modelBuilder.Entity<MPLibraryDataHistory>()
                .HasIndex(u => new { u.LibId });
            modelBuilder.Entity<MPLibraryData>()
                .HasIndex(u => new { u.LibId, u.DataId })
                .IsUnique();
            // 移除唯一索引，改为普通索引以支持 ON DUPLICATE KEY UPDATE 的节点名称冲突处理
            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => new { u.InstanceId, u.subProjectld })
                .HasDatabaseName("IX_MPProjectTreeNodes_InstanceId_subProjectld");

            // MPProjectTreeNodes 表的其他索引
            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => u.subProjectld)
                .HasDatabaseName("idx_subprojectId");

            // 动态冲突检测索引（基于ConflictScope的唯一索引）
            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => new { u.subProjectld, u.ConflictScope, u.NodeName })
                .IsUnique()
                .HasDatabaseName("idx_subprojectId_conflictScope_nodename");

            // 添加冲突相关字段的索引
            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => u.HasConflict)
                .HasDatabaseName("idx_hasConflict");

            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => new { u.subProjectld, u.NodeId })
                .HasDatabaseName("idx_subproject_node");

            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => new { u.subProjectld, u.level, u.indexCode2 })
                .HasDatabaseName("idx_newindecode");

            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => new { u.subProjectld, u.indexCode2, u.level })
                .HasDatabaseName("idx_indexcode2");

            modelBuilder.Entity<MPProjectTreeNode>()
                .HasIndex(u => u.ParentNodeId)
                .HasDatabaseName("idx_parentId");

            // 注意：以下触发器需要在数据库升级脚本中创建，因为EF Core不直接支持触发器定义
            // trg_before_insert_indexcode2: BEFORE INSERT 触发器，自动设置 indexCode2 = LEFT(indexCode, 190)
            // trg_before_update_indexcode2: BEFORE UPDATE 触发器，自动设置 indexCode2 = LEFT(indexCode, 190)
            
            // 其他配置...
            base.OnModelCreating(modelBuilder);
        }

        #region 数据表
        public DbSet<MPDatabaseVersion> DatabaseVersions { get; set; }
        public DbSet<BPExObject> BPExObjects { get; set; }
        public DbSet<BPExObjectPublish> BPExObjectPublishes { get; set; }
        public DbSet<ProvideVersionInfo> ProvideVersionInfoes { get; set; }
        public DbSet<MPUserGroup> MPUserGroups { get; set; }
        public DbSet<MPUserGroupMember> MPUserGroupMembers { get; set; }
        public DbSet<MPUserGroupAuth> MPUserGroupAuths { get; set; }
        public DbSet<MPTeamProject> MPTeamProjects { get; set; }
        public DbSet<MPFileDirectory> MPFileDirectories { get; set; }
        public DbSet<MPVolume> MPVolumes { get; set; }
        public DbSet<MPVolumeVersion> MPVolumeVersions { get; set; }
        public DbSet<MPCloudLinkFile> MPCloudLinkFiles { get; set; }
        public DbSet<MPProjectTreeNode> MPProjectTreeNodes { get; set; }

        public DbSet<MPLibraryInfo> MPLibraryInfoes { get; set; }

        public DbSet<MPUserGroupLibAuth> MPUserGroupLibAuths { get; set; }

        public DbSet<MPLibLock> MPLibLocks { get; set; }

        public DbSet<MPReleaseConfig> MPReleaseConfigs { get; set; }

        public DbSet<MPMessage> MPMessages { get; set; }
        public DbSet<MPDrawing> MPDrawings { get; set; }


        public DbSet<MPTreeNodeVersion> MPTreeNodeVersions { get; set; }
        public DbSet<MPCatalogTreeNode> MPCatalogTreeNodes { get; set; }
        public DbSet<MPLibraryData> MPLibraryDatas { get; set; }
        public DbSet<MPLibraryDataHistory> MPLibraryDataHistories { get; set; }
        #endregion
    }
}
