# GRPC 元数据标准规范

本文档规定了系统中 GRPC 请求的元数据(Metadata)和响应尾部数据(Trailers)的标准格式。所有客户端和服务端应当遵循此规范，以确保一致的数据交换和日志记录。

## 1. 请求元数据 (Request Metadata)

客户端发送请求时，可以在请求头中包含以下元数据：

| 元数据键名 | 类型 | 必填 | 说明 |
|----------|------|------|------|
| `session-id` | string | 否 | 会话标识符，用于身份验证和会话跟踪。如提供，服务器将根据会话ID获取用户信息 |
| `user-id` | string | 否 | 用户ID。仅当无法通过session-id获取用户信息时使用 |
| `user-name` | string | 否 | 用户名。仅当无法通过session-id获取用户信息时使用 |
| `client-id` | string | 是 | 客户端标识符，用于区分不同的客户端应用 |
| `client-version` | string | 是 | 客户端版本号，格式为x.y.z |
| `client-ip` | string | 否 | 客户端IP地址。客户端可主动提供，优先级高于服务器自动检测的IP |
| `physical-address` | string | 否 | 客户端物理地址(MAC地址)，格式如 "00:1A:2B:3C:4D:5E" |

### 示例（C#）

```csharp
// 创建元数据
var metadata = new Metadata
{
    { "session-id", "session123456" },
    { "client-id", "desktop-client" },
    { "client-version", "2.5.1" },
    { "client-ip", "***********" },
    { "physical-address", "00:1A:2B:3C:4D:5E" }
};

// 发送请求
var call = client.YourMethod(request, metadata);
```

## 2. 响应尾部数据 (Response Trailers)

服务器在响应中将包含以下尾部元数据：

| 尾部数据键名 | 类型 | 说明 |
|------------|------|------|
| `request-id` | string | 服务器为每个请求生成的唯一标识符(GUID格式)，用于日志关联和问题排查 |
| `processing-time-ms` | string | 服务器处理请求所需的时间，单位为毫秒 |

### 示例（C#）

```csharp
// 客户端读取响应尾部数据
var call = client.YourMethod(request, metadata);
var response = await call.ResponseAsync;
var trailers = call.GetTrailers();

// 获取请求ID和处理时间
string requestId = trailers.GetValue("request-id");
string processingTime = trailers.GetValue("processing-time-ms");
Console.WriteLine($"Request ID: {requestId}, Processing time: {processingTime}ms");
```

## 3. 特殊说明

### 3.1 IP地址获取优先级

系统使用以下优先级获取客户端IP地址：

1. 首先检查请求元数据中的 `client-ip`
2. 如无法获取，则使用 `context.Peer` 自动检测IP地址

系统会记录IP地址的来源，存储在请求上下文的 `ClientIpSource` 中：
- `"Header"` 表示IP来自客户端设置的请求头
- `"Peer"` 表示IP由服务器自动从连接中获取

### 3.2 请求上下文传递

请求上下文（RequestContext）使用 `AsyncLocal<T>` 在异步操作之间传递，确保在整个请求处理链中保持一致。对于复杂的异步场景，建议在主线程中初始化上下文，确保后续异步操作能正确访问上下文信息。

## 4. 日志记录

系统将记录所有请求的以下信息：

- 请求ID（RequestId）
- 会话ID（SessionId）
- 用户ID和用户名（UserId, UserName）
- 请求方法（Method）
- 状态码（StatusCode）
- 处理时间（TotalMilliseconds）
- 客户端IP（ClientIp）
- 客户端版本（ClientVersion）

这些信息将用于性能监控、问题排查和审计追踪。

## 5. 版本历史

| 版本 | 日期 | 变更说明 |
|-----|------|---------|
| 1.0 | 2025-05-21 | 初始版本，定义基本元数据标准 | 