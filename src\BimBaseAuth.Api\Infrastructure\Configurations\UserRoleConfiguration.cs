﻿using BimBaseAuth.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Configurations
{
    public partial class UserRoleConfiguration : IEntityTypeConfiguration<UserRole>
    {
        public void Configure(EntityTypeBuilder<UserRole> entity)
        {
            entity.ToTable("userrole");
            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.AppId)
                .HasCharSet("utf8")
                .UseCollation("utf8_general_ci");

            entity.Property(e => e.CreateId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.RoleId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            entity.Property(e => e.UserId)
                .HasCharSet("utf8")
                .UseCollation("utf8_bin");

            OnConfigurePartial(entity);
        }

        partial void OnConfigurePartial(EntityTypeBuilder<UserRole> entity);
    }
}
