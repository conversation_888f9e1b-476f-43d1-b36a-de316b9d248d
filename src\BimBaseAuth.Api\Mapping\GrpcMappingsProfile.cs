﻿using AutoMapper;
using BimBaseAuth.Api.Infrastructure.Domain;
using BimBaseAuth.Api.Protos;
using Google.Protobuf.WellKnownTypes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Mapping
{
    public class GrpcMappingsProfile: Profile
    {
        public GrpcMappingsProfile()
        {
            CreateMap<Role, GrpcRole>()
                .ForMember(desc => desc.CreateTime, opt => opt.MapFrom(src => Timestamp.FromDateTime(src.CreateTime)))
                .ForMember(desc => desc.UpdateTime, opt => opt.MapFrom(src => Timestamp.FromDateTime(src.UpdateTime)))
                .ForMember(desc => desc.Id, opt => opt.MapFrom(src => src.Id.ToString()))
                .ForMember(desc => desc.CreateId, opt => opt.MapFrom(src => src.CreateId.ToString()))
                .ReverseMap()
                .ForMember(src=> src.CreateTime, opt => opt.MapFrom(desc => desc.CreateTime.ToDateTime()))
                .ForMember(src=> src.UpdateTime, opt => opt.MapFrom(desc => desc.UpdateTime.ToDateTime()))
                .ForMember(src=> src.Id, opt => opt.MapFrom(desc => Guid.Parse(desc.Id)))
                .ForMember(src=> src.CreateId, opt => opt.MapFrom(desc => Guid.Parse(desc.CreateId)))
                .ReverseMap();


            CreateMap<AuthInfo, GrpcAuthInfo>()
                .ForMember(desc => desc.UpdateTime, opt => opt.MapFrom(src => Timestamp.FromDateTime(src.UpdateTime)))
                .ForMember(desc => desc.Id, opt => opt.MapFrom(src => src.Id.ToString()))
                .ForMember(desc => desc.CreateId, opt => opt.MapFrom(src => src.CreateId.ToString()))
                .ReverseMap()
                .ForMember(src => src.UpdateTime, opt => opt.MapFrom(desc => desc.UpdateTime.ToDateTime()))
                .ForMember(src => src.Id, opt => opt.MapFrom(desc => Guid.Parse(desc.Id)))
                .ForMember(src => src.CreateId, opt => opt.MapFrom(desc => Guid.Parse(desc.CreateId)))
                .ReverseMap();


        }
    }
}
