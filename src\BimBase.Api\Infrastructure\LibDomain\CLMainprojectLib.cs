﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Xml.Serialization;

namespace BimBase.Api.Infrastructure.LibDomain
{
    [XmlRoot(Namespace = "")]
    public class CLMainprojectLib
    {
        /// <summary>
        /// 自增id
        /// </summary>
        [Key]
        
        public int Id { get; set; }


        
        public Guid MainProjectGuid { get; set; }
        
        public Guid LibGuid { get; set; }
        
        public long TreeId { get; set; }
        
        public int TreeType { get; set; }
    }
}
