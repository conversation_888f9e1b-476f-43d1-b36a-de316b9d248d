# ModelDb 数据库升级系统

## 概述

ModelDb升级系统提供了一套完整的数据库版本管理和升级机制，参考TeamDb的架构设计。

## 架构组件

### 核心接口和基类

- **IModelDbUpgrade**: 定义升级操作的基本接口
- **AbstractModelDbUpgrade**: 提供升级操作的抽象基类，包含通用逻辑和辅助方法

### 升级管理器

- **ModelDbUpgradeManager**: 负责注册升级类、计算升级路径、执行升级操作

### 初始化器

- **ModelDbInitializer**: 主要的初始化入口，负责数据库创建和版本管理

### 数据上下文

- **ModelDbContext**: 已移动到DbInitializer命名空间，添加了DatabaseVersion表用于版本管理

## 使用方式

### 1. 添加新的升级逻辑

创建新的升级类，继承自`AbstractModelDbUpgrade`：

```csharp
public class ModelDbUpgrade_v2_to_v3 : AbstractModelDbUpgrade
{
    public ModelDbUpgrade_v2_to_v3(ILogger logger) : base(logger) { }

    public override string FromVersion => "v2";
    public override string ToVersion => "v3";
    public override string Description => "添加新功能的数据库升级";

    protected override async Task ExecuteUpgradeAsync(ModelDbContext context)
    {
        // 实现具体的升级逻辑
        await AddNewTableAsync(context);
        await ModifyExistingTableAsync(context);
    }
}
```

### 2. 注册升级类

在`ModelDbUpgradeManager.RegisterUpgrades()`方法中注册新的升级类：

```csharp
var upgrades = new List<IModelDbUpgrade>
{
    new ModelDbUpgrade_v0_to_v1(_logger),
    new ModelDbUpgrade_v1_to_v2(_logger),
    new ModelDbUpgrade_v2_to_v3(_logger)  // 新增
};
```

### 3. 版本管理配置

升级系统使用`DatabaseVersioningOptionsProvider`进行配置：

```json
{
  "DatabaseVersioningOptions": {
    "AutoExecute": true,
    "TargetVersion": "v2"
  }
}
```

## 辅助方法

### 表和列检查

```csharp
// 检查表是否存在
bool tableExists = await TableExistsAsync(context, "TableName");

// 检查列是否存在
bool columnExists = await ColumnExistsAsync(context, "TableName", "ColumnName");

// 检查索引是否存在
bool indexExists = await IndexExistsAsync(context, "TableName", "IndexName");
```

### SQL执行

```csharp
// 执行原生SQL
await context.Database.ExecuteSqlRawAsync(sql);
```

## 版本流程

1. **v0 -> v1**: 创建版本管理表和初始化StoreyLocks数据
2. **v1 -> v2**: 示例升级（添加索引和优化表结构）
3. **后续版本**: 根据需要添加新的升级逻辑

## 事务安全

所有升级操作都在事务中执行，确保：
- 升级过程中如果出现错误，自动回滚所有更改
- 版本记录的更新与业务逻辑更改保持一致性

## 与TeamDb的差异

1. **命名空间**: ModelDb使用独立的命名空间`BimBase.Api.Infrastructure.DbInitializer.ModelDb`
2. **初始化时机**: ModelDb在ProjectRepository中按需初始化，而不是在应用启动时
3. **版本管理表**: 共享同一个DatabaseVersion表结构，但数据独立
4. **兼容性过滤**: ModelDb不需要实现客户端版本兼容性过滤

## 注意事项

1. 升级类必须按顺序命名和实现（v0->v1, v1->v2, v2->v3...）
2. 每个升级操作都应该是幂等的（可以安全地重复执行）
3. 数据库结构变更使用原生SQL，以确保跨EF版本的兼容性
4. 升级逻辑应该向后兼容，避免破坏现有数据 