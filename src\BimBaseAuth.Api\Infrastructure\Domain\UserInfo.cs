﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BimBaseAuth.Api.Infrastructure.Domain
{
	public class UserInfo : Entity
	{
		public string Account
		{
			get;
			set;
		}

		public string Password
		{
			get;
			set;
		}

		public string Name
		{
			get;
			set;
		}

		public int Sex
		{
			get;
			set;
		}

		public int Status
		{
			get;
			set;
		}

		public int Type
		{
			get;
			set;
		}

		public string BizCode
		{
			get;
			set;
		}

		public DateTime CreateTime
		{
			get;
			set;
		}

		public UserInfo()
		{
			Account = string.Empty;
			Password = string.Empty;
			Name = string.Empty;
			Sex = 0;
			Status = 1;
			Type = 0;
			BizCode = string.Empty;
			CreateTime = DateTime.Now;
		}
	}
}
