﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class DomainRelationship
    {
        [Key]
        public Int64 Id { get; set; }


        [Required]
        public Int64 SourceId { get; set; }

        [Required]
        public int SourceDomainClassId { get; set; }

        [Required]
        public String SourceEcSchemaName { get; set; }

        public int SourceDomain { get; set; }

        [Required]
        public Int64 TargetId { get; set; }

        [Required]
        public int TargetDomainClassId { get; set; }

        [Required]
        public String TargetEcSchemaName { get; set; }

        public int TargetDomain { get; set; }

        public int SourceVersionNo { get; set; }
    }
}
