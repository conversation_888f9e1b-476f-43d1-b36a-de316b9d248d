﻿using BimBase.Api.Infrastructure.Domain;
using Microsoft.EntityFrameworkCore;

namespace BimBase.Api.Infrastructure
{
    public class LogDbContext: DbContext
    {
        public DbSet<PbimLog> pbimlogs { get; set; }
        public LogDbContext(DbContextOptions<LogDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

        }
    }
}
