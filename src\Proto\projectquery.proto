syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";

package bimbase.api;

message ProjectQueryRequest{
	string sessionId = 1;
	string projectId = 2;
}



message GetProjectMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated AuthInfoDto authInfos = 4;
	repeated GrpcProjectMember projectMembers = 5;
}


message GetProjectRolesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated RoleDto roles = 4;
}

message GetProjectDescriptionResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string projectDescription = 4;
}


message GetProjectNameResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string projectName = 4;
}

message GetProjectRoleMembersRequest{
	string sessionId = 1;
	string projectId = 2;
	string roleGuid = 3;
}

message GetProjectRoleMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcProjectMember projectMembers = 4;
}

message LoadMemberRoleByMemIDsRequest{
	string sessionId = 1;
	string projectId = 2;
	repeated GrpcProjectMember projectMembers = 3;
}

message GrpcMemberRoleInfo{
		int32 id = 1;
		string GuidID = 2;
		string loginName = 3;
		string displayName = 4;
		string roleName = 5;
		string avatar = 6;
}

message LoadMemberRoleByMemIDsResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMemberRoleInfo memRoleInfos = 4;
}



message GetUserAuthoritiesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated AuthInfoDto authInfos = 4;
}

message GetRoleAuthoritiesRequest{
	string sessionId = 1;
	string roleId = 2;
}


message GetRoleAuthoritiesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated AuthInfoDto authInfos = 4;
}

service GrpcProjectQuery {
	rpc GetProjectMembers(ProjectQueryRequest) returns (GetProjectMembersResponse){
		option (google.api.http) = {
			post: "/v1/GetProjectMembers"
			body: "*"
		};
	}

	rpc GetProjectRoles(ProjectQueryRequest) returns (GetProjectRolesResponse){
		option (google.api.http) = {
			post: "/v1/GetProjectRoles"
			body: "*"
		};
	}

	rpc GetProjectDescription(ProjectQueryRequest) returns (GetProjectDescriptionResponse){
		option (google.api.http) = {
			post: "/v1/GetProjectDescription"
			body: "*"
		};
	}

	rpc GetProjectName (ProjectQueryRequest) returns (GetProjectNameResponse){
		option (google.api.http) = {
			post: "/v1/GetProjectName"
			body: "*"
		};
	}

	rpc GetProjectRoleMembers(GetProjectRoleMembersRequest) returns (GetProjectRoleMembersResponse){
		option (google.api.http) = {
			post: "/v1/GetProjectRoleMembers"
			body: "*"
		};
	}


	rpc LoadMemberRoleByMemIDs(LoadMemberRoleByMemIDsRequest) returns (LoadMemberRoleByMemIDsResponse){
		option (google.api.http) = {
			post: "/v1/LoadMemberRoleByMemIDs"
			body: "*"
		};
	}

	rpc GetUserAuthorities(ProjectQueryRequest) returns (GetUserAuthoritiesResponse){
		option (google.api.http) = {
			post: "/v1/GetUserAuthorities"
			body: "*"
		};
	}

	rpc GetRoleAuthorities(GetRoleAuthoritiesRequest) returns(GetRoleAuthoritiesResponse){
		option (google.api.http) = {
			post: "/v1/GetRoleAuthorities"
			body: "*"
		};
	}

}