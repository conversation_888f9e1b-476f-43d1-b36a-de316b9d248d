{
    "urls": {
        "GrpcBimBaseAuth": "http://localhost:9001",
        "UploadRootPath": "E:\\GrpcDocumentTemp"
    },
  "ConnectionStrings": {
    "DefaultConnection": "server=localhost; port=3306; database=pkpm-pbimserver-teamdb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=80;Min Pool Size=10;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LoggerConnection": "server=localhost; port=3306; database=bimbase-logdb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=40;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "LibraryConnection": "server=localhost; port=3306; database=pkpm-pbimserver-librarydb; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=60;Min Pool Size=8;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    "TemplateConnection": "server=localhost; port=3306; database={DatabaseName}; user=root; password=******; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=150;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"DefaultConnection": "server=**************; port=8339; database=pkpm-pbimserver-teamdb; user=root; password=************; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=80;Min Pool Size=10;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"LoggerConnection": "server=**************; port=8339; database=bimbase-logdb; user=root; password=************; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=40;Min Pool Size=5;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"LibraryConnection": "server=**************; port=8339; database=pkpm-pbimserver-librarydb; user=root; password=************; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=60;Min Pool Size=8;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"TemplateConnection": "server=**************; port=8339; database={DatabaseName}; user=root; password=************; Persist Security Info=True; Connect Timeout=300;AllowLoadLocalInfile=true;SslMode=None;Max Pool Size=150;Min Pool Size=30;Pooling=true;Connection Lifetime=600;Connection Reset=true;",
    //"RedisConnection": "localhost:6379"
  }
}
