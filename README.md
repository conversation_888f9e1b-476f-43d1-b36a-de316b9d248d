# BIMBaseServer 项目分析

## 1. 项目概述

BIMBaseServer 是一个基于 .NET 8.0 的 BIM (建筑信息模型) 服务器项目，提供了 BIM 数据管理、团队协作、模型共享等功能。该项目采用微服务架构，使用 gRPC 作为主要通信协议，支持高效的数据传输和服务间通信。

## 2. 技术栈

- **框架**: .NET 8.0
- **通信协议**: gRPC
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: Entity Framework Core
- **日志**: Serilog
- **对象映射**: AutoMapper
- **容器化**: Docker

## 3. 目录结构分析

为了更详细地了解目录结构，我们可以逐层探索。以下是根据现有信息整理的主要目录结构：

```
BIMBaseServer/
├── .vscode/                  # VS Code 配置文件
├── depend/                   # 项目依赖
├── docs/                     # 文档
├── init/                     # 初始化脚本
│   └── init.sql              # 数据库初始化SQL脚本
├── src/                      # 源代码
│   ├── BimBase.Api/          # 主要 API 服务
│   │   ├── Grpc/             # gRPC 服务实现
│   │   ├── Infrastructure/   # 基础设施代码（数据库上下文等）
│   │   ├── Mappings/         # AutoMapper映射配置
│   │   └── Resources/        # 资源文件
│   ├── BimBaseAuth.Api/      # 认证服务
│   │   ├── Grpc/             # 认证相关gRPC服务实现
│   │   └── Infrastructure/   # 认证服务基础设施
│   └── Proto/                # Protobuf 定义文件
│       ├── auth.proto        # 认证服务定义
│       ├── bimbaseshared.proto # 共享数据结构定义
│       ├── librarymanagement.proto # 库管理服务定义
│       ├── mainprojectmanagement.proto # 主项目管理服务定义
│       ├── mainprojectquery.proto # 主项目查询服务定义
│       ├── projectquery.proto # 项目查询服务定义
│       ├── modelupload.proto # 模型上传服务定义
│       └── teammanagement.proto # 团队管理服务定义
├── docker-compose.yml        # Docker 编排配置
├── docker-compose.dev.yml    # 开发环境Docker配置
└── BimBaseServer.sln         # 解决方案文件
```
如果需要更深层次的目录结构，请告知，我可以尝试使用工具进行扫描。

## 4. 数据库分析

### 4.1 数据库实例

项目使用 MySQL 8.0 作为数据库，分为多个独立的数据库实例：

1.  **authdata (openauth)**: 存储用户认证和权限数据
    *   用户信息、角色、权限等
2.  **teamdata (pkpm-pbimserver-teamdb)**: 存储团队和项目管理数据
    *   团队信息、项目信息、成员关系等
3.  **logdata (bimbase-logdb)**: 存储系统日志
    *   操作日志、系统日志等
4.  **libdata (pkpm-pbimserver-librarydb)**: 存储 BIM 组件库数据
    *   BIM组件、元件库等

### 4.2 数据库初始化过程

1.  项目启动时，通过 `Program.cs` 中的 `CreateDbIfNotExists` 方法初始化数据库。
2.  各个服务的数据库初始化类：
    *   `BimBaseAuth.Api` 中的 `DbInitializer.Initialize`
    *   `BimBase.Api` 中的 `MainProjectDbInitializer.Initialize`
    *   `BimBase.Api` 中的 `ModelDbInitializer.Initialize`
    *   `BimBase.Api` 中的 `LibraryDbInitializer.Initialize`
3.  初始化过程主要包括：
    *   确保数据库存在 (`EnsureCreated`)
    *   添加初始用户和角色数据
    *   添加基础权限数据
    *   添加默认配置数据

### 4.3 动态切换数据库逻辑

项目实现了动态切换数据库的功能，主要通过以下方式：

1.  使用工厂模式创建数据库上下文：
    *   `MainProjectDbContextFactory`
    *   `ModelDbContextFactory`
    *   `LibraryDbContextFactory`
2.  动态构建连接字符串。例如，为主项目创建数据库连接时，会替换基础连接字符串中的数据库名：
    ```csharp
    var mpConn = _dbConnection.ConnectionString
        .Replace("database=pkpm-pbimserver-teamdb", "database=PKPM-PBIMServer-MPDB-" + guid.ToString(), StringComparison.OrdinalIgnoreCase);
    ```
3.  为每个主项目创建独立的数据库实例，命名规则：
    *   主项目数据库: `PKPM-PBIMServer-MPDB-{项目GUID}`
    *   模型数据库: `PKPM-PBIMServer-ModelDB-{项目GUID}`

## 5. 数据库模型分析

项目通过 Entity Framework Core 与数据库进行交互，定义了多个数据库上下文 (DbContext)，每个上下文对应一部分业务领域的数据库模型。以下是根据代码分析出的主要数据库模型：

### 5.1 认证数据库模型 (`OpenAuthDBContext` - 位于 `BimBaseAuth.Api`)

此上下文主要负责用户认证和权限管理相关的数据。

-   `UserInfo`: 存储用户的基本信息。
-   `Role`: 定义系统中的角色。
-   `AuthInfo`: 存储具体的权限信息。
-   `UserRole`: 用户与角色的多对多关联表。
-   `RoleExtend`: 角色的扩展信息。
-   `RoleRelation`: 角色之间的关系（例如，角色的层级或继承关系）。
-   `UserRelation`: 用户之间的关系。
-   `UserRelated`: 用户的关联信息。

### 5.2 团队与主项目基础数据库模型 (`TeamDbContext` - 位于 `BimBase.Api`)

此上下文管理团队、主项目的基础信息、共享配置以及备份等数据。

-   `MainProject`: 主项目核心信息。
-   `TeamProject`: 团队内的具体项目信息。
-   `TeamMember`: 团队成员及其在团队中的角色或信息。
-   `MainProjectTeamGroup`: 主项目与团队组的关联。
-   `TeamGroup`: 团队组信息。
-   `TeamAuth`: 团队权限设置。
-   `TeamUserGroup`: 团队用户组信息。
-   `LoginSession`: 用户登录会话信息。
-   `FileDirectory`: 项目中的文件目录结构。
-   `Volume`: 卷（可能是存储单元或逻辑分区）信息。
-   `VolumeVersion`: 卷的版本信息。
-   `BackupPath`: 数据库或项目备份文件的存储路径配置。
-   `ProjectBackupData`: 项目备份操作的元数据记录。
-   `DBBackupData`: 数据库备份操作的元数据记录。
-   `commonfiles`: 通用文件存储记录。
-   `cloudlinkfiles`: 特定于 CloudLink 集成的文件记录。
-   `IpMap`: IP 地址映射或相关配置。
-   `TeamProjectModelLock`: 团队项目中模型文件的锁定状态。
-   `RepositoryInformation`: 代码仓库或数据仓库的相关信息。
-   `MainProjectUserGroupMember`: 主项目内用户组与成员的关联。
-   `MainProjectUserGroupAuth`: 主项目内用户组的权限配置。
-   `MainProjectUserGroupLibAuth`: 主项目内用户组针对库资源的权限配置。
-   `MainProjectUserRole`: 主项目内用户与角色的关联。
-   `CacheMainproject`: 可能用于缓存主项目相关数据，以提高访问性能。
-   `DbVersion`: 数据库自身版本信息，可能用于迁移跟踪。
-   `SchemaVersion` (表名 `schemaversions`): 数据库模式版本信息。
-   `ObjectInfo` (表名 `objectinfoes`): 通用对象信息，具体用途需结合业务场景。

### 5.3 库数据库模型 (`LibraryDbContext` - 位于 `BimBase.Api`)

此上下文负责管理 BIM 组件库、公司标准库等相关的模型数据。

-   `CLCompanyLibInfo` (表名 `clcompanylibinfoes`): 公司库的基本信息。
-   `CLCatalogTreeNode`: 公司库中的目录树节点，用于组织库资源。
-   `CLLibraryData`: 存储在公司库中的具体BIM组件或库数据条目。
-   `CLTreeNodeLock`: 库中树节点的锁定信息，用于协同操作。
-   `CLTreeNodeVersion`: 库中树节点的版本历史。
-   `CLLibraryDataHistory`: 库中数据条目的历史版本记录。
-   `CLMainprojectLib`: 主项目与公司库之间的关联或引用关系。

### 5.4 日志数据库模型 (`LogDbContext` - 位于 `BimBase.Api`)

此上下文用于存储系统的运行日志、错误日志等。

-   `PbimLog` (表名 `pbimlogs`): 存储项目产生的各类日志记录。

### 5.5 主项目特定数据库模型 (`MainProjectDbContext` - 动态创建, 定义于 `BimBase.Api`)

每个主项目会拥有独立的数据库实例，此上下文定义了主项目内部具体的业务数据模型。

-   `BPExObject`: BIM 扩展对象，可能是项目中核心的BIM构件或元素。
-   `BPExObjectPublish`: BIM 扩展对象的发布记录或状态。
-   `ProvideVersionInfo`: 提供给外部或用于发布的版本信息。
-   `MPUserGroup`: 主项目内部的用户组。
-   `MPUserGroupMember`: 主项目用户组的成员列表。
-   `MPUserGroupAuth`: 主项目用户组的权限配置。
-   `MPTeamProject`: 主项目下的团队子项目。
-   `MPFileDirectory`: 主项目内部的文件目录结构。
-   `MPVolume`: 主项目内部的卷信息。
-   `MPVolumeVersion`: 主项目内部卷的版本。
-   `MPCloudLinkFile`: 主项目相关的 CloudLink 文件记录。
-   `MPProjectTreeNode`: 主项目内部的项目结构树节点。
-   `MPLibraryInfo`: 主项目引用的或内部的库信息。
-   `MPUserGroupLibAuth`: 主项目用户组对库的特定权限。
-   `MPLibLock`: 主项目内部库资源的锁定信息。
-   `MPReleaseConfig`: 主项目的发布配置项。
-   `MPMessage`: 主项目内部的消息或通知。
-   `MPDrawing`: 主项目相关的图纸信息。
-   `MPTreeNodeVersion`: 主项目树节点的版本。
-   `MPCatalogTreeNode`: 主项目内部的目录结构树节点。
-   `MPLibraryData`: 主项目内部使用的库数据。
-   `MPLibraryDataHistory`: 主项目内部库数据的历史记录。

### 5.6 模型特定数据库模型 (`ModelDbContext` - 动态创建, 定义于 `BimBase.Api`)

每个主项目下的模型数据也可能存储在独立的数据库实例中，此上下文定义了与具体BIM模型相关的详细数据。

-   `ProjectMember`: 参与当前模型的项目成员。
-   `ModelData`: 存储核心的模型几何和属性数据。
-   `Relationship`: 模型元素之间的关系数据。
-   `VersionData`: 模型的版本数据。
-   `HistoryData`: 模型数据的历史变更记录。
-   `HistoryRelationship`: 模型关系的历史变更记录。
-   `ReleaseInformation`: 模型发布的相关信息。
-   `RepositoryInformation`: 与模型版本控制相关的仓库信息。
-   `VersionDomain`: 模型版本的特定领域或专业数据。
-   `SchemaVersion`: 模型数据库的模式版本。
-   `StoreyLock`: 模型中楼层或构件的锁定信息。
-   `ModelFile`: 与模型关联的物理文件记录。
-   `MilestoneFiles`: 模型在特定里程碑版本时的文件快照。
-   `ModelLock`: 模型的整体锁定状态。
-   `SingletonClass`: 设计模式中的单例类在数据库中的表示（需结合具体业务）。
-   `ResourceClass`: 模型中资源分类信息。
-   `LockedComponents`: 被锁定的具体模型构件列表。
-   `CheckInRequestRecord` (表名 `checkinrequestrecords`): 模型构件的检入请求记录。

以上列表是基于对 `DbContext` 文件中 `DbSet<>` 属性的分析得出的。具体的表名、列名、关系和约束会在各自的 `OnModelCreating` 方法中通过 Fluent API 或实体类中的特性 (Annotations)进一步详细定义。

## 6. gRPC 协议分析

项目采用 gRPC 作为主要的微服务间通信协议。Protobuf (.proto) 文件定义了服务契约和消息格式，主要集中在 `src/Proto/` 目录下，认证相关的 `auth.proto` 则位于 `src/BimBaseAuth.Api/Protos/`。

### 6.1 Protobuf 定义文件列表

以下是项目中主要的 `.proto` 文件及其大致用途：

*   **`src/Proto/` (核心业务服务定义):**
    *   `bimbaseshared.proto`: 定义项目中通用的、跨多个服务共享的数据结构和消息类型。
    *   `connection.proto`: 定义与服务连接测试、心跳或基本状态查询相关的接口。
    *   `librarymanagement.proto`: 定义 BIM 组件库的管理接口，如库的创建、修改、删除、组件上传、版本控制等。
    *   `libraryquery.proto`: 定义 BIM 组件库的查询接口，如搜索库、获取组件列表、查询组件详情等。
    *   `mainprojectmanagement.proto`: 定义主项目的管理接口，如创建主项目、配置项目参数、管理项目版本等。
    *   `mainprojectquery.proto`: 定义主项目的查询接口，如获取主项目列表、查询主项目详细信息、项目状态等。
    *   `modelquery.proto`: 定义 BIM 模型数据的查询接口，如获取模型元素、查询构件属性、空间结构查询等。
    *   `modelupload.proto`: 定义模型文件上传的简单接口。
    *   `modelwrite.proto`: 定义 BIM 模型数据的写入与修改接口，如创建/更新/删除模型元素、修改属性等。
    *   `projectfilemanagement.proto`: 定义项目文件的管理接口（可能较通用，具体范围需看实现）。
    *   `projectfilequery.proto`: 定义项目文件的查询接口。
    *   `projectmanagement.proto`: 定义普通项目（相对于主项目）或子项目的管理接口。
    *   `projectquery.proto`: 定义普通项目或子项目的查询接口。
    *   `teammanagement.proto`: 定义团队及成员管理的接口，如创建团队、添加/移除成员、设置成员角色/权限等。
    *   `teamquery.proto`: 定义团队及成员信息的查询接口。

*   **`src/BimBaseAuth.Api/Protos/` (认证服务定义):**
    *   `auth.proto`: 定义用户认证 (登录、登出、令牌验证)、用户管理、角色管理、权限管理相关的接口。

*   **`src/Proto/google/`**: (未列出具体文件) 通常包含 Google 提供的标准 Protobuf 类型，如 `Timestamp`, `Duration`, `Empty` 等，或用于 gRPC API 配置如 HTTP 转换的 `annotations.proto`。

### 6.2 主要 gRPC 服务实现

这些 `.proto` 文件中定义的服务在各个 API 项目的 `Grpc/` 目录下有具体的 C# 实现。以下是主要的服务实现类及其对应的 `.proto` 文件（推测）：

*   **在 `BimBase.Api` 项目 (`src/BimBase.Api/Grpc/`) 中:**
    *   `ConnectionService.cs`: 实现 `connection.proto` 中定义的服务。
    *   `LibraryManagementService.cs`: 实现 `librarymanagement.proto` 中定义的服务。
    *   `LibraryQueryService.cs`: 实现 `libraryquery.proto` 中定义的服务。
    *   `MainprojectManagementService.cs`: 实现 `mainprojectmanagement.proto` 中定义的服务。
    *   `MainprojectQueryService.cs`: 实现 `mainprojectquery.proto` 中定义的服务。
    *   `ModelQueryService.cs`: 实现 `modelquery.proto` 中定义的服务。
    *   `ModelUploadService.cs`: 实现 `modelupload.proto` 中定义的服务。
    *   `ModelWriteService.cs`: 实现 `modelwrite.proto` 中定义的服务。
    *   `ProjectFileManagementService.cs`: 实现 `projectfilemanagement.proto` 中定义的服务。
    *   `ProjectFileQueryService.cs`: 实现 `projectfilequery.proto` 中定义的服务。
    *   `ProjectManagementService.cs`: 实现 `projectmanagement.proto` 中定义的服务。
    *   `ProjectQueryService.cs`: 实现 `projectquery.proto` 中定义的服务。
    *   `TeamManagementService.cs`: 实现 `teammanagement.proto` 中定义的服务。
    *   `TeamQueryService.cs`: 实现 `teamquery.proto` 中定义的服务。
    *   `ServerLoggerInterceptor.cs`: 这是一个 gRPC 拦截器，用于服务器端日志记录，并非直接的服务实现。
    *   `GrpcCallerService.cs`: 可能是一个用于从 `BimBase.Api` 内部调用其他 gRPC 服务的客户端封装或辅助服务，并非对外提供的 gRPC 服务端点。

*   **在 `BimBaseAuth.Api` 项目 (`src/BimBaseAuth.Api/Grpc/`) 中:**
    *   `AuthService.cs`: 实现 `auth.proto` 中定义的用户认证和权限管理服务。
    *   `GrpcHealthCheckService.cs`: 实现了 gRPC 标准的健康检查服务，允许外部监控系统检查服务的健康状态。

每个服务类都会继承自从相应 `.proto` 文件生成的 `ServiceNameBase` 基类，并重写其中的方法来实现具体的业务逻辑。

## 7. 项目启动过程

项目推荐使用 **Docker Compose** 进行启动，这样可以确保所有依赖服务（包括数据库和各个API服务）按照正确的顺序和配置启动。

**使用 Docker Compose 启动 (推荐):**

1.  **环境准备:**
    *   确保您的系统已安装 Docker 和 Docker Compose。
    *   确保 Docker 服务正在运行。

2.  **启动命令:**
    *   打开终端或命令行工具。
    *   导航到项目的根目录 (即包含 `docker-compose.yml` 文件的目录)。
    *   执行以下命令启动所有服务：
        ```bash
        docker-compose up
        ```
    *   如果希望服务在后台运行，请使用 `-d` 参数：
        ```bash
        docker-compose up -d
        ```

3.  **服务说明:**
    *   `docker-compose.yml` 文件定义了以下主要服务：
        *   `authdata`: MySQL 数据库实例，用于认证服务。
        *   `teamdata`: MySQL 数据库实例，用于团队和项目管理数据。
        *   `logdata`: MySQL 数据库实例，用于存储系统日志。
        *   `libdata`: MySQL 数据库实例，用于存储 BIM 组件库数据。
        *   `bimbaseauth.api`: 认证授权 API 服务，依赖 `authdata`。
        *   `bimbase.api`: 核心业务 API 服务，依赖 `libdata`, `logdata`, `teamdata`, 以及 `bimbaseauth.api`。
    *   Docker Compose 会自动处理服务间的依赖关系，确保例如数据库和认证服务在核心业务API启动前可用。

4.  **数据库初始化:**
    *   在各个 API 服务 (`BimBase.Api` 和 `BimBaseAuth.Api`) 启动时，其内部的 `Program.cs` 文件中包含 `CreateDbIfNotExists` 逻辑。
    *   此逻辑会检查对应的数据库是否存在，如果不存在，则会尝试创建数据库并执行初始化操作（例如创建表、填充初始数据）。
        *   `BimBaseAuth.Api` 初始化 `OpenAuthDBContext` (对应 `authdata` 数据库)。
        *   `BimBase.Api` 初始化 `TeamDbContext` (对应 `teamdata`), `LibraryDbContext` (对应 `libdata`), 和 `LogDbContext` (对应 `logdata`)。

**环境配置与 `ASPNETCORE_ENVIRONMENT`:**

*   ASP.NET Core 应用通过 `hostingContext.HostingEnvironment.EnvironmentName` 属性来识别当前运行的环境（如 `Development`, `Staging`, `Production`）。
*   这个 `EnvironmentName` **主要通过名为 `ASPNETCORE_ENVIRONMENT` 的环境变量来指定**。例如，在开发时，通常将其设置为 `Development`。
*   如果未设置 `ASPNETCORE_ENVIRONMENT` 环境变量，则 `EnvironmentName` **默认为 `Production`**。
*   此环境名称决定了哪些特定于环境的配置文件会被加载。例如，如果 `EnvironmentName` 为 `Development`，则 `appsettings.Development.json` 文件中的配置会覆盖 `appsettings.json` 中的同名配置。
*   在使用 Docker Compose 时，可以在 `docker-compose.yml` 或 `docker-compose.override.yml` (或 `.env` 文件) 中为服务设置此环境变量，以控制其运行环境。

**单独启动服务 (主要用于开发和调试，不推荐用于完整环境):**

如果您需要单独启动某个API服务（例如进行特定模块的开发和调试），可以按以下步骤操作，但必须确保其依赖的数据库和其他服务已经通过 Docker Compose 或其他方式手动启动，并且正确设置了 `ASPNETCORE_ENVIRONMENT` 环境变量以加载相应的配置。

1.  **启动 `BimBaseAuth.Api`:**
    *   确保 `authdata` 数据库服务已运行。
    *   (可选) 设置 `ASPNETCORE_ENVIRONMENT=Development` (或其他目标环境)。
    *   在终端中，导航到 `src/BimBaseAuth.Api/` 目录。
    *   执行命令: `dotnet run`

2.  **启动 `BimBase.Api`:**
    *   确保 `libdata`, `logdata`, `teamdata` 数据库服务已运行。
    *   确保 `bimbaseauth.api` 服务已运行。
    *   (可选) 设置 `ASPNETCORE_ENVIRONMENT=Development` (或其他目标环境)。
    *   在终端中，导航到 `src/BimBase.Api/` 目录。
    *   执行命令: `dotnet run`

**注意:**

*   在首次使用 `docker-compose up` 时，如果本地没有对应的 Docker 镜像，Docker Compose 会尝试根据项目中的 `Dockerfile` 文件构建镜像，这可能需要一些时间。
*   `docker-compose.dev.yml` 文件可能包含针对开发环境的特定覆盖配置 (例如预设 `ASPNETCORE_ENVIRONMENT=Development`)，如果需要，可以使用 `docker-compose -f docker-compose.yml -f docker-compose.dev.yml up` 命令来合并配置。
*   应用的具体监听端口等配置信息可以在 `appsettings.json`、特定环境的 `appsettings.{EnvironmentName}.json` 和 `docker-compose.yml` 文件中查看或修改。

## 8. 建议掌握的知识点

为了更好地理解、参与开发和维护 BIMBaseServer 项目，建议掌握以下技术和概念：

1.  **.NET 8.0 与 C# 核心**:
    *   熟练掌握 C# 语言最新特性 (如异步编程, LINQ, 记录类型等)。
    *   深入理解 ASP.NET Core 框架，包括：
        *   中间件 (Middleware) 管道和自定义中间件的开发。
        *   依赖注入 (Dependency Injection) 机制，项目中主要使用内置 DI 及 Autofac (可见于 `BimBase.Api/Program.cs`)。
        *   配置系统 (Configuration)，如 `appsettings.json` 的使用和管理。
        *   路由 (Routing) 机制。
        *   后台任务 (Background Tasks) 和托管服务 (Hosted Services)。

2.  **gRPC 协议与实践**:
    *   深刻理解 Protocol Buffers (protobuf) v3 的语法、数据类型、消息定义和服务定义。
    *   掌握 gRPC 服务端和客户端的实现方式（C# 为主）。
    *   了解 gRPC 的四种通信模式 (Unary, Server streaming, Client streaming, Bidirectional streaming) 及其适用场景。
    *   熟悉 gRPC 的错误处理、元数据 (Metadata)、拦截器 (Interceptors, 如项目中的 `ServerLoggerInterceptor.cs`) 和截止时间/超时 (Deadlines/Timeouts) 机制。
    *   了解 gRPC API 的版本管理和向后兼容性策略。

3.  **Entity Framework Core (EF Core)**:
    *   熟练配置和使用数据库上下文 (`DbContext`)。
    *   掌握实体类与数据库表之间的高级映射技巧 (Fluent API)。
    *   精通使用 LINQ to Entities 进行高效的数据查询和更新操作。
    *   理解并能应用 EF Core 的数据迁移 (Migrations) 功能。
    *   了解 EF Core 的性能优化技巧，如查询优化、变更跟踪、批处理等。
    *   熟悉项目中动态创建和切换数据库上下文的模式 (`DbContextFactory` 和动态连接字符串)。

4.  **微服务架构与设计**:
    *   理解微服务架构的核心原则、优点和挑战。
    *   熟悉常见的服务间通信模式 (项目中主要为 gRPC，但也可能涉及其他如事件驱动等)。
    *   了解服务注册与发现的概念 (虽然本项目中 `docker-compose.yml` 隐式处理服务发现，但理解其原理很重要)。
    *   掌握分布式系统中的常见问题及解决方案，如数据一致性 (CAP理论, Saga模式等)、容错与弹性设计 (断路器, 重试机制)、分布式追踪。

5.  **Docker 与容器化技术**:
    *   掌握 Docker 的核心概念：镜像 (Images)、容器 (Containers)、卷 (Volumes)、网络 (Networking)。
    *   能够熟练编写 `Dockerfile` 来容器化 .NET 应用程序。
    *   熟练使用 Docker Compose (`docker-compose.yml`) 管理和编排多容器应用，理解服务依赖、端口映射、环境变量配置等。
    *   对容器编排有基本了解 (如 Kubernetes 的概念)。

6.  **数据库技术 (MySQL)**:
    *   熟悉关系型数据库设计的基本原则 (如范式、索引、事务)。
    *   掌握 MySQL 的基本 SQL 语法、数据类型和常用函数。
    *   了解 MySQL 的索引优化、查询性能分析和基本的数据库管理操作。
    *   理解项目中多数据库实例的划分依据和管理方式。

7.  **认证与授权机制**:
    *   理解常见的认证流程 (如基于 Token 的认证，如 JWT)。
    *   熟悉基于角色的访问控制 (RBAC) 模型及其在项目中的应用 (`OpenAuthDBContext` 相关的表结构)。
    *   了解权限管理的设计和实现策略。

8.  **日志、监控与可观测性**:
    *   熟悉结构化日志的概念和实践，项目中使用了 Serilog 进行日志记录，并输出到控制台、文件及 MariaDB/MySQL。
    *   了解如何在微服务架构中实现有效的日志聚合与查询。
    *   理解应用性能监控 (APM) 和分布式追踪的基本概念和工具。

9.  **BIM (建筑信息模型) 领域知识**:
    *   对 BIM 的基础概念、核心价值和工作流程有一定了解。
    *   熟悉常见的 BIM 数据格式 (如 IFC) 和相关的行业标准（如果适用）。
    *   理解 BIM 项目中的数据协同和版本管理需求。

10. **软件设计原则与模式**:
    *   熟悉 SOLID 原则等面向对象设计原则。
    *   能够识别和应用常见的设计模式 (如工厂模式、仓储模式、单例模式等，这些可能在项目的 `Infrastructure` 层有体现)。

掌握以上知识点将有助于您更高效地参与到项目的开发和维护中。