# LibraryDb升级系统

## 概述
LibraryDb升级系统提供了与MainProjectDb/ModelDb一致的数据库版本管理和升级功能。

## 架构组件

### 1. 核心文件
- `ILibraryDbUpgrade.cs` - 升级接口定义
- `AbstractLibraryDbUpgrade.cs` - 升级抽象基类，提供通用功能
- `LibraryDbUpgradeManager.cs` - 升级管理器，负责路径计算和执行
- `LibraryDbUpgrade_v0_to_v1.cs` - v0到v1升级实现

## 使用方式

### 添加新升级
1. 继承 `AbstractLibraryDbUpgrade`
2. 实现必要的抽象方法
3. 在 `LibraryDbUpgradeManager.RegisterUpgrades()` 中注册

### 示例升级类
```csharp
public class LibraryDbUpgrade_v1_to_v2 : AbstractLibraryDbUpgrade
{
    public LibraryDbUpgrade_v1_to_v2(ILogger logger) : base(logger) { }
    public override string FromVersion => "v1";
    public override string ToVersion => "v2";
    public override string Description => "升级描述";
    protected override async Task ExecuteUpgradeAsync(LibraryDbContext context)
    {
        // 升级逻辑
    }
}
```

## 特性
- 事务安全的升级过程
- 完整的日志记录
- 升级路径自动计算
- 幂等操作支持
- 与MainProjectDb/ModelDb架构一致

## 注意事项
- LibraryDb目前使用简化的版本管理
- 如需要完整版本管理，可以添加DatabaseVersion表
- 所有升级操作都在事务中执行，确保数据一致性 