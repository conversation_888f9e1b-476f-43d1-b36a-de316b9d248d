﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public enum RequirementStatus
    {
        Created,
        InProgress,
        Cancelling,
        Completed,
        Cancelled,
        Shelved
    }

    //public class Requirement
    //{
    //    public int ID { get; set; }
    //    public String Title { get; set; }
    //    public DateTime CreatedTime { get; set; }
    //    public RequirementStatus Status { get; set; }
    //    [ForeignKey("Author")]
    //    public int AuthorID { get; set; }
    //    public virtual ProjectMember Author { get; set; }
    //    public virtual List<Opinion> Opinions { get; set; }
    //    public virtual List<Participant> Participants { get; set; }
    //}
}
