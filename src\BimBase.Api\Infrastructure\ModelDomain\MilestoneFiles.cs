﻿using System.ComponentModel.DataAnnotations;
using System;

namespace BimBase.Api.Infrastructure.ModelDomain
{
    public class MilestoneFiles
    {

        
        [Key]
        public Int64 Id { get; set; }

        
        public string FileName { get; set; }

        
        public string SavePath { get; set; }
        
        public string Description { get; set; }

        
        public int VersionNo { get; set; }

        
        public string UserName { get; set; }

        //上传时间
        
        public DateTime UploadTime { get; set; }

        
        public string Status { get; set; }

        
        public string ExtendStr { get; set; }
    }



    public class MilestoneVersions
    {
        
        [Key]
        public Int64 Id { get; set; }
        
        public string FileName { get; set; }

        
        public int FileVersionNo { get; set; }

        
        public int Domain { get; set; }

        
        public int PreDomainVersionNo { get; set; }

        
        public int StoryDomainVersionNo { get; set; }

        
        public int PostDomainVersionNo { get; set; }

        
        public string ExtendStr { get; set; }
    }
}
