using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BimBase.Api.Infrastructure.DbInitializer.MainProjectDb
{
    /// <summary>
    /// MainProjectDb数据库从v7升级到v8
    /// 主要变更：
    /// 1. 移除MPProjectTreeNodes表的唯一索引 {InstanceId, subProjectld}
    /// 2. 添加MPProjectTreeNodes表的唯一索引 {subProjectld, ParentNodeId, NodeName}
    /// 3. 优化节点名称冲突检测的数据库结构
    /// </summary>
    public class MainProjectDbUpgrade_v7_to_v8 : AbstractMainProjectDbUpgrade
    {
        public override string FromVersion => "v7";
        public override string ToVersion => "v8";
        public override string Description => "优化MPProjectTreeNodes表索引结构，支持高效的节点名称冲突检测";

        public MainProjectDbUpgrade_v7_to_v8(ILogger logger) : base(logger)
        {
        }

        protected override async Task ExecuteUpgradeAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {FromVersion} -> {ToVersion} 升级...");

            await ExecuteUpgradeStepsAsync(context);

            Logger.LogInformation($"{FromVersion} -> {ToVersion} 升级完成");
        }

        /// <summary>
        /// 执行 v7 到 v8 的具体升级步骤
        /// </summary>
        private async Task ExecuteUpgradeStepsAsync(MainProjectDbContext context)
        {
            // 1. 移除旧的唯一索引
            await RemoveOldUniqueIndexAsync(context);

            // 2. 添加新的唯一索引
            await AddNewUniqueIndexAsync(context);
        }

        /// <summary>
        /// 移除旧的唯一索引 {InstanceId, subProjectld}
        /// </summary>
        private async Task RemoveOldUniqueIndexAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `IX_MPProjectTreeNodes_InstanceId_subProjectld`;
                ");
                Logger.LogInformation("成功移除旧的唯一索引 IX_MPProjectTreeNodes_InstanceId_subProjectld");
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"移除旧索引时发生异常（可能索引不存在）: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加新的唯一索引 {subProjectld, ParentNodeId, NodeName}
        /// </summary>
        private async Task AddNewUniqueIndexAsync(MainProjectDbContext context)
        {
            try
            {
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD UNIQUE INDEX `idx_subprojectId_parentId_nodename` (`subProjectld`, `ParentNodeId`, `NodeName`);
                ");
                Logger.LogInformation("成功添加新的唯一索引 idx_subprojectId_parentId_nodename");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "添加新索引失败");
                throw;
            }
        }

        protected override async Task ExecuteRollbackAsync(MainProjectDbContext context)
        {
            Logger.LogInformation($"开始执行 {ToVersion} -> {FromVersion} 回滚...");

            try
            {
                // 移除新添加的唯一索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    DROP INDEX `idx_subprojectId_parentId_nodename`;
                ");
                Logger.LogInformation("成功移除新添加的唯一索引");

                // 重新添加旧的唯一索引
                await context.Database.ExecuteSqlRawAsync(@"
                    ALTER TABLE `mpprojecttreenodes` 
                    ADD UNIQUE INDEX `IX_MPProjectTreeNodes_InstanceId_subProjectld` (`InstanceId`, `subProjectld`);
                ");
                Logger.LogInformation("成功重新添加旧的唯一索引");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "回滚操作失败");
                throw;
            }

            Logger.LogInformation($"{ToVersion} -> {FromVersion} 回滚完成");
        }
    }
} 