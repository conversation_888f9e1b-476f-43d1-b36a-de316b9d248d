version: '3.7'

services:
  authdata:
    container_name: auth-db
    image: mysql:8.0
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1
    restart: always
    healthcheck:
      test: "/usr/bin/mysql --user=bimbase --password=******* --execute \"use openauth;\""
      interval: 3s
      timeout: 1s
      retries: 5
    environment:
      TZ: Asia/Shanghai
      MYSQL_DATABASE: 'openauth'
      MYSQL_USER: 'bimbase'
      MYSQL_PASSWORD: '*******'
      MYSQL_ROOT_PASSWORD: '*******'
 #   ports:
 #     - '43306:3306'
    volumes:
      - bimbase-authdata:/var/lib/mysql

  teamdata:
    container_name: team-db
    image: mysql:8.0
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1
    restart: always
    healthcheck:
      test: "/usr/bin/mysql --user=bimbase --password=******* --execute \"use pkpm-pbimserver-teamdb;\""
      interval: 3s
      timeout: 1s
      retries: 5
    environment:
      TZ: Asia/Shanghai
      MYSQL_DATABASE: 'pkpm-pbimserver-teamdb'
      MYSQL_USER: 'bimbase'
      MYSQL_PASSWORD: '*******'
      MYSQL_ROOT_PASSWORD: '*******'
#    ports:
#      - '53306:3306'
    volumes:
      - bimbase-teamdata:/var/lib/mysql
      - ./init:/docker-entrypoint-initdb.d/

  redis:
    image: redis
    user: root
    container_name: redis-db
    command: ["redis-server", "--appendonly", "yes"]
    hostname: redis
    ports:
      - "6379:6379"
    networks:
      - default
    volumes:
      - redis-data:/data


  bimbase.api:
    image: harbor.pkpm.cn/bimbase/bimbase-api:v20210205
    container_name: bimbase-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - Kestrel__Endpoints__Http__Url=http://0.0.0.0:8001
      - Kestrel__Endpoints__Http__Protocols=Http1AndHttp2
      - Kestrel__Endpoints__Grpc__Url=http://0.0.0.0:8002
      - Kestrel__Endpoints__Grpc__Protocols=Http2
      - ConnectionStrings__DefaultConnection=server=team-db; port=3306; database=pkpm-pbimserver-teamdb; user=bimbase; password=*******; Persist Security Info=False; Connect Timeout=300
      - urls__GrpcBimBaseAuth=http://auth-grpc
      - HealthChecksUI__HealthChecks__0__Name=bimbase-api-local
      - HealthChecksUI__HealthChecks__0__Uri=http://localhost:8001/hc
      - ConnectionStrings__RedisConnection=redis-db
    restart: on-failure
    healthcheck:
      test: curl --fail -s  http://localhost:8001/weatherforecast || exit 1
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
    networks:
      - default
    dns:
      - *******
      - *******
    ports:
      - "8088:8001"
      - "8089:8002"  #http2 port
    depends_on:
      - teamdata
      - bimbaseauth.api
      - redis
#    command: ["./wait-for-it.sh","team-db:3306","--","dotnet","BimBase.Api.dll"]
    entrypoint: ["./wait-for-it.sh","team-db:3306","-t", "30","--","dotnet","BimBase.Api.dll"]



  bimbaseauth.api:
    image: harbor.pkpm.cn/bimbase/bimbaseauth-api:v20210205
    container_name: auth-grpc
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=server=auth-db; port=3306; database=openauth; user=bimbase; password=*******; Persist Security Info=False; Connect Timeout=300
#      - Kestrel__Endpoints__Grpc__Url=http://0.0.0.0:5581
#      - Kestrel__Endpoints__Grpc__Protocols=Http2
    restart: on-failure
    healthcheck:
      test: ["CMD","/bin/grpc_health_probe", "-addr=:80"]
      interval: 60s
      timeout: 10s
      retries: 5
      start_period: 10s
    networks:
      - default
    dns:
      - *******
      - *******
    depends_on:
      - authdata
    entrypoint: ["./wait-for-it.sh", "auth-db:3306", "-t", "30", "--", "dotnet", "BimBaseAuth.Api.dll"]
    #command: [ "auth-db:3306", "--", "dotnet", "BimBaseAuth.Api.dll"]


  pingtools:
    image: busybox
    container_name: busybox
    command: sleep infinity


volumes:
  bimbase-authdata:
    external: false
  bimbase-teamdata:
    external: false
  redis-data:
    external: false

